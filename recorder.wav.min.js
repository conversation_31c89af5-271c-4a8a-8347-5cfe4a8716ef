!function (e) {var t = "object" == typeof window && !!window.document, r = t ? window : Object; !function (e, x) {"use strict"; var w = function () {}, u = function (e) {return "number" == typeof e }, I = function (e) {return JSON.stringify(e) }, X = function (e) {return new c(e) }, M = X.LM = "2025-01-11 09:28", k = "https: var f = X.Traffic = function (e) {if (x) {e = e ? "/" + T + "/Report/" + e : ""; var t = X.TrafficImgUrl; if (t) {var r = X.Traffic, n = /^(https?:..[^\/#]*\/?)[^#]*/i.exec(location.href) || [], a = n[1] || "http: }(r, t), "function" == typeof define && define.amd && define(function () {return r.Recorder }), "object" == typeof module && module.exports && (module.exports = r.Recorder) }();