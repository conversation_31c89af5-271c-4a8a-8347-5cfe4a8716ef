/*
录音
https://github.com/xiangyuecn/Recorder
src: recorder-core.js,engine/wav.js
*/
!function(e){var t="object"==typeof window&&!!window.document,r=t?window:Object;!function(e,S){"use strict";var x=function(){},u=function(e){return"number"==typeof e},L=function(e){return JSON.stringify(e)},X=function(e){return new c(e)},M=X.LM="2025-01-11 09:28",k="https://github.com/xiangyuecn/Recorder",T="Recorder",I="getUserMedia",G="srcSampleRate",N="sampleRate",i="bitRate",A="catch",t=e[T];if(t&&t.LM==M)return t.CLog(t.i18n.$T("K8zP::重复导入{1}",0,T),3);<PERSON><PERSON>=function(){var e=X.Stream;if(e){var t=W(e),r=t[0];if(r){var n=r.readyState;return"live"==n||n==r.LIVE}}return!1},<PERSON><PERSON><PERSON><PERSON>er<PERSON>ize=4096,X.Destroy=function(){for(var e in q(T+" Destroy"),P(),r)r[e]()};var r={};X.BindDestroy=function(e,t){r[e]=t},X.Support=function(){if(!S)return!1;var e=navigator.mediaDevices||{};return e[I]||(e=navigator)[I]||(e[I]=e.webkitGetUserMedia||e.mozGetUserMedia||e.msGetUserMedia),!!e[I]&&(X.Scope=e,!!X.GetContext())},X.GetContext=function(e){if(!S)return null;var t=window.AudioContext;if(t||(t=window.webkitAudioContext),!t)return null;var r=X.Ctx,n=0;return r||(r=X.Ctx=new t,n=1,X.NewCtxs=X.NewCtxs||[],X.BindDestroy("Ctx",function(){var e=X.Ctx;e&&e.close&&(a(e),X.Ctx=0);var t=X.NewCtxs;X.NewCtxs=[];for(var r=0;r<t.length;r++)a(t[r])})),e&&r.close&&(n||(r._useC||a(r),r=new t),r._useC=1,X.NewCtxs.push(r)),r},X.CloseNewCtx=function(e){if(e&&e.close){a(e);for(var t=X.NewCtxs||[],r=t.length,n=0;n<t.length;n++)if(t[n]==e){t.splice(n,1);break}q(Y("mSxV::剩{1}个GetContext未close",0,r+"-1="+t.length),t.length?3:0)}};var a=function(e){if(e&&e.close&&!e._isC&&(e._isC=1,"closed"!=e.state))try{e.close()}catch(e){q("ctx close err",1,e)}},R=X.ResumeCtx=function(r,n,a,o){var i=0,s=0,c=0,f=0,u="EventListener",l="ResumeCtx ",v=function(e,t){s&&p(),i||(i=1,e&&o(e,f),t&&a(f)),t&&(!r._LsSC&&r["add"+u]&&r["add"+u]("statechange",h),r._LsSC=1,c=1)},p=function(e){if(!e||!s){s=e?1:0;for(var t=["focus","mousedown","mouseup","touchstart","touchend"],r=0;r<t.length;r++)window[(e?"add":"remove")+u](t[r],h,!0)}},h=function(){var e=r.state,t=g(e);if(!i&&!n(t?++f:f))return v();t?(c&&q(l+"sc "+e,3),p(1),r.resume().then(function(){c&&q(l+"sc "+r.state),v(0,1)})[A](function(e){q(l+"error",1,e),g(r.state)||v(e.message||"error")})):"closed"==e?(c&&!r._isC&&q(l+"sc "+e,1),v("ctx closed")):v(0,1)};h()},g=X.CtxSpEnd=function(e){return"suspended"==e||"interrupted"==e},O=function(e){var t=e.state,r="ctx.state="+t;return g(t)&&(r+=Y("nMIy::（注意：ctx不是running状态，rec.open和start至少要有一个在用户操作(触摸、点击等)时进行调用，否则将在rec.start时尝试进行ctx.resume，可能会产生兼容性问题(仅iOS)，请参阅文档中runningContext配置）")),r},D="ConnectEnableWebM";X[D]=!0;var z="ConnectEnableWorklet";X[z]=!1;var E=function(e){var c=e.BufferSize||X.BufferSize,f=e.Stream,i=f._c,u=i[N],l={},t=W(f),r=t[0],n=null,a="";if(r&&r.getSettings){var o=(n=r.getSettings())[N];o&&o!=u&&(a=Y("eS8i::Stream的采样率{1}不等于{2}，将进行采样率转换（注意：音质不会变好甚至可能变差），主要在移动端未禁用回声消除时会产生此现象，浏览器有回声消除时可能只会返回16k采样率的音频数据，",0,o,u))}f._ts=n,q(a+"Stream TrackSet: "+L(n),a?3:0);var v,s,p,h=function(e){var t=f._m=i.createMediaStreamSource(f),r=i.destination,n="createMediaStreamDestination";i[n]&&(r=f._d=i[n]()),t.connect(e),e.connect(r)},g="",d=f._call,m=function(e,t){for(var r in d){if(t!=u){l.index=0;var n=(l=X.SampleData([e],t,u,l,{_sum:1})).data,a=l._sum}else{l={};for(var o=e.length,n=new Int16Array(o),a=0,i=0;i<o;i++){var s=Math.max(-1,Math.min(1,e[i]));s=s<0?32768*s:32767*s,n[i]=s,a+=Math.abs(s)}}for(var c in d)d[c](n,a);return}},C="ScriptProcessor",_="audioWorklet",y=T+" "+_,w="RecProc",b="MediaRecorder",S=b+".WebM.PCM",x=i.createScriptProcessor||i.createJavaScriptNode,M=Y("ZGlf::。由于{1}内部1秒375次回调，在移动端可能会有性能问题导致回调丢失录音变短，PC端无影响，暂不建议开启{1}。",0,_),k=function(){s=f.isWorklet=!1,F(f),q(Y("7TU0::Connect采用老的{1}，",0,C)+J.get(Y(X[z]?"JwCL::但已设置{1}尝试启用{2}":"VGjB::可设置{1}尝试启用{2}",2),[T+"."+z+"=true",_])+g+M,3);var e=f._p=x.call(i,c,1,1);h(e),e.onaudioprocess=function(e){var t=e.inputBuffer.getChannelData(0);m(t,u)}},I=function(){v=f.isWebM=!1,U(f),s=f.isWorklet=!x||X[z];var t=window.AudioWorkletNode;if(s&&i[_]&&t){var n=function(){return s&&f._na},a=f._na=function(){""!==p&&(clearTimeout(p),p=setTimeout(function(){p=0,n()&&(q(Y("MxX1::{1}未返回任何音频，恢复使用{2}",0,_,C),3),x&&k())},500))},o=function(){if(n()){var e=f._n=new t(i,w,{processorOptions:{bufferSize:c}});h(e),e.port.onmessage=function(e){p&&(clearTimeout(p),p=""),n()?m(e.data.val,u):s||q(Y("XUap::{1}多余回调",0,_),3)},q(Y("yOta::Connect采用{1}，设置{2}可恢复老式{3}",0,_,T+"."+z+"=false",C)+g+M,3)}},e=function(){if(n())if(i[w])o();else{var e,t,r=(t="class "+w+" extends AudioWorkletProcessor{",t+="constructor "+(e=function(e){return e.toString().replace(/^function|DEL_/g,"").replace(/\$RA/g,y)})(function(e){DEL_super(e);var t=this,r=e.processorOptions.bufferSize;t.bufferSize=r,t.buffer=new Float32Array(2*r),t.pos=0,t.port.onmessage=function(e){e.data.kill&&(t.kill=!0,$C.log("$RA kill call"))},$C.log("$RA .ctor call",e)}),t+="process "+e(function(e,t,r){var n=this,a=n.bufferSize,o=n.buffer,i=n.pos;if((e=(e[0]||[])[0]||[]).length){o.set(e,i);var s=~~((i+=e.length)/a)*a;if(s){this.port.postMessage({val:o.slice(0,s)});var c=o.subarray(s,i);(o=new Float32Array(2*a)).set(c),i=c.length,n.buffer=o}n.pos=i}return!n.kill}),t=(t+='}try{registerProcessor("'+w+'", '+w+')}catch(e){$C.error("'+y+' Reg Error",e)}').replace(/\$C\./g,"console."),"data:text/javascript;base64,"+btoa(unescape(encodeURIComponent(t))));i[_].addModule(r).then(function(e){n()&&(i[w]=1,o(),p&&a())})[A](function(e){q(_+".addModule Error",1,e),n()&&k()})}};R(i,function(){return n()},e,e)}else k()};!function(){var e=window[b],t="ondataavailable",r="audio/webm; codecs=pcm";v=f.isWebM=X[D];var n=e&&t in e.prototype&&e.isTypeSupported(r);if(g=n?"":Y("VwPd::（此浏览器不支持{1}）",0,S),!v||!n)return I();var a=function(){return v&&f._ra};f._ra=function(){""!==p&&(clearTimeout(p),p=setTimeout(function(){a()&&(q(Y("vHnb::{1}未返回任何音频，降级使用{2}",0,b,_),3),I())},500))};var o=Object.assign({mimeType:r},X.ConnectWebMOptions),i=f._r=new e(f,o),s=f._rd={};i[t]=function(e){var t=new FileReader;t.onloadend=function(){if(a()){var e=B(new Uint8Array(t.result),s);if(!e)return;if(-1==e)return void I();p&&(clearTimeout(p),p=""),m(e,s.webmSR)}else v||q(Y("O9P7::{1}多余回调",0,b),3)},t.readAsArrayBuffer(e.data)};try{i.start(~~(c/48)),q(Y("LMEm::Connect采用{1}，设置{2}可恢复使用{3}或老式{4}",0,S,T+"."+D+"=false",_,C))}catch(e){q("mr start err",1,e),I()}}()},o=function(e){e._na&&e._na(),e._ra&&e._ra()},F=function(e){e._na=null,e._n&&(e._n.port.postMessage({kill:!0}),e._n.disconnect(),e._n=null)},U=function(e){if(e._ra=null,e._r){try{e._r.stop()}catch(e){q("mr stop err",1,e)}e._r=null}},P=function(e){var t=(e=e||X)==X,r=e.Stream;r&&(r._m&&(r._m.disconnect(),r._m=null),!r._RC&&r._c&&X.CloseNewCtx(r._c),r._RC=null,r._c=null,r._d&&(n(r._d.stream),r._d=null),r._p&&(r._p.disconnect(),r._p.onaudioprocess=r._p=null),F(r),U(r),t&&n(r)),e.Stream=0},n=X.StopS_=function(e){for(var t=W(e),r=0;r<t.length;r++){var n=t[r];n.stop&&n.stop()}e.stop&&e.stop()},W=function(e){var t=0,r=0,n=[];e.getAudioTracks&&(t=e.getAudioTracks(),r=e.getVideoTracks()),t||(t=e.audioTracks,r=e.videoTracks);for(var a=0,o=t?t.length:0;a<o;a++)n.push(t[a]);for(var a=0,o=r?r.length:0;a<o;a++)n.push(r[a]);return n};X.SampleData=function(e,t,r,n,a){var o="SampleData";n||(n={});var i=n.index||0,s=n.offset||0,c=n.raisePrev||0,f=n.filter;if(f&&f.fn&&(f.sr&&f.sr!=t||f.srn&&f.srn!=r)&&(f=null,q(Y("d48C::{1}的filter采样率变了，重设滤波",0,o),3)),!f)if(r<=t){var u=3*t/4<r?0:r/2*3/4;f={fn:u?X.IIRFilter(!0,t,u):0}}else{var u=3*r/4<t?0:t/2*3/4;f={fn:u?X.IIRFilter(!0,r,u):0}}f.sr=t,f.srn=r;var l=f.fn,v=n.frameNext||[];a||(a={});var p=a.frameSize||1;a.frameType&&(p="mp3"==a.frameType?1152:1);var h=a._sum,g=0,d=e.length;d+1<i&&q(Y("tlbC::{1}似乎传入了未重置chunk {2}",0,o,i+">"+d),3);for(var m=0,C=i;C<d;C++)m+=e[C].length;var _=t/r;if(1<_)m=Math.max(0,m-Math.floor(s)),m=Math.floor(m/_);else if(_<1){var y=1/_;m=Math.floor(m*y)}m+=v.length;for(var w=new Int16Array(m),b=0,C=0;C<v.length;C++)w[b]=v[C],b++;for(;i<d;i++){var S=e[i],x=S instanceof Float32Array,C=s,M=S.length,k=l&&l.Embed,I=0,L=0,T=0,A=0;if(_<1){for(var R=b+C,O=c,D=0;D<M;D++){var z=S[D];x&&(z=(z=Math.max(-1,Math.min(1,z)))<0?32768*z:32767*z);var E=Math.floor(R);R+=y;for(var F=Math.floor(R),G=(z-O)/(F-E),N=1;E<F;E++,N++){var U=Math.floor(O+N*G);k?(T=U,A=k.b0*T+k.b1*k.x1+k.b0*k.x2-k.a1*k.y1-k.a2*k.y2,k.x2=k.x1,k.x1=T,k.y2=k.y1,k.y1=A,U=A):U=l?l(U):U,32767<U?U=32767:U<-32768&&(U=-32768),h&&(g+=Math.abs(U)),w[E]=U,b++}O=c=z,C+=y}s=C%1}else{for(var D=0,P=0;D<M;D++,P++){if(P<M){var z=S[P];x&&(z=(z=Math.max(-1,Math.min(1,z)))<0?32768*z:32767*z),k?(T=z,A=k.b0*T+k.b1*k.x1+k.b0*k.x2-k.a1*k.y1-k.a2*k.y2,k.x2=k.x1,k.x1=T,k.y2=k.y1,k.y1=A):A=l?l(z):z}if(I=L,L=A,0!=P){var W=Math.floor(C);if(D==W){var B=Math.ceil(C),$=C-W,V=I,j=B<M?L:V,H=V+(j-V)*$;32767<H?H=32767:H<-32768&&(H=-32768),h&&(g+=Math.abs(H)),w[b]=H,b++,C+=_}}else D--}s=Math.max(0,C-M)}}_<1&&b+1==m&&(m--,w=new Int16Array(w.buffer.slice(0,2*m))),b-1!=m&&b!=m&&q(o+" idx:"+b+" != size:"+m,3),v=null;var J=m%p;if(0<J){var K=2*(m-J);v=new Int16Array(w.buffer.slice(K)),w=new Int16Array(w.buffer.slice(0,K))}var Q={index:i,offset:s,raisePrev:c,filter:f,frameNext:v,sampleRate:r,data:w};return h&&(Q._sum=g),Q},X.IIRFilter=function(e,t,r){var n=2*Math.PI*r/t,a=Math.sin(n),o=Math.cos(n),i=a/2,s=1+i,c=-2*o/s,f=(1-i)/s;if(e)var u=(1-o)/2/s,l=(1-o)/s;else var u=(1+o)/2/s,l=-(1+o)/s;var v=0,p=0,h=0,g=0,d=0,m=function(e){return h=u*e+l*v+u*p-c*g-f*d,p=v,v=e,d=g,g=h};return m.Embed={x1:0,x2:0,y1:0,y2:0,b0:u,b1:l,a1:c,a2:f},m},X.PowerLevel=function(e,t){var r=e/t||0;return r<1251?Math.round(r/1250*10):Math.round(Math.min(100,Math.max(0,100*(1+Math.log(r/1e4)/Math.log(10)))))},X.PowerDBFS=function(e){var t=Math.max(.1,e||0);return t=Math.min(t,32767),t=20*Math.log(t/32767)/Math.log(10),Math.max(-100,Math.round(t))},X.CLog=function(e,t){if("object"==typeof console){var r=new Date,n=("0"+r.getMinutes()).substr(-2)+":"+("0"+r.getSeconds()).substr(-2)+"."+("00"+r.getMilliseconds()).substr(-3),a=this&&this.envIn&&this.envCheck&&this.id,o=["["+n+" "+T+(a?":"+a:"")+"]"+e],i=arguments,s=X.CLog,c=2,f=s.log||console.log;for(u(t)?f=1==t?s.error||console.error:3==t?s.warn||console.warn:f:c=1;c<i.length;c++)o.push(i[c]);l?f&&f("[IsLoser]"+o[0],1<o.length?o:""):f.apply(console,o)}};var q=function(){X.CLog.apply(this,arguments)},l=!0;try{l=!console.log.apply}catch(e){}var s=0;function c(e){var t=this;t.id=++s,f();var r={type:"mp3",onProcess:x};for(var n in e)r[n]=e[n];var a=(t.set=r)[i],o=r[N];(a&&!u(a)||o&&!u(o))&&t.CLog(Y.G("IllegalArgs-1",[Y("VtS4::{1}和{2}必须是数值",0,N,i)]),1,e),r[i]=+a||16,r[N]=+o||16e3,t.state=0,t._S=9,t.Sync={O:9,C:9}}X.Sync={O:9,C:9},X.prototype=c.prototype={CLog:q,_streamStore:function(){return this.set.sourceStream?this:X},_streamGet:function(){return this._streamStore().Stream},_streamCtx:function(){var e=this._streamGet();return e&&e._c},open:function(e,r){var c=this,f=c.set,n=c._streamStore(),a=0;e=e||x;var o=function(e,t){t=!!t,c.CLog(Y("5tWi::录音open失败：")+e+",isUserNotAllow:"+t,1),a&&X.CloseNewCtx(a),r&&r(e,t)};c._streamTag=I;var i=function(){c.CLog("open ok, id:"+c.id+" stream:"+c._streamTag),e(),c._SO=0},s=n.Sync,u=++s.O,l=s.C;c._O=c._O_=u,c._SO=c._S;if(S){var t=c.envCheck({envName:"H5",canProcess:!0});if(t)o(Y("A5bm::不能录音：")+t);else{var v,p=function(){(v=f.runningContext)||(v=a=X.GetContext(!0))};if(f.sourceStream){if(c._streamTag="set.sourceStream",!X.GetContext())return void o(Y("1iU7::不支持此浏览器从流中获取录音"));p(),P(n);var h=c.Stream=f.sourceStream;h._c=v,h._RC=f.runningContext,h._call={};try{E(n)}catch(e){return P(n),void o(Y("BTW2::从流中打开录音失败：")+e.message)}i()}else{var g=function(e,t){try{window.top.a}catch(e){return void o(Y("Nclz::无权录音(跨域，请尝试给iframe添加麦克风访问策略，如{1})",0,'allow="camera;microphone"'))}d(1,e)&&(/Found/i.test(e)?o(t+Y("jBa9::，无可用麦克风")):o(t))},d=function(e,t){if(/Permission|Allow/i.test(t))e&&o(Y("gyO5::用户拒绝了录音权限"),!0);else{if(!1!==window.isSecureContext)return 1;e&&o(Y("oWNo::浏览器禁止不安全页面录音，可开启https解决"))}};if(X.IsOpen())i();else if(X.Support()){p();var m,C,_=function(t){setTimeout(function(){t._call={};var e=X.Stream;e&&(P(),t._call=e._call),(X.Stream=t)._c=v,t._RC=f.runningContext,function(){if(l!=s.C||!c._O){var e=Y("dFm8::open被取消");return u==s.O?c.close():e=Y("VtJO::open被中断"),o(e),!0}}()||(X.IsOpen()?(e&&c.CLog(Y("upb8::发现同时多次调用open"),1),E(n),i()):o(Y("Q1GA::录音功能无效：无音频流")))},100)},y=function(e){var t=e.name||e.message||e.code+":"+e,r="";1==w&&d(0,t)&&(r=Y("KxE2::，将尝试禁用回声消除后重试"));var n=Y("xEQR::请求录音权限错误"),a=Y("bDOG::无法录音：");c.CLog(n+r+"|"+e,r||C?3:1,e),r?(m=t,C=e,b(1)):C?(c.CLog(n+"|"+C,1,C),g(m,a+C)):g(t,a+e)},w=0,b=function(e){w++;var t="audioTrackSet",r="autoGainControl",n="echoCancellation",a="noiseSuppression",o=JSON.parse(L(f[t]||!0));c.CLog("open... "+w+" "+t+":"+L(o)),e&&("object"!=typeof o&&(o={}),o[r]=!1,o[n]=!1,o[a]=!1),o[N]&&c.CLog(Y("IjL3::注意：已配置{1}参数，可能会出现浏览器不能正确选用麦克风、移动端无法启用回声消除等现象",0,t+"."+N),3);var i={audio:o,video:f.videoTrackSet||!1};try{var s=X.Scope[I](i,_,y)}catch(e){c.CLog(I,3,e),i={audio:!0,video:!1},s=X.Scope[I](i,_,y)}c.CLog(I+"("+L(i)+") "+O(v)+Y("RiWe::，未配置 {1} 时浏览器可能会自动启用回声消除，移动端未禁用回声消除时可能会降低系统播放音量（关闭录音后可恢复）和仅提供16k采样率的音频流（不需要回声消除时可明确配置成禁用来获得48k高音质的流），请参阅文档中{2}配置",0,"audioTrackSet:{echoCancellation,noiseSuppression,autoGainControl}",t)+"("+k+") LM:"+M+" UA:"+navigator.userAgent),s&&s.then&&s.then(_)[A](y)};b()}else g("",Y("COxc::此浏览器不支持录音"))}}}else o(Y.G("NonBrowser-1",["open"])+Y("EMJq::，可尝试使用RecordApp解决方案")+"("+k+"/tree/master/app-support-sample)")},close:function(e){e=e||x;var t=this,r=t._streamStore();t._stop();var n=" stream:"+t._streamTag,a=r.Sync;if(t._O=0,t._O_!=a.O)return t.CLog(Y("hWVz::close被忽略（因为同时open了多个rec，只有最后一个会真正close）")+n,3),void e();a.C++,P(r),t.CLog("close,"+n),e()},mock:function(e,t){var r=this;return r._stop(),r.isMock=1,r.mockEnvInfo=null,r.buffers=[e],r.recSize=e.length,r._setSrcSR(t),r._streamTag="mock",r},_setSrcSR:function(e){var t=this.set,r=t[N];e<r?t[N]=e:r=0,this[G]=e,this.CLog(G+": "+e+" set."+N+": "+t[N]+(r?" "+Y("UHvm::忽略")+": "+r:""),r?3:0)},envCheck:function(e){var t,r=this.set,n="CPU_BE";if(t||X[n]||"function"!=typeof Int8Array||new Int8Array(new Int32Array([1]).buffer)[0]||(f(n),t=Y("Essp::不支持{1}架构",0,n)),!t){var a=r.type,o=this[a+"_envCheck"];r.takeoffEncodeChunk&&(o?e.canProcess||(t=Y("7uMV::{1}环境不支持实时处理",0,e.envName)):t=Y("2XBl::{1}类型不支持设置takeoffEncodeChunk",0,a)+(this[a]?"":Y("LG7e::(未加载编码器)"))),!t&&o&&(t=this[a+"_envCheck"](e,r))}return t||""},envStart:function(e,t){var r=this,n=r.set;if(r.isMock=e?1:0,r.mockEnvInfo=e,r.buffers=[],r.recSize=0,e&&(r._streamTag="env$"+e.envName),r.state=1,r.envInLast=0,r.envInFirst=0,r.envInFix=0,r.envInFixTs=[],r._setSrcSR(t),r.engineCtx=0,r[n.type+"_start"]){var a=r.engineCtx=r[n.type+"_start"](n);a&&(a.pcmDatas=[],a.pcmSize=0)}},envResume:function(){this.envInFixTs=[]},envIn:function(e,t){var a=this,o=a.set,i=a.engineCtx;if(1==a.state){var r=a[G],n=e.length,s=X.PowerLevel(t,n),c=a.buffers,f=c.length;c.push(e);var u=c,l=f,v=Date.now(),p=Math.round(n/r*1e3);a.envInLast=v,1==a.buffers.length&&(a.envInFirst=v-p);var h=a.envInFixTs;h.splice(0,0,{t:v,d:p});for(var g=v,d=0,m=0;m<h.length;m++){var C=h[m];if(3e3<v-C.t){h.length=m;break}g=C.t,d+=C.d}var _=h[1],y=v-g,w=y-d;if(y/3<w&&(_&&1e3<y||6<=h.length)){var b=v-_.t-p;if(p/5<b){var S=!o.disableEnvInFix;if(a.CLog("["+v+"]"+J.get(Y(S?"4Kfd::补偿{1}ms":"bM5i::未补偿{1}ms",1),[b]),3),a.envInFix+=b,S){var x=new Int16Array(b*r/1e3);n+=x.length,c.push(x)}}}var M=a.recSize,k=n,I=M+k;if(a.recSize=I,i){var L=X.SampleData(c,r,o[N],i.chunkInfo);i.chunkInfo=L,M=i.pcmSize,k=L.data.length,I=M+k,i.pcmSize=I,c=i.pcmDatas,f=c.length,c.push(L.data),r=L[N]}var T=Math.round(I/r*1e3),A=c.length,R=u.length,O=function(){for(var e=D?0:-k,t=null==c[0],r=f;r<A;r++){var n=c[r];null==n?t=1:(e+=n.length,i&&n.length&&a[o.type+"_encode"](i,n))}if(t&&i){var r=l;for(u[0]&&(r=0);r<R;r++)u[r]=null}t&&(e=D?k:0,c[0]=null),i?i.pcmSize+=e:a.recSize+=e},D=0,z="rec.set.onProcess";try{D=!0===(D=o.onProcess(c,s,T,r,f,O))}catch(e){console.error(z+Y("gFUF::回调出错是不允许的，需保证不会抛异常"),e)}var E=Date.now()-v;if(10<E&&1e3<a.envInFirst-v&&a.CLog(z+Y("2ghS::低性能，耗时{1}ms",0,E),3),D){for(var F=0,m=f;m<A;m++)null==c[m]?F=1:c[m]=new Int16Array(0);F?a.CLog(Y("ufqH::未进入异步前不能清除buffers"),3):i?i.pcmSize-=k:a.recSize-=k}else O()}else a.state||a.CLog("envIn at state=0",3)},start:function(){var t=this,e=1;if(t.set.sourceStream?t.Stream||(e=0):X.IsOpen()||(e=0),e){var r=t._streamCtx();if(t.CLog(Y("kLDN::start 开始录音，")+O(r)+" stream:"+t._streamTag),t._stop(),t.envStart(null,r[N]),t.state=3,t._SO&&t._SO+1!=t._S)t.CLog(Y("Bp2y::start被中断"),3);else{t._SO=0;var n=function(){3==t.state&&(t.state=1,t.resume())},a="AudioContext resume: ",o=t._streamGet();o._call[t.id]=function(){t.CLog(a+r.state+"|stream ok"),n()},R(r,function(e){return e&&t.CLog(a+"wait..."),3==t.state},function(e){e&&t.CLog(a+r.state),n()},function(e){t.CLog(a+r.state+Y("upkE::，可能无法录音：")+e,1),n()})}}else t.CLog(Y("6WmN::start失败：未open"),1)},pause:function(){var e=this,t=e._streamGet();e.state&&(e.state=2,e.CLog("pause"),t&&delete t._call[e.id])},resume:function(){var r=this,t=r._streamGet(),n="resume(wait ctx)";if(3==r.state)r.CLog(n);else if(r.state){r.state=1,r.CLog("resume"),r.envResume(),t&&(t._call[r.id]=function(e,t){1==r.state&&r.envIn(e,t)},o(t));var a=r._streamCtx();a&&R(a,function(e){return e&&r.CLog(n+"..."),1==r.state},function(e){e&&r.CLog(n+a.state),o(t)},function(e){r.CLog(n+a.state+"[err]"+e,1)})}},_stop:function(e){var t=this,r=t.set;t.isMock||t._S++,t.state&&(t.pause(),t.state=0),!e&&t[r.type+"_stop"]&&(t[r.type+"_stop"](t.engineCtx),t.engineCtx=0)},stop:function(l,t,e){var v,p=this,h=p.set,r=p.envInLast-p.envInFirst,n=r&&p.buffers.length;p.CLog(Y("Xq4s::stop 和start时差:")+(r?r+"ms "+Y("3CQP::补偿:")+p.envInFix+"ms envIn:"+n+" fps:"+(n/r*1e3).toFixed(1):"-")+" stream:"+p._streamTag+" ("+k+") LM:"+M);var g=function(){p._stop(),e&&p.close()},d=function(e){p.CLog(Y("u8JG::结束录音失败：")+e,1),t&&t(e),g()},a=function(e,t,r){var n="arraybuffer",a="dataType",o="DefaultDataType",i=p[a]||X[o]||"blob",s=a+"="+i,c=e instanceof ArrayBuffer,f=0,u=c?e.byteLength:e.size;if(i==n?c||(f=1):"blob"==i?"function"!=typeof Blob?f=Y.G("NonBrowser-1",[s])+Y("1skY::，请设置{1}",0,T+"."+o+'="'+n+'"'):(c&&(e=new Blob([e],{type:t})),e instanceof Blob||(f=1),t=e.type||t):f=Y.G("NotSupport-1",[s]),p.CLog(Y("Wv7l::结束录音 编码花{1}ms 音频时长{2}ms 文件大小{3}b",0,Date.now()-v,r,u)+" "+s+","+t),f)d(1!=f?f:Y("Vkbd::{1}编码器返回的不是{2}",0,h.type,i)+", "+s);else{if(h.takeoffEncodeChunk)p.CLog(Y("QWnr::启用takeoffEncodeChunk后stop返回的blob长度为0不提供音频数据"),3);else if(u<Math.max(50,r/5))return void d(Y("Sz2H::生成的{1}无效",0,h.type));l&&l(e,r,t),g()}};if(!p.isMock){var o=3==p.state;if(!p.state||o)return void d(Y("wf9t::未开始录音")+(o?Y("Dl2c::，开始录音前无用户交互导致AudioContext未运行"):""))}p._stop(!0);var i=p.recSize;if(i)if(p[h.type]){if(p.isMock){var s=p.envCheck(p.mockEnvInfo||{envName:"mock",canProcess:!1});if(s)return void d(Y("AxOH::录音错误：")+s)}var c=p.engineCtx;if(p[h.type+"_complete"]&&c){var f=Math.round(c.pcmSize/h[N]*1e3);return v=Date.now(),void p[h.type+"_complete"](c,function(e,t){a(e,t,f)},d)}if(v=Date.now(),p.buffers[0]){var u=X.SampleData(p.buffers,p[G],h[N]);h[N]=u[N];var m=u.data,f=Math.round(m.length/h[N]*1e3);p.CLog(Y("CxeT::采样:{1} 花:{2}ms",0,i+"->"+m.length,Date.now()-v)),setTimeout(function(){v=Date.now(),p[h.type](m,function(e,t){a(e,t,f)},function(e){d(e)})})}else d(Y("xkKd::音频buffers被释放"))}else d(Y("xGuI::未加载{1}编码器，请尝试到{2}的src/engine内找到{1}的编码器并加载",0,h.type,T));else d(Y("Ltz3::未采集到录音"))}};var B=function(e,t){t.pos||(t.pos=[0],t.tracks={},t.bytes=[]);var r=t.tracks,n=[t.pos[0]],a=function(){t.pos[0]=n[0]},o=t.bytes.length,i=new Uint8Array(o+e.length);if(i.set(t.bytes),i.set(e,o),t.bytes=i,!t._ht){if(j(i,n),H(i,n),!$(j(i,n),[24,83,128,103]))return;for(j(i,n);n[0]<i.length;){var s=j(i,n),c=H(i,n),f=[0],u=0;if(!c)return;if($(s,[22,84,174,107])){for(;f[0]<c.length;){var l=j(c,f),v=H(c,f),p=[0],h={channels:0,sampleRate:0};if($(l,[174]))for(;p[0]<v.length;){var g=j(v,p),d=H(v,p),m=[0];if($(g,[215])){var C=V(d);h.number=C,r[C]=h}else if($(g,[131])){var C=V(d);1==C?h.type="video":2==C?(h.type="audio",u||(t.track0=h),h.idx=u++):h.type="Type-"+C}else if($(g,[134])){for(var _="",y=0;y<d.length;y++)_+=String.fromCharCode(d[y]);h.codec=_}else if($(g,[225]))for(;m[0]<d.length;){var w=j(d,m),b=H(d,m);if($(w,[181])){var C=0,S=new Uint8Array(b.reverse()).buffer;4==b.length?C=new Float32Array(S)[0]:8==b.length?C=new Float64Array(S)[0]:q("WebM Track !Float",1,b),h[N]=Math.round(C)}else $(w,[98,100])?h.bitDepth=V(b):$(w,[159])&&(h.channels=V(b))}}}t._ht=1,q("WebM Tracks",r),a();break}}}var x=t.track0;if(x){var M=x[N];if(t.webmSR=M,16==x.bitDepth&&/FLOAT/i.test(x.codec)&&(x.bitDepth=32,q("WebM 16->32 bit",3)),M<8e3||32!=x.bitDepth||x.channels<1||!/(\b|_)PCM\b/i.test(x.codec))return t.bytes=[],t.bad||q("WebM Track Unexpected",3,t),-(t.bad=1);for(var k=[],I=0;n[0]<i.length;){var l=j(i,n),v=H(i,n);if(!v)break;if($(l,[163])){var L=15&v[0],h=r[L];if(!h)return q("WebM !Track"+L,1,r),-1;if(0===h.idx){for(var T=new Uint8Array(v.length-4),y=4;y<v.length;y++)T[y-4]=v[y];k.push(T),I+=T.length}}a()}if(I){var A=new Uint8Array(i.length-t.pos[0]);A.set(i.subarray(t.pos[0])),t.bytes=A,t.pos[0]=0;for(var T=new Uint8Array(I),y=0,R=0;y<k.length;y++)T.set(k[y],R),R+=k[y].length;var S=new Float32Array(T.buffer);if(1<x.channels){for(var O=[],y=0;y<S.length;)O.push(S[y]),y+=x.channels;S=new Float32Array(O)}return S}}},$=function(e,t){if(!e||e.length!=t.length)return!1;if(1==e.length)return e[0]==t[0];for(var r=0;r<e.length;r++)if(e[r]!=t[r])return!1;return!0},V=function(e){for(var t="",r=0;r<e.length;r++){var n=e[r];t+=(n<16?"0":"")+n.toString(16)}return parseInt(t,16)||0},j=function(e,t,r){var n=t[0];if(!(n>=e.length)){var a=e[n],o=("0000000"+a.toString(2)).substr(-8),i=/^(0*1)(\d*)$/.exec(o);if(i){var s=i[1].length,c=[];if(!(n+s>e.length)){for(var f=0;f<s;f++)c[f]=e[n],n++;return r&&(c[0]=parseInt(i[2]||"0",2)),t[0]=n,c}}}},H=function(e,t){var r=j(e,t,1);if(r){var n=V(r),a=t[0],o=[];if(n<2147483647){if(a+n>e.length)return;for(var i=0;i<n;i++)o[i]=e[a],a++}return t[0]=a,o}},J=X.i18n={lang:"zh-CN",alias:{"zh-CN":"zh","en-US":"en"},locales:{},data:{},put:function(e,t){var r=T+".i18n.put: ",n=e.overwrite;n=null==n||n;var a=e.lang;if(!(a=J.alias[a]||a))throw new Error(r+"set.lang?");var o=J.locales[a];o||(o={},J.locales[a]=o);for(var i,s=/^([\w\-]+):/,c=0;c<t.length;c++){var f=t[c];if(i=s.exec(f)){var u=i[1],f=f.substr(u.length+1);!n&&o[u]||(o[u]=f)}else q(r+"'key:'? "+f,3,e)}},get:function(){return J.v_G.apply(null,arguments)},v_G:function(n,a,e){a=a||[],e=e||J.lang,e=J.alias[e]||e;var t=J.locales[e],o=t&&t[n]||"";return o||"zh"==e?(J.lastLang=e,"=Empty"==o?"":o.replace(/\{(\d+)(\!?)\}/g,function(e,t,r){return e=a[(t=+t||0)-1],(t<1||t>a.length)&&(e="{?}",q("i18n["+n+"] no {"+t+"}: "+o,3)),r?"":e})):"en"==e?J.v_G(n,a,"zh"):J.v_G(n,a,"en")},$T:function(){return J.v_T.apply(null,arguments)},v_T:function(){for(var e,t=arguments,r="",n=[],a=0,o=T+".i18n.$T:",i=/^([\w\-]*):/,s=0;s<t.length;s++){var c=t[s];if(0==s){if(e=i.exec(c),!(r=e&&e[1]))throw new Error(o+"0 'key:'?");c=c.substr(r.length+1)}if(-1===a)n.push(c);else{if(a)throw new Error(o+" bad args");if(0===c)a=-1;else if(u(c)){if(c<1)throw new Error(o+" bad args");a=c}else{var f=1==s?"en":s?"":"zh";if((e=i.exec(c))&&(f=e[1]||f,c=c.substr(e[1].length+1)),!e||!f)throw new Error(o+s+" 'lang:'?");J.put({lang:f,overwrite:!1},[r+":"+c])}}}return r?0<a?r:J.v_G(r,n):""}},Y=J.$T;Y.G=J.get,Y("NonBrowser-1::非浏览器环境，不支持{1}",1),Y("IllegalArgs-1::参数错误：{1}",1),Y("NeedImport-2::调用{1}需要先导入{2}",2),Y("NotSupport-1::不支持：{1}",1),X.TrafficImgUrl="//ia.51.la/go1?id=20469973&pvFlag=1";var f=X.Traffic=function(e){if(S){e=e?"/"+T+"/Report/"+e:"";var t=X.TrafficImgUrl;if(t){var r=X.Traffic,n=/^(https?:..[^\/#]*\/?)[^#]*/i.exec(location.href)||[],a=n[1]||"http://file/",o=(n[0]||a)+e;if(0==t.indexOf("//")&&(t=/^https:/i.test(o)?"https:"+t:"http:"+t),e&&(t=t+"&cu="+encodeURIComponent(a+e)),!r[o]){r[o]=1;var i=new Image;i.src=t,q("Traffic Analysis Image: "+(e||T+".TrafficImgUrl="+X.TrafficImgUrl))}}}};t&&(q(Y("8HO5::覆盖导入{1}",0,T),1),t.Destroy());e[T]=X}(r,t),"function"==typeof define&&define.amd&&define(function(){return r.Recorder}),"object"==typeof module&&module.exports&&(module.exports=r.Recorder)}(),function(e){var t="object"==typeof window&&!!window.document,r=(t?window:Object).Recorder,n=r.i18n;!function(p,e,h,t){"use strict";p.prototype.enc_wav={stable:!0,fast:!0,getTestMsg:function(){return h("gPSE::支持位数8位、16位（填在比特率里面），采样率取值无限制；此编码器仅在pcm数据前加了一个44字节的wav头，编码出来的16位wav文件去掉开头的44字节即可得到pcm（注：其他wav编码器可能不是44字节）")}};p.prototype.wav=function(e,t,r){var n=this.set;!function(e){var t=e.bitRate,r=8==t?8:16;t!=r&&p.CLog(h("wyw9::WAV Info: 不支持{1}位，已更新成{2}位",0,t,r),3);e.bitRate=r}(n);var a=e.length,o=n.sampleRate,i=n.bitRate,s=a*(i/8),c=p.wav_header(1,1,o,i,s),f=c.length,u=new Uint8Array(f+s);if(u.set(c),8==i)for(var l=0;l<a;l++){var v=128+(e[l]>>8);u[f++]=v}else(u=new Int16Array(u.buffer)).set(e,f/2);t(u.buffer,"audio/wav")},p.wav_header=function(e,t,r,n,a){var o=1==e?0:2,i=new ArrayBuffer(44+o),s=new DataView(i),c=0,f=function(e){for(var t=0;t<e.length;t++,c++)s.setUint8(c,e.charCodeAt(t))},u=function(e){s.setUint16(c,e,!0),c+=2},l=function(e){s.setUint32(c,e,!0),c+=4};return f("RIFF"),l(36+o+a),f("WAVE"),f("fmt "),l(16+o),u(e),u(t),l(r),l(r*(t*n/8)),u(t*n/8),u(n),1!=e&&u(0),f("data"),l(a),new Uint8Array(i)}}(r,0,n.$T)}();