/*
Recorder ../app-support-sample/demo_UniApp/uni_modules/Recorder-UniCore/i18n/en-US.js
https://github.com/xiangyuecn/Recorder

Usage: Recorder.i18n.lang="en-US" or "en"

Desc: English, 英语。This translation mainly comes from: google translation + Baidu translation, translated from Chinese to English. 此翻译主要来自：google翻译+百度翻译，由中文翻译成英文。

注意：请勿修改//@@打头的文本行；以下代码结构由/src/package-i18n.js自动生成，只允许在字符串中填写翻译后的文本，请勿改变代码结构；翻译的文本如果需要明确的空值，请填写"=Empty"；文本中的变量用{n}表示（n代表第几个变量），所有变量必须都出现至少一次，如果不要某变量用{n!}表示

Note: Do not modify the text lines starting with //@@; The following code structure is automatically generated by /src/package-i18n.js, only the translated text is allowed to be filled in the string, please do not change the code structure; If the translated text requires an explicit empty value, please fill in "=Empty"; Variables in the text are represented by {n} (n represents the number of variables), all variables must appear at least once, if a variable is not required, it is represented by {n!}
*/
(function(factory){
	var browser=typeof window=="object" && !!window.document;
	var win=browser?window:Object; //非浏览器环境，Recorder挂载在Object下面
	factory(win.Recorder,browser);
}(function(Recorder,isBrowser){
"use strict";
var i18n=Recorder.i18n;

//@@User Code-1 Begin 手写代码放这里 Put the handwritten code here @@

//@@User Code-1 End @@

//@@Exec i18n.lang="en-US";
Recorder.CLog('Import Page[Recorder_UniCore] lang="en-US"');

//@@Exec i18n.alias["en-US"]="en";

var putSet={lang:"en"};

//@@Exec i18n.data["rtl$en"]=false;
i18n.data["desc-page-Recorder_UniCore$en"]="English, 英语。This translation mainly comes from: google translation + Baidu translation, translated from Chinese to English. 此翻译主要来自：google翻译+百度翻译，由中文翻译成英文。";
//@@Exec i18n.GenerateDisplayEnglish=false;



//*************** Begin srcFile=../app-support-sample/demo_UniApp/uni_modules/Recorder-UniCore/app-uni-support.js ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="微信小程序中需要：{1}"
//@@Put0
 "RXs7:"+ //args: {1}
       "WeChat miniProgram requires: {1}"

//@@zh="Recorder-UniCore目前只支持：H5、APP(Android iOS)、MP-WEIXIN，其他平台环境需要自行编写适配文件实现接入"
,"4ATo:"+ //no args
       "Recorder-UniCore currently only supports: H5, APP (Android iOS), MP-WEIXIN, other platform environments need to write their own adaptation files to achieve access"

//@@zh="RecordApp.UniWebViewActivate 需要传入当前页面或组件的this对象作为参数"
,"GwCz:"+ //no args
       "RecordApp.UniWebViewActivate needs to pass in the this object of the current page or component as a parameter"

//@@zh="RecordApp.UniWebViewActivate 发生不应该出现的错误（可能需要升级插件代码）："
,"ipB3:"+ //no args
       "An error occurred in RecordApp.UniWebViewActivate that should not occur (the plug-in code may need to be upgraded): "

//@@zh="RecordApp.UniWebViewActivate 已切换当前页面或组件的renderjs所在的WebView"
,"WpKg:"+ //no args
       "RecordApp.UniWebViewActivate has switched the WebView where the renderjs of the current page or component is located"

//@@zh="RecordApp.UniRenderjsRegister 发生不应该出现的错误（可能需要升级插件代码）："
,"Uc9E:"+ //no args
       "An error occurred in RecordApp.UniRenderjsRegister that should not occur (the plugin code may need to be upgraded): "

//@@zh="RecordApp.UniRenderjsRegister 重复注册当前页面renderjs模块，一个组件内只允许一个renderjs模块进行注册"
,"mzKj:"+ //no args
       "RecordApp.UniRenderjsRegister repeatedly registers the renderjs module of the current page. Only one renderjs module is allowed to be registered in a component"

//@@zh="RecordApp.UniRenderjsRegister 已注册当前页面renderjs模块"
,"7kJS:"+ //no args
       "RecordApp.UniRenderjsRegister has registered the renderjs module of the current page"

//@@zh="严重兼容性问题：无法获取页面或组件this.$root.$scope或.$page"
,"KpY6:"+ //no args
       "Serious compatibility issue: Unable to get page or component this.$root.$scope or .$page"

//@@zh="需要先调用RecordApp.UniWebViewActivate方法"
,"AGd7:"+ //no args
       "You need to call the RecordApp.UniWebViewActivate method first"

//@@zh="需先调用RecordApp.RequestPermission方法"
,"7ot0:"+ //no args
       "You need to call the RecordApp.RequestPermission method first"

//@@zh="需重新调用RecordApp.RequestPermission方法"
,"VsdN:"+ //no args
       "The RecordApp.RequestPermission method needs to be called again"

//@@zh="检测到有其他页面或组件调用了RecordApp.UniPageOnShow（WvCid={1}），但未调用过RecordApp.UniWebViewActivate（当前WvCid={2}），部分功能会继续使用之前Activate的WebView和组件，请确保这是符合你的业务逻辑，不是因为忘记了调用UniWebViewActivate"
,"SWsy:"+ //args: {1}-{2}
       "It is detected that another page or component has called RecordApp.UniPageOnShow (WvCid={1}), but RecordApp.UniWebViewActivate (current WvCid={2}) has not been called. Some functions will continue to use the previously Activated WebView and components. Please make sure This is in line with your business logic, not because you forgot to call UniWebViewActivate"

//@@zh="{1}未正确查询到节点，将使用传入的当前页面或组件this的$el.parentNode作为组件根节点。如果template下存在多个根节点(vue3 multi-root)，尽量在最外面再套一层view来避免兼容性问题"
,"dX7B:"+ //args: {1}
       "{1} does not query the node correctly, and will use the current page or component this's $el.parentNode as the component root node. If there are multiple root nodes under the template (vue3 multi-root), try to add another layer of view on the outermost to avoid compatibility issues"

//@@zh="{1}需在renderjs中调用并且传入当前模块的this"
,"dX5B:"+ //args: {1}
       "{1} needs to be called in renderjs and pass in this of the current module"

//@@zh="{1}需要传入当前页面或组件的this对象作为参数"
,"dX6B:"+ //args: {1}
       "{1} needs to pass in the this object of the current page or component as a parameter"

//@@zh="当前不是App逻辑层"
,"TfJX:"+ //no args
       "Currently it is not the App logic layer"

//@@zh="当前还未调用过RecordApp.UniWebViewActivate"
,"peIm:"+ //no args
       "RecordApp.UniWebViewActivate has not been called yet"

//@@zh="未找到此页面renderjs所在的WebView"
,"qDo1:"+ //no args
       "The WebView where renderjs for this page is not found"

//@@zh="，不可以调用RecordApp.UniWebViewEval"
,"igw2:"+ //no args
       ", RecordApp.UniWebViewEval cannot be called"

//@@zh="当前不是App逻辑层"
,"lU1W:"+ //no args
       "Currently it is not the App logic layer"

//@@zh="当前还未调用过RecordApp.UniWebViewActivate"
,"mSbR:"+ //no args
       "RecordApp.UniWebViewActivate has not been called yet"

//@@zh="未找到此页面renderjs所在的WebView Cid"
,"6Iql:"+ //no args
       "The WebView Cid where renderjs for this page is not found"

//@@zh="，不可以调用RecordApp.UniWebViewVueCall"
,"TtoS:"+ //no args
       ", RecordApp.UniWebViewVueCall cannot be called"

//@@zh="renderjs中未import导入RecordApp"
,"U1Be:"+ //no args
       "RecordApp is not imported in renderjs"

//@@zh="renderjs中的mounted内需要调用RecordApp.UniRenderjsRegister"
,"Bcgi:"+ //no args
       "RecordApp.UniRenderjsRegister needs to be called in mounted in renderjs"

//@@zh="没有找到组件的renderjs模块"
,"URyD:"+ //no args
       "The renderjs module for the component was not found"

//@@zh="{1}连接renderjs超时"
,"KQhJ:"+ //args: {1}
       "{1} connection renderjs timeout"

//@@zh="{1}处理超时"
,"RDcZ:"+ //args: {1}
       "{1} processing timeout"

//@@zh="需要在页面中提供一个renderjs，在里面import导入RecordApp、录音格式编码器、可视化插件等"
,"TSmQ:"+ //no args
       "You need to provide a renderjs in the page, and import RecordApp, recording format encoder, visualization plug-in, etc."

//@@zh="需在renderjs中import {1}"
,"AN0e:"+ //args: {1}
       "Need to import {1} in renderjs"

//@@zh="不应该出现的MainReceiveBind重复绑定"
,"vEgr:"+ //no args
       "MainReceiveBind duplicate binding that should not occur"

//@@zh="从renderjs发回数据但UniMainCallBack回调不存在："
,"kZx6:"+ //no args
       "Sending data back from renderjs but UniMainCallBack callback does not exist: "

//@@zh="[MainReceive]从renderjs发回未知数据："
,"ZHwv:"+ //no args
       "[MainReceive] Unknown data sent back from renderjs: "

//@@zh="只允许在renderjs中调用RecordApp.UniWebViewSendBigBytesToMain"
,"MujG:"+ //no args
       "Only allowed to call RecordApp.UniWebViewSendBigBytesToMain in renderjs"

//@@zh="renderjs中的mounted内需要调用RecordApp.UniRenderjsRegister才能调用RecordApp.UniWebViewSendBigBytesToMain"
,"kE91:"+ //no args
       "RecordApp.UniRenderjsRegister needs to be called in mounted in renderjs to call RecordApp.UniWebViewSendBigBytesToMain"

//@@zh="无效的BigBytes回传数据"
,"CjMb:"+ //no args
       "Invalid BigBytes return data"

//@@zh="保存文件{1}失败："
,"UqfI:"+ //args: {1}
       "Failed to save file {1}: "

//@@zh="当前环境未支持保存本地文件"
,"kxOd:"+ //no args
       "The current environment does not support saving local files"

//@@zh=" | RecordApp的uni-app支持文档和示例：{1} "
,"1f2V:"+ //args: {1}
       " | RecordApp’s uni-app support documentation and examples: {1}"

//@@zh="当前录音由原生录音插件提供支持"
,"XSYY:"+ //no args
       "Current recording is powered by native recording plug-in"

//@@zh="当前录音由uts插件提供支持"
,"nnM6:"+ //no args
       "Current recording is powered by uts plugin"

//@@zh="当前已配置RecordApp.UniWithoutAppRenderjs，必须提供原生录音插件或uts插件才能录音，请参考RecordApp.UniNativeUtsPlugin配置"
,"fqhr:"+ //no args
       "RecordApp.UniWithoutAppRenderjs is currently configured. A native recording plug-in or uts plug-in must be provided to record. Please refer to the RecordApp.UniNativeUtsPlugin configuration"

//@@zh="当前RecordApp运行在逻辑层中（性能会略低一些，可视化等插件不可用）"
,"xYRb:"+ //no args
       "Currently RecordApp runs in the logical layer (performance will be slightly lower, and plug-ins such as visualization are not available) "

//@@zh="未找到当前页面renderjs所在的WebView"
,"S3eF:"+ //no args
       "The WebView where renderjs of the current page is located is not found"

//@@zh="当前RecordApp运行在renderjs所在的WebView中（逻辑层中只能做有限的实时处理，可视化等插件均需要在renderjs中进行调用）"
,"0hyi:"+ //no args
       "The current RecordApp runs in the WebView where renderjs is located (only limited real-time processing can be done in the logic layer, and visualization and other plug-ins need to be called in renderjs) "

//@@zh="，请检查此页面代码中是否编写了lang=renderjs的module，并且调用了RecordApp.UniRenderjsRegister；如果确实没有renderjs，比如nvue页面，请设置RecordApp.UniWithoutAppRenderjs=true并且搭配配套的原生插件在逻辑层中直接录音"
,"e6Mo:"+ //no args
       ", please check whether the module with lang=renderjs is written in the code of this page and RecordApp.UniRenderjsRegister is called; if there is indeed no renderjs, such as nvue page, please set RecordApp.UniWithoutAppRenderjs=true and use the matching native plug-in to record directly in the logic layer"

//@@zh="【在App内使用{1}的授权许可】"
,"FabE:"+ //args: {1}
       "[License for use of {1} within the App] "

//@@zh="已购买原生录音插件，获得授权许可"
,"w37G:"+ //no args
       "Purchased the native recording plug-in and obtained the license"

//@@zh="已购买uts插件，获得授权许可"
,"e71S:"+ //no args
       "Purchased uts plug-in and obtained license"

//@@zh="UniAppUseLicense填写无效，如果已获取到了商用授权，请填写：{1}，否则请使用空字符串"
,"aPoj:"+ //args: {1}
       "UniAppUseLicense is invalid. If you have obtained a commercial license, please fill in: {1}, otherwise please use an empty string"

//@@zh="未找到Canvas：{1}，请确保此DOM已挂载（可尝试用$nextTick等待DOM更新）"
,"k7im:"+ //args: {1}
       "Canvas not found: {1}, please make sure this DOM is mounted (try $nextTick to wait for DOM update) "

//@@zh="RecordApp.UniFindCanvas未适配当前环境"
,"yI24:"+ //no args
       "RecordApp.UniFindCanvas does not adapt to the current environment"

//@@zh="未配置RecordApp.UniNativeUtsPlugin原生录音插件"
,"H753:"+ //no args
       "RecordApp.UniNativeUtsPlugin native recording plug-in is not configured"

//@@zh="renderjs中不支持设置RecordApp.UniNativeUtsPlugin"
,"l6sY:"+ //no args
       "Setting RecordApp.UniNativeUtsPlugin is not supported in renderjs"

//@@zh="当前App未打包进双端原生插件[{1}]，尝试加载单端[{2}]"
,"kSjQ:"+ //args: {1}-{2}
       "The current App is not packaged into the dual-end native plug-in [{1}], try to load the single-end [{2}]"

//@@zh="已加载原生录音插件[{1}]"
,"Xh1W:"+ //args: {1}
       "Native recording plugin loaded [{1}]"

//@@zh="配置了RecordApp.UniNativeUtsPlugin，但当前App未打包进原生录音插件[{1}]"
,"SCW9:"+ //args: {1}
       "RecordApp.UniNativeUtsPlugin is configured, but the current App is not packaged with the native recording plug-in [{1}]"

//@@zh="提供的RecordApp.UniNativeUtsPlugin值不是RecordApp的uts原生录音插件"
,"TGMm:"+ //no args
       "The provided RecordApp.UniNativeUtsPlugin value is not RecordApp’s uts native recording plug-in"

//@@zh="需在App逻辑层中调用原生插件功能"
,"MrBx:"+ //no args
       "The native plug-in function needs to be called in the App logic layer"

//@@zh="未开始录音，不可以调用{1}"
,"0FGq:"+ //args: {1}
       "Recording has not started and {1} cannot be called"

//@@zh="需先调用RecordApp.UniWebViewActivate，然后才可以调用RequestPermission"
,"PkQ2:"+ //no args
       "RecordApp.UniWebViewActivate needs to be called first, and then RequestPermission can be called"

//@@zh="不应当出现的非H5权限请求"
,"Jk72:"+ //no args
       "Non-H5 permission requests that should not appear"

//@@zh="正在调用plus.ios@AVAudioSession请求iOS原生录音权限"
,"Y3rC:"+ //no args
       "Calling plus.ios@AVAudioSession to request iOS native recording permissions"

//@@zh="项目配置中未声明iOS录音权限{1}"
,"9xoE:"+ //args: {1}
       "iOS recording permission {1} is not declared in the project configuration"

//@@zh="已获得iOS原生录音权限"
,"j15C:"+ //no args
       "Obtained iOS native recording permissions"

//@@zh="plus.ios请求录音权限，状态值: "
,"iKhe:"+ //no args
       "plus.ios requests recording permission, status value: "

//@@zh="正在调用plus.android.requestPermissions请求Android原生录音权限"
,"7Noe:"+ //no args
       "Calling plus.android.requestPermissions to request Android native recording permissions"

//@@zh="已获得Android原生录音权限："
,"Bgls:"+ //no args
       "Obtained Android native recording permission: "

//@@zh="plus.android请求录音权限：无权限"
,"Ruxl:"+ //no args
       "plus.android requests recording permission: No permission"

//@@zh="plus.android请求录音权限出错："
,"0JQw:"+ //no args
       "plus.android error in requesting recording permission: "

//@@zh="调用plus的权限请求出错："
,"Mvl7:"+ //no args
       "An error occurred in the permission request to call plus: "

//@@zh="用户拒绝了录音权限"
,"0caE:"+ //no args
       "User denied recording permission"

//@@zh="正在调用原生插件请求录音权限"
,"Lx5r:"+ //no args
       "Calling the native plug-in to request recording permission"

//@@zh="已获得录音权限"
,"Lx6r:"+ //no args
       "Recording permission obtained"

//@@zh="无录音权限"
,"Lx7r:"+ //no args
       "No recording permission"

//@@zh="无法调用RequestPermission："
,"ksoA:"+ //no args
       "Unable to call RequestPermission: "

//@@zh="无法连接到renderjs"
,"KnF0:"+ //no args
       "Unable to connect to renderjs"

//@@zh="需先调用RecordApp.UniWebViewActivate，然后才可以调用Start"
,"XCMU:"+ //no args
       "RecordApp.UniWebViewActivate needs to be called first, and then Start can be called"

//@@zh="不应当出现的非H5录音Start"
,"rSLO:"+ //no args
       "Start of non-H5 recordings that should not appear"

//@@zh="无法调用Start："
,"Bjx9:"+ //no args
       "Unable to call Start: "

//@@zh="未开始录音，但收到renderjs回传的onRecEncodeChunk"
,"MTdp:"+ //no args
       "Recording did not start, but onRecEncodeChunk returned by renderjs was received"

//@@zh="未开始录音，但收到Uni Native PCM数据"
,"BjGP:"+ //no args
       "Recording did not start, but Uni Native PCM data was received"

//@@zh="未开始录音，但收到UniNativeUtsPlugin PCM数据"
,"byzO:"+ //no args
       "Recording did not start, but UniNativeUtsPlugin PCM data was received"

//@@zh="未开始录音"
,"YP4V:"+ //no args
       "Recording not started"

//@@zh="不应当出现的非H5录音Stop"
,"TPhg:"+ //no args
       "Stop non-H5 recordings that should not appear"

//@@zh="未开始录音"
,"pP4O:"+ //no args
       "Recording not started"

//@@zh="无法调用Stop："
,"H6cq:"+ //no args
       "Unable to call Stop: "

//@@zh="不应该出现的renderjs发回的文件数据丢失"
,"gomD:"+ //no args
       "The file data sent back by renderjs should not be lost"

]);
//*************** End srcFile=../app-support-sample/demo_UniApp/uni_modules/Recorder-UniCore/app-uni-support.js ***************

//@@User Code-2 Begin 手写代码放这里 Put the handwritten code here @@

//@@User Code-2 End @@

}));