/*
录音
https://github.com/xiangyuecn/Recorder
src: extensions/waveview.js
*/
!function(e){var t="object"==typeof window&&!!window.document,a=(t?window:Object).Recorder,r=a.i18n;!function(d,e,f,g){"use strict";var t=function(e){return new a(e)},v="WaveView",a=function(e){var t=this,a={scale:2,speed:9,phase:21.8,fps:20,keep:!0,lineWidth:3,linear1:[0,"rgba(150,96,238,1)",.2,"rgba(170,79,249,1)",1,"rgba(53,199,253,1)"],linear2:[0,"rgba(209,130,255,0.6)",1,"rgba(53,199,255,0.6)"],linearBg:[0,"rgba(255,255,255,0.2)",1,"rgba(54,197,252,0.2)"]};for(var r in e)a[r]=e[r];t.set=e=a;var i="compatibleCanvas";if(e[i])var n=t.canvas=e[i];else{if(!g)throw new Error(f.G("NonBrowser-1",[v]));var o=e.elem;o&&("string"==typeof o?o=document.querySelector(o):o.length&&(o=o[0])),o&&(e.width=o.offsetWidth,e.height=o.offsetHeight);var h=t.elem=document.createElement("div");h.style.fontSize=0,h.innerHTML='<canvas style="width:100%;height:100%;"/>';var n=t.canvas=h.querySelector("canvas");o&&(o.innerHTML="",o.appendChild(h))}var l=e.scale,s=e.width*l,c=e.height*l;if(!s||!c)throw new Error(f.G("IllegalArgs-1",[v+" width=0 height=0"]));n.width=s,n.height=c;var d=t.ctx=n.getContext("2d");t.linear1=t.genLinear(d,s,e.linear1),t.linear2=t.genLinear(d,s,e.linear2),t.linearBg=t.genLinear(d,c,e.linearBg,!0),t._phase=0};a.prototype=t.prototype={genLinear:function(e,t,a,r){for(var i=e.createLinearGradient(0,0,r?0:t,r?t:0),n=0;n<a.length;)i.addColorStop(a[n++],a[n++]);return i},genPath:function(e,t,a){for(var r=[],i=this.set,n=i.scale,o=i.width*n,h=i.height*n/2,l=0;l<=o;l+=n){var s=(1+Math.cos(Math.PI+l/o*2*Math.PI))/2,c=s*h*t*Math.sin(2*Math.PI*(l/o)*e+a)+h;r.push(c)}return r},input:function(e,t,a){var r=this;r.sampleRate=a,r.pcmData=e,r.pcmPos=0,r.inputTime=Date.now(),r.schedule()},schedule:function(){var e=this,t=e.set,a=Math.floor(1e3/t.fps);e.timer||(e.timer=setInterval(function(){e.schedule()},a));var r=Date.now(),i=e.drawTime||0;if(!(r-i<a)){e.drawTime=r;for(var n=e.sampleRate/t.fps,o=e.pcmData,h=e.pcmPos,l=Math.max(0,Math.min(n,o.length-h)),s=0,c=0;c<l;c++,h++)s+=Math.abs(o[h]);e.pcmPos=h,!l&&t.keep||e.draw(d.PowerLevel(s,l)),!l&&1300<r-e.inputTime&&(clearInterval(e.timer),e.timer=0)}},draw:function(e){var t=this,a=t.set,r=t.ctx,i=a.scale,n=a.width*i,o=a.height*i,h=a.speed/a.fps,l=t._phase-=h,s=l+h*a.phase,c=e/100,d=t.genPath(2,c,l),f=t.genPath(1.8,c,s);r.clearRect(0,0,n,o),r.beginPath();for(var g=0,v=0;v<=n;g++,v+=i)0==v?r.moveTo(v,d[g]):r.lineTo(v,d[g]);g--;for(var v=n-1;0<=v;g--,v-=i)r.lineTo(v,f[g]);r.closePath(),r.fillStyle=t.linearBg,r.fill(),t.drawPath(f,t.linear2),t.drawPath(d,t.linear1)},drawPath:function(e,t){var a=this.set,r=this.ctx,i=a.scale,n=a.width*i;r.beginPath();for(var o=0,h=0;h<=n;o++,h+=i)0==h?r.moveTo(h,e[o]):r.lineTo(h,e[o]);r.lineWidth=a.lineWidth*i,r.strokeStyle=t,r.stroke()}},d[v]=t}(a,0,r.$T,t)}();