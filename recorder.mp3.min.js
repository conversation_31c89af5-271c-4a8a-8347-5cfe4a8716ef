!function (e) {var t = "object" == typeof window && !!window.document, r = t ? window : Object; !function (e, x) {"use strict"; var w = function () {}, u = function (e) {return "number" == typeof e }, I = function (e) {return JSON.stringify(e) }, X = function (e) {return new c(e) }, M = X.LM = "2025-01-11 09:28", k = "https: var f = X.Traffic = function (e) {if (x) {e = e ? "/" + T + "/Report/" + e : ""; var t = X.TrafficImgUrl; if (t) {var r = X.Traffic, n = /^(https?:..[^\/#]*\/?)[^#]*/i.exec(location.href) || [], a = n[1] || "http: }(r, t), "function" == typeof define && define.amd && define(function () {return r.Recorder }), "object" == typeof module && module.exports && (module.exports = r.Recorder) }(); !function(e){var t="object"==typeof window&&!!window.document,a=(t?window:Object).Recorder,s=a.i18n;!function(m,e,u,t){"use strict";var _="48000, 44100, 32000, 24000, 22050, 16000, 12000, 11025, 8000",o="8, 16, 24, 32, 40, 48, 56, 64, 80, 96, 112, 128, 144, 160, 192, 224, 256, 320";m.prototype.enc_mp3={stable:!0,takeEC:"full",getTestMsg:function(){return u("Zm7L::采样率范围：{1}；比特率范围：{2}（不同比特率支持的采样率范围不同，小于32kbps时采样率需小于32000）",0,_,o)}};var h,v=function(e){var t=e.bitRate,a=e.sampleRate,s=a;if(-1==(" "+o+",").indexOf(" "+t+",")&&m.CLog(u("eGB9::{1}不在mp3支持的取值范围：{2}",0,"bitRate="+t,o),3),-1==(" "+_+",").indexOf(" "+a+",")){for(var n=_.split(", "),r=[],i=0;i<n.length;i++)r.push({v:+n[i],s:Math.abs(n[i]-a)});r.sort(function(e,t){return e.s-t.s}),s=r[0].v,e.sampleRate=s,m.CLog(u("zLTa::sampleRate已更新为{1}，因为{2}不在mp3支持的取值范围：{3}",0,s,a,_),3)}},s=function(){return u.G("NeedImport-2",["mp3.js","src/engine/mp3-engine.js"])},d=t&&"function"==typeof Worker;m.prototype.mp3=function(r,i,_){var e=this,o=e.set,l=r.length;if(m.lamejs){if(d){var t=e.mp3_start(o);if(t){if(t.isW)return e.mp3_encode(t,r),void e.mp3_complete(t,i,_,1);e.mp3_stop(t)}}v(o);var f=new m.lamejs.Mp3Encoder(1,o.sampleRate,o.bitRate),c=new Int8Array(5e5),u=0,h=0,b=0,p=function(){try{if(h<l)var e=f.encodeBuffer(r.subarray(h,h+57600));else{b=1;var e=f.flush()}}catch(e){if(console.error(e),!b)try{f.flush()}catch(e){console.error(e)}return void _("MP3 Encoder: "+e.message)}var t=e.length;if(0<t){if(u+t>c.length){var a=new Int8Array(c.length+Math.max(5e5,t));a.set(c.subarray(0,u)),c=a}c.set(e,u),u+=t}if(h<l)h+=57600,setTimeout(p);else{var s=[c.buffer.slice(0,u)],n=g.fn(s,u,l,o.sampleRate);w(n,o),i(s[0]||new ArrayBuffer(0),"audio/mp3")}};p()}else _(s())},m.BindDestroy("mp3Worker",function(){h&&(m.CLog("mp3Worker Destroy"),h.terminate(),h=null)}),m.prototype.mp3_envCheck=function(e,t){var a="";return t.takeoffEncodeChunk&&(p()||(a=u("yhUs::当前浏览器版本太低，无法实时处理"))),a||m.lamejs||(a=s()),a},m.prototype.mp3_start=function(e){return p(e)};var b={id:0},p=function(t,e){var f,a=function(e){var t=e.data,a=f.wkScope.wk_ctxs,s=f.wkScope.wk_lame,n=f.wkScope.wk_mp3TrimFix,r=a[t.id];if("init"==t.action)a[t.id]={sampleRate:t.sampleRate,bitRate:t.bitRate,takeoff:t.takeoff,pcmSize:0,memory:new Int8Array(5e5),mOffset:0,encObj:new s.Mp3Encoder(1,t.sampleRate,t.bitRate)};else if(!r)return;var i=function(e){var t=e.length;if(r.mOffset+t>r.memory.length){var a=new Int8Array(r.memory.length+Math.max(5e5,t));a.set(r.memory.subarray(0,r.mOffset)),r.memory=a}r.memory.set(e,r.mOffset),r.mOffset+=t};switch(t.action){case"stop":if(!r.isCp)try{r.encObj.flush()}catch(e){console.error(e)}r.encObj=null,delete a[t.id];break;case"encode":if(r.isCp)break;r.pcmSize+=t.pcm.length;try{var _=r.encObj.encodeBuffer(t.pcm)}catch(e){r.err=e,console.error(e)}_&&0<_.length&&(r.takeoff?c.onmessage({action:"takeoff",id:t.id,chunk:_}):i(_));break;case"complete":r.isCp=1;try{var _=r.encObj.flush()}catch(e){r.err=e,console.error(e)}if(_&&0<_.length&&(r.takeoff?c.onmessage({action:"takeoff",id:t.id,chunk:_}):i(_)),r.err){c.onmessage({action:t.action,id:t.id,err:"MP3 Encoder: "+r.err.message});break}var o=[r.memory.buffer.slice(0,r.mOffset)],l=n.fn(o,r.mOffset,r.pcmSize,r.sampleRate);c.onmessage({action:t.action,id:t.id,blob:o[0]||new ArrayBuffer(0),meta:l})}},s=function(s){c.onmessage=function(e){var t=e;s&&(t=e.data);var a=b[t.id];a&&("takeoff"==t.action?a.set.takeoffEncodeChunk(new Uint8Array(t.chunk.buffer)):(a.call&&a.call(t),a.call=null))}},n=function(){var e={worker:c,set:t};return t?(e.id=++b.id,b[e.id]=e,v(t),c.postMessage({action:"init",id:e.id,sampleRate:t.sampleRate,bitRate:t.bitRate,takeoff:!!t.takeoffEncodeChunk,x:new Int16Array(5)})):c.postMessage({x:new Int16Array(5)}),e},c=h;if(e||!d)return m.CLog(u("k9PT::当前环境不支持Web Worker，mp3实时编码器运行在主线程中"),3),c={postMessage:function(e){a({data:e})}},f={wkScope:{wk_ctxs:{},wk_lame:m.lamejs,wk_mp3TrimFix:g}},s(),n();try{if(!c){var r=(a+"").replace(/[\w\$]+\.onmessage/g,"self.postMessage"),i=");wk_lame();self.onmessage="+(r=r.replace(/[\w\$]+\.wkScope/g,"wkScope"));i+=";var wkScope={wk_ctxs:{},wk_lame:wk_lame",i+=",wk_mp3TrimFix:{rm:"+g.rm+",fn:"+g.fn+"}}";var _=m.lamejs.toString(),o=(window.URL||webkitURL).createObjectURL(new Blob(["var wk_lame=(",_,i],{type:"text/javascript"}));c=new Worker(o),setTimeout(function(){(window.URL||webkitURL).revokeObjectURL(o)},1e4),s(1)}var l=n();return l.isW=1,h=c,l}catch(e){return c&&c.terminate(),console.error(e),p(t,1)}};m.prototype.mp3_stop=function(e){if(e&&e.worker){e.worker.postMessage({action:"stop",id:e.id}),e.worker=null,delete b[e.id];var t=-1;for(var a in b)t++;t&&m.CLog(u("fT6M::mp3 worker剩{1}个未stop",0,t),3)}},m.prototype.mp3_encode=function(e,t){e&&e.worker&&e.worker.postMessage({action:"encode",id:e.id,pcm:t})},m.prototype.mp3_complete=function(t,a,s,n){var r=this;t&&t.worker?(t.call=function(e){n&&r.mp3_stop(t),e.err?s(e.err):(w(e.meta,t.set),a(e.blob,"audio/mp3"))},t.worker.postMessage({action:"complete",id:t.id})):s(u("mPxH::mp3编码器未start"))},m.mp3ReadMeta=function(e,t){var a="undefined"!=typeof window&&window.parseInt||"undefined"!=typeof self&&self.parseInt||parseInt,s=new Uint8Array(e[0]||[]);if(s.length<4)return null;var n=function(e,t){return("0000000"+((t||s)[e]||0).toString(2)).substr(-8)},r=n(0)+n(1),i=n(2)+n(3);if(!/^1{11}/.test(r))return null;var _={"00":2.5,10:2,11:1}[r.substr(11,2)],o={"01":3}[r.substr(13,2)],l={1:[44100,48e3,32e3],2:[22050,24e3,16e3],2.5:[11025,12e3,8e3]}[_];l&&(l=l[a(i.substr(4,2),2)]);var f=[[0,8,16,24,32,40,48,56,64,80,96,112,128,144,160],[0,32,40,48,56,64,80,96,112,128,160,192,224,256,320]][1==_?1:0][a(i.substr(0,4),2)];if(!(_&&o&&f&&l))return null;for(var c=Math.round(8*t/f),u=1==o?384:2==o?1152:1==_?1152:576,h=u/l*1e3,b=Math.floor(u*f/8/l*1e3),p=0,m=0,v=0;v<e.length;v++){var d=e[v];if(m+=d.byteLength,b+3<=m){var g=new Uint8Array(d),w=d.byteLength-(m-(b+3)+1),S=n(w,g);p="1"==S.charAt(6);break}}return p&&b++,{version:_,layer:o,sampleRate:l,bitRate:f,duration:c,size:t,hasPadding:p,frameSize:b,frameDurationFloat:h}};var g={rm:m.mp3ReadMeta,fn:function(e,t,a,s){var n=this.rm(e,t);if(!n)return{size:t,err:"mp3 unknown format"};var r=Math.round(a/s*1e3),i=Math.floor((n.duration-r)/n.frameDurationFloat);if(0<i){var _=i*n.frameSize-(n.hasPadding?1:0);t-=_;for(var o=0,l=[],f=0;f<e.length;f++){var c=e[f];if(_<=0)break;_>=c.byteLength?(_-=c.byteLength,l.push(c),e.splice(f,1),f--):(e[f]=c.slice(_),o=c,_=0)}var u=this.rm(e,t);if(!u){o&&(e[0]=o);for(var f=0;f<l.length;f++)e.splice(f,0,l[f]);n.err="mp3 fix error: 已还原，错误原因不明"}var h=n.trimFix={};h.remove=i,h.removeDuration=Math.round(i*n.frameDurationFloat),h.duration=Math.round(8*t/n.bitRate)}return n}},w=function(e,t){var a="MP3 Info: ";(e.sampleRate&&e.sampleRate!=t.sampleRate||e.bitRate&&e.bitRate!=t.bitRate)&&(m.CLog(a+u("uY9i::和设置的不匹配{1}，已更新成{2}",0,"set:"+t.bitRate+"kbps "+t.sampleRate+"hz","set:"+e.bitRate+"kbps "+e.sampleRate+"hz"),3,t),t.sampleRate=e.sampleRate,t.bitRate=e.bitRate);var s=e.trimFix;s?(a+=u("iMSm::Fix移除{1}帧",0,s.remove)+" "+s.removeDuration+"ms -> "+s.duration+"ms",2<s.remove&&(e.err=(e.err?e.err+", ":"")+u("b9zm::移除帧数过多"))):a+=(e.duration||"-")+"ms",e.err?m.CLog(a,e.size?1:0,e.err,e):m.CLog(a,e)}}(a,0,s.$T,t)}(),function(e){"use strict";function t(){var d=function(e){return Math.log(e)/Math.log(10)},me=function(e){throw new Error("abort("+e+")")};function S(e){return new Int8Array(e)}function n(e){return new Int16Array(e)}function ve(e){return new Int32Array(e)}function de(e){return new Float32Array(e)}function s(e){return new Float64Array(e)}function ge(e){if(1==e.length)return de(e[0]);var t=e[0];e=e.slice(1);for(var a=[],s=0;s<t;s++)a.push(ge(e));return a}function w(e){if(1==e.length)return ve(e[0]);var t=e[0];e=e.slice(1);for(var a=[],s=0;s<t;s++)a.push(w(e));return a}function M(e){if(1==e.length)return n(e[0]);var t=e[0];e=e.slice(1);for(var a=[],s=0;s<t;s++)a.push(M(e));return a}function E(e){if(1==e.length)return new Array(e[0]);var t=e[0];e=e.slice(1);for(var a=[],s=0;s<t;s++)a.push(E(e));return a}var we={fill:function(e,t,a,s){if(2==arguments.length)for(var n=0;n<e.length;n++)e[n]=t;else for(var n=t;n<a;n++)e[n]=s}},P={arraycopy:function(e,t,a,s,n){for(var r=t+n;t<r;)a[s++]=e[t++]}},D={};function Se(e){this.ordinal=e}D.SQRT2=1.4142135623730951,D.FAST_LOG10=function(e){return d(e)},D.FAST_LOG10_X=function(e,t){return d(e)*t},Se.short_block_allowed=new Se(0),Se.short_block_coupled=new Se(1),Se.short_block_dispensed=new Se(2),Se.short_block_forced=new Se(3);var Y={};function Me(e){this.ordinal=e}function Be(e){var t=e;this.ordinal=function(){return t}}function A(){var S=null;function v(e){this.bits=0|e}this.qupvt=null,this.setModules=function(e){this.qupvt=e,S=e};var n=[[0,0],[0,0],[0,0],[0,0],[0,0],[0,1],[1,1],[1,1],[1,2],[2,2],[2,3],[2,3],[3,4],[3,4],[3,4],[4,5],[4,5],[4,6],[5,6],[5,6],[5,7],[6,7],[6,7]];function M(e,t,a,s,n,r){var i=.5946/t;for(e>>=1;0!=e--;)n[r++]=i>a[s++]?0:1,n[r++]=i>a[s++]?0:1}function B(e,t,a,s,n,r){var i=(e>>=1)%2;for(e>>=1;0!=e--;){var _,o,l,f,c,u,h,b;_=a[s++]*t,o=a[s++]*t,c=0|_,l=a[s++]*t,u=0|o,f=a[s++]*t,h=0|l,_+=S.adj43[c],b=0|f,o+=S.adj43[u],n[r++]=0|_,l+=S.adj43[h],n[r++]=0|o,f+=S.adj43[b],n[r++]=0|l,n[r++]=0|f}0!=i&&(_=a[s++]*t,o=a[s++]*t,c=0|_,u=0|o,_+=S.adj43[c],o+=S.adj43[u],n[r++]=0|_,n[r++]=0|o)}var _=[1,2,5,7,7,10,10,13,13,13,13,13,13,13,13];function d(e,t,a,s){var n=function(e,t,a){var s=0,n=0;do{var r=e[t++],i=e[t++];s<r&&(s=r),n<i&&(n=i)}while(t<a);return s<n&&(s=n),s}(e,t,a);switch(n){case 0:return n;case 1:return function(e,t,a,s){var n=0,r=R.ht[1].hlen;do{var i=2*e[t+0]+e[t+1];t+=2,n+=r[i]}while(t<a);return s.bits+=n,1}(e,t,a,s);case 2:case 3:return function(e,t,a,s,n){var r,i,_=0,o=R.ht[s].xlen;i=2==s?R.table23:R.table56;do{var l=e[t+0]*o+e[t+1];t+=2,_+=i[l]}while(t<a);return(r=65535&_)<(_>>=16)&&(_=r,s++),n.bits+=_,s}(e,t,a,_[n-1],s);case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 12:case 13:case 14:case 15:return function(e,t,a,s,n){var r=0,i=0,_=0,o=R.ht[s].xlen,l=R.ht[s].hlen,f=R.ht[s+1].hlen,c=R.ht[s+2].hlen;do{var u=e[t+0]*o+e[t+1];t+=2,r+=l[u],i+=f[u],_+=c[u]}while(t<a);var h=s;return i<r&&(r=i,h++),_<r&&(r=_,h=s+2),n.bits+=r,h}(e,t,a,_[n-1],s);default:var r,i;for(x.IXMAX_VAL<n&&me(),n-=15,r=24;r<32&&!(R.ht[r].linmax>=n);r++);for(i=r-8;i<24&&!(R.ht[i].linmax>=n);i++);return function(e,t,a,s,n,r){var i,_=65536*R.ht[s].xlen+R.ht[n].xlen,o=0;do{var l=e[t++],f=e[t++];0!=l&&(14<l&&(l=15,o+=_),l*=16),0!=f&&(14<f&&(f=15,o+=_),l+=f),o+=R.largetbl[l]}while(t<a);return(i=65535&o)<(o>>=16)&&(o=i,s=n),r.bits+=o,s}(e,t,a,i,r,s)}}function h(e,t,a,s,n,r,i,_){for(var o=t.big_values,l=2;l<Ae.SBMAX_l+1;l++){var f=e.scalefac_band.l[l];if(o<=f)break;var c=n[l-2]+t.count1bits;if(a.part2_3_length<=c)break;var u=new v(c),h=d(s,f,o,u);c=u.bits,a.part2_3_length<=c||(a.assign(t),a.part2_3_length=c,a.region0_count=r[l-2],a.region1_count=l-2-r[l-2],a.table_select[0]=i[l-2],a.table_select[1]=_[l-2],a.table_select[2]=h)}}this.noquant_count_bits=function(e,t,a){var s=t.l3_enc,n=Math.min(576,t.max_nonzero_coeff+2>>1<<1);for(null!=a&&(a.sfb_count1=0);1<n&&0==(s[n-1]|s[n-2]);n-=2);t.count1=n;for(var r=0,i=0;3<n;n-=4){var _;if(1<(2147483647&(s[n-1]|s[n-2]|s[n-3]|s[n-4])))break;_=2*(2*(2*s[n-4]+s[n-3])+s[n-2])+s[n-1],r+=R.t32l[_],i+=R.t33l[_]}var o=r;if(t.count1table_select=0,i<r&&(o=i,t.count1table_select=1),t.count1bits=o,0==(t.big_values=n))return o;if(t.block_type==Ae.SHORT_TYPE)(r=3*e.scalefac_band.s[3])>t.big_values&&(r=t.big_values),i=t.big_values;else if(t.block_type==Ae.NORM_TYPE){if(r=t.region0_count=e.bv_scf[n-2],i=t.region1_count=e.bv_scf[n-1],i=e.scalefac_band.l[r+i+2],r=e.scalefac_band.l[r+1],i<n){var l=new v(o);t.table_select[2]=d(s,i,n,l),o=l.bits}}else t.region0_count=7,t.region1_count=Ae.SBMAX_l-1-7-1,r=e.scalefac_band.l[8],(i=n)<r&&(r=i);if(r=Math.min(r,n),i=Math.min(i,n),0<r){var l=new v(o);t.table_select[0]=d(s,0,r,l),o=l.bits}if(r<i){var l=new v(o);t.table_select[1]=d(s,r,i,l),o=l.bits}if(2==e.use_best_huffman&&me(),null!=a&&t.block_type==Ae.NORM_TYPE){for(var f=0;e.scalefac_band.l[f]<t.big_values;)f++;a.sfb_count1=f}return o},this.count_bits=function(e,t,a,s){var n=a.l3_enc,r=x.IXMAX_VAL/S.IPOW20(a.global_gain);return a.xrpow_max>r?x.LARGE_BITS:(function(e,t,a,s,n){var r,i,_,o=0,l=0,f=0,c=0,u=t,h=0,b=u,p=0,m=e,v=0;for(_=null!=n&&s.global_gain==n.global_gain,i=s.block_type==Ae.SHORT_TYPE?38:21,r=0;r<=i;r++){var d=-1;if((_||s.block_type==Ae.NORM_TYPE)&&(d=s.global_gain-(s.scalefac[r]+(0!=s.preflag?S.pretab[r]:0)<<s.scalefac_scale+1)-8*s.subblock_gain[s.window[r]]),_&&n.step[r]==d)0!=l&&(B(l,a,m,v,b,p),l=0),0!=f&&me();else{var g,w=s.width[r];if(o+s.width[r]>s.max_nonzero_coeff&&(g=s.max_nonzero_coeff-o+1,we.fill(t,s.max_nonzero_coeff,576,0),(w=g)<0&&(w=0),r=i+1),0==l&&0==f&&(b=u,p=h,m=e,v=c),null!=n&&0<n.sfb_count1&&r>=n.sfb_count1&&0<n.step[r]&&d>=n.step[r]?(0!=l&&(B(l,a,m,v,b,p),l=0,b=u,p=h,m=e,v=c),f+=w):(0!=f&&(M(f,a,m,v,b,p),f=0,b=u,p=h,m=e,v=c),l+=w),w<=0){0!=f&&me(),0!=l&&me();break}}r<=i&&(h+=s.width[r],c+=s.width[r],o+=s.width[r])}0!=l&&(B(l,a,m,v,b,p),l=0),0!=f&&me()}(t,n,S.IPOW20(a.global_gain),a,s),0!=(2&e.substep_shaping)&&me(),this.noquant_count_bits(e,a,s))},this.best_huffman_divide=function(e,t){var a=new y,s=t.l3_enc,n=ve(23),r=ve(23),i=ve(23),_=ve(23);if(t.block_type!=Ae.SHORT_TYPE||1!=e.mode_gr){a.assign(t),t.block_type==Ae.NORM_TYPE&&(function(e,t,a,s,n,r,i){for(var _=t.big_values,o=0;o<=22;o++)s[o]=x.LARGE_BITS;for(var o=0;o<16;o++){var l=e.scalefac_band.l[o+1];if(_<=l)break;var f=0,c=new v(f),u=d(a,0,l,c);f=c.bits;for(var h=0;h<8;h++){var b=e.scalefac_band.l[o+h+2];if(_<=b)break;var p=f;c=new v(p);var m=d(a,l,b,c);p=c.bits,s[o+h]>p&&(s[o+h]=p,n[o+h]=o,r[o+h]=u,i[o+h]=m)}}}(e,t,s,n,r,i,_),h(e,a,t,s,n,r,i,_));var o=a.big_values;if(!(0==o||1<(s[o-2]|s[o-1])||576<(o=t.count1+2))){a.assign(t),a.count1=o;for(var l=0,f=0;o>a.big_values;o-=4){var c=2*(2*(2*s[o-4]+s[o-3])+s[o-2])+s[o-1];l+=R.t32l[c],f+=R.t33l[c]}if(a.big_values=o,a.count1table_select=0,f<l&&(l=f,a.count1table_select=1),a.count1bits=l,a.block_type==Ae.NORM_TYPE)h(e,a,t,s,n,r,i,_);else{if(a.part2_3_length=l,l=e.scalefac_band.l[8],o<l&&(l=o),0<l){var u=new v(a.part2_3_length);a.table_select[0]=d(s,0,l,u),a.part2_3_length=u.bits}if(l<o){var u=new v(a.part2_3_length);a.table_select[1]=d(s,l,o,u),a.part2_3_length=u.bits}t.part2_3_length>a.part2_3_length&&t.assign(a)}}}};var u=[1,1,1,1,8,2,2,2,4,4,4,8,8,8,16,16],b=[1,2,4,8,1,2,4,8,2,4,8,2,4,8,4,8],p=[0,0,0,0,3,1,1,1,2,2,2,3,3,3,4,4],m=[0,1,2,3,0,1,2,3,1,2,3,1,2,3,2,3];A.slen1_tab=p,A.slen2_tab=m,this.best_scalefac_store=function(e,t,a,s){var n,r,i,_,o=s.tt[t][a],l=0;for(n=i=0;n<o.sfbmax;n++){var f=o.width[n];for(i+=f,_=-f;_<0&&0==o.l3_enc[_+i];_++);0==_&&(o.scalefac[n]=l=-2)}if(0==o.scalefac_scale&&0==o.preflag){var c=0;for(n=0;n<o.sfbmax;n++)0<o.scalefac[n]&&(c|=o.scalefac[n]);if(0==(1&c)&&0!=c){for(n=0;n<o.sfbmax;n++)0<o.scalefac[n]&&(o.scalefac[n]>>=1);o.scalefac_scale=l=1}}if(0==o.preflag&&o.block_type!=Ae.SHORT_TYPE&&2==e.mode_gr){for(n=11;n<Ae.SBPSY_l&&!(o.scalefac[n]<S.pretab[n]&&-2!=o.scalefac[n]);n++);if(n==Ae.SBPSY_l){for(n=11;n<Ae.SBPSY_l;n++)0<o.scalefac[n]&&(o.scalefac[n]-=S.pretab[n]);o.preflag=l=1}}for(r=0;r<4;r++)s.scfsi[a][r]=0;for(2==e.mode_gr&&1==t&&s.tt[0][a].block_type!=Ae.SHORT_TYPE&&s.tt[1][a].block_type!=Ae.SHORT_TYPE&&(function(e,t){for(var a,s=t.tt[1][e],n=t.tt[0][e],r=0;r<R.scfsi_band.length-1;r++){for(a=R.scfsi_band[r];a<R.scfsi_band[r+1]&&!(n.scalefac[a]!=s.scalefac[a]&&0<=s.scalefac[a]);a++);if(a==R.scfsi_band[r+1]){for(a=R.scfsi_band[r];a<R.scfsi_band[r+1];a++)s.scalefac[a]=-1;t.scfsi[e][r]=1}}var i=0,_=0;for(a=0;a<11;a++)-1!=s.scalefac[a]&&(_++,i<s.scalefac[a]&&(i=s.scalefac[a]));for(var o=0,l=0;a<Ae.SBPSY_l;a++)-1!=s.scalefac[a]&&(l++,o<s.scalefac[a]&&(o=s.scalefac[a]));for(var r=0;r<16;r++)if(i<u[r]&&o<b[r]){var f=p[r]*_+m[r]*l;s.part2_length>f&&(s.part2_length=f,s.scalefac_compress=r)}}(a,s),l=0),n=0;n<o.sfbmax;n++)-2==o.scalefac[n]&&(o.scalefac[n]=0);0!=l&&(2==e.mode_gr?this.scale_bitcount(o):this.scale_bitcount_lsf(e,o))};var o=[0,18,36,54,54,36,54,72,54,72,90,72,90,108,108,126],l=[0,18,36,54,51,35,53,71,52,70,88,69,87,105,104,122],f=[0,10,20,30,33,21,31,41,32,42,52,43,53,63,64,74];this.scale_bitcount=function(e){var t,a,s,n=0,r=0,i=e.scalefac;if(e.block_type==Ae.SHORT_TYPE)s=o,0!=e.mixed_block_flag&&(s=l);else if(s=f,0==e.preflag){for(a=11;a<Ae.SBPSY_l&&!(i[a]<S.pretab[a]);a++);if(a==Ae.SBPSY_l)for(e.preflag=1,a=11;a<Ae.SBPSY_l;a++)i[a]-=S.pretab[a]}for(a=0;a<e.sfbdivide;a++)n<i[a]&&(n=i[a]);for(;a<e.sfbmax;a++)r<i[a]&&(r=i[a]);for(e.part2_length=x.LARGE_BITS,t=0;t<16;t++)n<u[t]&&r<b[t]&&e.part2_length>s[t]&&(e.part2_length=s[t],e.scalefac_compress=t);return e.part2_length==x.LARGE_BITS};var g=[[15,15,7,7],[15,15,7,0],[7,3,0,0],[15,31,31,0],[7,7,7,0],[3,3,0,0]];this.scale_bitcount_lsf=function(e,t){var a,s,n,r,i,_,o,l,f=ve(4),c=t.scalefac;for(a=0!=t.preflag?2:0,o=0;o<4;o++)f[o]=0;if(t.block_type==Ae.SHORT_TYPE){s=1;var u=S.nr_of_sfb_block[a][s];for(n=l=0;n<4;n++)for(r=u[n]/3,o=0;o<r;o++,l++)for(i=0;i<3;i++)c[3*l+i]>f[n]&&(f[n]=c[3*l+i])}else{s=0;var u=S.nr_of_sfb_block[a][s];for(n=l=0;n<4;n++)for(r=u[n],o=0;o<r;o++,l++)c[l]>f[n]&&(f[n]=c[l])}for(_=!1,n=0;n<4;n++)f[n]>g[a][n]&&(_=!0);if(!_){var h,b,p,m;for(t.sfb_partition_table=S.nr_of_sfb_block[a][s],n=0;n<4;n++)t.slen[n]=w[f[n]];switch(h=t.slen[0],b=t.slen[1],p=t.slen[2],m=t.slen[3],a){case 0:t.scalefac_compress=(5*h+b<<4)+(p<<2)+m;break;case 1:t.scalefac_compress=400+(5*h+b<<2)+p;break;case 2:t.scalefac_compress=500+3*h+b}}if(!_)for(t.part2_length=0,n=0;n<4;n++)t.part2_length+=t.slen[n]*t.sfb_partition_table[n];return _};var w=[0,1,2,2,3,3,3,3,4,4,4,4,4,4,4,4];this.huffman_init=function(e){for(var t=2;t<=576;t+=2){for(var a,s=0;e.scalefac_band.l[++s]<t;);for(a=n[s][0];e.scalefac_band.l[a+1]>t;)a--;for(a<0&&(a=n[s][0]),e.bv_scf[t-2]=a,a=n[s][1];e.scalefac_band.l[a+e.bv_scf[t-2]+2]>t;)a--;a<0&&(a=n[s][1]),e.bv_scf[t-1]=a}}}function V(){}function B(){this.setModules=function(e,t,a){};var _=[0,49345,49537,320,49921,960,640,49729,50689,1728,1920,51009,1280,50625,50305,1088,52225,3264,3456,52545,3840,53185,52865,3648,2560,51905,52097,2880,51457,2496,2176,51265,55297,6336,6528,55617,6912,56257,55937,6720,7680,57025,57217,8e3,56577,7616,7296,56385,5120,54465,54657,5440,55041,6080,5760,54849,53761,4800,4992,54081,4352,53697,53377,4160,61441,12480,12672,61761,13056,62401,62081,12864,13824,63169,63361,14144,62721,13760,13440,62529,15360,64705,64897,15680,65281,16320,16e3,65089,64001,15040,15232,64321,14592,63937,63617,14400,10240,59585,59777,10560,60161,11200,10880,59969,60929,11968,12160,61249,11520,60865,60545,11328,58369,9408,9600,58689,9984,59329,59009,9792,8704,58049,58241,9024,57601,8640,8320,57409,40961,24768,24960,41281,25344,41921,41601,25152,26112,42689,42881,26432,42241,26048,25728,42049,27648,44225,44417,27968,44801,28608,28288,44609,43521,27328,27520,43841,26880,43457,43137,26688,30720,47297,47489,31040,47873,31680,31360,47681,48641,32448,32640,48961,32e3,48577,48257,31808,46081,29888,30080,46401,30464,47041,46721,30272,29184,45761,45953,29504,45313,29120,28800,45121,20480,37057,37249,20800,37633,21440,21120,37441,38401,22208,22400,38721,21760,38337,38017,21568,39937,23744,23936,40257,24320,40897,40577,24128,23040,39617,39809,23360,39169,22976,22656,38977,34817,18624,18816,35137,19200,35777,35457,19008,19968,36545,36737,20288,36097,19904,19584,35905,17408,33985,34177,17728,34561,18368,18048,34369,33281,17088,17280,33601,16640,33217,32897,16448];this.updateMusicCRC=function(e,t,a,s){for(var n=0;n<s;++n)e[0]=(r=t[a+n],i=(i=e[0])>>8^_[255&(i^r)]);var r,i}}function N(){var i=this,r=null,_=null;this.setModules=function(e,t,a,s){r=a,_=s};var o=null,l=0,f=0,c=0;function v(e,t,a){for(;0<a;){var s;0==c&&(c=8,f++,e.header[e.w_ptr].write_timing==l&&(n=e,P.arraycopy(n.header[n.w_ptr].buf,0,o,f,n.sideinfo_len),f+=n.sideinfo_len,l+=8*n.sideinfo_len,n.w_ptr=n.w_ptr+1&X.MAX_HEADER_BUF-1),o[f]=0),s=Math.min(a,c),a-=s,c-=s,o[f]|=t>>a<<c,l+=s}var n}function u(e,t){var a,s=e.internal_flags;if(8<=t&&(v(s,76,8),t-=8),8<=t&&(v(s,65,8),t-=8),8<=t&&(v(s,77,8),t-=8),8<=t&&(v(s,69,8),t-=8),32<=t){var n=r.getLameShortVersion();if(32<=t)for(a=0;a<n.length&&8<=t;++a)t-=8,v(s,n.charCodeAt(a),8)}for(;1<=t;t-=1)v(s,s.ancillary_flag,1),s.ancillary_flag^=e.disable_reservoir?0:1}function h(e,t,a){for(var s=e.header[e.h_ptr].ptr;0<a;){var n=Math.min(a,8-(7&s));a-=n,e.header[e.h_ptr].buf[s>>3]|=t>>a<<8-(7&s)-n,s+=n}e.header[e.h_ptr].ptr=s}function m(e,t){var a,s=R.ht[t.count1table_select+32],n=0,r=t.big_values,i=t.big_values;for(a=(t.count1-t.big_values)/4;0<a;--a){var _=0,o=0;0!=t.l3_enc[r+0]&&(o+=8,t.xr[i+0]<0&&_++),0!=t.l3_enc[r+1]&&(o+=4,_*=2,t.xr[i+1]<0&&_++),0!=t.l3_enc[r+2]&&(o+=2,_*=2,t.xr[i+2]<0&&_++),0!=t.l3_enc[r+3]&&(o++,_*=2,t.xr[i+3]<0&&_++),r+=4,i+=4,v(e,_+s.table[o],s.hlen[o]),n+=s.hlen[o]}return n}function b(e,t,a,s,n){var r=R.ht[t],i=0;if(0==t)return i;for(var _=a;_<s;_+=2){var o=0,l=0,f=r.xlen,c=r.xlen,u=0,h=n.l3_enc[_],b=n.l3_enc[_+1];if(0!=h&&(n.xr[_]<0&&u++,o--),15<t){if(14<h){var p=h-15;u|=p<<1,l=f,h=15}if(14<b){var m=b-15;u<<=f,u|=m,l+=f,b=15}c=16}0!=b&&(u<<=1,n.xr[_+1]<0&&u++,o--),h=h*c+b,l-=o,o+=r.hlen[h],v(e,r.table[h],o),v(e,u,l),i+=o+l}return i}function d(e,t){var a=3*e.scalefac_band.s[3];a>t.big_values&&(a=t.big_values);var s=b(e,t.table_select[0],0,a,t);return s+=b(e,t.table_select[1],a,t.big_values,t)}function g(e,t){var a,s,n,r;a=t.big_values;var i=t.region0_count+1;return n=e.scalefac_band.l[i],i+=t.region1_count+1,r=e.scalefac_band.l[i],a<n&&(n=a),a<r&&(r=a),s=b(e,t.table_select[0],0,n,t),s+=b(e,t.table_select[1],n,r,t),s+=b(e,t.table_select[2],r,a,t)}function p(){this.total=0}function w(e,t){var a,s,n,r=e.internal_flags;return r.w_ptr,-1==(n=r.h_ptr-1)&&(n=X.MAX_HEADER_BUF-1),a=r.header[n].write_timing-l,0<=(t.total=a)&&me(),s=i.getframebits(e),a+=s,t.total+=s,t.total%8!=0?t.total=1+t.total/8:t.total=t.total/8,t.total+=f+1,a}this.getframebits=function(e){var t,a=e.internal_flags;t=0!=a.bitrate_index?R.bitrate_table[e.version][a.bitrate_index]:e.brate;var s=0|72e3*(e.version+1)*t/e.out_samplerate+a.padding;return 8*s},this.flush_bitstream=function(e){var t,a,s=e.internal_flags,n=s.h_ptr-1;-1==n&&(n=X.MAX_HEADER_BUF-1),t=s.l3_side,(a=w(e,new p))<0||(u(e,a),s.ResvSize=0,t.main_data_begin=0,s.findReplayGain&&me(),s.findPeakSample&&me())},this.format_bitstream=function(e){var t,a=e.internal_flags;t=a.l3_side;var s=this.getframebits(e);u(e,t.resvDrain_pre),function(e,t){var a,s,n,r=e.internal_flags;if(a=r.l3_side,r.header[r.h_ptr].ptr=0,we.fill(r.header[r.h_ptr].buf,0,r.sideinfo_len,0),e.out_samplerate<16e3?h(r,4094,12):h(r,4095,12),h(r,e.version,1),h(r,1,2),h(r,e.error_protection?0:1,1),h(r,r.bitrate_index,4),h(r,r.samplerate_index,2),h(r,r.padding,1),h(r,e.extension,1),h(r,e.mode.ordinal(),2),h(r,r.mode_ext,2),h(r,e.copyright,1),h(r,e.original,1),h(r,e.emphasis,2),e.error_protection&&h(r,0,16),1==e.version){for(h(r,a.main_data_begin,9),2==r.channels_out?h(r,a.private_bits,3):h(r,a.private_bits,5),n=0;n<r.channels_out;n++){var i;for(i=0;i<4;i++)h(r,a.scfsi[n][i],1)}for(s=0;s<2;s++)for(n=0;n<r.channels_out;n++){var _=a.tt[s][n];h(r,_.part2_3_length+_.part2_length,12),h(r,_.big_values/2,9),h(r,_.global_gain,8),h(r,_.scalefac_compress,4),_.block_type!=Ae.NORM_TYPE?(h(r,1,1),h(r,_.block_type,2),h(r,_.mixed_block_flag,1),14==_.table_select[0]&&(_.table_select[0]=16),h(r,_.table_select[0],5),14==_.table_select[1]&&(_.table_select[1]=16),h(r,_.table_select[1],5),h(r,_.subblock_gain[0],3),h(r,_.subblock_gain[1],3),h(r,_.subblock_gain[2],3)):(h(r,0,1),14==_.table_select[0]&&(_.table_select[0]=16),h(r,_.table_select[0],5),14==_.table_select[1]&&(_.table_select[1]=16),h(r,_.table_select[1],5),14==_.table_select[2]&&(_.table_select[2]=16),h(r,_.table_select[2],5),h(r,_.region0_count,4),h(r,_.region1_count,3)),h(r,_.preflag,1),h(r,_.scalefac_scale,1),h(r,_.count1table_select,1)}}else for(h(r,a.main_data_begin,8),h(r,a.private_bits,r.channels_out),n=s=0;n<r.channels_out;n++){var _=a.tt[s][n];h(r,_.part2_3_length+_.part2_length,12),h(r,_.big_values/2,9),h(r,_.global_gain,8),h(r,_.scalefac_compress,9),_.block_type!=Ae.NORM_TYPE?(h(r,1,1),h(r,_.block_type,2),h(r,_.mixed_block_flag,1),14==_.table_select[0]&&(_.table_select[0]=16),h(r,_.table_select[0],5),14==_.table_select[1]&&(_.table_select[1]=16),h(r,_.table_select[1],5),h(r,_.subblock_gain[0],3),h(r,_.subblock_gain[1],3),h(r,_.subblock_gain[2],3)):(h(r,0,1),14==_.table_select[0]&&(_.table_select[0]=16),h(r,_.table_select[0],5),14==_.table_select[1]&&(_.table_select[1]=16),h(r,_.table_select[1],5),14==_.table_select[2]&&(_.table_select[2]=16),h(r,_.table_select[2],5),h(r,_.region0_count,4),h(r,_.region1_count,3)),h(r,_.scalefac_scale,1),h(r,_.count1table_select,1)}e.error_protection&&me();var o=r.h_ptr;r.h_ptr=o+1&X.MAX_HEADER_BUF-1,r.header[r.h_ptr].write_timing=r.header[o].write_timing+t,r.h_ptr,r.w_ptr}(e,s);var n=8*a.sideinfo_len;if(n+=function(e){var t,a,s,n,r=0,i=e.internal_flags,_=i.l3_side;if(1==e.version)for(t=0;t<2;t++)for(a=0;a<i.channels_out;a++){var o=_.tt[t][a],l=A.slen1_tab[o.scalefac_compress],f=A.slen2_tab[o.scalefac_compress];for(s=n=0;s<o.sfbdivide;s++)-1!=o.scalefac[s]&&(v(i,o.scalefac[s],l),n+=l);for(;s<o.sfbmax;s++)-1!=o.scalefac[s]&&(v(i,o.scalefac[s],f),n+=f);o.block_type==Ae.SHORT_TYPE?n+=d(i,o):n+=g(i,o),n+=m(i,o),r+=n}else for(a=t=0;a<i.channels_out;a++){var c,u,o=_.tt[t][a],h=0;if(u=s=n=0,o.block_type==Ae.SHORT_TYPE){for(;u<4;u++){var b=o.sfb_partition_table[u]/3,p=o.slen[u];for(c=0;c<b;c++,s++)v(i,Math.max(o.scalefac[3*s+0],0),p),v(i,Math.max(o.scalefac[3*s+1],0),p),v(i,Math.max(o.scalefac[3*s+2],0),p),h+=3*p}n+=d(i,o)}else{for(;u<4;u++){var b=o.sfb_partition_table[u],p=o.slen[u];for(c=0;c<b;c++,s++)v(i,Math.max(o.scalefac[s],0),p),h+=p}n+=g(i,o)}n+=m(i,o),r+=h+n}return r}(e),u(e,t.resvDrain_post),n+=t.resvDrain_post,t.main_data_begin+=(s-n)/8,w(e,new p),a.ResvSize,8*t.main_data_begin!=a.ResvSize&&(a.ResvSize=8*t.main_data_begin),1e9<l){var r;for(r=0;r<X.MAX_HEADER_BUF;++r)a.header[r].write_timing-=l;l=0}return 0},this.copy_buffer=function(e,t,a,s,n){var r=f+1;if(r<=0)return 0;if(0!=s&&s<r)return-1;if(P.arraycopy(o,0,t,a,r),f=-1,(c=0)!=n){var i=ve(1);i[0]=e.nMusicCRC,_.updateMusicCRC(i,t,a,r),e.nMusicCRC=i[0],0<r&&(e.VBR_seek_table.nBytesWritten+=r),e.decode_on_the_fly&&me()}return r},this.init_bit_stream_w=function(e){o=S(F.LAME_MAXMP3BUFFER),e.h_ptr=e.w_ptr=0,e.header[e.h_ptr].write_timing=0,f=-1,l=c=0}}function e(e,t,a,s){this.xlen=e,this.linmax=t,this.table=a,this.hlen=s}Y.MAX_VALUE=3.4028235e38,Me.vbr_off=new Me(0),Me.vbr_mt=new Me(1),Me.vbr_rh=new Me(2),Me.vbr_abr=new Me(3),Me.vbr_mtrh=new Me(4),Me.vbr_default=Me.vbr_mtrh,Be.STEREO=new Be(0),Be.JOINT_STEREO=new Be(1),Be.DUAL_CHANNEL=new Be(2),Be.MONO=new Be(3),Be.NOT_SET=new Be(4),V.STEPS_per_dB=100,V.MAX_dB=120,V.GAIN_NOT_ENOUGH_SAMPLES=-24601,V.GAIN_ANALYSIS_ERROR=0,V.GAIN_ANALYSIS_OK=1,V.INIT_GAIN_ANALYSIS_ERROR=0,V.INIT_GAIN_ANALYSIS_OK=1,V.MAX_ORDER=V.YULE_ORDER=10,V.MAX_SAMPLES_PER_WINDOW=(V.MAX_SAMP_FREQ=48e3)*(V.RMS_WINDOW_TIME_NUMERATOR=1)/(V.RMS_WINDOW_TIME_DENOMINATOR=20)+1,B.NUMTOCENTRIES=100,B.MAXFRAMESIZE=2880,N.EQ=function(e,t){return Math.abs(e)>Math.abs(t)?Math.abs(e-t)<=1e-6*Math.abs(e):Math.abs(e-t)<=1e-6*Math.abs(t)},N.NEQ=function(e,t){return!N.EQ(e,t)};var R={};function k(e){this.bits=e}function T(){this.over_noise=0,this.tot_noise=0,this.max_noise=0,this.over_count=0,this.over_SSD=0,this.bits=0}function r(e,t,a,s){this.l=ve(1+Ae.SBMAX_l),this.s=ve(1+Ae.SBMAX_s),this.psfb21=ve(1+Ae.PSFB21),this.psfb12=ve(1+Ae.PSFB12);var n=this.l,r=this.s;4==arguments.length&&(this.arrL=e,this.arrS=t,this.arr21=a,this.arr12=s,P.arraycopy(this.arrL,0,n,0,Math.min(this.arrL.length,this.l.length)),P.arraycopy(this.arrS,0,r,0,Math.min(this.arrS.length,this.s.length)),P.arraycopy(this.arr21,0,this.psfb21,0,Math.min(this.arr21.length,this.psfb21.length)),P.arraycopy(this.arr12,0,this.psfb12,0,Math.min(this.arr12.length,this.psfb12.length)))}function x(){var l=null,b=null,s=null;this.setModules=function(e,t,a){l=e,b=t,s=a},this.IPOW20=function(e){return h[e]};var y=2.220446049250313e-16,e=x.IXMAX_VAL,f=e+2,c=x.Q_MAX,u=x.Q_MAX2,n=100;this.nr_of_sfb_block=[[[6,5,5,5],[9,9,9,9],[6,9,9,9]],[[6,5,7,3],[9,9,12,6],[6,9,12,6]],[[11,10,0,0],[18,18,0,0],[15,18,0,0]],[[7,7,7,0],[12,12,12,0],[6,15,12,0]],[[6,6,6,3],[12,9,9,6],[6,12,9,6]],[[8,8,5,0],[15,12,9,0],[6,18,9,0]]];var M=[0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,2,2,3,3,3,2,0];this.pretab=M,this.sfBandIndex=[new r([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,24,32,42,56,74,100,132,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new r([0,6,12,18,24,30,36,44,54,66,80,96,114,136,162,194,232,278,332,394,464,540,576],[0,4,8,12,18,26,36,48,62,80,104,136,180,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new r([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,26,36,48,62,80,104,134,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new r([0,4,8,12,16,20,24,30,36,44,52,62,74,90,110,134,162,196,238,288,342,418,576],[0,4,8,12,16,22,30,40,52,66,84,106,136,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new r([0,4,8,12,16,20,24,30,36,42,50,60,72,88,106,128,156,190,230,276,330,384,576],[0,4,8,12,16,22,28,38,50,64,80,100,126,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new r([0,4,8,12,16,20,24,30,36,44,54,66,82,102,126,156,194,240,296,364,448,550,576],[0,4,8,12,16,22,30,42,58,78,104,138,180,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new r([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,26,36,48,62,80,104,134,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new r([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,26,36,48,62,80,104,134,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new r([0,12,24,36,48,60,72,88,108,132,160,192,232,280,336,400,476,566,568,570,572,574,576],[0,8,16,24,36,52,72,96,124,160,162,164,166,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0])];var B=de(c+u+1),h=de(c),p=de(f),m=de(f);function v(e,t){var a=s.ATHformula(t,e);return a-=n,a=Math.pow(10,a/10+e.ATHlower)}function A(e){this.s=e}this.adj43=m,this.iteration_init=function(e){var t,a=e.internal_flags,s=a.l3_side;if(0==a.iteration_init_init){for(a.iteration_init_init=1,s.main_data_begin=0,function(e){for(var t=e.internal_flags.ATH.l,a=e.internal_flags.ATH.psfb21,s=e.internal_flags.ATH.s,n=e.internal_flags.ATH.psfb12,r=e.internal_flags,i=e.out_samplerate,_=0;_<Ae.SBMAX_l;_++){var o=r.scalefac_band.l[_],l=r.scalefac_band.l[_+1];t[_]=Y.MAX_VALUE;for(var f=o;f<l;f++){var c=f*i/1152,u=v(e,c);t[_]=Math.min(t[_],u)}}for(var _=0;_<Ae.PSFB21;_++){var o=r.scalefac_band.psfb21[_],l=r.scalefac_band.psfb21[_+1];a[_]=Y.MAX_VALUE;for(var f=o;f<l;f++){var c=f*i/1152,u=v(e,c);a[_]=Math.min(a[_],u)}}for(var _=0;_<Ae.SBMAX_s;_++){var o=r.scalefac_band.s[_],l=r.scalefac_band.s[_+1];s[_]=Y.MAX_VALUE;for(var f=o;f<l;f++){var c=f*i/384,u=v(e,c);s[_]=Math.min(s[_],u)}s[_]*=r.scalefac_band.s[_+1]-r.scalefac_band.s[_]}for(var _=0;_<Ae.PSFB12;_++){var o=r.scalefac_band.psfb12[_],l=r.scalefac_band.psfb12[_+1];n[_]=Y.MAX_VALUE;for(var f=o;f<l;f++){var c=f*i/384,u=v(e,c);n[_]=Math.min(n[_],u)}n[_]*=r.scalefac_band.s[13]-r.scalefac_band.s[12]}e.noATH&&me(),r.ATH.floor=10*d(v(e,-1))}(e),p[0]=0,t=1;t<f;t++)p[t]=Math.pow(t,4/3);for(t=0;t<f-1;t++)m[t]=t+1-Math.pow(.5*(p[t]+p[t+1]),.75);for(m[t]=.5,t=0;t<c;t++)h[t]=Math.pow(2,-.1875*(t-210));for(t=0;t<=c+u;t++)B[t]=Math.pow(2,.25*(t-210-u));var n,r,i,_;for(l.huffman_init(a),32<=(t=e.exp_nspsytune>>2&63)&&(t-=64),n=Math.pow(10,t/4/10),32<=(t=e.exp_nspsytune>>8&63)&&(t-=64),r=Math.pow(10,t/4/10),32<=(t=e.exp_nspsytune>>14&63)&&(t-=64),i=Math.pow(10,t/4/10),32<=(t=e.exp_nspsytune>>20&63)&&(t-=64),_=i*Math.pow(10,t/4/10),t=0;t<Ae.SBMAX_l;t++)o=t<=6?n:t<=13?r:t<=20?i:_,a.nsPsy.longfact[t]=o;for(t=0;t<Ae.SBMAX_s;t++){var o;o=t<=5?n:t<=10?r:t<=11?i:_,a.nsPsy.shortfact[t]=o}}},this.on_pe=function(e,t,a,s,n,r){var i,_,o=e.internal_flags,l=0,f=ve(2),c=new k(l),u=b.ResvMaxBits(e,s,c,r),h=(l=c.bits)+u;for(X.MAX_BITS_PER_GRANULE<h&&(h=X.MAX_BITS_PER_GRANULE),_=i=0;_<o.channels_out;++_)a[_]=Math.min(X.MAX_BITS_PER_CHANNEL,l/o.channels_out),f[_]=0|a[_]*t[n][_]/700-a[_],f[_]>3*s/4&&(f[_]=3*s/4),f[_]<0&&(f[_]=0),f[_]+a[_]>X.MAX_BITS_PER_CHANNEL&&(f[_]=Math.max(0,X.MAX_BITS_PER_CHANNEL-a[_])),i+=f[_];if(u<i)for(_=0;_<o.channels_out;++_)f[_]=u*f[_]/i;for(_=0;_<o.channels_out;++_)a[_]+=f[_],u-=f[_];for(_=i=0;_<o.channels_out;++_)i+=a[_];return X.MAX_BITS_PER_GRANULE<i&&me(),h},this.athAdjust=function(e,t,a){var s=90.30873362,n=D.FAST_LOG10_X(t,10),r=e*e,i=0;return n-=a,1e-20<r&&(i=1+D.FAST_LOG10_X(r,10/s)),i<0&&(i=0),n*=i,n+=a+s-94.82444863,Math.pow(10,.1*n)},this.calc_xmin=function(e,t,a,s){var n,r=0,i=e.internal_flags,_=0,o=0,l=i.ATH,f=a.xr,c=e.VBR==Me.vbr_mtrh?1:0,u=i.masking_lower;for(e.VBR!=Me.vbr_mtrh&&e.VBR!=Me.vbr_mt||(u=1),n=0;n<a.psy_lmax;n++){for(w=e.VBR==Me.vbr_rh||e.VBR==Me.vbr_mtrh?athAdjust(l.adjust,l.l[n],l.floor):l.adjust*l.l[n],v=a.width[n],S=w/v,M=y,A=v>>1,B=0;R=f[_]*f[_],B+=R,M+=R<S?R:S,k=f[++_]*f[_],B+=k,M+=k<S?k:S,_++,0<--A;);if(w<B&&o++,n==Ae.SBPSY_l&&me(),0!=c&&(w=M),!e.ATHonly){var h=t.en.l[n];0<h&&(T=B*t.thm.l[n]*u/h,0!=c&&(T*=i.nsPsy.longfact[n]),w<T&&(w=T))}s[r++]=0!=c?w:w*i.nsPsy.longfact[n]}var b=575;if(a.block_type!=Ae.SHORT_TYPE)for(var p=576;0!=p--&&N.EQ(f[p],0);)b=p;a.max_nonzero_coeff=b;for(var m=a.sfb_smin;n<a.psymax;m++,n+=3){var v,d,g;for(g=e.VBR==Me.vbr_rh||e.VBR==Me.vbr_mtrh?athAdjust(l.adjust,l.s[m],l.floor):l.adjust*l.s[m],v=a.width[n],d=0;d<3;d++){var w,S,M,B=0,A=v>>1;S=g/v,M=y;do{var R,k;R=f[_]*f[_],B+=R,M+=R<S?R:S,k=f[++_]*f[_],B+=k,M+=k<S?k:S,_++}while(0<--A);if(g<B&&o++,m==Ae.SBPSY_s&&me(),w=0!=c?M:g,!e.ATHonly&&!e.ATHshort){var T,h=t.en.s[m][d];0<h&&(T=B*t.thm.s[m][d]*u/h,0!=c&&(T*=i.nsPsy.shortfact[m]),w<T&&(w=T))}s[r++]=0!=c?w:w*i.nsPsy.shortfact[m]}e.useTemporal&&(s[r-3]>s[r-3+1]&&(s[r-3+1]+=(s[r-3]-s[r-3+1])*i.decay),s[r-3+1]>s[r-3+2]&&(s[r-3+2]+=(s[r-3+1]-s[r-3+2])*i.decay))}return o},this.calc_noise_core=function(e,t,a,s){var n=0,r=t.s,i=e.l3_enc;if(r>e.count1)for(;0!=a--;)o=e.xr[r],r++,n+=o*o,o=e.xr[r],r++,n+=o*o;else if(r>e.big_values){var _=de(2);for(_[0]=0,_[1]=s;0!=a--;)o=Math.abs(e.xr[r])-_[i[r]],r++,n+=o*o,o=Math.abs(e.xr[r])-_[i[r]],r++,n+=o*o}else for(;0!=a--;){var o;o=Math.abs(e.xr[r])-p[i[r]]*s,r++,n+=o*o,o=Math.abs(e.xr[r])-p[i[r]]*s,r++,n+=o*o}return t.s=r,n},this.calc_noise=function(e,t,a,s,n){var r,i,_=0,o=0,l=0,f=0,c=0,u=-20,h=0,b=e.scalefac,p=0;for(s.over_SSD=0,r=0;r<e.psymax;r++){var m,v=e.global_gain-(b[p++]+(0!=e.preflag?M[r]:0)<<e.scalefac_scale+1)-8*e.subblock_gain[e.window[r]],d=0;if(null!=n&&n.step[r]==v)d=n.noise[r],h+=e.width[r],a[_++]=d/t[o++],d=n.noise_log[r];else{var g,w=B[v+x.Q_MAX2];i=e.width[r]>>1,h+e.width[r]>e.max_nonzero_coeff&&(g=e.max_nonzero_coeff-h+1,i=0<g?g>>1:0);var S=new A(h);d=this.calc_noise_core(e,S,i,w),h=S.s,null!=n&&(n.step[r]=v,n.noise[r]=d),d=a[_++]=d/t[o++],d=D.FAST_LOG10(Math.max(d,1e-20)),null!=n&&(n.noise_log[r]=d)}null!=n&&(n.global_gain=e.global_gain),c+=d,0<d&&(m=Math.max(0|10*d+.5,1),s.over_SSD+=m*m,l++,f+=d),u=Math.max(u,d)}return s.over_count=l,s.tot_noise=c,s.over_noise=f,s.max_noise=u,l}}function y(){this.xr=de(576),this.l3_enc=ve(576),this.scalefac=ve(C.SFBMAX),this.xrpow_max=0,this.part2_3_length=0,this.big_values=0,this.count1=0,this.global_gain=0,this.scalefac_compress=0,this.block_type=0,this.mixed_block_flag=0,this.table_select=ve(3),this.subblock_gain=ve(4),this.region0_count=0,this.region1_count=0,this.preflag=0,this.scalefac_scale=0,this.count1table_select=0,this.part2_length=0,this.sfb_lmax=0,this.sfb_smin=0,this.psy_lmax=0,this.sfbmax=0,this.psymax=0,this.sfbdivide=0,this.width=ve(C.SFBMAX),this.window=ve(C.SFBMAX),this.count1bits=0,this.sfb_partition_table=null,this.slen=ve(4),this.max_nonzero_coeff=0;var a=this;function s(e){return new Int32Array(e)}this.assign=function(e){var t;a.xr=(t=e.xr,new Float32Array(t)),a.l3_enc=s(e.l3_enc),a.scalefac=s(e.scalefac),a.xrpow_max=e.xrpow_max,a.part2_3_length=e.part2_3_length,a.big_values=e.big_values,a.count1=e.count1,a.global_gain=e.global_gain,a.scalefac_compress=e.scalefac_compress,a.block_type=e.block_type,a.mixed_block_flag=e.mixed_block_flag,a.table_select=s(e.table_select),a.subblock_gain=s(e.subblock_gain),a.region0_count=e.region0_count,a.region1_count=e.region1_count,a.preflag=e.preflag,a.scalefac_scale=e.scalefac_scale,a.count1table_select=e.count1table_select,a.part2_length=e.part2_length,a.sfb_lmax=e.sfb_lmax,a.sfb_smin=e.sfb_smin,a.psy_lmax=e.psy_lmax,a.sfbmax=e.sfbmax,a.psymax=e.psymax,a.sfbdivide=e.sfbdivide,a.width=s(e.width),a.window=s(e.window),a.count1bits=e.count1bits,a.sfb_partition_table=e.sfb_partition_table.slice(0),a.slen=s(e.slen),a.max_nonzero_coeff=e.max_nonzero_coeff}}R.t1HB=[1,1,1,0],R.t2HB=[1,2,1,3,1,1,3,2,0],R.t3HB=[3,2,1,1,1,1,3,2,0],R.t5HB=[1,2,6,5,3,1,4,4,7,5,7,1,6,1,1,0],R.t6HB=[7,3,5,1,6,2,3,2,5,4,4,1,3,3,2,0],R.t7HB=[1,2,10,19,16,10,3,3,7,10,5,3,11,4,13,17,8,4,12,11,18,15,11,2,7,6,9,14,3,1,6,4,5,3,2,0],R.t8HB=[3,4,6,18,12,5,5,1,2,16,9,3,7,3,5,14,7,3,19,17,15,13,10,4,13,5,8,11,5,1,12,4,4,1,1,0],R.t9HB=[7,5,9,14,15,7,6,4,5,5,6,7,7,6,8,8,8,5,15,6,9,10,5,1,11,7,9,6,4,1,14,4,6,2,6,0],R.t10HB=[1,2,10,23,35,30,12,17,3,3,8,12,18,21,12,7,11,9,15,21,32,40,19,6,14,13,22,34,46,23,18,7,20,19,33,47,27,22,9,3,31,22,41,26,21,20,5,3,14,13,10,11,16,6,5,1,9,8,7,8,4,4,2,0],R.t11HB=[3,4,10,24,34,33,21,15,5,3,4,10,32,17,11,10,11,7,13,18,30,31,20,5,25,11,19,59,27,18,12,5,35,33,31,58,30,16,7,5,28,26,32,19,17,15,8,14,14,12,9,13,14,9,4,1,11,4,6,6,6,3,2,0],R.t12HB=[9,6,16,33,41,39,38,26,7,5,6,9,23,16,26,11,17,7,11,14,21,30,10,7,17,10,15,12,18,28,14,5,32,13,22,19,18,16,9,5,40,17,31,29,17,13,4,2,27,12,11,15,10,7,4,1,27,12,8,12,6,3,1,0],R.t13HB=[1,5,14,21,34,51,46,71,42,52,68,52,67,44,43,19,3,4,12,19,31,26,44,33,31,24,32,24,31,35,22,14,15,13,23,36,59,49,77,65,29,40,30,40,27,33,42,16,22,20,37,61,56,79,73,64,43,76,56,37,26,31,25,14,35,16,60,57,97,75,114,91,54,73,55,41,48,53,23,24,58,27,50,96,76,70,93,84,77,58,79,29,74,49,41,17,47,45,78,74,115,94,90,79,69,83,71,50,59,38,36,15,72,34,56,95,92,85,91,90,86,73,77,65,51,44,43,42,43,20,30,44,55,78,72,87,78,61,46,54,37,30,20,16,53,25,41,37,44,59,54,81,66,76,57,54,37,18,39,11,35,33,31,57,42,82,72,80,47,58,55,21,22,26,38,22,53,25,23,38,70,60,51,36,55,26,34,23,27,14,9,7,34,32,28,39,49,75,30,52,48,40,52,28,18,17,9,5,45,21,34,64,56,50,49,45,31,19,12,15,10,7,6,3,48,23,20,39,36,35,53,21,16,23,13,10,6,1,4,2,16,15,17,27,25,20,29,11,17,12,16,8,1,1,0,1],R.t15HB=[7,12,18,53,47,76,124,108,89,123,108,119,107,81,122,63,13,5,16,27,46,36,61,51,42,70,52,83,65,41,59,36,19,17,15,24,41,34,59,48,40,64,50,78,62,80,56,33,29,28,25,43,39,63,55,93,76,59,93,72,54,75,50,29,52,22,42,40,67,57,95,79,72,57,89,69,49,66,46,27,77,37,35,66,58,52,91,74,62,48,79,63,90,62,40,38,125,32,60,56,50,92,78,65,55,87,71,51,73,51,70,30,109,53,49,94,88,75,66,122,91,73,56,42,64,44,21,25,90,43,41,77,73,63,56,92,77,66,47,67,48,53,36,20,71,34,67,60,58,49,88,76,67,106,71,54,38,39,23,15,109,53,51,47,90,82,58,57,48,72,57,41,23,27,62,9,86,42,40,37,70,64,52,43,70,55,42,25,29,18,11,11,118,68,30,55,50,46,74,65,49,39,24,16,22,13,14,7,91,44,39,38,34,63,52,45,31,52,28,19,14,8,9,3,123,60,58,53,47,43,32,22,37,24,17,12,15,10,2,1,71,37,34,30,28,20,17,26,21,16,10,6,8,6,2,0],R.t16HB=[1,5,14,44,74,63,110,93,172,149,138,242,225,195,376,17,3,4,12,20,35,62,53,47,83,75,68,119,201,107,207,9,15,13,23,38,67,58,103,90,161,72,127,117,110,209,206,16,45,21,39,69,64,114,99,87,158,140,252,212,199,387,365,26,75,36,68,65,115,101,179,164,155,264,246,226,395,382,362,9,66,30,59,56,102,185,173,265,142,253,232,400,388,378,445,16,111,54,52,100,184,178,160,133,257,244,228,217,385,366,715,10,98,48,91,88,165,157,148,261,248,407,397,372,380,889,884,8,85,84,81,159,156,143,260,249,427,401,392,383,727,713,708,7,154,76,73,141,131,256,245,426,406,394,384,735,359,710,352,11,139,129,67,125,247,233,229,219,393,743,737,720,885,882,439,4,243,120,118,115,227,223,396,746,742,736,721,712,706,223,436,6,202,224,222,218,216,389,386,381,364,888,443,707,440,437,1728,4,747,211,210,208,370,379,734,723,714,1735,883,877,876,3459,865,2,377,369,102,187,726,722,358,711,709,866,1734,871,3458,870,434,0,12,10,7,11,10,17,11,9,13,12,10,7,5,3,1,3],R.t24HB=[15,13,46,80,146,262,248,434,426,669,653,649,621,517,1032,88,14,12,21,38,71,130,122,216,209,198,327,345,319,297,279,42,47,22,41,74,68,128,120,221,207,194,182,340,315,295,541,18,81,39,75,70,134,125,116,220,204,190,178,325,311,293,271,16,147,72,69,135,127,118,112,210,200,188,352,323,306,285,540,14,263,66,129,126,119,114,214,202,192,180,341,317,301,281,262,12,249,123,121,117,113,215,206,195,185,347,330,308,291,272,520,10,435,115,111,109,211,203,196,187,353,332,313,298,283,531,381,17,427,212,208,205,201,193,186,177,169,320,303,286,268,514,377,16,335,199,197,191,189,181,174,333,321,305,289,275,521,379,371,11,668,184,183,179,175,344,331,314,304,290,277,530,383,373,366,10,652,346,171,168,164,318,309,299,287,276,263,513,375,368,362,6,648,322,316,312,307,302,292,284,269,261,512,376,370,364,359,4,620,300,296,294,288,282,273,266,515,380,374,369,365,361,357,2,1033,280,278,274,267,264,259,382,378,372,367,363,360,358,356,0,43,20,19,17,15,13,11,9,7,6,4,7,5,3,1,3],R.t32HB=[1,10,8,20,12,20,16,32,14,12,24,0,28,16,24,16],R.t33HB=[15,28,26,48,22,40,36,64,14,24,20,32,12,16,8,0],R.t1l=[1,4,3,5],R.t2l=[1,4,7,4,5,7,6,7,8],R.t3l=[2,3,7,4,4,7,6,7,8],R.t5l=[1,4,7,8,4,5,8,9,7,8,9,10,8,8,9,10],R.t6l=[3,4,6,8,4,4,6,7,5,6,7,8,7,7,8,9],R.t7l=[1,4,7,9,9,10,4,6,8,9,9,10,7,7,9,10,10,11,8,9,10,11,11,11,8,9,10,11,11,12,9,10,11,12,12,12],R.t8l=[2,4,7,9,9,10,4,4,6,10,10,10,7,6,8,10,10,11,9,10,10,11,11,12,9,9,10,11,12,12,10,10,11,11,13,13],R.t9l=[3,4,6,7,9,10,4,5,6,7,8,10,5,6,7,8,9,10,7,7,8,9,9,10,8,8,9,9,10,11,9,9,10,10,11,11],R.t10l=[1,4,7,9,10,10,10,11,4,6,8,9,10,11,10,10,7,8,9,10,11,12,11,11,8,9,10,11,12,12,11,12,9,10,11,12,12,12,12,12,10,11,12,12,13,13,12,13,9,10,11,12,12,12,13,13,10,10,11,12,12,13,13,13],R.t11l=[2,4,6,8,9,10,9,10,4,5,6,8,10,10,9,10,6,7,8,9,10,11,10,10,8,8,9,11,10,12,10,11,9,10,10,11,11,12,11,12,9,10,11,12,12,13,12,13,9,9,9,10,11,12,12,12,9,9,10,11,12,12,12,12],R.t12l=[4,4,6,8,9,10,10,10,4,5,6,7,9,9,10,10,6,6,7,8,9,10,9,10,7,7,8,8,9,10,10,10,8,8,9,9,10,10,10,11,9,9,10,10,10,11,10,11,9,9,9,10,10,11,11,12,10,10,10,11,11,11,11,12],R.t13l=[1,5,7,8,9,10,10,11,10,11,12,12,13,13,14,14,4,6,8,9,10,10,11,11,11,11,12,12,13,14,14,14,7,8,9,10,11,11,12,12,11,12,12,13,13,14,15,15,8,9,10,11,11,12,12,12,12,13,13,13,13,14,15,15,9,9,11,11,12,12,13,13,12,13,13,14,14,15,15,16,10,10,11,12,12,12,13,13,13,13,14,13,15,15,16,16,10,11,12,12,13,13,13,13,13,14,14,14,15,15,16,16,11,11,12,13,13,13,14,14,14,14,15,15,15,16,18,18,10,10,11,12,12,13,13,14,14,14,14,15,15,16,17,17,11,11,12,12,13,13,13,15,14,15,15,16,16,16,18,17,11,12,12,13,13,14,14,15,14,15,16,15,16,17,18,19,12,12,12,13,14,14,14,14,15,15,15,16,17,17,17,18,12,13,13,14,14,15,14,15,16,16,17,17,17,18,18,18,13,13,14,15,15,15,16,16,16,16,16,17,18,17,18,18,14,14,14,15,15,15,17,16,16,19,17,17,17,19,18,18,13,14,15,16,16,16,17,16,17,17,18,18,21,20,21,18],R.t15l=[3,5,6,8,8,9,10,10,10,11,11,12,12,12,13,14,5,5,7,8,9,9,10,10,10,11,11,12,12,12,13,13,6,7,7,8,9,9,10,10,10,11,11,12,12,13,13,13,7,8,8,9,9,10,10,11,11,11,12,12,12,13,13,13,8,8,9,9,10,10,11,11,11,11,12,12,12,13,13,13,9,9,9,10,10,10,11,11,11,11,12,12,13,13,13,14,10,9,10,10,10,11,11,11,11,12,12,12,13,13,14,14,10,10,10,11,11,11,11,12,12,12,12,12,13,13,13,14,10,10,10,11,11,11,11,12,12,12,12,13,13,14,14,14,10,10,11,11,11,11,12,12,12,13,13,13,13,14,14,14,11,11,11,11,12,12,12,12,12,13,13,13,13,14,15,14,11,11,11,11,12,12,12,12,13,13,13,13,14,14,14,15,12,12,11,12,12,12,13,13,13,13,13,13,14,14,15,15,12,12,12,12,12,13,13,13,13,14,14,14,14,14,15,15,13,13,13,13,13,13,13,13,14,14,14,14,15,15,14,15,13,13,13,13,13,13,13,14,14,14,14,14,15,15,15,15],R.t16_5l=[1,5,7,9,10,10,11,11,12,12,12,13,13,13,14,11,4,6,8,9,10,11,11,11,12,12,12,13,14,13,14,11,7,8,9,10,11,11,12,12,13,12,13,13,13,14,14,12,9,9,10,11,11,12,12,12,13,13,14,14,14,15,15,13,10,10,11,11,12,12,13,13,13,14,14,14,15,15,15,12,10,10,11,11,12,13,13,14,13,14,14,15,15,15,16,13,11,11,11,12,13,13,13,13,14,14,14,14,15,15,16,13,11,11,12,12,13,13,13,14,14,15,15,15,15,17,17,13,11,12,12,13,13,13,14,14,15,15,15,15,16,16,16,13,12,12,12,13,13,14,14,15,15,15,15,16,15,16,15,14,12,13,12,13,14,14,14,14,15,16,16,16,17,17,16,13,13,13,13,13,14,14,15,16,16,16,16,16,16,15,16,14,13,14,14,14,14,15,15,15,15,17,16,16,16,16,18,14,15,14,14,14,15,15,16,16,16,18,17,17,17,19,17,14,14,15,13,14,16,16,15,16,16,17,18,17,19,17,16,14,11,11,11,12,12,13,13,13,14,14,14,14,14,14,14,12],R.t16l=[1,5,7,9,10,10,11,11,12,12,12,13,13,13,14,10,4,6,8,9,10,11,11,11,12,12,12,13,14,13,14,10,7,8,9,10,11,11,12,12,13,12,13,13,13,14,14,11,9,9,10,11,11,12,12,12,13,13,14,14,14,15,15,12,10,10,11,11,12,12,13,13,13,14,14,14,15,15,15,11,10,10,11,11,12,13,13,14,13,14,14,15,15,15,16,12,11,11,11,12,13,13,13,13,14,14,14,14,15,15,16,12,11,11,12,12,13,13,13,14,14,15,15,15,15,17,17,12,11,12,12,13,13,13,14,14,15,15,15,15,16,16,16,12,12,12,12,13,13,14,14,15,15,15,15,16,15,16,15,13,12,13,12,13,14,14,14,14,15,16,16,16,17,17,16,12,13,13,13,13,14,14,15,16,16,16,16,16,16,15,16,13,13,14,14,14,14,15,15,15,15,17,16,16,16,16,18,13,15,14,14,14,15,15,16,16,16,18,17,17,17,19,17,13,14,15,13,14,16,16,15,16,16,17,18,17,19,17,16,13,10,10,10,11,11,12,12,12,13,13,13,13,13,13,13,10],R.t24l=[4,5,7,8,9,10,10,11,11,12,12,12,12,12,13,10,5,6,7,8,9,10,10,11,11,11,12,12,12,12,12,10,7,7,8,9,9,10,10,11,11,11,11,12,12,12,13,9,8,8,9,9,10,10,10,11,11,11,11,12,12,12,12,9,9,9,9,10,10,10,10,11,11,11,12,12,12,12,13,9,10,9,10,10,10,10,11,11,11,11,12,12,12,12,12,9,10,10,10,10,10,11,11,11,11,12,12,12,12,12,13,9,11,10,10,10,11,11,11,11,12,12,12,12,12,13,13,10,11,11,11,11,11,11,11,11,11,12,12,12,12,13,13,10,11,11,11,11,11,11,11,12,12,12,12,12,13,13,13,10,12,11,11,11,11,12,12,12,12,12,12,13,13,13,13,10,12,12,11,11,11,12,12,12,12,12,12,13,13,13,13,10,12,12,12,12,12,12,12,12,12,12,13,13,13,13,13,10,12,12,12,12,12,12,12,12,13,13,13,13,13,13,13,10,13,12,12,12,12,12,12,13,13,13,13,13,13,13,13,10,9,9,9,9,9,9,9,9,9,9,9,10,10,10,10,6],R.t32l=[1,5,5,7,5,8,7,9,5,7,7,9,7,9,9,10],R.t33l=[4,5,5,6,5,6,6,7,5,6,6,7,6,7,7,8],R.ht=[new e(0,0,null,null),new e(2,0,R.t1HB,R.t1l),new e(3,0,R.t2HB,R.t2l),new e(3,0,R.t3HB,R.t3l),new e(0,0,null,null),new e(4,0,R.t5HB,R.t5l),new e(4,0,R.t6HB,R.t6l),new e(6,0,R.t7HB,R.t7l),new e(6,0,R.t8HB,R.t8l),new e(6,0,R.t9HB,R.t9l),new e(8,0,R.t10HB,R.t10l),new e(8,0,R.t11HB,R.t11l),new e(8,0,R.t12HB,R.t12l),new e(16,0,R.t13HB,R.t13l),new e(0,0,null,R.t16_5l),new e(16,0,R.t15HB,R.t15l),new e(1,1,R.t16HB,R.t16l),new e(2,3,R.t16HB,R.t16l),new e(3,7,R.t16HB,R.t16l),new e(4,15,R.t16HB,R.t16l),new e(6,63,R.t16HB,R.t16l),new e(8,255,R.t16HB,R.t16l),new e(10,1023,R.t16HB,R.t16l),new e(13,8191,R.t16HB,R.t16l),new e(4,15,R.t24HB,R.t24l),new e(5,31,R.t24HB,R.t24l),new e(6,63,R.t24HB,R.t24l),new e(7,127,R.t24HB,R.t24l),new e(8,255,R.t24HB,R.t24l),new e(9,511,R.t24HB,R.t24l),new e(11,2047,R.t24HB,R.t24l),new e(13,8191,R.t24HB,R.t24l),new e(0,0,R.t32HB,R.t32l),new e(0,0,R.t33HB,R.t33l)],R.largetbl=[65540,327685,458759,589832,655369,655370,720906,720907,786443,786444,786444,851980,851980,851980,917517,655370,262149,393222,524295,589832,655369,720906,720906,720907,786443,786443,786444,851980,917516,851980,917516,655370,458759,524295,589832,655369,720905,720906,786442,786443,851979,786443,851979,851980,851980,917516,917517,720905,589832,589832,655369,720905,720906,786442,786442,786443,851979,851979,917515,917516,917516,983052,983052,786441,655369,655369,720905,720906,786442,786442,851978,851979,851979,917515,917516,917516,983052,983052,983053,720905,655370,655369,720906,720906,786442,851978,851979,917515,851979,917515,917516,983052,983052,983052,1048588,786441,720906,720906,720906,786442,851978,851979,851979,851979,917515,917516,917516,917516,983052,983052,1048589,786441,720907,720906,786442,786442,851979,851979,851979,917515,917516,983052,983052,983052,983052,1114125,1114125,786442,720907,786443,786443,851979,851979,851979,917515,917515,983051,983052,983052,983052,1048588,1048589,1048589,786442,786443,786443,786443,851979,851979,917515,917515,983052,983052,983052,983052,1048588,983053,1048589,983053,851978,786444,851979,786443,851979,917515,917516,917516,917516,983052,1048588,1048588,1048589,1114125,1114125,1048589,786442,851980,851980,851979,851979,917515,917516,983052,1048588,1048588,1048588,1048588,1048589,1048589,983053,1048589,851978,851980,917516,917516,917516,917516,983052,983052,983052,983052,1114124,1048589,1048589,1048589,1048589,1179661,851978,983052,917516,917516,917516,983052,983052,1048588,1048588,1048589,1179661,1114125,1114125,1114125,1245197,1114125,851978,917517,983052,851980,917516,1048588,1048588,983052,1048589,1048589,1114125,1179661,1114125,1245197,1114125,1048589,851978,655369,655369,655369,720905,720905,786441,786441,786441,851977,851977,851977,851978,851978,851978,851978,655366],R.table23=[65538,262147,458759,262148,327684,458759,393222,458759,524296],R.table56=[65539,262148,458758,524296,262148,327684,524294,589831,458757,524294,589831,655368,524295,524295,589832,655369],R.bitrate_table=[[0,8,16,24,32,40,48,56,64,80,96,112,128,144,160,-1],[0,32,40,48,56,64,80,96,112,128,160,192,224,256,320,-1],[0,8,16,24,32,40,48,56,64,-1,-1,-1,-1,-1,-1,-1]],R.samplerate_table=[[22050,24e3,16e3,-1],[44100,48e3,32e3,-1],[11025,12e3,8e3,-1]],R.scfsi_band=[0,6,11,16,21],x.Q_MAX=257,x.Q_MAX2=116,x.LARGE_BITS=1e5,x.IXMAX_VAL=8206;var C={};function H(){var r,g;this.rv=null,this.qupvt=null;var w,n=new function(){this.setModules=function(e,t){}};function S(e){this.ordinal=e}function _(e){for(var t=0;t<e.sfbmax;t++)if(e.scalefac[t]+e.subblock_gain[e.window[t]]==0)return!1;return!0}function M(e,t,a,s,n){var r;switch(e){default:case 9:0<t.over_count?(r=a.over_SSD<=t.over_SSD,a.over_SSD==t.over_SSD&&(r=a.bits<t.bits)):r=a.max_noise<0&&10*a.max_noise+a.bits<=10*t.max_noise+t.bits;break;case 0:r=a.over_count<t.over_count||a.over_count==t.over_count&&a.over_noise<t.over_noise||a.over_count==t.over_count&&N.EQ(a.over_noise,t.over_noise)&&a.tot_noise<t.tot_noise;break;case 8:me();case 1:r=a.max_noise<t.max_noise;break;case 2:r=a.tot_noise<t.tot_noise;break;case 3:r=a.tot_noise<t.tot_noise&&a.max_noise<t.max_noise;break;case 4:r=a.max_noise<=0&&.2<t.max_noise||a.max_noise<=0&&t.max_noise<0&&t.max_noise>a.max_noise-.2&&a.tot_noise<t.tot_noise||a.max_noise<=0&&0<t.max_noise&&t.max_noise>a.max_noise-.2&&a.tot_noise<t.tot_noise+t.over_noise||0<a.max_noise&&-.05<t.max_noise&&t.max_noise>a.max_noise-.1&&a.tot_noise+a.over_noise<t.tot_noise+t.over_noise||0<a.max_noise&&-.1<t.max_noise&&t.max_noise>a.max_noise-.15&&a.tot_noise+a.over_noise+a.over_noise<t.tot_noise+t.over_noise+t.over_noise;break;case 5:r=a.over_noise<t.over_noise||N.EQ(a.over_noise,t.over_noise)&&a.tot_noise<t.tot_noise;break;case 6:r=a.over_noise<t.over_noise||N.EQ(a.over_noise,t.over_noise)&&(a.max_noise<t.max_noise||N.EQ(a.max_noise,t.max_noise)&&a.tot_noise<=t.tot_noise);break;case 7:r=a.over_count<t.over_count||a.over_noise<t.over_noise}return 0==t.over_count&&(r=r&&a.bits<t.bits),r}function B(e,t,a,s,n){var r=e.internal_flags;!function(e,t,a,s,n){var r,i=e.internal_flags;r=0==t.scalefac_scale?1.2968395546510096:1.6817928305074292;for(var _=0,o=0;o<t.sfbmax;o++)_<a[o]&&(_=a[o]);var l=i.noise_shaping_amp;switch(3==l&&me(),l){case 2:break;case 1:1<_?_=Math.pow(_,.5):_*=.95;break;case 0:default:1<_?_=1:_*=.95}for(var f=0,o=0;o<t.sfbmax;o++){var c,u=t.width[o];if(f+=u,!(a[o]<_)){for(0!=(2&i.substep_shaping)&&me(),t.scalefac[o]++,c=-u;c<0;c++)s[f+c]*=r,s[f+c]>t.xrpow_max&&(t.xrpow_max=s[f+c]);if(2==i.noise_shaping_amp)return}}}(e,t,a,s);var i=_(t);return!(i||(i=2==r.mode_gr?w.scale_bitcount(t):w.scale_bitcount_lsf(r,t))&&(1<r.noise_shaping&&(we.fill(r.pseudohalf,0),0==t.scalefac_scale?(function(e,t){for(var a=0,s=0;s<e.sfbmax;s++){var n=e.width[s],r=e.scalefac[s];if(0!=e.preflag&&(r+=g.pretab[s]),a+=n,0!=(1&r)){r++;for(var i=-n;i<0;i++)t[a+i]*=1.2968395546510096,t[a+i]>e.xrpow_max&&(e.xrpow_max=t[a+i])}e.scalefac[s]=r>>1}e.preflag=0,e.scalefac_scale=1}(t,s),i=!1):t.block_type==Ae.SHORT_TYPE&&0<r.subblock_gain&&(i=function(e,t,a){var s,n=t.scalefac;for(s=0;s<t.sfb_lmax;s++)if(16<=n[s])return!0;for(var r=0;r<3;r++){var i=0,_=0;for(s=t.sfb_lmax+r;s<t.sfbdivide;s+=3)i<n[s]&&(i=n[s]);for(;s<t.sfbmax;s+=3)_<n[s]&&(_=n[s]);if(!(i<16&&_<8)){if(7<=t.subblock_gain[r])return!0;t.subblock_gain[r]++;var o=e.scalefac_band.l[t.sfb_lmax];for(s=t.sfb_lmax+r;s<t.sfbmax;s+=3){var l=t.width[s],f=n[s];if(0<=(f-=4>>t.scalefac_scale))n[s]=f,o+=3*l;else{n[s]=0;var c=210+(f<<t.scalefac_scale+1);h=g.IPOW20(c),o+=l*(r+1);for(var u=-l;u<0;u++)a[o+u]*=h,a[o+u]>t.xrpow_max&&(t.xrpow_max=a[o+u]);o+=l*(3-r-1)}}var h=g.IPOW20(202);o+=t.width[s]*(r+1);for(var u=-t.width[s];u<0;u++)a[o+u]*=h,a[o+u]>t.xrpow_max&&(t.xrpow_max=a[o+u])}}return!1}(r,t,s)||_(t))),i||(i=2==r.mode_gr?w.scale_bitcount(t):w.scale_bitcount_lsf(r,t)),i))}this.setModules=function(e,t,a,s){r=t,this.rv=t,g=a,this.qupvt=a,w=s,n.setModules(g,w)},this.init_xrpow=function(e,t,a){var s=0,n=0|t.max_nonzero_coeff;if(t.xrpow_max=0,we.fill(a,n,576,0),1e-20<(s=function(e,t,a,s){for(var n=s=0;n<=a;++n){var r=Math.abs(e.xr[n]);s+=r,t[n]=Math.sqrt(r*Math.sqrt(r)),t[n]>e.xrpow_max&&(e.xrpow_max=t[n])}return s}(t,a,n,s))){var r=0;0!=(2&e.substep_shaping)&&(r=1);for(var i=0;i<t.psymax;i++)e.pseudohalf[i]=r;return!0}return we.fill(t.l3_enc,0,576,0),!1},this.init_outer_loop=function(e,t){t.part2_3_length=0,t.big_values=0,t.count1=0,t.global_gain=210,t.scalefac_compress=0,t.table_select[0]=0,t.table_select[1]=0,t.table_select[2]=0,t.subblock_gain[0]=0,t.subblock_gain[1]=0,t.subblock_gain[2]=0,t.subblock_gain[3]=0,t.region0_count=0,t.region1_count=0,t.preflag=0,t.scalefac_scale=0,t.count1table_select=0,t.part2_length=0,t.sfb_lmax=Ae.SBPSY_l,t.sfb_smin=Ae.SBPSY_s,t.psy_lmax=e.sfb21_extra?Ae.SBMAX_l:Ae.SBPSY_l,t.psymax=t.psy_lmax,t.sfbmax=t.sfb_lmax,t.sfbdivide=11;for(var a=0;a<Ae.SBMAX_l;a++)t.width[a]=e.scalefac_band.l[a+1]-e.scalefac_band.l[a],t.window[a]=3;if(t.block_type==Ae.SHORT_TYPE){var s=de(576);t.sfb_smin=0,(t.sfb_lmax=0)!=t.mixed_block_flag&&me(),t.psymax=t.sfb_lmax+3*((e.sfb21_extra?Ae.SBMAX_s:Ae.SBPSY_s)-t.sfb_smin),t.sfbmax=t.sfb_lmax+3*(Ae.SBPSY_s-t.sfb_smin),t.sfbdivide=t.sfbmax-18,t.psy_lmax=t.sfb_lmax;var n=e.scalefac_band.l[t.sfb_lmax];P.arraycopy(t.xr,0,s,0,576);for(var a=t.sfb_smin;a<Ae.SBMAX_s;a++)for(var r=e.scalefac_band.s[a],i=e.scalefac_band.s[a+1],_=0;_<3;_++)for(var o=r;o<i;o++)t.xr[n++]=s[3*o+_];for(var l=t.sfb_lmax,a=t.sfb_smin;a<Ae.SBMAX_s;a++)t.width[l]=t.width[l+1]=t.width[l+2]=e.scalefac_band.s[a+1]-e.scalefac_band.s[a],t.window[l]=0,t.window[l+1]=1,t.window[l+2]=2,l+=3}t.count1bits=0,t.sfb_partition_table=g.nr_of_sfb_block[0][0],t.slen[0]=0,t.slen[1]=0,t.slen[2]=0,t.slen[3]=0,t.max_nonzero_coeff=575,we.fill(t.scalefac,0),function(e,t){var a=e.ATH,s=t.xr;if(t.block_type!=Ae.SHORT_TYPE)for(var n=!1,r=Ae.PSFB21-1;0<=r&&!n;r--){var i=e.scalefac_band.psfb21[r],_=e.scalefac_band.psfb21[r+1],o=g.athAdjust(a.adjust,a.psfb21[r],a.floor);1e-12<e.nsPsy.longfact[21]&&(o*=e.nsPsy.longfact[21]);for(var l=_-1;i<=l;l--){if(!(Math.abs(s[l])<o)){n=!0;break}s[l]=0}}else for(var f=0;f<3;f++)for(var n=!1,r=Ae.PSFB12-1;0<=r&&!n;r--){var i=3*e.scalefac_band.s[12]+(e.scalefac_band.s[13]-e.scalefac_band.s[12])*f+(e.scalefac_band.psfb12[r]-e.scalefac_band.psfb12[0]),_=i+(e.scalefac_band.psfb12[r+1]-e.scalefac_band.psfb12[r]),c=g.athAdjust(a.adjust,a.psfb12[r],a.floor);1e-12<e.nsPsy.shortfact[12]&&(c*=e.nsPsy.shortfact[12]);for(var l=_-1;i<=l;l--){if(!(Math.abs(s[l])<c)){n=!0;break}s[l]=0}}}(e,t)},S.BINSEARCH_NONE=new S(0),S.BINSEARCH_UP=new S(1),S.BINSEARCH_DOWN=new S(2),this.outer_loop=function(e,t,a,s,n,r){var i=e.internal_flags,_=new y,o=de(576),l=de(C.SFBMAX),f=new T,c=new function(){this.global_gain=0,this.sfb_count1=0,this.step=ve(39),this.noise=de(39),this.noise_log=de(39)},u=9999999,h=!1;if(function(e,t,a,s,n){var r,i=e.CurrentStep[s],_=!1,o=e.OldValue[s],l=S.BINSEARCH_NONE;for(t.global_gain=o,a-=t.part2_length;;){var f;if(r=w.count_bits(e,n,t,null),1==i||r==a)break;a<r?(l==S.BINSEARCH_DOWN&&(_=!0),_&&(i/=2),l=S.BINSEARCH_UP,f=i):(l==S.BINSEARCH_UP&&(_=!0),_&&(i/=2),l=S.BINSEARCH_DOWN,f=-i),t.global_gain+=f,t.global_gain<0&&me(),255<t.global_gain&&me()}for(;a<r&&t.global_gain<255;)t.global_gain++,r=w.count_bits(e,n,t,null);e.CurrentStep[s]=4<=o-t.global_gain?4:2,e.OldValue[s]=t.global_gain,t.part2_3_length=r}(i,t,r,n,s),0==i.noise_shaping)return 100;g.calc_noise(t,a,l,f,c),f.bits=t.part2_3_length,_.assign(t);var b=0;for(P.arraycopy(s,0,o,0,576);!h;){do{var p,m=new T,v=255;if(p=0!=(2&i.substep_shaping)?20:3,i.sfb21_extra&&me(),!B(e,_,l,s))break;0!=_.scalefac_scale&&(v=254);var d=r-_.part2_length;if(d<=0)break;for(;(_.part2_3_length=w.count_bits(i,s,_,c))>d&&_.global_gain<=v;)_.global_gain++;if(_.global_gain>v)break;if(0==f.over_count){for(;(_.part2_3_length=w.count_bits(i,s,_,c))>u&&_.global_gain<=v;)_.global_gain++;if(_.global_gain>v)break}if(g.calc_noise(_,a,l,m,c),m.bits=_.part2_3_length,0!=(M(t.block_type!=Ae.SHORT_TYPE?e.quant_comp:e.quant_comp_short,f,m)?1:0))u=t.part2_3_length,f=m,t.assign(_),b=0,P.arraycopy(s,0,o,0,576);else if(0==i.full_outer_loop){if(++b>p&&0==f.over_count)break;i.noise_shaping_amp,i.noise_shaping_amp}}while(_.global_gain+_.scalefac_scale<255);3==i.noise_shaping_amp?me():h=!0}return e.VBR==Me.vbr_rh||e.VBR==Me.vbr_mtrh?P.arraycopy(o,0,s,0,576):0!=(1&i.substep_shaping)&&me(),f.over_count},this.iteration_finish_one=function(e,t,a){var s=e.l3_side,n=s.tt[t][a];w.best_scalefac_store(e,t,a,s),1==e.use_best_huffman&&w.best_huffman_divide(e,n),r.ResvAdjust(e,n)}}function I(){this.thm=new i,this.en=new i}function Ae(){var R=Ae.MPG_MD_MS_LR,k=null,T=this.psy=null,y=null;this.setModules=function(e,t,a,s){k=e,this.psy=t,T=t,y=s};var x=new function(){var u=[-.1482523854003001,32.308141959636465,296.40344946382766,883.1344870032432,11113.947376231741,1057.2713659324597,305.7402417275812,30.825928907280012,3.8533188138216365,59.42900443849514,709.5899960123345,5281.91112291017,-5829.66483675846,-817.6293103748613,-76.91656988279972,-4.594269939176596,.9063471690191471,.1960342806591213,-.15466694054279598,34.324387823855965,301.8067566458425,817.599602898885,11573.795901679885,1181.2520595540152,321.59731579894424,31.232021761053772,3.7107095756221318,53.650946155329365,684.167428119626,5224.56624370173,-6366.391851890084,-908.9766368219582,-89.83068876699639,-5.411397422890401,.8206787908286602,.3901806440322567,-.16070888947830023,36.147034243915876,304.11815768187864,732.7429163887613,11989.60988270091,1300.012278487897,335.28490093152146,31.48816102859945,3.373875931311736,47.232241542899175,652.7371796173471,5132.414255594984,-6909.087078780055,-1001.9990371107289,-103.62185754286375,-6.104916304710272,.7416505462720353,.5805693545089249,-.16636367662261495,37.751650073343995,303.01103387567713,627.9747488785183,12358.763425278165,1412.2779918482834,346.7496836825721,31.598286663170416,3.1598635433980946,40.57878626349686,616.1671130880391,5007.833007176154,-7454.040671756168,-1095.7960341867115,-118.24411666465777,-6.818469345853504,.6681786379192989,.7653668647301797,-.1716176790982088,39.11551877123304,298.3413246578966,503.5259106886539,12679.589408408976,1516.5821921214542,355.9850766329023,31.395241710249053,2.9164211881972335,33.79716964664243,574.8943997801362,4853.234992253242,-7997.57021486075,-1189.7624067269965,-133.6444792601766,-7.7202770609839915,.5993769336819237,.9427934736519954,-.17645823955292173,40.21879108166477,289.9982036694474,359.3226160751053,12950.259102786438,1612.1013903507662,362.85067106591504,31.045922092242872,2.822222032597987,26.988862316190684,529.8996541764288,4671.371946949588,-8535.899136645805,-1282.5898586244496,-149.58553632943463,-8.643494270763135,.5345111359507916,1.111140466039205,-.36174739330527045,41.04429910497807,277.5463268268618,195.6386023135583,13169.43812144731,1697.6433561479398,367.40983966190305,30.557037410382826,2.531473372857427,20.070154905927314,481.50208566532336,4464.970341588308,-9065.36882077239,-1373.62841526722,-166.1660487028118,-9.58289321133207,.4729647758913199,1.268786568327291,-.36970682634889585,41.393213350082036,261.2935935556502,12.935476055240873,13336.131683328815,1772.508612059496,369.76534388639965,29.751323653701338,2.4023193045459172,13.304795348228817,430.5615775526625,4237.0568611071185,-9581.931701634761,-1461.6913552409758,-183.12733958476446,-10.718010163869403,.41421356237309503,1.414213562373095,-.37677560326535325,41.619486213528496,241.05423794991074,-187.94665032361226,13450.063605744153,1836.153896465782,369.4908799925761,29.001847876923147,2.0714759319987186,6.779591200894186,377.7767837205709,3990.386575512536,-10081.709459700915,-1545.947424837898,-200.3762958015653,-11.864482073055006,.3578057213145241,1.546020906725474,-.3829366947518991,41.1516456456653,216.47684307105183,-406.1569483347166,13511.136535077321,1887.8076599260432,367.3025214564151,28.136213436723654,1.913880671464418,.3829366947518991,323.85365704338597,3728.1472257487526,-10561.233882199509,-1625.2025997821418,-217.62525175416,-13.015432208941645,.3033466836073424,1.66293922460509,-.5822628872992417,40.35639251440489,188.20071124269245,-640.2706748618148,13519.21490106562,1927.6022433578062,362.8197642637487,26.968821921868447,1.7463817695935329,-5.62650678237171,269.3016715297017,3453.386536448852,-11016.145278780888,-1698.6569643425091,-234.7658734267683,-14.16351421663124,.2504869601913055,1.76384252869671,-.5887180101749253,39.23429103868072,155.76096234403798,-889.2492977967378,13475.470561874661,1955.0535223723712,356.4450994756727,25.894952980042156,1.5695032905781554,-11.181939564328772,214.80884394039484,3169.1640829158237,-11443.321309975563,-1765.1588461316153,-251.68908574481912,-15.49755935939164,.198912367379658,1.847759065022573,-.7912582233652842,37.39369355329111,119.699486012458,-1151.0956593239027,13380.446257078214,1970.3952110853447,348.01959814116185,24.731487364283044,1.3850130831637748,-16.421408865300393,161.05030052864092,2878.3322807850063,-11838.991423510031,-1823.985884688674,-268.2854986386903,-16.81724543849939,.1483359875383474,1.913880671464418,-.7960642926861912,35.2322109610459,80.01928065061526,-1424.0212633405113,13235.794061869668,1973.804052543835,337.9908651258184,23.289159354463873,1.3934255946442087,-21.099669467133474,108.48348407242611,2583.700758091299,-12199.726194855148,-1874.2780658979746,-284.2467154529415,-18.11369784385905,.09849140335716425,1.961570560806461,-.998795456205172,32.56307803611191,36.958364584370486,-1706.075448829146,13043.287458812016,1965.3831106103316,326.43182772364605,22.175018750622293,1.198638339011324,-25.371248002043963,57.53505923036915,2288.41886619975,-12522.674544337233,-1914.8400385312243,-299.26241273417224,-19.37805630698734,.04912684976946725,1.990369453344394,.035780907*D.SQRT2*.5/2384e-9,.017876148*D.SQRT2*.5/2384e-9,.003134727*D.SQRT2*.5/2384e-9,.002457142*D.SQRT2*.5/2384e-9,971317e-9*D.SQRT2*.5/2384e-9,218868e-9*D.SQRT2*.5/2384e-9,101566e-9*D.SQRT2*.5/2384e-9,13828e-9*D.SQRT2*.5/2384e-9,12804.797818791945,1945.5515939597317,313.4244966442953,49591e-9/2384e-9,1995.1556208053692,21458e-9/2384e-9,-69618e-9/2384e-9],A=[[2.382191739347913e-13,6.423305872147834e-13,9.400849094049688e-13,1.122435026096556e-12,1.183840321267481e-12,1.122435026096556e-12,9.40084909404969e-13,6.423305872147839e-13,2.382191739347918e-13,5.456116108943412e-12,4.878985199565852e-12,4.240448995017367e-12,3.559909094758252e-12,2.858043359288075e-12,2.156177623817898e-12,1.475637723558783e-12,8.371015190102974e-13,2.599706096327376e-13,-5.456116108943412e-12,-4.878985199565852e-12,-4.240448995017367e-12,-3.559909094758252e-12,-2.858043359288076e-12,-2.156177623817898e-12,-1.475637723558783e-12,-8.371015190102975e-13,-2.599706096327376e-13,-2.382191739347923e-13,-6.423305872147843e-13,-9.400849094049696e-13,-1.122435026096556e-12,-1.183840321267481e-12,-1.122435026096556e-12,-9.400849094049694e-13,-6.42330587214784e-13,-2.382191739347918e-13],[2.382191739347913e-13,6.423305872147834e-13,9.400849094049688e-13,1.122435026096556e-12,1.183840321267481e-12,1.122435026096556e-12,9.400849094049688e-13,6.423305872147841e-13,2.382191739347918e-13,5.456116108943413e-12,4.878985199565852e-12,4.240448995017367e-12,3.559909094758253e-12,2.858043359288075e-12,2.156177623817898e-12,1.475637723558782e-12,8.371015190102975e-13,2.599706096327376e-13,-5.461314069809755e-12,-4.921085770524055e-12,-4.343405037091838e-12,-3.732668368707687e-12,-3.093523840190885e-12,-2.430835727329465e-12,-1.734679010007751e-12,-9.74825365660928e-13,-2.797435120168326e-13,0,0,0,0,0,0,-2.283748241799531e-13,-4.037858874020686e-13,-2.146547464825323e-13],[.1316524975873958,.414213562373095,.7673269879789602,1.091308501069271,1.303225372841206,1.56968557711749,1.920982126971166,2.414213562373094,3.171594802363212,4.510708503662055,7.595754112725146,22.90376554843115,.984807753012208,.6427876096865394,.3420201433256688,.9396926207859084,-.1736481776669303,-.7660444431189779,.8660254037844387,.5,-.5144957554275265,-.4717319685649723,-.3133774542039019,-.1819131996109812,-.09457419252642064,-.04096558288530405,-.01419856857247115,-.003699974673760037,.8574929257125442,.8817419973177052,.9496286491027329,.9833145924917901,.9955178160675857,.9991605581781475,.999899195244447,.9999931550702802],[0,0,0,0,0,0,2.283748241799531e-13,4.037858874020686e-13,2.146547464825323e-13,5.461314069809755e-12,4.921085770524055e-12,4.343405037091838e-12,3.732668368707687e-12,3.093523840190885e-12,2.430835727329466e-12,1.734679010007751e-12,9.74825365660928e-13,2.797435120168326e-13,-5.456116108943413e-12,-4.878985199565852e-12,-4.240448995017367e-12,-3.559909094758253e-12,-2.858043359288075e-12,-2.156177623817898e-12,-1.475637723558782e-12,-8.371015190102975e-13,-2.599706096327376e-13,-2.382191739347913e-13,-6.423305872147834e-13,-9.400849094049688e-13,-1.122435026096556e-12,-1.183840321267481e-12,-1.122435026096556e-12,-9.400849094049688e-13,-6.423305872147841e-13,-2.382191739347918e-13]],R=A[Ae.SHORT_TYPE],S=A[Ae.SHORT_TYPE],k=A[Ae.SHORT_TYPE],T=A[Ae.SHORT_TYPE],y=[0,1,16,17,8,9,24,25,4,5,20,21,12,13,28,29,2,3,18,19,10,11,26,27,6,7,22,23,14,15,30,31];function x(e,t,a){for(var s,n,r,i=10,_=t+238-14-286,o=-15;o<0;o++){var l,f,c;l=u[i+-10],f=e[_+-224]*l,c=e[t+224]*l,l=u[i+-9],f+=e[_+-160]*l,c+=e[t+160]*l,l=u[i+-8],f+=e[_+-96]*l,c+=e[t+96]*l,l=u[i+-7],f+=e[_+-32]*l,c+=e[t+32]*l,l=u[i+-6],f+=e[_+32]*l,c+=e[t+-32]*l,l=u[i+-5],f+=e[_+96]*l,c+=e[t+-96]*l,l=u[i+-4],f+=e[_+160]*l,c+=e[t+-160]*l,l=u[i+-3],f+=e[_+224]*l,c+=e[t+-224]*l,l=u[i+-2],f+=e[t+-256]*l,c-=e[_+256]*l,l=u[i+-1],f+=e[t+-192]*l,c-=e[_+192]*l,l=u[i+0],f+=e[t+-128]*l,c-=e[_+128]*l,l=u[i+1],f+=e[t+-64]*l,c-=e[_+64]*l,l=u[i+2],f+=e[t+0]*l,c-=e[_+0]*l,l=u[i+3],f+=e[t+64]*l,c-=e[_+-64]*l,l=u[i+4],f+=e[t+128]*l,c-=e[_+-128]*l,l=u[i+5],f+=e[t+192]*l,c-=e[_+-192]*l,f*=u[i+6],l=c-f,a[30+2*o]=c+f,a[31+2*o]=u[i+7]*l,i+=18,t--,_++}c=e[t+-16]*u[i+-10],f=e[t+-32]*u[i+-2],c+=(e[t+-48]-e[t+16])*u[i+-9],f+=e[t+-96]*u[i+-1],c+=(e[t+-80]+e[t+48])*u[i+-8],f+=e[t+-160]*u[i+0],c+=(e[t+-112]-e[t+80])*u[i+-7],f+=e[t+-224]*u[i+1],c+=(e[t+-144]+e[t+112])*u[i+-6],f-=e[t+32]*u[i+2],c+=(e[t+-176]-e[t+144])*u[i+-5],f-=e[t+96]*u[i+3],c+=(e[t+-208]+e[t+176])*u[i+-4],f-=e[t+160]*u[i+4],c+=(e[t+-240]-e[t+208])*u[i+-3],f-=e[t+224],s=f-c,n=f+c,c=a[14],f=a[15]-c,a[31]=n+c,a[30]=s+f,a[15]=s-f,a[14]=n-c,r=a[28]-a[0],a[0]+=a[28],a[28]=r*u[i+-36+7],r=a[29]-a[1],a[1]+=a[29],a[29]=r*u[i+-36+7],r=a[26]-a[2],a[2]+=a[26],a[26]=r*u[i+-72+7],r=a[27]-a[3],a[3]+=a[27],a[27]=r*u[i+-72+7],r=a[24]-a[4],a[4]+=a[24],a[24]=r*u[i+-108+7],r=a[25]-a[5],a[5]+=a[25],a[25]=r*u[i+-108+7],r=a[22]-a[6],a[6]+=a[22],a[22]=r*D.SQRT2,r=a[23]-a[7],a[7]+=a[23],a[23]=r*D.SQRT2-a[7],a[7]-=a[6],a[22]-=a[7],a[23]-=a[22],r=a[6],a[6]=a[31]-r,a[31]=a[31]+r,r=a[7],a[7]=a[30]-r,a[30]=a[30]+r,r=a[22],a[22]=a[15]-r,a[15]=a[15]+r,r=a[23],a[23]=a[14]-r,a[14]=a[14]+r,r=a[20]-a[8],a[8]+=a[20],a[20]=r*u[i+-180+7],r=a[21]-a[9],a[9]+=a[21],a[21]=r*u[i+-180+7],r=a[18]-a[10],a[10]+=a[18],a[18]=r*u[i+-216+7],r=a[19]-a[11],a[11]+=a[19],a[19]=r*u[i+-216+7],r=a[16]-a[12],a[12]+=a[16],a[16]=r*u[i+-252+7],r=a[17]-a[13],a[13]+=a[17],a[17]=r*u[i+-252+7],r=-a[20]+a[24],a[20]+=a[24],a[24]=r*u[i+-216+7],r=-a[21]+a[25],a[21]+=a[25],a[25]=r*u[i+-216+7],r=a[4]-a[8],a[4]+=a[8],a[8]=r*u[i+-216+7],r=a[5]-a[9],a[5]+=a[9],a[9]=r*u[i+-216+7],r=a[0]-a[12],a[0]+=a[12],a[12]=r*u[i+-72+7],r=a[1]-a[13],a[1]+=a[13],a[13]=r*u[i+-72+7],r=a[16]-a[28],a[16]+=a[28],a[28]=r*u[i+-72+7],r=-a[17]+a[29],a[17]+=a[29],a[29]=r*u[i+-72+7],r=D.SQRT2*(a[2]-a[10]),a[2]+=a[10],a[10]=r,r=D.SQRT2*(a[3]-a[11]),a[3]+=a[11],a[11]=r,r=D.SQRT2*(-a[18]+a[26]),a[18]+=a[26],a[26]=r-a[18],r=D.SQRT2*(-a[19]+a[27]),a[19]+=a[27],a[27]=r-a[19],r=a[2],a[19]-=a[3],a[3]-=r,a[2]=a[31]-r,a[31]+=r,r=a[3],a[11]-=a[19],a[18]-=r,a[3]=a[30]-r,a[30]+=r,r=a[18],a[27]-=a[11],a[19]-=r,a[18]=a[15]-r,a[15]+=r,r=a[19],a[10]-=r,a[19]=a[14]-r,a[14]+=r,r=a[10],a[11]-=r,a[10]=a[23]-r,a[23]+=r,r=a[11],a[26]-=r,a[11]=a[22]-r,a[22]+=r,r=a[26],a[27]-=r,a[26]=a[7]-r,a[7]+=r,r=a[27],a[27]=a[6]-r,a[6]+=r,r=D.SQRT2*(a[0]-a[4]),a[0]+=a[4],a[4]=r,r=D.SQRT2*(a[1]-a[5]),a[1]+=a[5],a[5]=r,r=D.SQRT2*(a[16]-a[20]),a[16]+=a[20],a[20]=r,r=D.SQRT2*(a[17]-a[21]),a[17]+=a[21],a[21]=r,r=-D.SQRT2*(a[8]-a[12]),a[8]+=a[12],a[12]=r-a[8],r=-D.SQRT2*(a[9]-a[13]),a[9]+=a[13],a[13]=r-a[9],r=-D.SQRT2*(a[25]-a[29]),a[25]+=a[29],a[29]=r-a[25],r=-D.SQRT2*(a[24]+a[28]),a[24]-=a[28],a[28]=r-a[24],r=a[24]-a[16],a[24]=r,r=a[20]-r,a[20]=r,r=a[28]-r,a[28]=r,r=a[25]-a[17],a[25]=r,r=a[21]-r,a[21]=r,r=a[29]-r,a[29]=r,r=a[17]-a[1],a[17]=r,r=a[9]-r,a[9]=r,r=a[25]-r,a[25]=r,r=a[5]-r,a[5]=r,r=a[21]-r,a[21]=r,r=a[13]-r,a[13]=r,r=a[29]-r,a[29]=r,r=a[1]-a[0],a[1]=r,r=a[16]-r,a[16]=r,r=a[17]-r,a[17]=r,r=a[8]-r,a[8]=r,r=a[9]-r,a[9]=r,r=a[24]-r,a[24]=r,r=a[25]-r,a[25]=r,r=a[4]-r,a[4]=r,r=a[5]-r,a[5]=r,r=a[20]-r,a[20]=r,r=a[21]-r,a[21]=r,r=a[12]-r,a[12]=r,r=a[13]-r,a[13]=r,r=a[28]-r,a[28]=r,r=a[29]-r,a[29]=r,r=a[0],a[0]+=a[31],a[31]-=r,r=a[1],a[1]+=a[30],a[30]-=r,r=a[16],a[16]+=a[15],a[15]-=r,r=a[17],a[17]+=a[14],a[14]-=r,r=a[8],a[8]+=a[23],a[23]-=r,r=a[9],a[9]+=a[22],a[22]-=r,r=a[24],a[24]+=a[7],a[7]-=r,r=a[25],a[25]+=a[6],a[6]-=r,r=a[4],a[4]+=a[27],a[27]-=r,r=a[5],a[5]+=a[26],a[26]-=r,r=a[20],a[20]+=a[11],a[11]-=r,r=a[21],a[21]+=a[10],a[10]-=r,r=a[12],a[12]+=a[19],a[19]-=r,r=a[13],a[13]+=a[18],a[18]-=r,r=a[28],a[28]+=a[3],a[3]-=r,r=a[29],a[29]+=a[2],a[2]-=r}function E(e,t){for(var a=0;a<3;a++){var s,n,r,i,_,o;i=e[t+6]*A[Ae.SHORT_TYPE][0]-e[t+15],s=e[t+0]*A[Ae.SHORT_TYPE][2]-e[t+9],n=i+s,r=i-s,i=e[t+15]*A[Ae.SHORT_TYPE][0]+e[t+6],s=e[t+9]*A[Ae.SHORT_TYPE][2]+e[t+0],_=i+s,o=-i+s,s=2.069978111953089e-11*(e[t+3]*A[Ae.SHORT_TYPE][1]-e[t+12]),i=2.069978111953089e-11*(e[t+12]*A[Ae.SHORT_TYPE][1]+e[t+3]),e[t+0]=1.90752519173728e-11*n+s,e[t+15]=1.90752519173728e-11*-_+i,r=.8660254037844387*r*1.907525191737281e-11,_=.5*_*1.907525191737281e-11+i,e[t+3]=r-_,e[t+6]=r+_,n=.5*n*1.907525191737281e-11-s,o=.8660254037844387*o*1.907525191737281e-11,e[t+9]=n+o,e[t+12]=n-o,t++}}function H(e,t,a){var s,n,r,i,_,o,l,f,c,u,h,b,p,m,v,d,g,w;r=a[17]-a[9],_=a[15]-a[11],o=a[14]-a[12],l=a[0]+a[8],f=a[1]+a[7],c=a[2]+a[6],u=a[3]+a[5],e[t+17]=l+c-u-(f-a[4]),n=(l+c-u)*S[19]+(f-a[4]),s=(r-_-o)*S[18],e[t+5]=s+n,e[t+6]=s-n,i=(a[16]-a[10])*S[18],f=f*S[19]+a[4],s=r*S[12]+i+_*S[13]+o*S[14],n=-l*S[16]+f-c*S[17]+u*S[15],e[t+1]=s+n,e[t+2]=s-n,s=r*S[13]-i-_*S[14]+o*S[12],n=-l*S[17]+f-c*S[15]+u*S[16],e[t+9]=s+n,e[t+10]=s-n,s=r*S[14]-i+_*S[12]-o*S[13],n=l*S[15]-f+c*S[16]-u*S[17],e[t+13]=s+n,e[t+14]=s-n,h=a[8]-a[0],p=a[6]-a[2],m=a[5]-a[3],v=a[17]+a[9],d=a[16]+a[10],g=a[15]+a[11],w=a[14]+a[12],e[t+0]=v+g+w+(d+a[13]),s=(v+g+w)*S[19]-(d+a[13]),n=(h-p+m)*S[18],e[t+11]=s+n,e[t+12]=s-n,b=(a[7]-a[1])*S[18],d=a[13]-d*S[19],s=v*S[15]-d+g*S[16]+w*S[17],n=h*S[14]+b+p*S[12]+m*S[13],e[t+3]=s+n,e[t+4]=s-n,s=-v*S[17]+d-g*S[15]-w*S[16],n=h*S[13]+b-p*S[14]-m*S[12],e[t+7]=s+n,e[t+8]=s-n,s=-v*S[16]+d-g*S[17]-w*S[15],n=h*S[12]-b+p*S[13]-m*S[14],e[t+15]=s+n,e[t+16]=s-n}this.mdct_sub48=function(e,t,a){for(var s=t,n=286,r=0;r<e.channels_out;r++){for(var i=0;i<e.mode_gr;i++){for(var _,o=e.l3_side.tt[i][r],l=o.xr,f=0,c=e.sb_sample[r][1-i],u=0,h=0;h<9;h++)for(x(s,n,c[u]),x(s,n+32,c[u+1]),u+=2,n+=64,_=1;_<32;_+=2)c[u-1][_]*=-1;for(_=0;_<32;_++,f+=18){var b=o.block_type,p=e.sb_sample[r][i],m=e.sb_sample[r][1-i];if(0!=o.mixed_block_flag&&_<2&&(b=0),e.amp_filter[_]<1e-12)we.fill(l,f+0,f+18,0);else if(e.amp_filter[_]<1&&me(),b==Ae.SHORT_TYPE){for(var h=-3;h<0;h++){var v=A[Ae.SHORT_TYPE][h+3];l[f+3*h+9]=p[9+h][y[_]]*v-p[8-h][y[_]],l[f+3*h+18]=p[14-h][y[_]]*v+p[15+h][y[_]],l[f+3*h+10]=p[15+h][y[_]]*v-p[14-h][y[_]],l[f+3*h+19]=m[2-h][y[_]]*v+m[3+h][y[_]],l[f+3*h+11]=m[3+h][y[_]]*v-m[2-h][y[_]],l[f+3*h+20]=m[8-h][y[_]]*v+m[9+h][y[_]]}E(l,f)}else{for(var d=de(18),h=-9;h<0;h++){var g,w;g=A[b][h+27]*m[h+9][y[_]]+A[b][h+36]*m[8-h][y[_]],w=A[b][h+9]*p[h+9][y[_]]-A[b][h+18]*p[8-h][y[_]],d[h+9]=g-w*R[3+h+9],d[h+18]=g*R[3+h+9]+w}H(l,f,d)}if(b!=Ae.SHORT_TYPE&&0!=_)for(var h=7;0<=h;--h){var S,M;S=l[f+h]*k[20+h]+l[f+-1-h]*T[28+h],M=l[f+h]*T[28+h]-l[f+-1-h]*k[20+h],l[f+-1-h]=S,l[f+h]=M}}}if(s=a,n=286,1==e.mode_gr)for(var B=0;B<18;B++)P.arraycopy(e.sb_sample[r][1][B],0,e.sb_sample[r][0][B],0,32)}}};this.lame_encode_mp3_frame=function(e,t,a,s,n,r){var i,_=E([2,2]);_[0][0]=new I,_[0][1]=new I,_[1][0]=new I,_[1][1]=new I;var o,l=E([2,2]);l[0][0]=new I,l[0][1]=new I,l[1][0]=new I,l[1][1]=new I;var f,c,u,h=[null,null],b=e.internal_flags,p=ge([2,4]),m=[[0,0],[0,0]],v=[[0,0],[0,0]];if(h[0]=t,h[1]=a,0==b.lame_encode_frame_init&&function(e,t){var a,s,n=e.internal_flags;if(0==n.lame_encode_frame_init){var r,i,_=de(2014),o=de(2014);for(n.lame_encode_frame_init=1,i=r=0;r<286+576*(1+n.mode_gr);++r)r<576*n.mode_gr?(_[r]=0,2==n.channels_out&&(o[r]=0)):(_[r]=t[0][i],2==n.channels_out&&(o[r]=t[1][i]),++i);for(s=0;s<n.mode_gr;s++)for(a=0;a<n.channels_out;a++)n.l3_side.tt[s][a].block_type=Ae.SHORT_TYPE;x.mdct_sub48(n,_,o)}}(e,h),b.padding=0,(b.slot_lag-=b.frac_SpF)<0&&(b.slot_lag+=e.out_samplerate,b.padding=1),0!=b.psymodel){var d,g=[null,null],w=0,S=ve(2);for(u=0;u<b.mode_gr;u++){for(c=0;c<b.channels_out;c++)g[c]=h[c],w=576+576*u-Ae.FFTOFFSET;if(e.VBR==Me.vbr_mtrh||e.VBR==Me.vbr_mt?me():d=T.L3psycho_anal_ns(e,g,w,u,_,l,m[u],v[u],p[u],S),0!=d)return-4;for(e.mode==Be.JOINT_STEREO&&me(),c=0;c<b.channels_out;c++){var M=b.l3_side.tt[u][c];M.block_type=S[c],M.mixed_block_flag=0}}}else me();if(function(e){var t,a;if(0==e.ATH.useAdjust)return e.ATH.adjust=1;if(a=e.loudness_sq[0][0],t=e.loudness_sq[1][0],2==e.channels_out?me():(a+=a,t+=t),2==e.mode_gr&&(a=Math.max(a,t)),a*=.5,.03125<(a*=e.ATH.aaSensitivityP))1<=e.ATH.adjust?e.ATH.adjust=1:e.ATH.adjust<e.ATH.adjustLimit&&(e.ATH.adjust=e.ATH.adjustLimit),e.ATH.adjustLimit=1;else{var s=31.98*a+625e-6;e.ATH.adjust>=s?(e.ATH.adjust*=.075*s+.925,e.ATH.adjust<s&&(e.ATH.adjust=s)):e.ATH.adjustLimit>=s?e.ATH.adjust=s:e.ATH.adjust<e.ATH.adjustLimit&&(e.ATH.adjust=e.ATH.adjustLimit),e.ATH.adjustLimit=s}}(b),x.mdct_sub48(b,h[0],h[1]),b.mode_ext=Ae.MPG_MD_LR_LR,e.force_ms?b.mode_ext=Ae.MPG_MD_MS_LR:e.mode==Be.JOINT_STEREO&&me(),b.mode_ext==R?(o=l,f=v):(o=_,f=m),e.analysis&&null!=b.pinfo&&me(),e.VBR==Me.vbr_off||e.VBR==Me.vbr_abr){var B,A;for(B=0;B<18;B++)b.nsPsy.pefirbuf[B]=b.nsPsy.pefirbuf[B+1];for(u=A=0;u<b.mode_gr;u++)for(c=0;c<b.channels_out;c++)A+=f[u][c];for(b.nsPsy.pefirbuf[18]=A,A=b.nsPsy.pefirbuf[9],B=0;B<9;B++)A+=(b.nsPsy.pefirbuf[B]+b.nsPsy.pefirbuf[18-B])*Ae.fircoef[B];for(A=3350*b.mode_gr*b.channels_out/A,u=0;u<b.mode_gr;u++)for(c=0;c<b.channels_out;c++)f[u][c]*=A}return b.iteration_loop.iteration_loop(e,f,[.5,.5],o),k.format_bitstream(e),i=k.copy_buffer(b,s,n,r,1),e.bWriteVbrTag&&y.addVbrFrame(e),e.analysis&&null!=b.pinfo&&me(),function(e){var t,a;for(e.bitrate_stereoMode_Hist[e.bitrate_index][4]++,e.bitrate_stereoMode_Hist[15][4]++,2==e.channels_out&&me(),t=0;t<e.mode_gr;++t)for(a=0;a<e.channels_out;++a){var s=0|e.l3_side.tt[t][a].block_type;0!=e.l3_side.tt[t][a].mixed_block_flag&&(s=4),e.bitrate_blockType_Hist[e.bitrate_index][s]++,e.bitrate_blockType_Hist[e.bitrate_index][5]++,e.bitrate_blockType_Hist[15][s]++,e.bitrate_blockType_Hist[15][5]++}}(b),i}}function i(){this.l=de(Ae.SBMAX_l),this.s=ge([Ae.SBMAX_s,3]);var s=this;this.assign=function(e){P.arraycopy(e.l,0,s.l,0,Ae.SBMAX_l);for(var t=0;t<Ae.SBMAX_s;t++)for(var a=0;a<3;a++)s.s[t][a]=e.s[t][a]}}function X(){var e=40;function t(){this.write_timing=0,this.ptr=0,this.buf=S(e)}this.Class_ID=0,this.lame_encode_frame_init=0,this.iteration_init_init=0,this.fill_buffer_resample_init=0,this.mfbuf=ge([2,X.MFSIZE]),this.mode_gr=0,this.channels_in=0,this.channels_out=0,this.resample_ratio=0,this.mf_samples_to_encode=0,this.mf_size=0,this.VBR_min_bitrate=0,this.VBR_max_bitrate=0,this.bitrate_index=0,this.samplerate_index=0,this.mode_ext=0,this.lowpass1=0,this.lowpass2=0,this.highpass1=0,this.highpass2=0,this.noise_shaping=0,this.noise_shaping_amp=0,this.substep_shaping=0,this.psymodel=0,this.noise_shaping_stop=0,this.subblock_gain=0,this.use_best_huffman=0,this.full_outer_loop=0,this.l3_side=new function(){this.tt=[[null,null],[null,null]],this.main_data_begin=0,this.private_bits=0,this.resvDrain_pre=0,this.resvDrain_post=0,this.scfsi=[ve(4),ve(4)];for(var e=0;e<2;e++)for(var t=0;t<2;t++)this.tt[e][t]=new y},this.ms_ratio=de(2),this.padding=0,this.frac_SpF=0,this.slot_lag=0,this.tag_spec=null,this.nMusicCRC=0,this.OldValue=ve(2),this.CurrentStep=ve(2),this.masking_lower=0,this.bv_scf=ve(576),this.pseudohalf=ve(C.SFBMAX),this.sfb21_extra=!1,this.inbuf_old=new Array(2),this.blackfilt=new Array(2*X.BPC+1),this.itime=s(2),this.sideinfo_len=0,this.sb_sample=ge([2,2,18,Ae.SBLIMIT]),this.amp_filter=de(32),this.header=new Array(X.MAX_HEADER_BUF),this.h_ptr=0,this.w_ptr=0,this.ancillary_flag=0,this.ResvSize=0,this.ResvMax=0,this.scalefac_band=new r,this.minval_l=de(Ae.CBANDS),this.minval_s=de(Ae.CBANDS),this.nb_1=ge([4,Ae.CBANDS]),this.nb_2=ge([4,Ae.CBANDS]),this.nb_s1=ge([4,Ae.CBANDS]),this.nb_s2=ge([4,Ae.CBANDS]),this.s3_ss=null,this.s3_ll=null,this.decay=0,this.thm=new Array(4),this.en=new Array(4),this.tot_ener=de(4),this.loudness_sq=ge([2,2]),this.loudness_sq_save=de(2),this.mld_l=de(Ae.SBMAX_l),this.mld_s=de(Ae.SBMAX_s),this.bm_l=ve(Ae.SBMAX_l),this.bo_l=ve(Ae.SBMAX_l),this.bm_s=ve(Ae.SBMAX_s),this.bo_s=ve(Ae.SBMAX_s),this.npart_l=0,this.npart_s=0,this.s3ind=w([Ae.CBANDS,2]),this.s3ind_s=w([Ae.CBANDS,2]),this.numlines_s=ve(Ae.CBANDS),this.numlines_l=ve(Ae.CBANDS),this.rnumlines_l=de(Ae.CBANDS),this.mld_cb_l=de(Ae.CBANDS),this.mld_cb_s=de(Ae.CBANDS),this.numlines_s_num1=0,this.numlines_l_num1=0,this.pe=de(4),this.ms_ratio_s_old=0,this.ms_ratio_l_old=0,this.ms_ener_ratio_old=0,this.blocktype_old=ve(2),this.nsPsy=new function(){this.last_en_subshort=ge([4,9]),this.lastAttacks=ve(4),this.pefirbuf=de(19),this.longfact=de(Ae.SBMAX_l),this.shortfact=de(Ae.SBMAX_s),this.attackthre=0,this.attackthre_s=0},this.VBR_seek_table=new function(){this.sum=0,this.seen=0,this.want=0,this.pos=0,this.size=0,this.bag=null,this.nVbrNumFrames=0,this.nBytesWritten=0,this.TotalFrameSize=0},this.ATH=null,this.PSY=null,this.nogap_total=0,this.nogap_current=0,this.decode_on_the_fly=!0,this.findReplayGain=!0,this.findPeakSample=!0,this.PeakSample=0,this.RadioGain=0,this.AudiophileGain=0,this.rgdata=null,this.noclipGainChange=0,this.noclipScale=0,this.bitrate_stereoMode_Hist=w([16,5]),this.bitrate_blockType_Hist=w([16,6]),this.pinfo=null,this.hip=null,this.in_buffer_nsamples=0,this.in_buffer_0=null,this.in_buffer_1=null,this.iteration_loop=null;for(var a=0;a<this.en.length;a++)this.en[a]=new i;for(var a=0;a<this.thm.length;a++)this.thm[a]=new i;for(var a=0;a<this.header.length;a++)this.header[a]=new t}function j(){var R=new function(){var h=de(Ae.BLKSIZE),p=de(Ae.BLKSIZE_s/2),T=[.9238795325112867,.3826834323650898,.9951847266721969,.0980171403295606,.9996988186962042,.02454122852291229,.9999811752826011,.006135884649154475];function m(e,t,a){var s,n,r,i=0,_=t+(a<<=1);s=4;do{var o,l,f,c,u,h,b;for(b=s>>1,h=(u=(c=s)<<1)+c,s=u<<1,r=(n=t)+b;S=e[n+0]-e[n+c],w=e[n+0]+e[n+c],R=e[n+u]-e[n+h],B=e[n+u]+e[n+h],e[n+u]=w-B,e[n+0]=w+B,e[n+h]=S-R,e[n+c]=S+R,S=e[r+0]-e[r+c],w=e[r+0]+e[r+c],R=D.SQRT2*e[r+h],B=D.SQRT2*e[r+u],e[r+u]=w-B,e[r+0]=w+B,e[r+h]=S-R,e[r+c]=S+R,r+=s,(n+=s)<_;);for(l=T[i+0],o=T[i+1],f=1;f<b;f++){var p,m;p=1-2*o*o,m=2*o*l,n=t+f,r=t+c-f;do{var v,d,g,w,S,M,B,A,R,k;d=m*e[n+c]-p*e[r+c],v=p*e[n+c]+m*e[r+c],S=e[n+0]-v,w=e[n+0]+v,M=e[r+0]-d,g=e[r+0]+d,d=m*e[n+h]-p*e[r+h],v=p*e[n+h]+m*e[r+h],R=e[n+u]-v,B=e[n+u]+v,k=e[r+u]-d,A=e[r+u]+d,d=o*B-l*k,v=l*B+o*k,e[n+u]=w-v,e[n+0]=w+v,e[r+h]=M-d,e[r+c]=M+d,d=l*A-o*R,v=o*A+l*R,e[r+u]=g-v,e[r+0]=g+v,e[n+h]=S-d,e[n+c]=S+d,r+=s,n+=s}while(n<_);l=(p=l)*T[i+0]-o*T[i+1],o=p*T[i+1]+o*T[i+0]}i+=2}while(s<a)}var v=[0,128,64,192,32,160,96,224,16,144,80,208,48,176,112,240,8,136,72,200,40,168,104,232,24,152,88,216,56,184,120,248,4,132,68,196,36,164,100,228,20,148,84,212,52,180,116,244,12,140,76,204,44,172,108,236,28,156,92,220,60,188,124,252,2,130,66,194,34,162,98,226,18,146,82,210,50,178,114,242,10,138,74,202,42,170,106,234,26,154,90,218,58,186,122,250,6,134,70,198,38,166,102,230,22,150,86,214,54,182,118,246,14,142,78,206,46,174,110,238,30,158,94,222,62,190,126,254];this.fft_short=function(e,t,a,s,n){for(var r=0;r<3;r++){var i=Ae.BLKSIZE_s/2,_=65535&192*(r+1),o=Ae.BLKSIZE_s/8-1;do{var l,f,c,u,h,b=255&v[o<<2];l=p[b]*s[a][n+b+_],h=p[127-b]*s[a][n+b+_+128],f=l-h,l+=h,c=p[b+64]*s[a][n+b+_+64],h=p[63-b]*s[a][n+b+_+192],u=c-h,c+=h,i-=4,t[r][i+0]=l+c,t[r][i+2]=l-c,t[r][i+1]=f+u,t[r][i+3]=f-u,l=p[b+1]*s[a][n+b+_+1],h=p[126-b]*s[a][n+b+_+129],f=l-h,l+=h,c=p[b+65]*s[a][n+b+_+65],h=p[62-b]*s[a][n+b+_+193],u=c-h,c+=h,t[r][i+Ae.BLKSIZE_s/2+0]=l+c,t[r][i+Ae.BLKSIZE_s/2+2]=l-c,t[r][i+Ae.BLKSIZE_s/2+1]=f+u,t[r][i+Ae.BLKSIZE_s/2+3]=f-u}while(0<=--o);m(t[r],i,Ae.BLKSIZE_s/2)}},this.fft_long=function(e,t,a,s,n){var r=Ae.BLKSIZE/8-1,i=Ae.BLKSIZE/2;do{var _,o,l,f,c,u=255&v[r];_=h[u]*s[a][n+u],c=h[u+512]*s[a][n+u+512],o=_-c,_+=c,l=h[u+256]*s[a][n+u+256],c=h[u+768]*s[a][n+u+768],f=l-c,l+=c,t[0+(i-=4)]=_+l,t[i+2]=_-l,t[i+1]=o+f,t[i+3]=o-f,_=h[u+1]*s[a][n+u+1],c=h[u+513]*s[a][n+u+513],o=_-c,_+=c,l=h[u+257]*s[a][n+u+257],c=h[u+769]*s[a][n+u+769],f=l-c,l+=c,t[i+Ae.BLKSIZE/2+0]=_+l,t[i+Ae.BLKSIZE/2+2]=_-l,t[i+Ae.BLKSIZE/2+1]=o+f,t[i+Ae.BLKSIZE/2+3]=o-f}while(0<=--r);m(t,i,Ae.BLKSIZE/2)},this.init_fft=function(e){for(var t=0;t<Ae.BLKSIZE;t++)h[t]=.42-.5*Math.cos(2*Math.PI*(t+.5)/Ae.BLKSIZE)+.08*Math.cos(4*Math.PI*(t+.5)/Ae.BLKSIZE);for(var t=0;t<Ae.BLKSIZE_s/2;t++)p[t]=.5*(1-Math.cos(2*Math.PI*(t+.5)/Ae.BLKSIZE_s))}},k=2.302585092994046,d=2,g=16,E=.34,v=1/217621504/(Ae.BLKSIZE/2),w=.2302585093;function ne(e,t,a,s,n,r,i,_,o,l,f){var c=e.internal_flags;o<2?(R.fft_long(c,s[n],o,l,f),R.fft_short(c,r[i],o,l,f)):2==o&&me(),t[0]=s[n+0][0],t[0]*=t[0];for(var u=Ae.BLKSIZE/2-1;0<=u;--u){var h=s[n+0][Ae.BLKSIZE/2-u],b=s[n+0][Ae.BLKSIZE/2+u];t[Ae.BLKSIZE/2-u]=.5*(h*h+b*b)}for(var p=2;0<=p;--p){a[p][0]=r[i+0][p][0],a[p][0]*=a[p][0];for(var u=Ae.BLKSIZE_s/2-1;0<=u;--u){var h=r[i+0][p][Ae.BLKSIZE_s/2-u],b=r[i+0][p][Ae.BLKSIZE_s/2+u];a[p][Ae.BLKSIZE_s/2-u]=.5*(h*h+b*b)}}for(var m=0,u=11;u<Ae.HBLKSIZE;u++)m+=t[u];c.tot_ener[o]=m,e.analysis&&me(),2==e.athaa_loudapprox&&o<2&&(c.loudness_sq[_][o]=c.loudness_sq_save[o],c.loudness_sq_save[o]=function(e,t){for(var a=0,s=0;s<Ae.BLKSIZE/2;++s)a+=e[s]*t.ATH.eql_w[s];return a*=v}(t,c))}var T,y,x,H=8,P=23,I=15,re=[1,.79433,.63096,.63096,.63096,.63096,.63096,.25119,.11749],f=[3.3246*3.3246,3.23837*3.23837,9.9500500969,9.0247369744,8.1854926609,7.0440875649,2.46209*2.46209,2.284*2.284,4.4892710641,1.96552*1.96552,1.82335*1.82335,1.69146*1.69146,2.4621061921,2.1508568964,1.37074*1.37074,1.31036*1.31036,1.5691069696,1.4555939904,1.16203*1.16203,1.2715945225,1.09428*1.09428,1.0659*1.0659,1.0779838276,1.0382591025,1],c=[1.7782755904,1.35879*1.35879,1.38454*1.38454,1.39497*1.39497,1.40548*1.40548,1.3537*1.3537,1.6999465924,1.22321*1.22321,1.3169398564,1],u=[5.5396212496,2.29259*2.29259,4.9868695969,2.12675*2.12675,2.02545*2.02545,1.87894*1.87894,1.74303*1.74303,1.61695*1.61695,2.2499700001,1.39148*1.39148,1.29083*1.29083,1.19746*1.19746,1.2339655056,1.0779838276];function ie(e,t,a,s,n,r){var i;if(e<t){if(!(t<e*y))return e+t;i=t/e}else{if(t*y<=e)return e+t;i=e/t}if(e+=t,s+3<=6){if(T<=i)return e;var _=0|D.FAST_LOG10_X(i,16);return e*c[_]}var o,l,_=0|D.FAST_LOG10_X(i,16);return t=0!=r?n.ATH.cb_s[a]*n.ATH.adjust:n.ATH.cb_l[a]*n.ATH.adjust,e<x*t?t<e?(o=1,_<=13&&(o=u[_]),l=D.FAST_LOG10_X(e/t,10/15),e*((f[_]-o)*l+o)):13<_?e:e*u[_]:e*f[_]}function _e(e,t,a,s,n){var r,i,_=0,o=0;for(r=i=0;r<Ae.SBMAX_s;++i,++r){for(var l=e.bo_s[r],f=e.npart_s,c=l<f?l:f;i<c;)_+=t[i],o+=a[i],i++;if(e.en[s].s[r][n]=_,e.thm[s].s[r][n]=o,f<=i){++r;break}var u=e.PSY.bo_s_weight[r],h=1-u;_=u*t[i],o=u*a[i],e.en[s].s[r][n]+=_,e.thm[s].s[r][n]+=o,_=h*t[i],o=h*a[i]}for(;r<Ae.SBMAX_s;++r)e.en[s].s[r][n]=0,e.thm[s].s[r][n]=0}function oe(e,t,a,s){var n,r,i=0,_=0;for(n=r=0;n<Ae.SBMAX_l;++r,++n){for(var o=e.bo_l[n],l=e.npart_l,f=o<l?o:l;r<f;)i+=t[r],_+=a[r],r++;if(e.en[s].l[n]=i,e.thm[s].l[n]=_,l<=r){++n;break}var c=e.PSY.bo_l_weight[n],u=1-c;i=c*t[r],_=c*a[r],e.en[s].l[n]+=i,e.thm[s].l[n]+=_,i=u*t[r],_=u*a[r]}for(;n<Ae.SBMAX_l;++n)e.en[s].l[n]=0,e.thm[s].l[n]=0}function le(e,t,a,s,n,r){var i,_,o=e.internal_flags;for(_=i=0;_<o.npart_s;++_){for(var l=0,f=0,c=o.numlines_s[_],u=0;u<c;++u,++i){var h=t[r][i];l+=h,f<h&&(f=h)}a[_]=l}for(i=_=0;_<o.npart_s;_++){var b=o.s3ind_s[_][0],p=o.s3_ss[i++]*a[b];for(++b;b<=o.s3ind_s[_][1];)p+=o.s3_ss[i]*a[b],++i,++b;var m=d*o.nb_s1[n][_];if(s[_]=Math.min(p,m),o.blocktype_old[1&n]==Ae.SHORT_TYPE){var m=g*o.nb_s2[n][_],v=s[_];s[_]=Math.min(m,v)}o.nb_s2[n][_]=o.nb_s1[n][_],o.nb_s1[n][_]=p}for(;_<=Ae.CBANDS;++_)a[_]=0,s[_]=0}function fe(e,t,a){return 1<=a?e:a<=0?t:0<t?Math.pow(e/t,a)*t:0}var o=[11.8,13.6,17.2,32,46.5,51.3,57.5,67.1,71.5,84.6,97.6,130];function ce(e,t){for(var a=309.07,s=0;s<Ae.SBMAX_s-1;s++)for(var n=0;n<3;n++){var r=e.thm.s[s][n];if(0<r){var i=r*t,_=e.en.s[s][n];i<_&&(a+=1e10*i<_?o[s]*(10*k):o[s]*D.FAST_LOG10(_/i))}}return a}var _=[6.8,5.8,5.8,6.4,6.5,9.9,12.1,14.4,15,18.9,21.6,26.9,34.2,40.2,46.8,56.5,60.7,73.9,85.7,93.4,126.1];function ue(e,t){for(var a=281.0575,s=0;s<Ae.SBMAX_l-1;s++){var n=e.thm.l[s];if(0<n){var r=n*t,i=e.en.l[s];r<i&&(a+=1e10*r<i?_[s]*(10*k):_[s]*D.FAST_LOG10(i/r))}}return a}function he(e,t,a,s,n){var r,i;for(r=i=0;r<e.npart_l;++r){var _,o=0,l=0;for(_=0;_<e.numlines_l[r];++_,++i){var f=t[i];o+=f,l<f&&(l=f)}a[r]=o,s[r]=l,n[r]=o*e.rnumlines_l[r]}}function be(e,t,a,s){var n=re.length-1,r=0,i=a[r]+a[r+1];if(0<i){var _=t[r];_<t[r+1]&&(_=t[r+1]);var o=0|(i=20*(2*_-i)/(i*(e.numlines_l[r]+e.numlines_l[r+1]-1)));n<o&&(o=n),s[r]=o}else s[r]=0;for(r=1;r<e.npart_l-1;r++)if(0<(i=a[r-1]+a[r]+a[r+1])){var _=t[r-1];_<t[r]&&(_=t[r]),_<t[r+1]&&(_=t[r+1]);var o=0|(i=20*(3*_-i)/(i*(e.numlines_l[r-1]+e.numlines_l[r]+e.numlines_l[r+1]-1)));n<o&&(o=n),s[r]=o}else s[r]=0;if(0<(i=a[r-1]+a[r])){var _=t[r-1];_<t[r]&&(_=t[r]);var o=0|(i=20*(2*_-i)/(i*(e.numlines_l[r-1]+e.numlines_l[r]-1)));n<o&&(o=n),s[r]=o}else s[r]=0}var pe=[-1.730326e-17,-.01703172,-1.349528e-17,.0418072,-6.73278e-17,-.0876324,-3.0835e-17,.1863476,-1.104424e-16,-.627638];function L(e){return e<0&&(e=0),e*=.001,13*Math.atan(.76*e)+3.5*Math.atan(e*e/56.25)}function O(e,t,a,s,n,r,i,_,o,l,f,c){var u,h=de(Ae.CBANDS+1),b=_/(15<c?1152:384),p=ve(Ae.HBLKSIZE);_/=o;var m=0,v=0;for(u=0;u<Ae.CBANDS;u++){var d;for(T=L(_*m),h[u]=_*m,d=m;L(_*d)-T<E&&d<=o/2;d++);for(e[u]=d-m,v=u+1;m<d;)p[m++]=u;if(o/2<m){m=o/2,++u;break}}h[u]=_*m;for(var g=0;g<c;g++){var w,S,M,B,A;M=l[g],B=l[g+1],(w=0|Math.floor(.5+f*(M-.5)))<0&&(w=0),S=0|Math.floor(.5+f*(B-.5)),o/2<S&&(S=o/2),a[g]=(p[w]+p[S])/2,t[g]=p[S];var R=b*B;i[g]=(R-h[t[g]])/(h[t[g]+1]-h[t[g]]),i[g]<0?i[g]=0:1<i[g]&&(i[g]=1),A=L(_*l[g]*f),A=Math.min(A,15.5)/15.5,r[g]=Math.pow(10,1.25*(1-Math.cos(Math.PI*A))-2.5)}for(var k=m=0;k<v;k++){var T,y,x=e[k];T=L(_*m),y=L(_*(m+x-1)),s[k]=.5*(T+y),T=L(_*(m-.5)),y=L(_*(m+x-.5)),n[k]=y-T,m+=x}return v}function V(e,t,a,s,n,r){var i,_,o,l,f,c,u=ge([Ae.CBANDS,Ae.CBANDS]),h=0;if(r)for(var b=0;b<t;b++)for(i=0;i<t;i++){var p=(_=a[b]-a[i],c=f=l=o=void 0,o=_,l=.5<=(o*=0<=o?3:1.5)&&o<=2.5?8*((c=o-.5)*c-2*c):0,((f=15.811389+7.5*(o+=.474)-17.5*Math.sqrt(1+o*o))<=-60?0:(o=Math.exp((l+f)*w),o/=.6609193))*s[i]);u[b][i]=p*n[b]}else me();for(var b=0;b<t;b++){for(i=0;i<t&&!(0<u[b][i]);i++);for(e[b][0]=i,i=t-1;0<i&&!(0<u[b][i]);i--);e[b][1]=i,h+=e[b][1]-e[b][0]+1}for(var m=de(h),v=0,b=0;b<t;b++)for(i=e[b][0];i<=e[b][1];i++)m[v++]=u[b][i];return m}function N(e){var t=L(e);return t=Math.min(t,15.5)/15.5,Math.pow(10,1.25*(1-Math.cos(Math.PI*t))-2.5)}function s(e,t){e<-.3&&(e=3410),e/=1e3,e=Math.max(.1,e);var a=3.64*Math.pow(e,-.8)-6.8*Math.exp(-.6*Math.pow(e-3.4,2))+6*Math.exp(-.15*Math.pow(e-8.7,2))+.001*(.6+.04*t)*Math.pow(e,4);return a}this.L3psycho_anal_ns=function(e,t,a,s,n,r,i,_,o,l){var f,c,u,h,b,p,m,v,d,g=e.internal_flags,w=ge([2,Ae.BLKSIZE]),S=ge([2,3,Ae.BLKSIZE_s]),M=de(Ae.CBANDS+1),B=de(Ae.CBANDS+1),A=de(Ae.CBANDS+2),R=ve(2),k=ve(2),T=ge([2,576]),y=ve(Ae.CBANDS+2),x=ve(Ae.CBANDS+2);for(we.fill(x,0),f=g.channels_out,e.mode==Be.JOINT_STEREO&&(f=4),d=e.VBR==Me.vbr_off?0==g.ResvMax?0:g.ResvSize/g.ResvMax*.5:e.VBR==Me.vbr_rh||e.VBR==Me.vbr_mtrh||e.VBR==Me.vbr_mt?.6:1,c=0;c<g.channels_out;c++){var E=t[c],H=a+576-350-21+192;for(h=0;h<576;h++){var P,I;for(P=E[H+h+10],b=I=0;b<9;b+=2)P+=pe[b]*(E[H+h+b]+E[H+h+21-b]),I+=pe[b+1]*(E[H+h+b+1]+E[H+h+21-b-1]);T[c][h]=P+I}n[s][c].en.assign(g.en[c]),n[s][c].thm.assign(g.thm[c]),2<f&&me()}for(c=0;c<f;c++){var L,O=de(12),V=[0,0,0,0],N=de(12),D=1,Y=de(Ae.CBANDS),C=de(Ae.CBANDS),X=[0,0,0,0],j=de(Ae.HBLKSIZE),F=ge([3,Ae.HBLKSIZE_s]);for(h=0;h<3;h++)O[h]=g.nsPsy.last_en_subshort[c][h+6],N[h]=O[h]/g.nsPsy.last_en_subshort[c][h+4],V[0]+=O[h];2==c&&me();var q=T[1&c],z=0;for(h=0;h<9;h++){for(var Z=z+64,K=1;z<Z;z++)K<Math.abs(q[z])&&(K=Math.abs(q[z]));g.nsPsy.last_en_subshort[c][h]=O[h+3]=K,V[1+h/3]+=K,K>O[h+3-2]?K/=O[h+3-2]:K=O[h+3-2]>10*K?O[h+3-2]/(10*K):0,N[h+3]=K}for(e.analysis&&me(),L=3==c?g.nsPsy.attackthre_s:g.nsPsy.attackthre,h=0;h<12;h++)0==X[h/3]&&N[h]>L&&(X[h/3]=h%3+1);for(h=1;h<4;h++)(V[h-1]>V[h]?V[h-1]/V[h]:V[h]/V[h-1])<1.7&&(X[h]=0,1==h&&(X[0]=0));for(0!=X[0]&&0!=g.nsPsy.lastAttacks[c]&&(X[0]=0),3!=g.nsPsy.lastAttacks[c]&&X[0]+X[1]+X[2]+X[3]==0||((D=0)!=X[1]&&0!=X[0]&&(X[1]=0),0!=X[2]&&0!=X[1]&&(X[2]=0),0!=X[3]&&0!=X[2]&&(X[3]=0)),c<2?k[c]=D:me(),o[c]=g.tot_ener[c],ne(e,j,F,w,1&c,S,1&c,s,c,t,a),he(g,j,M,Y,C),be(g,Y,C,y),v=0;v<3;v++){var G,Q;for(le(e,F,B,A,c,v),_e(g,B,A,c,v),m=0;m<Ae.SBMAX_s;m++){if(Q=g.thm[c].s[m][v],Q*=.8,2<=X[v]||1==X[v+1]){var U=0!=v?v-1:2,K=fe(g.thm[c].s[m][U],Q,.6*d);Q=Math.min(Q,K)}if(1==X[v]){var U=0!=v?v-1:2,K=fe(g.thm[c].s[m][U],Q,.3*d);Q=Math.min(Q,K)}else if(0!=v&&3==X[v-1]||0==v&&3==g.nsPsy.lastAttacks[c]){var U=2!=v?v+1:0,K=fe(g.thm[c].s[m][U],Q,.3*d);Q=Math.min(Q,K)}G=O[3*v+3]+O[3*v+4]+O[3*v+5],6*O[3*v+5]<G&&(Q*=.5,6*O[3*v+4]<G&&(Q*=.5)),g.thm[c].s[m][v]=Q}}for(g.nsPsy.lastAttacks[c]=X[2],u=p=0;u<g.npart_l;u++){for(var W=g.s3ind[u][0],J=M[W]*re[y[W]],$=g.s3_ll[p++]*J;++W<=g.s3ind[u][1];)J=M[W]*re[y[W]],$=ie($,g.s3_ll[p++]*J,W,W-u,g,0);$*=.158489319246111,g.blocktype_old[1&c]==Ae.SHORT_TYPE?A[u]=$:A[u]=fe(Math.min($,Math.min(2*g.nb_1[c][u],16*g.nb_2[c][u])),$,d),g.nb_2[c][u]=g.nb_1[c][u],g.nb_1[c][u]=$}for(;u<=Ae.CBANDS;++u)M[u]=0,A[u]=0;oe(g,M,A,c)}for(e.mode!=Be.STEREO&&e.mode!=Be.JOINT_STEREO||me(),e.mode==Be.JOINT_STEREO&&me(),function(e,t,a,s){var n=e.internal_flags;e.short_blocks!=Se.short_block_coupled||0!=t[0]&&0!=t[1]||(t[0]=t[1]=0);for(var r=0;r<n.channels_out;r++)s[r]=Ae.NORM_TYPE,e.short_blocks==Se.short_block_dispensed&&(t[r]=1),e.short_blocks==Se.short_block_forced&&(t[r]=0),0!=t[r]?n.blocktype_old[r]==Ae.SHORT_TYPE&&(s[r]=Ae.STOP_TYPE):(s[r]=Ae.SHORT_TYPE,n.blocktype_old[r]==Ae.NORM_TYPE&&(n.blocktype_old[r]=Ae.START_TYPE),n.blocktype_old[r]==Ae.STOP_TYPE&&(n.blocktype_old[r]=Ae.SHORT_TYPE)),a[r]=n.blocktype_old[r],n.blocktype_old[r]=s[r]}(e,k,l,R),c=0;c<f;c++){var ee,te,ae,se=0;1<c?me():(ee=i,se=0,te=l[c],ae=n[s][c]),ee[se+c]=te==Ae.SHORT_TYPE?ce(ae,g.masking_lower):ue(ae,g.masking_lower),e.analysis&&(g.pinfo.pe[s][c]=ee[se+c])}return 0},this.psymodel_init=function(e){var t,a=e.internal_flags,s=!0,n=13,r=0,i=0,_=-8.25,o=-4.5,l=de(Ae.CBANDS),f=de(Ae.CBANDS),c=de(Ae.CBANDS),u=e.out_samplerate;switch(e.experimentalZ){default:case 0:s=!0;break;case 1:s=e.VBR!=Me.vbr_mtrh&&e.VBR!=Me.vbr_mt;break;case 2:s=!1;break;case 3:n=8,r=-1.75,i=-.0125,_=-8.25,o=-2.25}for(a.ms_ener_ratio_old=.25,a.blocktype_old[0]=a.blocktype_old[1]=Ae.NORM_TYPE,t=0;t<4;++t){for(var h=0;h<Ae.CBANDS;++h)a.nb_1[t][h]=1e20,a.nb_2[t][h]=1e20,a.nb_s1[t][h]=a.nb_s2[t][h]=1;for(var b=0;b<Ae.SBMAX_l;b++)a.en[t].l[b]=1e20,a.thm[t].l[b]=1e20;for(var h=0;h<3;++h){for(var b=0;b<Ae.SBMAX_s;b++)a.en[t].s[b][h]=1e20,a.thm[t].s[b][h]=1e20;a.nsPsy.lastAttacks[t]=0}for(var h=0;h<9;h++)a.nsPsy.last_en_subshort[t][h]=10}for(a.loudness_sq_save[0]=a.loudness_sq_save[1]=0,a.npart_l=O(a.numlines_l,a.bo_l,a.bm_l,l,f,a.mld_l,a.PSY.bo_l_weight,u,Ae.BLKSIZE,a.scalefac_band.l,Ae.BLKSIZE/1152,Ae.SBMAX_l),t=0;t<a.npart_l;t++){var p=r;l[t]>=n&&(p=i*(l[t]-n)/(24-n)+r*(24-l[t])/(24-n)),c[t]=Math.pow(10,p/10),0<a.numlines_l[t]?a.rnumlines_l[t]=1/a.numlines_l[t]:a.rnumlines_l[t]=0}a.s3_ll=V(a.s3ind,a.npart_l,l,f,c,s);var m,h=0;for(t=0;t<a.npart_l;t++){g=Y.MAX_VALUE;for(var v=0;v<a.numlines_l[t];v++,h++){var d=u*h/(1e3*Ae.BLKSIZE);w=this.ATHformula(1e3*d,e)-20,w=Math.pow(10,.1*w),(w*=a.numlines_l[t])<g&&(g=w)}a.ATH.cb_l[t]=g,6<(g=20*l[t]/10-20)&&(g=100),g<-15&&(g=-15),g-=8,a.minval_l[t]=Math.pow(10,g/10)*a.numlines_l[t]}for(a.npart_s=O(a.numlines_s,a.bo_s,a.bm_s,l,f,a.mld_s,a.PSY.bo_s_weight,u,Ae.BLKSIZE_s,a.scalefac_band.s,Ae.BLKSIZE_s/384,Ae.SBMAX_s),t=h=0;t<a.npart_s;t++){var g,p=_;l[t]>=n&&(p=o*(l[t]-n)/(24-n)+_*(24-l[t])/(24-n)),c[t]=Math.pow(10,p/10),g=Y.MAX_VALUE;for(var v=0;v<a.numlines_s[t];v++,h++){var w,d=u*h/(1e3*Ae.BLKSIZE_s);w=this.ATHformula(1e3*d,e)-20,w=Math.pow(10,.1*w),(w*=a.numlines_s[t])<g&&(g=w)}a.ATH.cb_s[t]=g,g=7*l[t]/12-7,12<l[t]&&(g*=1+3.1*Math.log(1+g)),l[t]<12&&(g*=1+2.3*Math.log(1-g)),g<-15&&(g=-15),g-=8,a.minval_s[t]=Math.pow(10,g/10)*a.numlines_s[t]}a.s3_ss=V(a.s3ind_s,a.npart_s,l,f,c,s),T=Math.pow(10,(H+1)/16),y=Math.pow(10,(P+1)/16),x=Math.pow(10,I/10),R.init_fft(a),a.decay=Math.exp(-1*k/(.01*u/192)),m=3.5,0!=(2&e.exp_nspsytune)&&(m=1),0<Math.abs(e.msfix)&&(m=e.msfix),e.msfix=m;for(var S=0;S<a.npart_l;S++)a.s3ind[S][1]>a.npart_l-1&&(a.s3ind[S][1]=a.npart_l-1);var M=576*a.mode_gr/u;if(a.ATH.decay=Math.pow(10,-1.2*M),a.ATH.adjust=.01,-(a.ATH.adjustLimit=1)!=e.ATHtype){var B=e.out_samplerate/Ae.BLKSIZE,A=0;for(t=d=0;t<Ae.BLKSIZE/2;++t)d+=B,a.ATH.eql_w[t]=1/Math.pow(10,this.ATHformula(d,e)/10),A+=a.ATH.eql_w[t];for(A=1/A,t=Ae.BLKSIZE/2;0<=--t;)a.ATH.eql_w[t]*=A}for(var S=h=0;S<a.npart_s;++S)for(t=0;t<a.numlines_s[S];++t)++h;for(var S=h=0;S<a.npart_l;++S)for(t=0;t<a.numlines_l[S];++t)++h;for(t=h=0;t<a.npart_l;t++){var d=u*(h+a.numlines_l[t]/2)/(1*Ae.BLKSIZE);a.mld_cb_l[t]=N(d),h+=a.numlines_l[t]}for(;t<Ae.CBANDS;++t)a.mld_cb_l[t]=1;for(t=h=0;t<a.npart_s;t++){var d=u*(h+a.numlines_s[t]/2)/(1*Ae.BLKSIZE_s);a.mld_cb_s[t]=N(d),h+=a.numlines_s[t]}for(;t<Ae.CBANDS;++t)a.mld_cb_s[t]=1;return 0},this.ATHformula=function(e,t){var a;switch(t.ATHtype){case 0:a=s(e,9);break;case 1:a=s(e,-1);break;case 2:a=s(e,0);break;case 3:a=s(e,1)+6;break;case 4:a=s(e,t.ATHcurve);break;default:a=s(e,0)}return a}}function F(){var x,E,u,h,b,H=this;F.V9=410,F.V8=420,F.V7=430,F.V6=440,F.V5=450,F.V4=460,F.V3=470,F.V2=480,F.V1=490,F.V0=500,F.R3MIX=1e3,F.STANDARD=1001,F.EXTREME=1002,F.INSANE=1003,F.STANDARD_FAST=1004,F.EXTREME_FAST=1005,F.MEDIUM=1006,F.MEDIUM_FAST=1007,F.LAME_MAXMP3BUFFER=147456;var p,m,v=new j;function d(){this.lowerlimit=0}function n(e,t){this.lowpass=t}this.enc=new Ae,this.setModules=function(e,t,a,s,n,r,i,_,o){x=e,E=t,u=a,h=s,b=n,p=r,m=_,this.enc.setModules(E,v,h,p)};var P=4294479419;function g(e,t){var a=[new n(8,2e3),new n(16,3700),new n(24,3900),new n(32,5500),new n(40,7e3),new n(48,7500),new n(56,1e4),new n(64,11e3),new n(80,13500),new n(96,15100),new n(112,15600),new n(128,17e3),new n(160,17500),new n(192,18600),new n(224,19400),new n(256,19700),new n(320,20500)],s=H.nearestBitrateFullIndex(t);e.lowerlimit=a[s].lowpass}function I(e){var t=Ae.BLKSIZE+e.framesize-Ae.FFTOFFSET;return t=Math.max(t,512+e.framesize-32)}function L(){this.n_in=0,this.n_out=0}function O(e,t,a,s,n,r){var i=e.internal_flags;if(i.resample_ratio<.9999||1.0001<i.resample_ratio)me();else{r.n_out=Math.min(e.framesize,n),r.n_in=r.n_out;for(var _=0;_<r.n_out;++_)t[0][i.mf_size+_]=a[0][s+_],2==i.channels_out&&(t[1][i.mf_size+_]=a[1][s+_])}}this.lame_init=function(){var e,t,a=new function(){this.class_id=0,this.num_samples=0,this.num_channels=0,this.in_samplerate=0,this.out_samplerate=0,this.scale=0,this.scale_left=0,this.scale_right=0,this.analysis=!1,this.bWriteVbrTag=!1,this.decode_only=!1,this.quality=0,this.mode=Be.STEREO,this.force_ms=!1,this.free_format=!1,this.findReplayGain=!1,this.decode_on_the_fly=!1,this.write_id3tag_automatic=!1,this.brate=0,this.compression_ratio=0,this.copyright=0,this.original=0,this.extension=0,this.emphasis=0,this.error_protection=0,this.strict_ISO=!1,this.disable_reservoir=!1,this.quant_comp=0,this.quant_comp_short=0,this.experimentalY=!1,this.experimentalZ=0,this.exp_nspsytune=0,this.preset=0,this.VBR=null,this.VBR_q_frac=0,this.VBR_q=0,this.VBR_mean_bitrate_kbps=0,this.VBR_min_bitrate_kbps=0,this.VBR_max_bitrate_kbps=0,this.VBR_hard_min=0,this.lowpassfreq=0,this.highpassfreq=0,this.lowpasswidth=0,this.highpasswidth=0,this.maskingadjust=0,this.maskingadjust_short=0,this.ATHonly=!1,this.ATHshort=!1,this.noATH=!1,this.ATHtype=0,this.ATHcurve=0,this.ATHlower=0,this.athaa_type=0,this.athaa_loudapprox=0,this.athaa_sensitivity=0,this.short_blocks=null,this.useTemporal=!1,this.interChRatio=0,this.msfix=0,this.tune=!1,this.tune_value_a=0,this.version=0,this.encoder_delay=0,this.encoder_padding=0,this.framesize=0,this.frameNum=0,this.lame_allocated_gfp=0,this.internal_flags=null},s=((e=a).class_id=P,t=e.internal_flags=new X,e.mode=Be.NOT_SET,e.original=1,e.in_samplerate=44100,e.num_channels=2,e.num_samples=-1,e.bWriteVbrTag=!0,e.quality=-1,e.short_blocks=null,t.subblock_gain=-1,e.lowpassfreq=0,e.highpassfreq=0,e.lowpasswidth=-1,e.highpasswidth=-1,e.VBR=Me.vbr_off,e.VBR_q=4,e.ATHcurve=-1,e.VBR_mean_bitrate_kbps=128,e.VBR_min_bitrate_kbps=0,e.VBR_max_bitrate_kbps=0,e.VBR_hard_min=0,t.VBR_min_bitrate=1,t.VBR_max_bitrate=13,e.quant_comp=-1,e.quant_comp_short=-1,e.msfix=-1,t.resample_ratio=1,t.OldValue[0]=180,t.OldValue[1]=180,t.CurrentStep[0]=4,t.CurrentStep[1]=4,t.masking_lower=1,t.nsPsy.attackthre=-1,t.nsPsy.attackthre_s=-1,e.scale=-1,e.athaa_type=-1,e.ATHtype=-1,e.athaa_loudapprox=-1,e.athaa_sensitivity=0,e.useTemporal=null,e.interChRatio=-1,t.mf_samples_to_encode=Ae.ENCDELAY+Ae.POSTDELAY,e.encoder_padding=0,t.mf_size=Ae.ENCDELAY-Ae.MDCTDELAY,e.findReplayGain=!1,e.decode_on_the_fly=!1,t.decode_on_the_fly=!1,t.findReplayGain=!1,t.findPeakSample=!1,t.RadioGain=0,t.AudiophileGain=0,t.noclipGainChange=0,t.noclipScale=-1,e.preset=0,e.write_id3tag_automatic=!0,0);return 0!=s?null:(a.lame_allocated_gfp=1,a)},this.nearestBitrateFullIndex=function(e){var t=[8,16,24,32,40,48,56,64,80,96,112,128,160,192,224,256,320],a=0,s=0,n=0,r=0;r=t[16],s=t[n=16],a=16;for(var i=0;i<16;i++)if(Math.max(e,t[i+1])!=e){r=t[i+1],n=i+1,s=t[i],a=i;break}return e-s<r-e?a:n},this.lame_init_params=function(e){var t,a,s=e.internal_flags;if(s.Class_ID=0,null==s.ATH&&(s.ATH=new function(){this.useAdjust=0,this.aaSensitivityP=0,this.adjust=0,this.adjustLimit=0,this.decay=0,this.floor=0,this.l=de(Ae.SBMAX_l),this.s=de(Ae.SBMAX_s),this.psfb21=de(Ae.PSFB21),this.psfb12=de(Ae.PSFB12),this.cb_l=de(Ae.CBANDS),this.cb_s=de(Ae.CBANDS),this.eql_w=de(Ae.BLKSIZE/2)}),null==s.PSY&&(s.PSY=new function(){this.mask_adjust=0,this.mask_adjust_short=0,this.bo_l_weight=de(Ae.SBMAX_l),this.bo_s_weight=de(Ae.SBMAX_s)}),null==s.rgdata&&(s.rgdata=new function(){}),s.channels_in=e.num_channels,1==s.channels_in&&(e.mode=Be.MONO),s.channels_out=e.mode==Be.MONO?1:2,s.mode_ext=Ae.MPG_MD_MS_LR,e.mode==Be.MONO&&(e.force_ms=!1),e.VBR==Me.vbr_off&&128!=e.VBR_mean_bitrate_kbps&&0==e.brate&&(e.brate=e.VBR_mean_bitrate_kbps),e.VBR==Me.vbr_off||e.VBR==Me.vbr_mtrh||e.VBR==Me.vbr_mt||(e.free_format=!1),e.VBR==Me.vbr_off&&0==e.brate&&me(),e.VBR==Me.vbr_off&&0<e.compression_ratio&&me(),0!=e.out_samplerate&&(e.out_samplerate<16e3?(e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,8),e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,64)):e.out_samplerate<32e3?(e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,8),e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,160)):(e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,32),e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,320))),0==e.lowpassfreq){var n=16e3;switch(e.VBR){case Me.vbr_off:var r=new d;g(r,e.brate),n=r.lowerlimit;break;case Me.vbr_abr:var r=new d;g(r,e.VBR_mean_bitrate_kbps),n=r.lowerlimit;break;case Me.vbr_rh:me();default:me()}e.mode!=Be.MONO||e.VBR!=Me.vbr_off&&e.VBR!=Me.vbr_abr||(n*=1.5),e.lowpassfreq=0|n}switch(0==e.out_samplerate&&me(),e.lowpassfreq=Math.min(20500,e.lowpassfreq),e.lowpassfreq=Math.min(e.out_samplerate/2,e.lowpassfreq),e.VBR==Me.vbr_off&&(e.compression_ratio=16*e.out_samplerate*s.channels_out/(1e3*e.brate)),e.VBR==Me.vbr_abr&&me(),e.bWriteVbrTag||(e.findReplayGain=!1,e.decode_on_the_fly=!1,s.findPeakSample=!1),s.findReplayGain=e.findReplayGain,s.decode_on_the_fly=e.decode_on_the_fly,s.decode_on_the_fly&&(s.findPeakSample=!0),s.findReplayGain&&me(),s.decode_on_the_fly&&!e.decode_only&&me(),s.mode_gr=e.out_samplerate<=24e3?1:2,e.framesize=576*s.mode_gr,e.encoder_delay=Ae.ENCDELAY,s.resample_ratio=e.in_samplerate/e.out_samplerate,e.VBR){case Me.vbr_mt:case Me.vbr_rh:case Me.vbr_mtrh:e.compression_ratio=[5.7,6.5,7.3,8.2,10,11.9,13,14,15,16.5][e.VBR_q];break;case Me.vbr_abr:e.compression_ratio=16*e.out_samplerate*s.channels_out/(1e3*e.VBR_mean_bitrate_kbps);break;default:e.compression_ratio=16*e.out_samplerate*s.channels_out/(1e3*e.brate)}e.mode==Be.NOT_SET&&(e.mode=Be.JOINT_STEREO),0<e.highpassfreq?me():(s.highpass1=0,s.highpass2=0),0<e.lowpassfreq?(s.lowpass2=2*e.lowpassfreq,0<=e.lowpasswidth?me():s.lowpass1=2*e.lowpassfreq,s.lowpass1/=e.out_samplerate,s.lowpass2/=e.out_samplerate):me(),function(e){var t,a=e.internal_flags,s=32;if(0<a.lowpass1){for(var n=999,r=0;r<=31;r++){var i=r/31;i>=a.lowpass2&&(s=Math.min(s,r)),a.lowpass1<i&&i<a.lowpass2&&(n=Math.min(n,r))}a.lowpass1=999==n?(s-.75)/31:(n-.75)/31,a.lowpass2=s/31}0<a.highpass2&&me(),0<a.highpass2&&me();for(var r=0;r<32;r++){var _,o,i=r/31;a.highpass2>a.highpass1?me():_=1,o=a.lowpass2>a.lowpass1?1<(t=(i-a.lowpass1)/(a.lowpass2-a.lowpass1+1e-20))?0:t<=0?1:Math.cos(Math.PI/2*t):1,a.amp_filter[r]=_*o}}(e),s.samplerate_index=function(e,t){switch(e){case 44100:return t.version=1,0;case 48e3:return t.version=1;case 32e3:return t.version=1,2;case 22050:return t.version=0;case 24e3:return t.version=0,1;case 16e3:return t.version=0,2;case 11025:return t.version=0;case 12e3:return t.version=0,1;case 8e3:return t.version=0,2;default:return t.version=0,-1}}(e.out_samplerate,e),s.samplerate_index<0&&me(),e.VBR==Me.vbr_off?e.free_format?s.bitrate_index=0:(e.brate=function(e,t,a){a<16e3&&(t=2);for(var s=R.bitrate_table[t][1],n=2;n<=14;n++)0<R.bitrate_table[t][n]&&Math.abs(R.bitrate_table[t][n]-e)<Math.abs(s-e)&&(s=R.bitrate_table[t][n]);return s}(e.brate,e.version,e.out_samplerate),s.bitrate_index=function(e,t,a){a<16e3&&(t=2);for(var s=0;s<=14;s++)if(0<R.bitrate_table[t][s]&&R.bitrate_table[t][s]==e)return s;return-1}(e.brate,e.version,e.out_samplerate),s.bitrate_index<=0&&me()):s.bitrate_index=1,e.analysis&&(e.bWriteVbrTag=!1),null!=s.pinfo&&(e.bWriteVbrTag=!1),E.init_bit_stream_w(s);for(var i,_=s.samplerate_index+3*e.version+6*(e.out_samplerate<16e3?1:0),o=0;o<Ae.SBMAX_l+1;o++)s.scalefac_band.l[o]=h.sfBandIndex[_].l[o];for(var o=0;o<Ae.PSFB21+1;o++){var l=(s.scalefac_band.l[22]-s.scalefac_band.l[21])/Ae.PSFB21,f=s.scalefac_band.l[21]+o*l;s.scalefac_band.psfb21[o]=f}s.scalefac_band.psfb21[Ae.PSFB21]=576;for(var o=0;o<Ae.SBMAX_s+1;o++)s.scalefac_band.s[o]=h.sfBandIndex[_].s[o];for(var o=0;o<Ae.PSFB12+1;o++){var l=(s.scalefac_band.s[13]-s.scalefac_band.s[12])/Ae.PSFB12,f=s.scalefac_band.s[12]+o*l;s.scalefac_band.psfb12[o]=f}for(s.scalefac_band.psfb12[Ae.PSFB12]=192,1==e.version?s.sideinfo_len=1==s.channels_out?21:36:s.sideinfo_len=1==s.channels_out?13:21,e.error_protection&&(s.sideinfo_len+=2),a=void 0,a=(t=e).internal_flags,t.frameNum=0,t.write_id3tag_automatic&&m.id3tag_write_v2(t),a.bitrate_stereoMode_Hist=w([16,5]),a.bitrate_blockType_Hist=w([16,6]),a.PeakSample=0,t.bWriteVbrTag&&p.InitVbrTag(t),s.Class_ID=P,i=0;i<19;i++)s.nsPsy.pefirbuf[i]=700*s.mode_gr*s.channels_out;switch(-1==e.ATHtype&&(e.ATHtype=4),e.VBR){case Me.vbr_mt:e.VBR=Me.vbr_mtrh;case Me.vbr_mtrh:null==e.useTemporal&&(e.useTemporal=!1),u.apply_preset(e,500-10*e.VBR_q,0),e.quality<0&&(e.quality=LAME_DEFAULT_QUALITY),e.quality<5&&(e.quality=0),5<e.quality&&(e.quality=5),s.PSY.mask_adjust=e.maskingadjust,s.PSY.mask_adjust_short=e.maskingadjust_short,e.experimentalY?s.sfb21_extra=!1:s.sfb21_extra=44e3<e.out_samplerate,s.iteration_loop=new VBRNewIterationLoop(b);break;case Me.vbr_rh:u.apply_preset(e,500-10*e.VBR_q,0),s.PSY.mask_adjust=e.maskingadjust,s.PSY.mask_adjust_short=e.maskingadjust_short,e.experimentalY?s.sfb21_extra=!1:s.sfb21_extra=44e3<e.out_samplerate,6<e.quality&&(e.quality=6),e.quality<0&&(e.quality=LAME_DEFAULT_QUALITY),s.iteration_loop=new VBROldIterationLoop(b);break;default:var c;s.sfb21_extra=!1,e.quality<0&&(e.quality=LAME_DEFAULT_QUALITY),(c=e.VBR)==Me.vbr_off&&(e.VBR_mean_bitrate_kbps=e.brate),u.apply_preset(e,e.VBR_mean_bitrate_kbps,0),e.VBR=c,s.PSY.mask_adjust=e.maskingadjust,s.PSY.mask_adjust_short=e.maskingadjust_short,c==Me.vbr_off?s.iteration_loop=new function(e){var t=e;this.quantize=t,this.iteration_loop=function(e,t,a,s){var n=e.internal_flags,r=de(C.SFBMAX),i=de(576),_=ve(2),o=0,l=n.l3_side,f=new k(o);this.quantize.rv.ResvFrameBegin(e,f),o=f.bits;for(var c=0;c<n.mode_gr;c++){this.quantize.qupvt.on_pe(e,t,_,o,c,c),n.mode_ext==Ae.MPG_MD_MS_LR&&me();for(var u=0;u<n.channels_out;u++){var h,b,p=l.tt[c][u];p.block_type!=Ae.SHORT_TYPE?(h=0,b=n.PSY.mask_adjust-h):(h=0,b=n.PSY.mask_adjust_short-h),n.masking_lower=Math.pow(10,.1*b),this.quantize.init_outer_loop(n,p),this.quantize.init_xrpow(n,p,i)&&(this.quantize.qupvt.calc_xmin(e,s[c][u],p,r),this.quantize.outer_loop(e,p,r,i,u,_[u])),this.quantize.iteration_finish_one(n,c,u)}}this.quantize.rv.ResvFrameEnd(n,o)}}(b):me()}return e.VBR!=Me.vbr_off&&me(),e.tune&&me(),function(e){var t=e.internal_flags;switch(e.quality){default:case 9:t.psymodel=0,t.noise_shaping=0,t.noise_shaping_amp=0,t.noise_shaping_stop=0,t.use_best_huffman=0,t.full_outer_loop=0;break;case 8:e.quality=7;case 7:t.psymodel=1,t.noise_shaping=0,t.noise_shaping_amp=0,t.noise_shaping_stop=0,t.use_best_huffman=0,t.full_outer_loop=0;break;case 6:case 5:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),t.noise_shaping_amp=0,t.noise_shaping_stop=0,-1==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=0,t.full_outer_loop=0;break;case 4:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),t.noise_shaping_amp=0,t.noise_shaping_stop=0,-1==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=1,t.full_outer_loop=0;break;case 3:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),t.noise_shaping_amp=1,-(t.noise_shaping_stop=1)==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=1,t.full_outer_loop=0;break;case 2:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),0==t.substep_shaping&&(t.substep_shaping=2),t.noise_shaping_amp=1,-(t.noise_shaping_stop=1)==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=1,t.full_outer_loop=0;break;case 1:case 0:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),0==t.substep_shaping&&(t.substep_shaping=2),t.noise_shaping_amp=2,-(t.noise_shaping_stop=1)==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=1,t.full_outer_loop=0}}(e),e.athaa_type<0?s.ATH.useAdjust=3:s.ATH.useAdjust=e.athaa_type,s.ATH.aaSensitivityP=Math.pow(10,e.athaa_sensitivity/-10),null==e.short_blocks&&(e.short_blocks=Se.short_block_allowed),e.short_blocks!=Se.short_block_allowed||e.mode!=Be.JOINT_STEREO&&e.mode!=Be.STEREO||(e.short_blocks=Se.short_block_coupled),e.quant_comp<0&&(e.quant_comp=1),e.quant_comp_short<0&&(e.quant_comp_short=0),e.msfix<0&&(e.msfix=0),e.exp_nspsytune=1|e.exp_nspsytune,e.internal_flags.nsPsy.attackthre<0&&(e.internal_flags.nsPsy.attackthre=j.NSATTACKTHRE),e.internal_flags.nsPsy.attackthre_s<0&&(e.internal_flags.nsPsy.attackthre_s=j.NSATTACKTHRE_S),e.scale<0&&(e.scale=1),e.ATHtype<0&&(e.ATHtype=4),e.ATHcurve<0&&(e.ATHcurve=4),e.athaa_loudapprox<0&&(e.athaa_loudapprox=2),e.interChRatio<0&&(e.interChRatio=0),null==e.useTemporal&&(e.useTemporal=!0),s.slot_lag=s.frac_SpF=0,e.VBR==Me.vbr_off&&(s.slot_lag=s.frac_SpF=72e3*(e.version+1)*e.brate%e.out_samplerate|0),h.iteration_init(e),v.psymodel_init(e),0},this.lame_encode_flush=function(e,t,a,s){var n,r,i,_,o=e.internal_flags,l=M([2,1152]),f=0,c=o.mf_samples_to_encode-Ae.POSTDELAY,u=I(e);if(o.mf_samples_to_encode<1)return 0;for(n=0,e.in_samplerate!=e.out_samplerate&&me(),(i=e.framesize-c%e.framesize)<576&&(i+=e.framesize),e.encoder_padding=i,_=(c+i)/e.framesize;0<_&&0<=f;){var h=u-o.mf_size,b=e.frameNum;h*=e.in_samplerate,1152<(h/=e.out_samplerate)&&(h=1152),h<1&&(h=1),r=s-n,0==s&&(r=0),f=this.lame_encode_buffer(e,l[0],l[1],h,t,a,r),a+=f,n+=f,_-=b!=e.frameNum?1:0}return o.mf_samples_to_encode=0,f<0?f:(r=s-n,0==s&&(r=0),E.flush_bitstream(e),(f=E.copy_buffer(o,t,a,r,1))<0?f:(a+=f,r=s-(n+=f),0==s&&(r=0),e.write_id3tag_automatic&&me(),n))},this.lame_encode_buffer=function(e,t,a,s,n,r,i){var _,o,l=e.internal_flags,f=[null,null];if(l.Class_ID!=P)return-3;if(0==s)return 0;o=s,(null==(_=l).in_buffer_0||_.in_buffer_nsamples<o)&&(_.in_buffer_0=de(o),_.in_buffer_1=de(o),_.in_buffer_nsamples=o),f[0]=l.in_buffer_0,f[1]=l.in_buffer_1;for(var c=0;c<s;c++)f[0][c]=t[c],1<l.channels_in&&(f[1][c]=a[c]);return function(e,t,a,s,n,r,i){var _,o,l,f,c,u=e.internal_flags,h=0,b=[null,null],p=[null,null];if(u.Class_ID!=P)return-3;if(0==s)return 0;if((c=E.copy_buffer(u,n,r,i,0))<0)return c;if(r+=c,h+=c,p[0]=t,p[1]=a,N.NEQ(e.scale,0)&&N.NEQ(e.scale,1))for(o=0;o<s;++o)p[0][o]*=e.scale,2==u.channels_out&&(p[1][o]*=e.scale);if(N.NEQ(e.scale_left,0)&&N.NEQ(e.scale_left,1))for(o=0;o<s;++o)p[0][o]*=e.scale_left;if(N.NEQ(e.scale_right,0)&&N.NEQ(e.scale_right,1))for(o=0;o<s;++o)p[1][o]*=e.scale_right;2==e.num_channels&&1==u.channels_out&&me(),f=I(e),b[0]=u.mfbuf[0],b[1]=u.mfbuf[1];for(var m,v,d,g,w,S,M,B=0;0<s;){var A=[null,null],R=0,k=0;A[0]=p[0],A[1]=p[1];var T=new L;if(O(e,b,A,B,s,T),R=T.n_in,k=T.n_out,u.findReplayGain&&!u.decode_on_the_fly&&x.AnalyzeSamples(u.rgdata,b[0],u.mf_size,b[1],u.mf_size,k,u.channels_out)==V.GAIN_ANALYSIS_ERROR)return-6;if(s-=R,B+=R,u.channels_out,u.mf_size+=k,u.mf_samples_to_encode<1&&me(),u.mf_samples_to_encode+=k,u.mf_size>=f){var y=i-h;if(0==i&&(y=0),m=e,v=b[0],d=b[1],g=n,w=r,S=y,void 0,M=H.enc.lame_encode_mp3_frame(m,v,d,g,w,S),m.frameNum++,(_=M)<0)return _;for(r+=_,h+=_,u.mf_size-=e.framesize,u.mf_samples_to_encode-=e.framesize,l=0;l<u.channels_out;l++)for(o=0;o<u.mf_size;o++)b[l][o]=b[l][o+e.framesize]}}return h}(e,f[0],f[1],s,n,r,i)}}C.SFBMAX=3*Ae.SBMAX_s,Ae.ENCDELAY=576,Ae.POSTDELAY=1152,Ae.FFTOFFSET=224+(Ae.MDCTDELAY=48),Ae.DECDELAY=528,Ae.SBLIMIT=32,Ae.CBANDS=64,Ae.SBPSY_l=21,Ae.SBPSY_s=12,Ae.SBMAX_l=22,Ae.SBMAX_s=13,Ae.PSFB21=6,Ae.PSFB12=6,Ae.HBLKSIZE=(Ae.BLKSIZE=1024)/2+1,Ae.HBLKSIZE_s=(Ae.BLKSIZE_s=256)/2+1,Ae.NORM_TYPE=0,Ae.START_TYPE=1,Ae.SHORT_TYPE=2,Ae.STOP_TYPE=3,Ae.MPG_MD_LR_LR=0,Ae.MPG_MD_LR_I=1,Ae.MPG_MD_MS_LR=2,Ae.MPG_MD_MS_I=3,Ae.fircoef=[-.1039435,-.1892065,5*-.0432472,-.155915,3.898045e-17,.0467745*5,.50455,.756825,.187098*5],X.MFSIZE=3456+Ae.ENCDELAY-Ae.MDCTDELAY,X.MAX_HEADER_BUF=256,X.MAX_BITS_PER_CHANNEL=4095,X.MAX_BITS_PER_GRANULE=7680,X.BPC=320,C.SFBMAX=3*Ae.SBMAX_s,t.Mp3Encoder=function(s,e,t){1!=s&&me("fix cc: only supports mono");var n=new F,a=new function(){this.setModules=function(e,t){}},r=new V,i=new N,_=new function(){function e(e,t,a,s,n,r,i,_,o,l,f,c,u,h){this.quant_comp=t,this.quant_comp_s=a,this.safejoint=s,this.nsmsfix=n,this.st_lrm=r,this.st_s=i,this.nsbass=_,this.scale=o,this.masking_adj=l,this.ath_lower=f,this.ath_curve=c,this.interch=u,this.sfscale=h}var i;function s(e,t,a){me()}this.setModules=function(e){i=e};var _=[new e(8,9,9,0,0,6.6,145,0,.95,0,-30,11,.0012,1),new e(16,9,9,0,0,6.6,145,0,.95,0,-25,11,.001,1),new e(24,9,9,0,0,6.6,145,0,.95,0,-20,11,.001,1),new e(32,9,9,0,0,6.6,145,0,.95,0,-15,11,.001,1),new e(40,9,9,0,0,6.6,145,0,.95,0,-10,11,9e-4,1),new e(48,9,9,0,0,6.6,145,0,.95,0,-10,11,9e-4,1),new e(56,9,9,0,0,6.6,145,0,.95,0,-6,11,8e-4,1),new e(64,9,9,0,0,6.6,145,0,.95,0,-2,11,8e-4,1),new e(80,9,9,0,0,6.6,145,0,.95,0,0,8,7e-4,1),new e(96,9,9,0,2.5,6.6,145,0,.95,0,1,5.5,6e-4,1),new e(112,9,9,0,2.25,6.6,145,0,.95,0,2,4.5,5e-4,1),new e(128,9,9,0,1.95,6.4,140,0,.95,0,3,4,2e-4,1),new e(160,9,9,1,1.79,6,135,0,.95,-2,5,3.5,0,1),new e(192,9,9,1,1.49,5.6,125,0,.97,-4,7,3,0,0),new e(224,9,9,1,1.25,5.2,125,0,.98,-6,9,2,0,0),new e(256,9,9,1,.97,5.2,125,0,1,-8,10,1,0,0),new e(320,9,9,1,.9,5.2,125,0,1,-10,12,0,0,0)];function n(e,t,a){var s=t,n=i.nearestBitrateFullIndex(t);if(e.VBR=Me.vbr_abr,e.VBR_mean_bitrate_kbps=s,e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,320),e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,8),e.brate=e.VBR_mean_bitrate_kbps,320<e.VBR_mean_bitrate_kbps&&(e.disable_reservoir=!0),0<_[n].safejoint&&(e.exp_nspsytune=2|e.exp_nspsytune),0<_[n].sfscale&&(e.internal_flags.noise_shaping=2),0<Math.abs(_[n].nsbass)){var r=int(4*_[n].nsbass);r<0&&(r+=64),e.exp_nspsytune=e.exp_nspsytune|r<<2}return 0!=a?e.quant_comp=_[n].quant_comp:0<Math.abs(e.quant_comp- -1)||(e.quant_comp=_[n].quant_comp),0!=a?e.quant_comp_short=_[n].quant_comp_s:0<Math.abs(e.quant_comp_short- -1)||(e.quant_comp_short=_[n].quant_comp_s),0!=a?e.msfix=_[n].nsmsfix:0<Math.abs(e.msfix- -1)||(e.msfix=_[n].nsmsfix),0!=a?e.internal_flags.nsPsy.attackthre=_[n].st_lrm:0<Math.abs(e.internal_flags.nsPsy.attackthre- -1)||(e.internal_flags.nsPsy.attackthre=_[n].st_lrm),0!=a?e.internal_flags.nsPsy.attackthre_s=_[n].st_s:0<Math.abs(e.internal_flags.nsPsy.attackthre_s- -1)||(e.internal_flags.nsPsy.attackthre_s=_[n].st_s),0!=a?e.scale=_[n].scale:0<Math.abs(e.scale- -1)||(e.scale=_[n].scale),0!=a?e.maskingadjust=_[n].masking_adj:0<Math.abs(e.maskingadjust-0)||(e.maskingadjust=_[n].masking_adj),0<_[n].masking_adj?0!=a?e.maskingadjust_short=.9*_[n].masking_adj:0<Math.abs(e.maskingadjust_short-0)||(e.maskingadjust_short=.9*_[n].masking_adj):0!=a?e.maskingadjust_short=1.1*_[n].masking_adj:0<Math.abs(e.maskingadjust_short-0)||(e.maskingadjust_short=1.1*_[n].masking_adj),0!=a?e.ATHlower=-_[n].ath_lower/10:0<Math.abs(10*-e.ATHlower-0)||(e.ATHlower=-_[n].ath_lower/10),0!=a?e.ATHcurve=_[n].ath_curve:0<Math.abs(e.ATHcurve- -1)||(e.ATHcurve=_[n].ath_curve),0!=a?e.interChRatio=_[n].interch:0<Math.abs(e.interChRatio- -1)||(e.interChRatio=_[n].interch),t}this.apply_preset=function(e,t,a){switch(t){case F.R3MIX:t=F.V3,e.VBR=Me.vbr_mtrh;break;case F.MEDIUM:t=F.V4,e.VBR=Me.vbr_rh;break;case F.MEDIUM_FAST:t=F.V4,e.VBR=Me.vbr_mtrh;break;case F.STANDARD:t=F.V2,e.VBR=Me.vbr_rh;break;case F.STANDARD_FAST:t=F.V2,e.VBR=Me.vbr_mtrh;break;case F.EXTREME:t=F.V0,e.VBR=Me.vbr_rh;break;case F.EXTREME_FAST:t=F.V0,e.VBR=Me.vbr_mtrh;break;case F.INSANE:return t=320,e.preset=t,n(e,t,a),e.VBR=Me.vbr_off,t}switch(e.preset=t){case F.V9:return s(e,9,a),t;case F.V8:return s(e,8,a),t;case F.V7:return s(e,7,a),t;case F.V6:return s(e,6,a),t;case F.V5:return s(e,5,a),t;case F.V4:return s(e,4,a),t;case F.V3:return s(e,3,a),t;case F.V2:return s(e,2,a),t;case F.V1:return s(e,1,a),t;case F.V0:return s(e,0,a),t}return 8<=t&&t<=320?n(e,t,a):(e.preset=0,t)}},o=new x,l=new H,f=new B,c=new function(){this.getLameShortVersion=function(){return"3.98.4"}},u=new function(){this.setModules=function(e,t){}},h=new function(){var o;this.setModules=function(e){o=e},this.ResvFrameBegin=function(e,t){var a,s=e.internal_flags,n=s.l3_side,r=o.getframebits(e);t.bits=(r-8*s.sideinfo_len)/s.mode_gr;var i=2048*s.mode_gr-8;320<e.brate?me():(a=11520,e.strict_ISO&&me()),s.ResvMax=a-r,s.ResvMax>i&&(s.ResvMax=i),(s.ResvMax<0||e.disable_reservoir)&&(s.ResvMax=0);var _=t.bits*s.mode_gr+Math.min(s.ResvSize,s.ResvMax);return a<_&&(_=a),n.resvDrain_pre=0,null!=s.pinfo&&me(),_},this.ResvMaxBits=function(e,t,a,s){var n,r=e.internal_flags,i=r.ResvSize,_=r.ResvMax;0!=s&&(i+=t),0!=(1&r.substep_shaping)&&(_*=.9),a.bits=t,9*_<10*i?(n=i-9*_/10,a.bits+=n,r.substep_shaping|=128):(n=0,r.substep_shaping&=127,e.disable_reservoir||0!=(1&r.substep_shaping)||(a.bits-=.1*t));var o=i<6*r.ResvMax/10?i:6*r.ResvMax/10;return(o-=n)<0&&(o=0),o},this.ResvAdjust=function(e,t){e.ResvSize-=t.part2_3_length+t.part2_length},this.ResvFrameEnd=function(e,t){var a,s=e.l3_side;e.ResvSize+=t*e.mode_gr;var n=0;s.resvDrain_post=0,(s.resvDrain_pre=0)!=(a=e.ResvSize%8)&&(n+=a),0<(a=e.ResvSize-n-e.ResvMax)&&(n+=a);var r=Math.min(8*s.main_data_begin,n)/8;s.resvDrain_pre+=8*r,n-=8*r,e.ResvSize-=8*r,s.main_data_begin-=r,s.resvDrain_post+=n,e.ResvSize-=n}},b=new A,p=new function(){this.setModules=function(e,t,a){}},m=new function(){};n.setModules(r,i,_,o,l,f,c,u,m),i.setModules(r,m,c,f),u.setModules(i,c),_.setModules(n),l.setModules(i,h,o,b),o.setModules(b,h,n.enc.psy),h.setModules(i),b.setModules(o),f.setModules(n,i,c),a.setModules(p,m),p.setModules(c,u,_);var v=n.lame_init();v.num_channels=s,v.in_samplerate=e,v.out_samplerate=e,v.brate=t,v.mode=Be.STEREO,v.quality=3,v.bWriteVbrTag=!1,v.disable_reservoir=!0,v.write_id3tag_automatic=!1,n.lame_init_params(v);var d=1152,g=0|1.25*d+7200,w=S(g);this.encodeBuffer=function(e,t){1==s&&(t=e),e.length>d&&(d=e.length,w=S(g=0|1.25*d+7200));var a=n.lame_encode_buffer(v,e,t,e.length,w,0,g);return new Int8Array(w.subarray(0,a))},this.flush=function(){var e=n.lame_encode_flush(v,w,0,g);return new Int8Array(w.subarray(0,e))}}}t(),e.lamejs=t}(("object"==typeof window&&window.document?window:Object).Recorder);