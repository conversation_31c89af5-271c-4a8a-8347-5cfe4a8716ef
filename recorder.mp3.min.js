/*
录音
https://github.com/xiangyuecn/Recorder
src: recorder-core.js,engine/mp3.js,engine/mp3-engine.js
*/
!function (e) { var t = "object" == typeof window && !!window.document, a = t ? window : Object; !function (e, y) { "use strict"; var A = function () { }, f = function (e) { return "number" == typeof e }, B = function (e) { return JSON.stringify(e) }, W = function (e) { return new _(e) }, k = W.LM = "2025-01-11 09:28", R = "https://github.com/xiangyuecn/Recorder", T = "Recorder", x = "getUserMedia", N = "srcSampleRate", V = "sampleRate", i = "bitRate", E = "catch", t = e[T]; if (t && t.LM == k) return t.CLog(t.i18n.$T("K8zP::重复导入{1}", 0, T), 3); W.<PERSON> = function () { var e = W.Stream; if (e) { var t = j(e), a = t[0]; if (a) { var n = a.readyState; return "live" == n || n == a.LIVE } } return !1 }, W.BufferSize = 4096, W.Destroy = function () { for (var e in Q(T + " Destroy"), F(), a) a[e]() }; var a = {}; W.BindDestroy = function (e, t) { a[e] = t }, W.Support = function () { if (!y) return !1; var e = navigator.mediaDevices || {}; return e[x] || (e = navigator)[x] || (e[x] = e.webkitGetUserMedia || e.mozGetUserMedia || e.msGetUserMedia), !!e[x] && (W.Scope = e, !!W.GetContext()) }, W.GetContext = function (e) { if (!y) return null; var t = window.AudioContext; if (t || (t = window.webkitAudioContext), !t) return null; var a = W.Ctx, n = 0; return a || (a = W.Ctx = new t, n = 1, W.NewCtxs = W.NewCtxs || [], W.BindDestroy("Ctx", function () { var e = W.Ctx; e && e.close && (s(e), W.Ctx = 0); var t = W.NewCtxs; W.NewCtxs = []; for (var a = 0; a < t.length; a++)s(t[a]) })), e && a.close && (n || (a._useC || s(a), a = new t), a._useC = 1, W.NewCtxs.push(a)), a }, W.CloseNewCtx = function (e) { if (e && e.close) { s(e); for (var t = W.NewCtxs || [], a = t.length, n = 0; n < t.length; n++)if (t[n] == e) { t.splice(n, 1); break } Q($("mSxV::剩{1}个GetContext未close", 0, a + "-1=" + t.length), t.length ? 3 : 0) } }; var s = function (e) { if (e && e.close && !e._isC && (e._isC = 1, "closed" != e.state)) try { e.close() } catch (e) { Q("ctx close err", 1, e) } }, C = W.ResumeCtx = function (a, n, s, r) { var i = 0, o = 0, _ = 0, l = 0, f = "EventListener", c = "ResumeCtx ", u = function (e, t) { o && h(), i || (i = 1, e && r(e, l), t && s(l)), t && (!a._LsSC && a["add" + f] && a["add" + f]("statechange", b), a._LsSC = 1, _ = 1) }, h = function (e) { if (!e || !o) { o = e ? 1 : 0; for (var t = ["focus", "mousedown", "mouseup", "touchstart", "touchend"], a = 0; a < t.length; a++)window[(e ? "add" : "remove") + f](t[a], b, !0) } }, b = function () { var e = a.state, t = p(e); if (!i && !n(t ? ++l : l)) return u(); t ? (_ && Q(c + "sc " + e, 3), h(1), a.resume().then(function () { _ && Q(c + "sc " + a.state), u(0, 1) })[E](function (e) { Q(c + "error", 1, e), p(a.state) || u(e.message || "error") })) : "closed" == e ? (_ && !a._isC && Q(c + "sc " + e, 1), u("ctx closed")) : u(0, 1) }; b() }, p = W.CtxSpEnd = function (e) { return "suspended" == e || "interrupted" == e }, I = function (e) { var t = e.state, a = "ctx.state=" + t; return p(t) && (a += $("nMIy::（注意：ctx不是running状态，rec.open和start至少要有一个在用户操作(触摸、点击等)时进行调用，否则将在rec.start时尝试进行ctx.resume，可能会产生兼容性问题(仅iOS)，请参阅文档中runningContext配置）")), a }, L = "ConnectEnableWebM"; W[L] = !0; var P = "ConnectEnableWorklet"; W[P] = !1; var H = function (e) { var _ = e.BufferSize || W.BufferSize, l = e.Stream, i = l._c, f = i[V], c = {}, t = j(l), a = t[0], n = null, s = ""; if (a && a.getSettings) { var r = (n = a.getSettings())[V]; r && r != f && (s = $("eS8i::Stream的采样率{1}不等于{2}，将进行采样率转换（注意：音质不会变好甚至可能变差），主要在移动端未禁用回声消除时会产生此现象，浏览器有回声消除时可能只会返回16k采样率的音频数据，", 0, r, f)) } l._ts = n, Q(s + "Stream TrackSet: " + B(n), s ? 3 : 0); var u, o, h, b = function (e) { var t = l._m = i.createMediaStreamSource(l), a = i.destination, n = "createMediaStreamDestination"; i[n] && (a = l._d = i[n]()), t.connect(e), e.connect(a) }, p = "", m = l._call, v = function (e, t) { for (var a in m) { if (t != f) { c.index = 0; var n = (c = W.SampleData([e], t, f, c, { _sum: 1 })).data, s = c._sum } else { c = {}; for (var r = e.length, n = new Int16Array(r), s = 0, i = 0; i < r; i++) { var o = Math.max(-1, Math.min(1, e[i])); o = o < 0 ? 32768 * o : 32767 * o, n[i] = o, s += Math.abs(o) } } for (var _ in m) m[_](n, s); return } }, d = "ScriptProcessor", g = "audioWorklet", S = T + " " + g, w = "RecProc", M = "MediaRecorder", y = M + ".WebM.PCM", A = i.createScriptProcessor || i.createJavaScriptNode, k = $("ZGlf::。由于{1}内部1秒375次回调，在移动端可能会有性能问题导致回调丢失录音变短，PC端无影响，暂不建议开启{1}。", 0, g), R = function () { o = l.isWorklet = !1, O(l), Q($("7TU0::Connect采用老的{1}，", 0, d) + U.get($(W[P] ? "JwCL::但已设置{1}尝试启用{2}" : "VGjB::可设置{1}尝试启用{2}", 2), [T + "." + P + "=true", g]) + p + k, 3); var e = l._p = A.call(i, _, 1, 1); b(e), e.onaudioprocess = function (e) { var t = e.inputBuffer.getChannelData(0); v(t, f) } }, x = function () { u = l.isWebM = !1, D(l), o = l.isWorklet = !A || W[P]; var t = window.AudioWorkletNode; if (o && i[g] && t) { var n = function () { return o && l._na }, s = l._na = function () { "" !== h && (clearTimeout(h), h = setTimeout(function () { h = 0, n() && (Q($("MxX1::{1}未返回任何音频，恢复使用{2}", 0, g, d), 3), A && R()) }, 500)) }, r = function () { if (n()) { var e = l._n = new t(i, w, { processorOptions: { bufferSize: _ } }); b(e), e.port.onmessage = function (e) { h && (clearTimeout(h), h = ""), n() ? v(e.data.val, f) : o || Q($("XUap::{1}多余回调", 0, g), 3) }, Q($("yOta::Connect采用{1}，设置{2}可恢复老式{3}", 0, g, T + "." + P + "=false", d) + p + k, 3) } }, e = function () { if (n()) if (i[w]) r(); else { var e, t, a = (t = "class " + w + " extends AudioWorkletProcessor{", t += "constructor " + (e = function (e) { return e.toString().replace(/^function|DEL_/g, "").replace(/\$RA/g, S) })(function (e) { DEL_super(e); var t = this, a = e.processorOptions.bufferSize; t.bufferSize = a, t.buffer = new Float32Array(2 * a), t.pos = 0, t.port.onmessage = function (e) { e.data.kill && (t.kill = !0, $C.log("$RA kill call")) }, $C.log("$RA .ctor call", e) }), t += "process " + e(function (e, t, a) { var n = this, s = n.bufferSize, r = n.buffer, i = n.pos; if ((e = (e[0] || [])[0] || []).length) { r.set(e, i); var o = ~~((i += e.length) / s) * s; if (o) { this.port.postMessage({ val: r.slice(0, o) }); var _ = r.subarray(o, i); (r = new Float32Array(2 * s)).set(_), i = _.length, n.buffer = r } n.pos = i } return !n.kill }), t = (t += '}try{registerProcessor("' + w + '", ' + w + ')}catch(e){$C.error("' + S + ' Reg Error",e)}').replace(/\$C\./g, "console."), "data:text/javascript;base64," + btoa(unescape(encodeURIComponent(t)))); i[g].addModule(a).then(function (e) { n() && (i[w] = 1, r(), h && s()) })[E](function (e) { Q(g + ".addModule Error", 1, e), n() && R() }) } }; C(i, function () { return n() }, e, e) } else R() }; !function () { var e = window[M], t = "ondataavailable", a = "audio/webm; codecs=pcm"; u = l.isWebM = W[L]; var n = e && t in e.prototype && e.isTypeSupported(a); if (p = n ? "" : $("VwPd::（此浏览器不支持{1}）", 0, y), !u || !n) return x(); var s = function () { return u && l._ra }; l._ra = function () { "" !== h && (clearTimeout(h), h = setTimeout(function () { s() && (Q($("vHnb::{1}未返回任何音频，降级使用{2}", 0, M, g), 3), x()) }, 500)) }; var r = Object.assign({ mimeType: a }, W.ConnectWebMOptions), i = l._r = new e(l, r), o = l._rd = {}; i[t] = function (e) { var t = new FileReader; t.onloadend = function () { if (s()) { var e = X(new Uint8Array(t.result), o); if (!e) return; if (-1 == e) return void x(); h && (clearTimeout(h), h = ""), v(e, o.webmSR) } else u || Q($("O9P7::{1}多余回调", 0, M), 3) }, t.readAsArrayBuffer(e.data) }; try { i.start(~~(_ / 48)), Q($("LMEm::Connect采用{1}，设置{2}可恢复使用{3}或老式{4}", 0, y, T + "." + L + "=false", g, d)) } catch (e) { Q("mr start err", 1, e), x() } }() }, r = function (e) { e._na && e._na(), e._ra && e._ra() }, O = function (e) { e._na = null, e._n && (e._n.port.postMessage({ kill: !0 }), e._n.disconnect(), e._n = null) }, D = function (e) { if (e._ra = null, e._r) { try { e._r.stop() } catch (e) { Q("mr stop err", 1, e) } e._r = null } }, F = function (e) { var t = (e = e || W) == W, a = e.Stream; a && (a._m && (a._m.disconnect(), a._m = null), !a._RC && a._c && W.CloseNewCtx(a._c), a._RC = null, a._c = null, a._d && (n(a._d.stream), a._d = null), a._p && (a._p.disconnect(), a._p.onaudioprocess = a._p = null), O(a), D(a), t && n(a)), e.Stream = 0 }, n = W.StopS_ = function (e) { for (var t = j(e), a = 0; a < t.length; a++) { var n = t[a]; n.stop && n.stop() } e.stop && e.stop() }, j = function (e) { var t = 0, a = 0, n = []; e.getAudioTracks && (t = e.getAudioTracks(), a = e.getVideoTracks()), t || (t = e.audioTracks, a = e.videoTracks); for (var s = 0, r = t ? t.length : 0; s < r; s++)n.push(t[s]); for (var s = 0, r = a ? a.length : 0; s < r; s++)n.push(a[s]); return n }; W.SampleData = function (e, t, a, n, s) { var r = "SampleData"; n || (n = {}); var i = n.index || 0, o = n.offset || 0, _ = n.raisePrev || 0, l = n.filter; if (l && l.fn && (l.sr && l.sr != t || l.srn && l.srn != a) && (l = null, Q($("d48C::{1}的filter采样率变了，重设滤波", 0, r), 3)), !l) if (a <= t) { var f = 3 * t / 4 < a ? 0 : a / 2 * 3 / 4; l = { fn: f ? W.IIRFilter(!0, t, f) : 0 } } else { var f = 3 * a / 4 < t ? 0 : t / 2 * 3 / 4; l = { fn: f ? W.IIRFilter(!0, a, f) : 0 } } l.sr = t, l.srn = a; var c = l.fn, u = n.frameNext || []; s || (s = {}); var h = s.frameSize || 1; s.frameType && (h = "mp3" == s.frameType ? 1152 : 1); var b = s._sum, p = 0, m = e.length; m + 1 < i && Q($("tlbC::{1}似乎传入了未重置chunk {2}", 0, r, i + ">" + m), 3); for (var v = 0, d = i; d < m; d++)v += e[d].length; var g = t / a; if (1 < g) v = Math.max(0, v - Math.floor(o)), v = Math.floor(v / g); else if (g < 1) { var S = 1 / g; v = Math.floor(v * S) } v += u.length; for (var w = new Int16Array(v), M = 0, d = 0; d < u.length; d++)w[M] = u[d], M++; for (; i < m; i++) { var y = e[i], A = y instanceof Float32Array, d = o, k = y.length, R = c && c.Embed, x = 0, B = 0, T = 0, E = 0; if (g < 1) { for (var C = M + d, I = _, L = 0; L < k; L++) { var P = y[L]; A && (P = (P = Math.max(-1, Math.min(1, P))) < 0 ? 32768 * P : 32767 * P); var H = Math.floor(C); C += S; for (var O = Math.floor(C), N = (P - I) / (O - H), V = 1; H < O; H++, V++) { var D = Math.floor(I + V * N); R ? (T = D, E = R.b0 * T + R.b1 * R.x1 + R.b0 * R.x2 - R.a1 * R.y1 - R.a2 * R.y2, R.x2 = R.x1, R.x1 = T, R.y2 = R.y1, R.y1 = E, D = E) : D = c ? c(D) : D, 32767 < D ? D = 32767 : D < -32768 && (D = -32768), b && (p += Math.abs(D)), w[H] = D, M++ } I = _ = P, d += S } o = d % 1 } else { for (var L = 0, F = 0; L < k; L++, F++) { if (F < k) { var P = y[F]; A && (P = (P = Math.max(-1, Math.min(1, P))) < 0 ? 32768 * P : 32767 * P), R ? (T = P, E = R.b0 * T + R.b1 * R.x1 + R.b0 * R.x2 - R.a1 * R.y1 - R.a2 * R.y2, R.x2 = R.x1, R.x1 = T, R.y2 = R.y1, R.y1 = E) : E = c ? c(P) : P } if (x = B, B = E, 0 != F) { var j = Math.floor(d); if (L == j) { var X = Math.ceil(d), Y = d - j, z = x, q = X < k ? B : z, G = z + (q - z) * Y; 32767 < G ? G = 32767 : G < -32768 && (G = -32768), b && (p += Math.abs(G)), w[M] = G, M++, d += g } } else L-- } o = Math.max(0, d - k) } } g < 1 && M + 1 == v && (v--, w = new Int16Array(w.buffer.slice(0, 2 * v))), M - 1 != v && M != v && Q(r + " idx:" + M + " != size:" + v, 3), u = null; var U = v % h; if (0 < U) { var K = 2 * (v - U); u = new Int16Array(w.buffer.slice(K)), w = new Int16Array(w.buffer.slice(0, K)) } var Z = { index: i, offset: o, raisePrev: _, filter: l, frameNext: u, sampleRate: a, data: w }; return b && (Z._sum = p), Z }, W.IIRFilter = function (e, t, a) { var n = 2 * Math.PI * a / t, s = Math.sin(n), r = Math.cos(n), i = s / 2, o = 1 + i, _ = -2 * r / o, l = (1 - i) / o; if (e) var f = (1 - r) / 2 / o, c = (1 - r) / o; else var f = (1 + r) / 2 / o, c = -(1 + r) / o; var u = 0, h = 0, b = 0, p = 0, m = 0, v = function (e) { return b = f * e + c * u + f * h - _ * p - l * m, h = u, u = e, m = p, p = b }; return v.Embed = { x1: 0, x2: 0, y1: 0, y2: 0, b0: f, b1: c, a1: _, a2: l }, v }, W.PowerLevel = function (e, t) { var a = e / t || 0; return a < 1251 ? Math.round(a / 1250 * 10) : Math.round(Math.min(100, Math.max(0, 100 * (1 + Math.log(a / 1e4) / Math.log(10))))) }, W.PowerDBFS = function (e) { var t = Math.max(.1, e || 0); return t = Math.min(t, 32767), t = 20 * Math.log(t / 32767) / Math.log(10), Math.max(-100, Math.round(t)) }, W.CLog = function (e, t) { if ("object" == typeof console) { var a = new Date, n = ("0" + a.getMinutes()).substr(-2) + ":" + ("0" + a.getSeconds()).substr(-2) + "." + ("00" + a.getMilliseconds()).substr(-3), s = this && this.envIn && this.envCheck && this.id, r = ["[" + n + " " + T + (s ? ":" + s : "") + "]" + e], i = arguments, o = W.CLog, _ = 2, l = o.log || console.log; for (f(t) ? l = 1 == t ? o.error || console.error : 3 == t ? o.warn || console.warn : l : _ = 1; _ < i.length; _++)r.push(i[_]); c ? l && l("[IsLoser]" + r[0], 1 < r.length ? r : "") : l.apply(console, r) } }; var Q = function () { W.CLog.apply(this, arguments) }, c = !0; try { c = !console.log.apply } catch (e) { } var o = 0; function _(e) { var t = this; t.id = ++o, l(); var a = { type: "mp3", onProcess: A }; for (var n in e) a[n] = e[n]; var s = (t.set = a)[i], r = a[V]; (s && !f(s) || r && !f(r)) && t.CLog($.G("IllegalArgs-1", [$("VtS4::{1}和{2}必须是数值", 0, V, i)]), 1, e), a[i] = +s || 16, a[V] = +r || 16e3, t.state = 0, t._S = 9, t.Sync = { O: 9, C: 9 } } W.Sync = { O: 9, C: 9 }, W.prototype = _.prototype = { CLog: Q, _streamStore: function () { return this.set.sourceStream ? this : W }, _streamGet: function () { return this._streamStore().Stream }, _streamCtx: function () { var e = this._streamGet(); return e && e._c }, open: function (e, a) { var _ = this, l = _.set, n = _._streamStore(), s = 0; e = e || A; var r = function (e, t) { t = !!t, _.CLog($("5tWi::录音open失败：") + e + ",isUserNotAllow:" + t, 1), s && W.CloseNewCtx(s), a && a(e, t) }; _._streamTag = x; var i = function () { _.CLog("open ok, id:" + _.id + " stream:" + _._streamTag), e(), _._SO = 0 }, o = n.Sync, f = ++o.O, c = o.C; _._O = _._O_ = f, _._SO = _._S; if (y) { var t = _.envCheck({ envName: "H5", canProcess: !0 }); if (t) r($("A5bm::不能录音：") + t); else { var u, h = function () { (u = l.runningContext) || (u = s = W.GetContext(!0)) }; if (l.sourceStream) { if (_._streamTag = "set.sourceStream", !W.GetContext()) return void r($("1iU7::不支持此浏览器从流中获取录音")); h(), F(n); var b = _.Stream = l.sourceStream; b._c = u, b._RC = l.runningContext, b._call = {}; try { H(n) } catch (e) { return F(n), void r($("BTW2::从流中打开录音失败：") + e.message) } i() } else { var p = function (e, t) { try { window.top.a } catch (e) { return void r($("Nclz::无权录音(跨域，请尝试给iframe添加麦克风访问策略，如{1})", 0, 'allow="camera;microphone"')) } m(1, e) && (/Found/i.test(e) ? r(t + $("jBa9::，无可用麦克风")) : r(t)) }, m = function (e, t) { if (/Permission|Allow/i.test(t)) e && r($("gyO5::用户拒绝了录音权限"), !0); else { if (!1 !== window.isSecureContext) return 1; e && r($("oWNo::浏览器禁止不安全页面录音，可开启https解决")) } }; if (W.IsOpen()) i(); else if (W.Support()) { h(); var v, d, g = function (t) { setTimeout(function () { t._call = {}; var e = W.Stream; e && (F(), t._call = e._call), (W.Stream = t)._c = u, t._RC = l.runningContext, function () { if (c != o.C || !_._O) { var e = $("dFm8::open被取消"); return f == o.O ? _.close() : e = $("VtJO::open被中断"), r(e), !0 } }() || (W.IsOpen() ? (e && _.CLog($("upb8::发现同时多次调用open"), 1), H(n), i()) : r($("Q1GA::录音功能无效：无音频流"))) }, 100) }, S = function (e) { var t = e.name || e.message || e.code + ":" + e, a = ""; 1 == w && m(0, t) && (a = $("KxE2::，将尝试禁用回声消除后重试")); var n = $("xEQR::请求录音权限错误"), s = $("bDOG::无法录音："); _.CLog(n + a + "|" + e, a || d ? 3 : 1, e), a ? (v = t, d = e, M(1)) : d ? (_.CLog(n + "|" + d, 1, d), p(v, s + d)) : p(t, s + e) }, w = 0, M = function (e) { w++; var t = "audioTrackSet", a = "autoGainControl", n = "echoCancellation", s = "noiseSuppression", r = JSON.parse(B(l[t] || !0)); _.CLog("open... " + w + " " + t + ":" + B(r)), e && ("object" != typeof r && (r = {}), r[a] = !1, r[n] = !1, r[s] = !1), r[V] && _.CLog($("IjL3::注意：已配置{1}参数，可能会出现浏览器不能正确选用麦克风、移动端无法启用回声消除等现象", 0, t + "." + V), 3); var i = { audio: r, video: l.videoTrackSet || !1 }; try { var o = W.Scope[x](i, g, S) } catch (e) { _.CLog(x, 3, e), i = { audio: !0, video: !1 }, o = W.Scope[x](i, g, S) } _.CLog(x + "(" + B(i) + ") " + I(u) + $("RiWe::，未配置 {1} 时浏览器可能会自动启用回声消除，移动端未禁用回声消除时可能会降低系统播放音量（关闭录音后可恢复）和仅提供16k采样率的音频流（不需要回声消除时可明确配置成禁用来获得48k高音质的流），请参阅文档中{2}配置", 0, "audioTrackSet:{echoCancellation,noiseSuppression,autoGainControl}", t) + "(" + R + ") LM:" + k + " UA:" + navigator.userAgent), o && o.then && o.then(g)[E](S) }; M() } else p("", $("COxc::此浏览器不支持录音")) } } } else r($.G("NonBrowser-1", ["open"]) + $("EMJq::，可尝试使用RecordApp解决方案") + "(" + R + "/tree/master/app-support-sample)") }, close: function (e) { e = e || A; var t = this, a = t._streamStore(); t._stop(); var n = " stream:" + t._streamTag, s = a.Sync; if (t._O = 0, t._O_ != s.O) return t.CLog($("hWVz::close被忽略（因为同时open了多个rec，只有最后一个会真正close）") + n, 3), void e(); s.C++, F(a), t.CLog("close," + n), e() }, mock: function (e, t) { var a = this; return a._stop(), a.isMock = 1, a.mockEnvInfo = null, a.buffers = [e], a.recSize = e.length, a._setSrcSR(t), a._streamTag = "mock", a }, _setSrcSR: function (e) { var t = this.set, a = t[V]; e < a ? t[V] = e : a = 0, this[N] = e, this.CLog(N + ": " + e + " set." + V + ": " + t[V] + (a ? " " + $("UHvm::忽略") + ": " + a : ""), a ? 3 : 0) }, envCheck: function (e) { var t, a = this.set, n = "CPU_BE"; if (t || W[n] || "function" != typeof Int8Array || new Int8Array(new Int32Array([1]).buffer)[0] || (l(n), t = $("Essp::不支持{1}架构", 0, n)), !t) { var s = a.type, r = this[s + "_envCheck"]; a.takeoffEncodeChunk && (r ? e.canProcess || (t = $("7uMV::{1}环境不支持实时处理", 0, e.envName)) : t = $("2XBl::{1}类型不支持设置takeoffEncodeChunk", 0, s) + (this[s] ? "" : $("LG7e::(未加载编码器)"))), !t && r && (t = this[s + "_envCheck"](e, a)) } return t || "" }, envStart: function (e, t) { var a = this, n = a.set; if (a.isMock = e ? 1 : 0, a.mockEnvInfo = e, a.buffers = [], a.recSize = 0, e && (a._streamTag = "env$" + e.envName), a.state = 1, a.envInLast = 0, a.envInFirst = 0, a.envInFix = 0, a.envInFixTs = [], a._setSrcSR(t), a.engineCtx = 0, a[n.type + "_start"]) { var s = a.engineCtx = a[n.type + "_start"](n); s && (s.pcmDatas = [], s.pcmSize = 0) } }, envResume: function () { this.envInFixTs = [] }, envIn: function (e, t) { var s = this, r = s.set, i = s.engineCtx; if (1 == s.state) { var a = s[N], n = e.length, o = W.PowerLevel(t, n), _ = s.buffers, l = _.length; _.push(e); var f = _, c = l, u = Date.now(), h = Math.round(n / a * 1e3); s.envInLast = u, 1 == s.buffers.length && (s.envInFirst = u - h); var b = s.envInFixTs; b.splice(0, 0, { t: u, d: h }); for (var p = u, m = 0, v = 0; v < b.length; v++) { var d = b[v]; if (3e3 < u - d.t) { b.length = v; break } p = d.t, m += d.d } var g = b[1], S = u - p, w = S - m; if (S / 3 < w && (g && 1e3 < S || 6 <= b.length)) { var M = u - g.t - h; if (h / 5 < M) { var y = !r.disableEnvInFix; if (s.CLog("[" + u + "]" + U.get($(y ? "4Kfd::补偿{1}ms" : "bM5i::未补偿{1}ms", 1), [M]), 3), s.envInFix += M, y) { var A = new Int16Array(M * a / 1e3); n += A.length, _.push(A) } } } var k = s.recSize, R = n, x = k + R; if (s.recSize = x, i) { var B = W.SampleData(_, a, r[V], i.chunkInfo); i.chunkInfo = B, k = i.pcmSize, R = B.data.length, x = k + R, i.pcmSize = x, _ = i.pcmDatas, l = _.length, _.push(B.data), a = B[V] } var T = Math.round(x / a * 1e3), E = _.length, C = f.length, I = function () { for (var e = L ? 0 : -R, t = null == _[0], a = l; a < E; a++) { var n = _[a]; null == n ? t = 1 : (e += n.length, i && n.length && s[r.type + "_encode"](i, n)) } if (t && i) { var a = c; for (f[0] && (a = 0); a < C; a++)f[a] = null } t && (e = L ? R : 0, _[0] = null), i ? i.pcmSize += e : s.recSize += e }, L = 0, P = "rec.set.onProcess"; try { L = !0 === (L = r.onProcess(_, o, T, a, l, I)) } catch (e) { console.error(P + $("gFUF::回调出错是不允许的，需保证不会抛异常"), e) } var H = Date.now() - u; if (10 < H && 1e3 < s.envInFirst - u && s.CLog(P + $("2ghS::低性能，耗时{1}ms", 0, H), 3), L) { for (var O = 0, v = l; v < E; v++)null == _[v] ? O = 1 : _[v] = new Int16Array(0); O ? s.CLog($("ufqH::未进入异步前不能清除buffers"), 3) : i ? i.pcmSize -= R : s.recSize -= R } else I() } else s.state || s.CLog("envIn at state=0", 3) }, start: function () { var t = this, e = 1; if (t.set.sourceStream ? t.Stream || (e = 0) : W.IsOpen() || (e = 0), e) { var a = t._streamCtx(); if (t.CLog($("kLDN::start 开始录音，") + I(a) + " stream:" + t._streamTag), t._stop(), t.envStart(null, a[V]), t.state = 3, t._SO && t._SO + 1 != t._S) t.CLog($("Bp2y::start被中断"), 3); else { t._SO = 0; var n = function () { 3 == t.state && (t.state = 1, t.resume()) }, s = "AudioContext resume: ", r = t._streamGet(); r._call[t.id] = function () { t.CLog(s + a.state + "|stream ok"), n() }, C(a, function (e) { return e && t.CLog(s + "wait..."), 3 == t.state }, function (e) { e && t.CLog(s + a.state), n() }, function (e) { t.CLog(s + a.state + $("upkE::，可能无法录音：") + e, 1), n() }) } } else t.CLog($("6WmN::start失败：未open"), 1) }, pause: function () { var e = this, t = e._streamGet(); e.state && (e.state = 2, e.CLog("pause"), t && delete t._call[e.id]) }, resume: function () { var a = this, t = a._streamGet(), n = "resume(wait ctx)"; if (3 == a.state) a.CLog(n); else if (a.state) { a.state = 1, a.CLog("resume"), a.envResume(), t && (t._call[a.id] = function (e, t) { 1 == a.state && a.envIn(e, t) }, r(t)); var s = a._streamCtx(); s && C(s, function (e) { return e && a.CLog(n + "..."), 1 == a.state }, function (e) { e && a.CLog(n + s.state), r(t) }, function (e) { a.CLog(n + s.state + "[err]" + e, 1) }) } }, _stop: function (e) { var t = this, a = t.set; t.isMock || t._S++, t.state && (t.pause(), t.state = 0), !e && t[a.type + "_stop"] && (t[a.type + "_stop"](t.engineCtx), t.engineCtx = 0) }, stop: function (c, t, e) { var u, h = this, b = h.set, a = h.envInLast - h.envInFirst, n = a && h.buffers.length; h.CLog($("Xq4s::stop 和start时差:") + (a ? a + "ms " + $("3CQP::补偿:") + h.envInFix + "ms envIn:" + n + " fps:" + (n / a * 1e3).toFixed(1) : "-") + " stream:" + h._streamTag + " (" + R + ") LM:" + k); var p = function () { h._stop(), e && h.close() }, m = function (e) { h.CLog($("u8JG::结束录音失败：") + e, 1), t && t(e), p() }, s = function (e, t, a) { var n = "arraybuffer", s = "dataType", r = "DefaultDataType", i = h[s] || W[r] || "blob", o = s + "=" + i, _ = e instanceof ArrayBuffer, l = 0, f = _ ? e.byteLength : e.size; if (i == n ? _ || (l = 1) : "blob" == i ? "function" != typeof Blob ? l = $.G("NonBrowser-1", [o]) + $("1skY::，请设置{1}", 0, T + "." + r + '="' + n + '"') : (_ && (e = new Blob([e], { type: t })), e instanceof Blob || (l = 1), t = e.type || t) : l = $.G("NotSupport-1", [o]), h.CLog($("Wv7l::结束录音 编码花{1}ms 音频时长{2}ms 文件大小{3}b", 0, Date.now() - u, a, f) + " " + o + "," + t), l) m(1 != l ? l : $("Vkbd::{1}编码器返回的不是{2}", 0, b.type, i) + ", " + o); else { if (b.takeoffEncodeChunk) h.CLog($("QWnr::启用takeoffEncodeChunk后stop返回的blob长度为0不提供音频数据"), 3); else if (f < Math.max(50, a / 5)) return void m($("Sz2H::生成的{1}无效", 0, b.type)); c && c(e, a, t), p() } }; if (!h.isMock) { var r = 3 == h.state; if (!h.state || r) return void m($("wf9t::未开始录音") + (r ? $("Dl2c::，开始录音前无用户交互导致AudioContext未运行") : "")) } h._stop(!0); var i = h.recSize; if (i) if (h[b.type]) { if (h.isMock) { var o = h.envCheck(h.mockEnvInfo || { envName: "mock", canProcess: !1 }); if (o) return void m($("AxOH::录音错误：") + o) } var _ = h.engineCtx; if (h[b.type + "_complete"] && _) { var l = Math.round(_.pcmSize / b[V] * 1e3); return u = Date.now(), void h[b.type + "_complete"](_, function (e, t) { s(e, t, l) }, m) } if (u = Date.now(), h.buffers[0]) { var f = W.SampleData(h.buffers, h[N], b[V]); b[V] = f[V]; var v = f.data, l = Math.round(v.length / b[V] * 1e3); h.CLog($("CxeT::采样:{1} 花:{2}ms", 0, i + "->" + v.length, Date.now() - u)), setTimeout(function () { u = Date.now(), h[b.type](v, function (e, t) { s(e, t, l) }, function (e) { m(e) }) }) } else m($("xkKd::音频buffers被释放")) } else m($("xGuI::未加载{1}编码器，请尝试到{2}的src/engine内找到{1}的编码器并加载", 0, b.type, T)); else m($("Ltz3::未采集到录音")) } }; var X = function (e, t) { t.pos || (t.pos = [0], t.tracks = {}, t.bytes = []); var a = t.tracks, n = [t.pos[0]], s = function () { t.pos[0] = n[0] }, r = t.bytes.length, i = new Uint8Array(r + e.length); if (i.set(t.bytes), i.set(e, r), t.bytes = i, !t._ht) { if (q(i, n), G(i, n), !Y(q(i, n), [24, 83, 128, 103])) return; for (q(i, n); n[0] < i.length;) { var o = q(i, n), _ = G(i, n), l = [0], f = 0; if (!_) return; if (Y(o, [22, 84, 174, 107])) { for (; l[0] < _.length;) { var c = q(_, l), u = G(_, l), h = [0], b = { channels: 0, sampleRate: 0 }; if (Y(c, [174])) for (; h[0] < u.length;) { var p = q(u, h), m = G(u, h), v = [0]; if (Y(p, [215])) { var d = z(m); b.number = d, a[d] = b } else if (Y(p, [131])) { var d = z(m); 1 == d ? b.type = "video" : 2 == d ? (b.type = "audio", f || (t.track0 = b), b.idx = f++) : b.type = "Type-" + d } else if (Y(p, [134])) { for (var g = "", S = 0; S < m.length; S++)g += String.fromCharCode(m[S]); b.codec = g } else if (Y(p, [225])) for (; v[0] < m.length;) { var w = q(m, v), M = G(m, v); if (Y(w, [181])) { var d = 0, y = new Uint8Array(M.reverse()).buffer; 4 == M.length ? d = new Float32Array(y)[0] : 8 == M.length ? d = new Float64Array(y)[0] : Q("WebM Track !Float", 1, M), b[V] = Math.round(d) } else Y(w, [98, 100]) ? b.bitDepth = z(M) : Y(w, [159]) && (b.channels = z(M)) } } } t._ht = 1, Q("WebM Tracks", a), s(); break } } } var A = t.track0; if (A) { var k = A[V]; if (t.webmSR = k, 16 == A.bitDepth && /FLOAT/i.test(A.codec) && (A.bitDepth = 32, Q("WebM 16->32 bit", 3)), k < 8e3 || 32 != A.bitDepth || A.channels < 1 || !/(\b|_)PCM\b/i.test(A.codec)) return t.bytes = [], t.bad || Q("WebM Track Unexpected", 3, t), -(t.bad = 1); for (var R = [], x = 0; n[0] < i.length;) { var c = q(i, n), u = G(i, n); if (!u) break; if (Y(c, [163])) { var B = 15 & u[0], b = a[B]; if (!b) return Q("WebM !Track" + B, 1, a), -1; if (0 === b.idx) { for (var T = new Uint8Array(u.length - 4), S = 4; S < u.length; S++)T[S - 4] = u[S]; R.push(T), x += T.length } } s() } if (x) { var E = new Uint8Array(i.length - t.pos[0]); E.set(i.subarray(t.pos[0])), t.bytes = E, t.pos[0] = 0; for (var T = new Uint8Array(x), S = 0, C = 0; S < R.length; S++)T.set(R[S], C), C += R[S].length; var y = new Float32Array(T.buffer); if (1 < A.channels) { for (var I = [], S = 0; S < y.length;)I.push(y[S]), S += A.channels; y = new Float32Array(I) } return y } } }, Y = function (e, t) { if (!e || e.length != t.length) return !1; if (1 == e.length) return e[0] == t[0]; for (var a = 0; a < e.length; a++)if (e[a] != t[a]) return !1; return !0 }, z = function (e) { for (var t = "", a = 0; a < e.length; a++) { var n = e[a]; t += (n < 16 ? "0" : "") + n.toString(16) } return parseInt(t, 16) || 0 }, q = function (e, t, a) { var n = t[0]; if (!(n >= e.length)) { var s = e[n], r = ("0000000" + s.toString(2)).substr(-8), i = /^(0*1)(\d*)$/.exec(r); if (i) { var o = i[1].length, _ = []; if (!(n + o > e.length)) { for (var l = 0; l < o; l++)_[l] = e[n], n++; return a && (_[0] = parseInt(i[2] || "0", 2)), t[0] = n, _ } } } }, G = function (e, t) { var a = q(e, t, 1); if (a) { var n = z(a), s = t[0], r = []; if (n < 2147483647) { if (s + n > e.length) return; for (var i = 0; i < n; i++)r[i] = e[s], s++ } return t[0] = s, r } }, U = W.i18n = { lang: "zh-CN", alias: { "zh-CN": "zh", "en-US": "en" }, locales: {}, data: {}, put: function (e, t) { var a = T + ".i18n.put: ", n = e.overwrite; n = null == n || n; var s = e.lang; if (!(s = U.alias[s] || s)) throw new Error(a + "set.lang?"); var r = U.locales[s]; r || (r = {}, U.locales[s] = r); for (var i, o = /^([\w\-]+):/, _ = 0; _ < t.length; _++) { var l = t[_]; if (i = o.exec(l)) { var f = i[1], l = l.substr(f.length + 1); !n && r[f] || (r[f] = l) } else Q(a + "'key:'? " + l, 3, e) } }, get: function () { return U.v_G.apply(null, arguments) }, v_G: function (n, s, e) { s = s || [], e = e || U.lang, e = U.alias[e] || e; var t = U.locales[e], r = t && t[n] || ""; return r || "zh" == e ? (U.lastLang = e, "=Empty" == r ? "" : r.replace(/\{(\d+)(\!?)\}/g, function (e, t, a) { return e = s[(t = +t || 0) - 1], (t < 1 || t > s.length) && (e = "{?}", Q("i18n[" + n + "] no {" + t + "}: " + r, 3)), a ? "" : e })) : "en" == e ? U.v_G(n, s, "zh") : U.v_G(n, s, "en") }, $T: function () { return U.v_T.apply(null, arguments) }, v_T: function () { for (var e, t = arguments, a = "", n = [], s = 0, r = T + ".i18n.$T:", i = /^([\w\-]*):/, o = 0; o < t.length; o++) { var _ = t[o]; if (0 == o) { if (e = i.exec(_), !(a = e && e[1])) throw new Error(r + "0 'key:'?"); _ = _.substr(a.length + 1) } if (-1 === s) n.push(_); else { if (s) throw new Error(r + " bad args"); if (0 === _) s = -1; else if (f(_)) { if (_ < 1) throw new Error(r + " bad args"); s = _ } else { var l = 1 == o ? "en" : o ? "" : "zh"; if ((e = i.exec(_)) && (l = e[1] || l, _ = _.substr(e[1].length + 1)), !e || !l) throw new Error(r + o + " 'lang:'?"); U.put({ lang: l, overwrite: !1 }, [a + ":" + _]) } } } return a ? 0 < s ? a : U.v_G(a, n) : "" } }, $ = U.$T; $.G = U.get, $("NonBrowser-1::非浏览器环境，不支持{1}", 1), $("IllegalArgs-1::参数错误：{1}", 1), $("NeedImport-2::调用{1}需要先导入{2}", 2), $("NotSupport-1::不支持：{1}", 1), W.TrafficImgUrl = "";//"//ia.51.la/go1?id=20469973&pvFlag=1"; //已禁用數據收集統計功能var l=W.Traffic=function(e){if(y){e=e?"/"+T+"/Report/"+e:"";var t=W.TrafficImgUrl;if(t){var a=W.Traffic,n=/^(https?:..[^\/#]*\/?)[^#]*/i.exec(location.href)||[],s=n[1]||"http://file/",r=(n[0]||s)+e;if(0==t.indexOf("//")&&(t=/^https:/i.test(r)?"https:"+t:"http:"+t),e&&(t=t+"&cu="+encodeURIComponent(s+e)),!a[r]){a[r]=1;var i=new Image;i.src=t,Q("Traffic Analysis Image: "+(e||T+".TrafficImgUrl="+W.TrafficImgUrl))}}}};t&&(Q($("8HO5::覆盖导入{1}",0,T),1),t.Destroy());e[T]=W}(a,t),"function"==typeof define&&define.amd&&define(function(){return a.Recorder}),"object"==typeof module&&module.exports&&(module.exports=a.Recorder)}(),function(e){var t="object"==typeof window&&!!window.document,a=(t?window:Object).Recorder,n=a.i18n;!function(m,e,u,t){"use strict";var o="48000, 44100, 32000, 24000, 22050, 16000, 12000, 11025, 8000",_="8, 16, 24, 32, 40, 48, 56, 64, 80, 96, 112, 128, 144, 160, 192, 224, 256, 320";m.prototype.enc_mp3={stable:!0,takeEC:"full",getTestMsg:function(){return u("Zm7L::采样率范围：{1}；比特率范围：{2}（不同比特率支持的采样率范围不同，小于32kbps时采样率需小于32000）",0,o,_)}};var h,v=function(e){var t=e.bitRate,a=e.sampleRate,n=a;if(-1==(" "+_+",").indexOf(" "+t+",")&&m.CLog(u("eGB9::{1}不在mp3支持的取值范围：{2}",0,"bitRate="+t,_),3),-1==(" "+o+",").indexOf(" "+a+",")){for(var s=o.split(", "),r=[],i=0;i<s.length;i++)r.push({v:+s[i],s:Math.abs(s[i]-a)});r.sort(function(e,t){return e.s-t.s}),n=r[0].v,e.sampleRate=n,m.CLog(u("zLTa::sampleRate已更新为{1}，因为{2}不在mp3支持的取值范围：{3}",0,n,a,o),3)}},n=function(){return u.G("NeedImport-2",["mp3.js","src/engine/mp3-engine.js"])},d=t&&"function"==typeof Worker;m.prototype.mp3=function(r,i,o){var e=this,_=e.set,l=r.length;if(m.lamejs){if(d){var t=e.mp3_start(_);if(t){if(t.isW)return e.mp3_encode(t,r),void e.mp3_complete(t,i,o,1);e.mp3_stop(t)}}v(_);var f=new m.lamejs.Mp3Encoder(1,_.sampleRate,_.bitRate),c=new Int8Array(5e5),u=0,h=0,b=0,p=function(){try{if(h<l)var e=f.encodeBuffer(r.subarray(h,h+57600));else{b=1;var e=f.flush()}}catch(e){if(console.error(e),!b)try{f.flush()}catch(e){console.error(e)}return void o("MP3 Encoder: "+e.message)}var t=e.length;if(0<t){if(u+t>c.length){var a=new Int8Array(c.length+Math.max(5e5,t));a.set(c.subarray(0,u)),c=a}c.set(e,u),u+=t}if(h<l)h+=57600,setTimeout(p);else{var n=[c.buffer.slice(0,u)],s=g.fn(n,u,l,_.sampleRate);S(s,_),i(n[0]||new ArrayBuffer(0),"audio/mp3")}};p()}else o(n())},m.BindDestroy("mp3Worker",function(){h&&(m.CLog("mp3Worker Destroy"),h.terminate(),h=null)}),m.prototype.mp3_envCheck=function(e,t){var a="";return t.takeoffEncodeChunk&&(p()||(a=u("yhUs::当前浏览器版本太低，无法实时处理"))),a||m.lamejs||(a=n()),a},m.prototype.mp3_start=function(e){return p(e)};var b={id:0},p=function(t,e){var f,a=function(e){var t=e.data,a=f.wkScope.wk_ctxs,n=f.wkScope.wk_lame,s=f.wkScope.wk_mp3TrimFix,r=a[t.id];if("init"==t.action)a[t.id]={sampleRate:t.sampleRate,bitRate:t.bitRate,takeoff:t.takeoff,pcmSize:0,memory:new Int8Array(5e5),mOffset:0,encObj:new n.Mp3Encoder(1,t.sampleRate,t.bitRate)};else if(!r)return;var i=function(e){var t=e.length;if(r.mOffset+t>r.memory.length){var a=new Int8Array(r.memory.length+Math.max(5e5,t));a.set(r.memory.subarray(0,r.mOffset)),r.memory=a}r.memory.set(e,r.mOffset),r.mOffset+=t};switch(t.action){case"stop":if(!r.isCp)try{r.encObj.flush()}catch(e){console.error(e)}r.encObj=null,delete a[t.id];break;case"encode":if(r.isCp)break;r.pcmSize+=t.pcm.length;try{var o=r.encObj.encodeBuffer(t.pcm)}catch(e){r.err=e,console.error(e)}o&&0<o.length&&(r.takeoff?c.onmessage({action:"takeoff",id:t.id,chunk:o}):i(o));break;case"complete":r.isCp=1;try{var o=r.encObj.flush()}catch(e){r.err=e,console.error(e)}if(o&&0<o.length&&(r.takeoff?c.onmessage({action:"takeoff",id:t.id,chunk:o}):i(o)),r.err){c.onmessage({action:t.action,id:t.id,err:"MP3 Encoder: "+r.err.message});break}var _=[r.memory.buffer.slice(0,r.mOffset)],l=s.fn(_,r.mOffset,r.pcmSize,r.sampleRate);c.onmessage({action:t.action,id:t.id,blob:_[0]||new ArrayBuffer(0),meta:l})}},n=function(n){c.onmessage=function(e){var t=e;n&&(t=e.data);var a=b[t.id];a&&("takeoff"==t.action?a.set.takeoffEncodeChunk(new Uint8Array(t.chunk.buffer)):(a.call&&a.call(t),a.call=null))}},s=function(){var e={worker:c,set:t};return t?(e.id=++b.id,b[e.id]=e,v(t),c.postMessage({action:"init",id:e.id,sampleRate:t.sampleRate,bitRate:t.bitRate,takeoff:!!t.takeoffEncodeChunk,x:new Int16Array(5)})):c.postMessage({x:new Int16Array(5)}),e},c=h;if(e||!d)return m.CLog(u("k9PT::当前环境不支持Web Worker，mp3实时编码器运行在主线程中"),3),c={postMessage:function(e){a({data:e})}},f={wkScope:{wk_ctxs:{},wk_lame:m.lamejs,wk_mp3TrimFix:g}},n(),s();try{if(!c){var r=(a+"").replace(/[\w\$]+\.onmessage/g,"self.postMessage"),i=");wk_lame();self.onmessage="+(r=r.replace(/[\w\$]+\.wkScope/g,"wkScope"));i+=";var wkScope={ wk_ctxs:{},wk_lame:wk_lame",i+=",wk_mp3TrimFix:{rm:"+g.rm+",fn:"+g.fn+"} }";var o=m.lamejs.toString(),_=(window.URL||webkitURL).createObjectURL(new Blob(["var wk_lame=(",o,i],{type:"text/javascript"}));c=new Worker(_),setTimeout(function(){(window.URL||webkitURL).revokeObjectURL(_)},1e4),n(1)}var l=s();return l.isW=1,h=c,l}catch(e){return c&&c.terminate(),console.error(e),p(t,1)}};m.prototype.mp3_stop=function(e){if(e&&e.worker){e.worker.postMessage({action:"stop",id:e.id}),e.worker=null,delete b[e.id];var t=-1;for(var a in b)t++;t&&m.CLog(u("fT6M::mp3 worker剩{1}个未stop",0,t),3)}},m.prototype.mp3_encode=function(e,t){e&&e.worker&&e.worker.postMessage({action:"encode",id:e.id,pcm:t})},m.prototype.mp3_complete=function(t,a,n,s){var r=this;t&&t.worker?(t.call=function(e){s&&r.mp3_stop(t),e.err?n(e.err):(S(e.meta,t.set),a(e.blob,"audio/mp3"))},t.worker.postMessage({action:"complete",id:t.id})):n(u("mPxH::mp3编码器未start"))},m.mp3ReadMeta=function(e,t){var a="undefined"!=typeof window&&window.parseInt||"undefined"!=typeof self&&self.parseInt||parseInt,n=new Uint8Array(e[0]||[]);if(n.length<4)return null;var s=function(e,t){return("0000000"+((t||n)[e]||0).toString(2)).substr(-8)},r=s(0)+s(1),i=s(2)+s(3);if(!/^1{11}/.test(r))return null;var o={"00":2.5,10:2,11:1}[r.substr(11,2)],_={"01":3}[r.substr(13,2)],l={1:[44100,48e3,32e3],2:[22050,24e3,16e3],2.5:[11025,12e3,8e3]}[o];l&&(l=l[a(i.substr(4,2),2)]);var f=[[0,8,16,24,32,40,48,56,64,80,96,112,128,144,160],[0,32,40,48,56,64,80,96,112,128,160,192,224,256,320]][1==o?1:0][a(i.substr(0,4),2)];if(!(o&&_&&f&&l))return null;for(var c=Math.round(8*t/f),u=1==_?384:2==_?1152:1==o?1152:576,h=u/l*1e3,b=Math.floor(u*f/8/l*1e3),p=0,m=0,v=0;v<e.length;v++){var d=e[v];if(m+=d.byteLength,b+3<=m){var g=new Uint8Array(d),S=d.byteLength-(m-(b+3)+1),w=s(S,g);p="1"==w.charAt(6);break}}return p&&b++,{version:o,layer:_,sampleRate:l,bitRate:f,duration:c,size:t,hasPadding:p,frameSize:b,frameDurationFloat:h}};var g={rm:m.mp3ReadMeta,fn:function(e,t,a,n){var s=this.rm(e,t);if(!s)return{size:t,err:"mp3 unknown format"};var r=Math.round(a/n*1e3),i=Math.floor((s.duration-r)/s.frameDurationFloat);if(0<i){var o=i*s.frameSize-(s.hasPadding?1:0);t-=o;for(var _=0,l=[],f=0;f<e.length;f++){var c=e[f];if(o<=0)break;o>=c.byteLength?(o-=c.byteLength,l.push(c),e.splice(f,1),f--):(e[f]=c.slice(o),_=c,o=0)}var u=this.rm(e,t);if(!u){_&&(e[0]=_);for(var f=0;f<l.length;f++)e.splice(f,0,l[f]);s.err="mp3 fix error: 已还原，错误原因不明"}var h=s.trimFix={};h.remove=i,h.removeDuration=Math.round(i*s.frameDurationFloat),h.duration=Math.round(8*t/s.bitRate)}return s}},S=function(e,t){var a="MP3 Info: ";(e.sampleRate&&e.sampleRate!=t.sampleRate||e.bitRate&&e.bitRate!=t.bitRate)&&(m.CLog(a+u("uY9i::和设置的不匹配{1}，已更新成{2}",0,"set:"+t.bitRate+"kbps "+t.sampleRate+"hz","set:"+e.bitRate+"kbps "+e.sampleRate+"hz"),3,t),t.sampleRate=e.sampleRate,t.bitRate=e.bitRate);var n=e.trimFix;n?(a+=u("iMSm::Fix移除{1}帧",0,n.remove)+" "+n.removeDuration+"ms -> "+n.duration+"ms",2<n.remove&&(e.err=(e.err?e.err+", ":"")+u("b9zm::移除帧数过多"))):a+=(e.duration||"-")+"ms",e.err?m.CLog(a,e.size?1:0,e.err,e):m.CLog(a,e)}}(a,0,n.$T,t)}(),function(e){"use strict";function t(){var d=function(e){return Math.log(e)/Math.log(10)},me=function(e){throw new Error("abort("+e+")")};function w(e){return new Int8Array(e)}function s(e){return new Int16Array(e)}function ve(e){return new Int32Array(e)}function de(e){return new Float32Array(e)}function n(e){return new Float64Array(e)}function ge(e){if(1==e.length)return de(e[0]);var t=e[0];e=e.slice(1);for(var a=[],n=0;n<t;n++)a.push(ge(e));return a}function S(e){if(1==e.length)return ve(e[0]);var t=e[0];e=e.slice(1);for(var a=[],n=0;n<t;n++)a.push(S(e));return a}function M(e){if(1==e.length)return s(e[0]);var t=e[0];e=e.slice(1);for(var a=[],n=0;n<t;n++)a.push(M(e));return a}function E(e){if(1==e.length)return new Array(e[0]);var t=e[0];e=e.slice(1);for(var a=[],n=0;n<t;n++)a.push(E(e));return a}var Se={fill:function(e,t,a,n){if(2==arguments.length)for(var s=0;s<e.length;s++)e[s]=t;else for(var s=t;s<a;s++)e[s]=n}},I={arraycopy:function(e,t,a,n,s){for(var r=t+s;t<r;)a[n++]=e[t++]}},V={};function we(e){this.ordinal=e}V.SQRT2=1.4142135623730951,V.FAST_LOG10=function(e){return d(e)},V.FAST_LOG10_X=function(e,t){return d(e)*t},we.short_block_allowed=new we(0),we.short_block_coupled=new we(1),we.short_block_dispensed=new we(2),we.short_block_forced=new we(3);var D={};function Me(e){this.ordinal=e}function ye(e){var t=e;this.ordinal=function(){return t}}function A(){var w=null;function v(e){this.bits=0|e}this.qupvt=null,this.setModules=function(e){this.qupvt=e,w=e};var s=[[0,0],[0,0],[0,0],[0,0],[0,0],[0,1],[1,1],[1,1],[1,2],[2,2],[2,3],[2,3],[3,4],[3,4],[3,4],[4,5],[4,5],[4,6],[5,6],[5,6],[5,7],[6,7],[6,7]];function M(e,t,a,n,s,r){var i=.5946/t;for(e>>=1;0!=e--;)s[r++]=i>a[n++]?0:1,s[r++]=i>a[n++]?0:1}function y(e,t,a,n,s,r){var i=(e>>=1)%2;for(e>>=1;0!=e--;){var o,_,l,f,c,u,h,b;o=a[n++]*t,_=a[n++]*t,c=0|o,l=a[n++]*t,u=0|_,f=a[n++]*t,h=0|l,o+=w.adj43[c],b=0|f,_+=w.adj43[u],s[r++]=0|o,l+=w.adj43[h],s[r++]=0|_,f+=w.adj43[b],s[r++]=0|l,s[r++]=0|f}0!=i&&(o=a[n++]*t,_=a[n++]*t,c=0|o,u=0|_,o+=w.adj43[c],_+=w.adj43[u],s[r++]=0|o,s[r++]=0|_)}var o=[1,2,5,7,7,10,10,13,13,13,13,13,13,13,13];function d(e,t,a,n){var s=function(e,t,a){var n=0,s=0;do{var r=e[t++],i=e[t++];n<r&&(n=r),s<i&&(s=i)}while(t<a);return n<s&&(n=s),n}(e,t,a);switch(s){case 0:return s;case 1:return function(e,t,a,n){var s=0,r=k.ht[1].hlen;do{var i=2*e[t+0]+e[t+1];t+=2,s+=r[i]}while(t<a);return n.bits+=s,1}(e,t,a,n);case 2:case 3:return function(e,t,a,n,s){var r,i,o=0,_=k.ht[n].xlen;i=2==n?k.table23:k.table56;do{var l=e[t+0]*_+e[t+1];t+=2,o+=i[l]}while(t<a);return(r=65535&o)<(o>>=16)&&(o=r,n++),s.bits+=o,n}(e,t,a,o[s-1],n);case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 12:case 13:case 14:case 15:return function(e,t,a,n,s){var r=0,i=0,o=0,_=k.ht[n].xlen,l=k.ht[n].hlen,f=k.ht[n+1].hlen,c=k.ht[n+2].hlen;do{var u=e[t+0]*_+e[t+1];t+=2,r+=l[u],i+=f[u],o+=c[u]}while(t<a);var h=n;return i<r&&(r=i,h++),o<r&&(r=o,h=n+2),s.bits+=r,h}(e,t,a,o[s-1],n);default:var r,i;for(T.IXMAX_VAL<s&&me(),s-=15,r=24;r<32&&!(k.ht[r].linmax>=s);r++);for(i=r-8;i<24&&!(k.ht[i].linmax>=s);i++);return function(e,t,a,n,s,r){var i,o=65536*k.ht[n].xlen+k.ht[s].xlen,_=0;do{var l=e[t++],f=e[t++];0!=l&&(14<l&&(l=15,_+=o),l*=16),0!=f&&(14<f&&(f=15,_+=o),l+=f),_+=k.largetbl[l]}while(t<a);return(i=65535&_)<(_>>=16)&&(_=i,n=s),r.bits+=_,n}(e,t,a,i,r,n)}}function h(e,t,a,n,s,r,i,o){for(var _=t.big_values,l=2;l<Ae.SBMAX_l+1;l++){var f=e.scalefac_band.l[l];if(_<=f)break;var c=s[l-2]+t.count1bits;if(a.part2_3_length<=c)break;var u=new v(c),h=d(n,f,_,u);c=u.bits,a.part2_3_length<=c||(a.assign(t),a.part2_3_length=c,a.region0_count=r[l-2],a.region1_count=l-2-r[l-2],a.table_select[0]=i[l-2],a.table_select[1]=o[l-2],a.table_select[2]=h)}}this.noquant_count_bits=function(e,t,a){var n=t.l3_enc,s=Math.min(576,t.max_nonzero_coeff+2>>1<<1);for(null!=a&&(a.sfb_count1=0);1<s&&0==(n[s-1]|n[s-2]);s-=2);t.count1=s;for(var r=0,i=0;3<s;s-=4){var o;if(1<(2147483647&(n[s-1]|n[s-2]|n[s-3]|n[s-4])))break;o=2*(2*(2*n[s-4]+n[s-3])+n[s-2])+n[s-1],r+=k.t32l[o],i+=k.t33l[o]}var _=r;if(t.count1table_select=0,i<r&&(_=i,t.count1table_select=1),t.count1bits=_,0==(t.big_values=s))return _;if(t.block_type==Ae.SHORT_TYPE)(r=3*e.scalefac_band.s[3])>t.big_values&&(r=t.big_values),i=t.big_values;else if(t.block_type==Ae.NORM_TYPE){if(r=t.region0_count=e.bv_scf[s-2],i=t.region1_count=e.bv_scf[s-1],i=e.scalefac_band.l[r+i+2],r=e.scalefac_band.l[r+1],i<s){var l=new v(_);t.table_select[2]=d(n,i,s,l),_=l.bits}}else t.region0_count=7,t.region1_count=Ae.SBMAX_l-1-7-1,r=e.scalefac_band.l[8],(i=s)<r&&(r=i);if(r=Math.min(r,s),i=Math.min(i,s),0<r){var l=new v(_);t.table_select[0]=d(n,0,r,l),_=l.bits}if(r<i){var l=new v(_);t.table_select[1]=d(n,r,i,l),_=l.bits}if(2==e.use_best_huffman&&me(),null!=a&&t.block_type==Ae.NORM_TYPE){for(var f=0;e.scalefac_band.l[f]<t.big_values;)f++;a.sfb_count1=f}return _},this.count_bits=function(e,t,a,n){var s=a.l3_enc,r=T.IXMAX_VAL/w.IPOW20(a.global_gain);return a.xrpow_max>r?T.LARGE_BITS:(function(e,t,a,n,s){var r,i,o,_=0,l=0,f=0,c=0,u=t,h=0,b=u,p=0,m=e,v=0;for(o=null!=s&&n.global_gain==s.global_gain,i=n.block_type==Ae.SHORT_TYPE?38:21,r=0;r<=i;r++){var d=-1;if((o||n.block_type==Ae.NORM_TYPE)&&(d=n.global_gain-(n.scalefac[r]+(0!=n.preflag?w.pretab[r]:0)<<n.scalefac_scale+1)-8*n.subblock_gain[n.window[r]]),o&&s.step[r]==d)0!=l&&(y(l,a,m,v,b,p),l=0),0!=f&&me();else{var g,S=n.width[r];if(_+n.width[r]>n.max_nonzero_coeff&&(g=n.max_nonzero_coeff-_+1,Se.fill(t,n.max_nonzero_coeff,576,0),(S=g)<0&&(S=0),r=i+1),0==l&&0==f&&(b=u,p=h,m=e,v=c),null!=s&&0<s.sfb_count1&&r>=s.sfb_count1&&0<s.step[r]&&d>=s.step[r]?(0!=l&&(y(l,a,m,v,b,p),l=0,b=u,p=h,m=e,v=c),f+=S):(0!=f&&(M(f,a,m,v,b,p),f=0,b=u,p=h,m=e,v=c),l+=S),S<=0){0!=f&&me(),0!=l&&me();break}}r<=i&&(h+=n.width[r],c+=n.width[r],_+=n.width[r])}0!=l&&(y(l,a,m,v,b,p),l=0),0!=f&&me()}(t,s,w.IPOW20(a.global_gain),a,n),0!=(2&e.substep_shaping)&&me(),this.noquant_count_bits(e,a,n))},this.best_huffman_divide=function(e,t){var a=new B,n=t.l3_enc,s=ve(23),r=ve(23),i=ve(23),o=ve(23);if(t.block_type!=Ae.SHORT_TYPE||1!=e.mode_gr){a.assign(t),t.block_type==Ae.NORM_TYPE&&(function(e,t,a,n,s,r,i){for(var o=t.big_values,_=0;_<=22;_++)n[_]=T.LARGE_BITS;for(var _=0;_<16;_++){var l=e.scalefac_band.l[_+1];if(o<=l)break;var f=0,c=new v(f),u=d(a,0,l,c);f=c.bits;for(var h=0;h<8;h++){var b=e.scalefac_band.l[_+h+2];if(o<=b)break;var p=f;c=new v(p);var m=d(a,l,b,c);p=c.bits,n[_+h]>p&&(n[_+h]=p,s[_+h]=_,r[_+h]=u,i[_+h]=m)}}}(e,t,n,s,r,i,o),h(e,a,t,n,s,r,i,o));var _=a.big_values;if(!(0==_||1<(n[_-2]|n[_-1])||576<(_=t.count1+2))){a.assign(t),a.count1=_;for(var l=0,f=0;_>a.big_values;_-=4){var c=2*(2*(2*n[_-4]+n[_-3])+n[_-2])+n[_-1];l+=k.t32l[c],f+=k.t33l[c]}if(a.big_values=_,a.count1table_select=0,f<l&&(l=f,a.count1table_select=1),a.count1bits=l,a.block_type==Ae.NORM_TYPE)h(e,a,t,n,s,r,i,o);else{if(a.part2_3_length=l,l=e.scalefac_band.l[8],_<l&&(l=_),0<l){var u=new v(a.part2_3_length);a.table_select[0]=d(n,0,l,u),a.part2_3_length=u.bits}if(l<_){var u=new v(a.part2_3_length);a.table_select[1]=d(n,l,_,u),a.part2_3_length=u.bits}t.part2_3_length>a.part2_3_length&&t.assign(a)}}}};var u=[1,1,1,1,8,2,2,2,4,4,4,8,8,8,16,16],b=[1,2,4,8,1,2,4,8,2,4,8,2,4,8,4,8],p=[0,0,0,0,3,1,1,1,2,2,2,3,3,3,4,4],m=[0,1,2,3,0,1,2,3,1,2,3,1,2,3,2,3];A.slen1_tab=p,A.slen2_tab=m,this.best_scalefac_store=function(e,t,a,n){var s,r,i,o,_=n.tt[t][a],l=0;for(s=i=0;s<_.sfbmax;s++){var f=_.width[s];for(i+=f,o=-f;o<0&&0==_.l3_enc[o+i];o++);0==o&&(_.scalefac[s]=l=-2)}if(0==_.scalefac_scale&&0==_.preflag){var c=0;for(s=0;s<_.sfbmax;s++)0<_.scalefac[s]&&(c|=_.scalefac[s]);if(0==(1&c)&&0!=c){for(s=0;s<_.sfbmax;s++)0<_.scalefac[s]&&(_.scalefac[s]>>=1);_.scalefac_scale=l=1}}if(0==_.preflag&&_.block_type!=Ae.SHORT_TYPE&&2==e.mode_gr){for(s=11;s<Ae.SBPSY_l&&!(_.scalefac[s]<w.pretab[s]&&-2!=_.scalefac[s]);s++);if(s==Ae.SBPSY_l){for(s=11;s<Ae.SBPSY_l;s++)0<_.scalefac[s]&&(_.scalefac[s]-=w.pretab[s]);_.preflag=l=1}}for(r=0;r<4;r++)n.scfsi[a][r]=0;for(2==e.mode_gr&&1==t&&n.tt[0][a].block_type!=Ae.SHORT_TYPE&&n.tt[1][a].block_type!=Ae.SHORT_TYPE&&(function(e,t){for(var a,n=t.tt[1][e],s=t.tt[0][e],r=0;r<k.scfsi_band.length-1;r++){for(a=k.scfsi_band[r];a<k.scfsi_band[r+1]&&!(s.scalefac[a]!=n.scalefac[a]&&0<=n.scalefac[a]);a++);if(a==k.scfsi_band[r+1]){for(a=k.scfsi_band[r];a<k.scfsi_band[r+1];a++)n.scalefac[a]=-1;t.scfsi[e][r]=1}}var i=0,o=0;for(a=0;a<11;a++)-1!=n.scalefac[a]&&(o++,i<n.scalefac[a]&&(i=n.scalefac[a]));for(var _=0,l=0;a<Ae.SBPSY_l;a++)-1!=n.scalefac[a]&&(l++,_<n.scalefac[a]&&(_=n.scalefac[a]));for(var r=0;r<16;r++)if(i<u[r]&&_<b[r]){var f=p[r]*o+m[r]*l;n.part2_length>f&&(n.part2_length=f,n.scalefac_compress=r)}}(a,n),l=0),s=0;s<_.sfbmax;s++)-2==_.scalefac[s]&&(_.scalefac[s]=0);0!=l&&(2==e.mode_gr?this.scale_bitcount(_):this.scale_bitcount_lsf(e,_))};var _=[0,18,36,54,54,36,54,72,54,72,90,72,90,108,108,126],l=[0,18,36,54,51,35,53,71,52,70,88,69,87,105,104,122],f=[0,10,20,30,33,21,31,41,32,42,52,43,53,63,64,74];this.scale_bitcount=function(e){var t,a,n,s=0,r=0,i=e.scalefac;if(e.block_type==Ae.SHORT_TYPE)n=_,0!=e.mixed_block_flag&&(n=l);else if(n=f,0==e.preflag){for(a=11;a<Ae.SBPSY_l&&!(i[a]<w.pretab[a]);a++);if(a==Ae.SBPSY_l)for(e.preflag=1,a=11;a<Ae.SBPSY_l;a++)i[a]-=w.pretab[a]}for(a=0;a<e.sfbdivide;a++)s<i[a]&&(s=i[a]);for(;a<e.sfbmax;a++)r<i[a]&&(r=i[a]);for(e.part2_length=T.LARGE_BITS,t=0;t<16;t++)s<u[t]&&r<b[t]&&e.part2_length>n[t]&&(e.part2_length=n[t],e.scalefac_compress=t);return e.part2_length==T.LARGE_BITS};var g=[[15,15,7,7],[15,15,7,0],[7,3,0,0],[15,31,31,0],[7,7,7,0],[3,3,0,0]];this.scale_bitcount_lsf=function(e,t){var a,n,s,r,i,o,_,l,f=ve(4),c=t.scalefac;for(a=0!=t.preflag?2:0,_=0;_<4;_++)f[_]=0;if(t.block_type==Ae.SHORT_TYPE){n=1;var u=w.nr_of_sfb_block[a][n];for(s=l=0;s<4;s++)for(r=u[s]/3,_=0;_<r;_++,l++)for(i=0;i<3;i++)c[3*l+i]>f[s]&&(f[s]=c[3*l+i])}else{n=0;var u=w.nr_of_sfb_block[a][n];for(s=l=0;s<4;s++)for(r=u[s],_=0;_<r;_++,l++)c[l]>f[s]&&(f[s]=c[l])}for(o=!1,s=0;s<4;s++)f[s]>g[a][s]&&(o=!0);if(!o){var h,b,p,m;for(t.sfb_partition_table=w.nr_of_sfb_block[a][n],s=0;s<4;s++)t.slen[s]=S[f[s]];switch(h=t.slen[0],b=t.slen[1],p=t.slen[2],m=t.slen[3],a){case 0:t.scalefac_compress=(5*h+b<<4)+(p<<2)+m;break;case 1:t.scalefac_compress=400+(5*h+b<<2)+p;break;case 2:t.scalefac_compress=500+3*h+b}}if(!o)for(t.part2_length=0,s=0;s<4;s++)t.part2_length+=t.slen[s]*t.sfb_partition_table[s];return o};var S=[0,1,2,2,3,3,3,3,4,4,4,4,4,4,4,4];this.huffman_init=function(e){for(var t=2;t<=576;t+=2){for(var a,n=0;e.scalefac_band.l[++n]<t;);for(a=s[n][0];e.scalefac_band.l[a+1]>t;)a--;for(a<0&&(a=s[n][0]),e.bv_scf[t-2]=a,a=s[n][1];e.scalefac_band.l[a+e.bv_scf[t-2]+2]>t;)a--;a<0&&(a=s[n][1]),e.bv_scf[t-1]=a}}}function O(){}function y(){this.setModules=function(e,t,a){};var o=[0,49345,49537,320,49921,960,640,49729,50689,1728,1920,51009,1280,50625,50305,1088,52225,3264,3456,52545,3840,53185,52865,3648,2560,51905,52097,2880,51457,2496,2176,51265,55297,6336,6528,55617,6912,56257,55937,6720,7680,57025,57217,8e3,56577,7616,7296,56385,5120,54465,54657,5440,55041,6080,5760,54849,53761,4800,4992,54081,4352,53697,53377,4160,61441,12480,12672,61761,13056,62401,62081,12864,13824,63169,63361,14144,62721,13760,13440,62529,15360,64705,64897,15680,65281,16320,16e3,65089,64001,15040,15232,64321,14592,63937,63617,14400,10240,59585,59777,10560,60161,11200,10880,59969,60929,11968,12160,61249,11520,60865,60545,11328,58369,9408,9600,58689,9984,59329,59009,9792,8704,58049,58241,9024,57601,8640,8320,57409,40961,24768,24960,41281,25344,41921,41601,25152,26112,42689,42881,26432,42241,26048,25728,42049,27648,44225,44417,27968,44801,28608,28288,44609,43521,27328,27520,43841,26880,43457,43137,26688,30720,47297,47489,31040,47873,31680,31360,47681,48641,32448,32640,48961,32e3,48577,48257,31808,46081,29888,30080,46401,30464,47041,46721,30272,29184,45761,45953,29504,45313,29120,28800,45121,20480,37057,37249,20800,37633,21440,21120,37441,38401,22208,22400,38721,21760,38337,38017,21568,39937,23744,23936,40257,24320,40897,40577,24128,23040,39617,39809,23360,39169,22976,22656,38977,34817,18624,18816,35137,19200,35777,35457,19008,19968,36545,36737,20288,36097,19904,19584,35905,17408,33985,34177,17728,34561,18368,18048,34369,33281,17088,17280,33601,16640,33217,32897,16448];this.updateMusicCRC=function(e,t,a,n){for(var s=0;s<n;++s)e[0]=(r=t[a+s],i=(i=e[0])>>8^o[255&(i^r)]);var r,i}}function N(){var i=this,r=null,o=null;this.setModules=function(e,t,a,n){r=a,o=n};var _=null,l=0,f=0,c=0;function v(e,t,a){for(;0<a;){var n;0==c&&(c=8,f++,e.header[e.w_ptr].write_timing==l&&(s=e,I.arraycopy(s.header[s.w_ptr].buf,0,_,f,s.sideinfo_len),f+=s.sideinfo_len,l+=8*s.sideinfo_len,s.w_ptr=s.w_ptr+1&j.MAX_HEADER_BUF-1),_[f]=0),n=Math.min(a,c),a-=n,c-=n,_[f]|=t>>a<<c,l+=n}var s}function u(e,t){var a,n=e.internal_flags;if(8<=t&&(v(n,76,8),t-=8),8<=t&&(v(n,65,8),t-=8),8<=t&&(v(n,77,8),t-=8),8<=t&&(v(n,69,8),t-=8),32<=t){var s=r.getLameShortVersion();if(32<=t)for(a=0;a<s.length&&8<=t;++a)t-=8,v(n,s.charCodeAt(a),8)}for(;1<=t;t-=1)v(n,n.ancillary_flag,1),n.ancillary_flag^=e.disable_reservoir?0:1}function h(e,t,a){for(var n=e.header[e.h_ptr].ptr;0<a;){var s=Math.min(a,8-(7&n));a-=s,e.header[e.h_ptr].buf[n>>3]|=t>>a<<8-(7&n)-s,n+=s}e.header[e.h_ptr].ptr=n}function m(e,t){var a,n=k.ht[t.count1table_select+32],s=0,r=t.big_values,i=t.big_values;for(a=(t.count1-t.big_values)/4;0<a;--a){var o=0,_=0;0!=t.l3_enc[r+0]&&(_+=8,t.xr[i+0]<0&&o++),0!=t.l3_enc[r+1]&&(_+=4,o*=2,t.xr[i+1]<0&&o++),0!=t.l3_enc[r+2]&&(_+=2,o*=2,t.xr[i+2]<0&&o++),0!=t.l3_enc[r+3]&&(_++,o*=2,t.xr[i+3]<0&&o++),r+=4,i+=4,v(e,o+n.table[_],n.hlen[_]),s+=n.hlen[_]}return s}function b(e,t,a,n,s){var r=k.ht[t],i=0;if(0==t)return i;for(var o=a;o<n;o+=2){var _=0,l=0,f=r.xlen,c=r.xlen,u=0,h=s.l3_enc[o],b=s.l3_enc[o+1];if(0!=h&&(s.xr[o]<0&&u++,_--),15<t){if(14<h){var p=h-15;u|=p<<1,l=f,h=15}if(14<b){var m=b-15;u<<=f,u|=m,l+=f,b=15}c=16}0!=b&&(u<<=1,s.xr[o+1]<0&&u++,_--),h=h*c+b,l-=_,_+=r.hlen[h],v(e,r.table[h],_),v(e,u,l),i+=_+l}return i}function d(e,t){var a=3*e.scalefac_band.s[3];a>t.big_values&&(a=t.big_values);var n=b(e,t.table_select[0],0,a,t);return n+=b(e,t.table_select[1],a,t.big_values,t)}function g(e,t){var a,n,s,r;a=t.big_values;var i=t.region0_count+1;return s=e.scalefac_band.l[i],i+=t.region1_count+1,r=e.scalefac_band.l[i],a<s&&(s=a),a<r&&(r=a),n=b(e,t.table_select[0],0,s,t),n+=b(e,t.table_select[1],s,r,t),n+=b(e,t.table_select[2],r,a,t)}function p(){this.total=0}function S(e,t){var a,n,s,r=e.internal_flags;return r.w_ptr,-1==(s=r.h_ptr-1)&&(s=j.MAX_HEADER_BUF-1),a=r.header[s].write_timing-l,0<=(t.total=a)&&me(),n=i.getframebits(e),a+=n,t.total+=n,t.total%8!=0?t.total=1+t.total/8:t.total=t.total/8,t.total+=f+1,a}this.getframebits=function(e){var t,a=e.internal_flags;t=0!=a.bitrate_index?k.bitrate_table[e.version][a.bitrate_index]:e.brate;var n=0|72e3*(e.version+1)*t/e.out_samplerate+a.padding;return 8*n},this.flush_bitstream=function(e){var t,a,n=e.internal_flags,s=n.h_ptr-1;-1==s&&(s=j.MAX_HEADER_BUF-1),t=n.l3_side,(a=S(e,new p))<0||(u(e,a),n.ResvSize=0,t.main_data_begin=0,n.findReplayGain&&me(),n.findPeakSample&&me())},this.format_bitstream=function(e){var t,a=e.internal_flags;t=a.l3_side;var n=this.getframebits(e);u(e,t.resvDrain_pre),function(e,t){var a,n,s,r=e.internal_flags;if(a=r.l3_side,r.header[r.h_ptr].ptr=0,Se.fill(r.header[r.h_ptr].buf,0,r.sideinfo_len,0),e.out_samplerate<16e3?h(r,4094,12):h(r,4095,12),h(r,e.version,1),h(r,1,2),h(r,e.error_protection?0:1,1),h(r,r.bitrate_index,4),h(r,r.samplerate_index,2),h(r,r.padding,1),h(r,e.extension,1),h(r,e.mode.ordinal(),2),h(r,r.mode_ext,2),h(r,e.copyright,1),h(r,e.original,1),h(r,e.emphasis,2),e.error_protection&&h(r,0,16),1==e.version){for(h(r,a.main_data_begin,9),2==r.channels_out?h(r,a.private_bits,3):h(r,a.private_bits,5),s=0;s<r.channels_out;s++){var i;for(i=0;i<4;i++)h(r,a.scfsi[s][i],1)}for(n=0;n<2;n++)for(s=0;s<r.channels_out;s++){var o=a.tt[n][s];h(r,o.part2_3_length+o.part2_length,12),h(r,o.big_values/2,9),h(r,o.global_gain,8),h(r,o.scalefac_compress,4),o.block_type!=Ae.NORM_TYPE?(h(r,1,1),h(r,o.block_type,2),h(r,o.mixed_block_flag,1),14==o.table_select[0]&&(o.table_select[0]=16),h(r,o.table_select[0],5),14==o.table_select[1]&&(o.table_select[1]=16),h(r,o.table_select[1],5),h(r,o.subblock_gain[0],3),h(r,o.subblock_gain[1],3),h(r,o.subblock_gain[2],3)):(h(r,0,1),14==o.table_select[0]&&(o.table_select[0]=16),h(r,o.table_select[0],5),14==o.table_select[1]&&(o.table_select[1]=16),h(r,o.table_select[1],5),14==o.table_select[2]&&(o.table_select[2]=16),h(r,o.table_select[2],5),h(r,o.region0_count,4),h(r,o.region1_count,3)),h(r,o.preflag,1),h(r,o.scalefac_scale,1),h(r,o.count1table_select,1)}}else for(h(r,a.main_data_begin,8),h(r,a.private_bits,r.channels_out),s=n=0;s<r.channels_out;s++){var o=a.tt[n][s];h(r,o.part2_3_length+o.part2_length,12),h(r,o.big_values/2,9),h(r,o.global_gain,8),h(r,o.scalefac_compress,9),o.block_type!=Ae.NORM_TYPE?(h(r,1,1),h(r,o.block_type,2),h(r,o.mixed_block_flag,1),14==o.table_select[0]&&(o.table_select[0]=16),h(r,o.table_select[0],5),14==o.table_select[1]&&(o.table_select[1]=16),h(r,o.table_select[1],5),h(r,o.subblock_gain[0],3),h(r,o.subblock_gain[1],3),h(r,o.subblock_gain[2],3)):(h(r,0,1),14==o.table_select[0]&&(o.table_select[0]=16),h(r,o.table_select[0],5),14==o.table_select[1]&&(o.table_select[1]=16),h(r,o.table_select[1],5),14==o.table_select[2]&&(o.table_select[2]=16),h(r,o.table_select[2],5),h(r,o.region0_count,4),h(r,o.region1_count,3)),h(r,o.scalefac_scale,1),h(r,o.count1table_select,1)}e.error_protection&&me();var _=r.h_ptr;r.h_ptr=_+1&j.MAX_HEADER_BUF-1,r.header[r.h_ptr].write_timing=r.header[_].write_timing+t,r.h_ptr,r.w_ptr}(e,n);var s=8*a.sideinfo_len;if(s+=function(e){var t,a,n,s,r=0,i=e.internal_flags,o=i.l3_side;if(1==e.version)for(t=0;t<2;t++)for(a=0;a<i.channels_out;a++){var _=o.tt[t][a],l=A.slen1_tab[_.scalefac_compress],f=A.slen2_tab[_.scalefac_compress];for(n=s=0;n<_.sfbdivide;n++)-1!=_.scalefac[n]&&(v(i,_.scalefac[n],l),s+=l);for(;n<_.sfbmax;n++)-1!=_.scalefac[n]&&(v(i,_.scalefac[n],f),s+=f);_.block_type==Ae.SHORT_TYPE?s+=d(i,_):s+=g(i,_),s+=m(i,_),r+=s}else for(a=t=0;a<i.channels_out;a++){var c,u,_=o.tt[t][a],h=0;if(u=n=s=0,_.block_type==Ae.SHORT_TYPE){for(;u<4;u++){var b=_.sfb_partition_table[u]/3,p=_.slen[u];for(c=0;c<b;c++,n++)v(i,Math.max(_.scalefac[3*n+0],0),p),v(i,Math.max(_.scalefac[3*n+1],0),p),v(i,Math.max(_.scalefac[3*n+2],0),p),h+=3*p}s+=d(i,_)}else{for(;u<4;u++){var b=_.sfb_partition_table[u],p=_.slen[u];for(c=0;c<b;c++,n++)v(i,Math.max(_.scalefac[n],0),p),h+=p}s+=g(i,_)}s+=m(i,_),r+=h+s}return r}(e),u(e,t.resvDrain_post),s+=t.resvDrain_post,t.main_data_begin+=(n-s)/8,S(e,new p),a.ResvSize,8*t.main_data_begin!=a.ResvSize&&(a.ResvSize=8*t.main_data_begin),1e9<l){var r;for(r=0;r<j.MAX_HEADER_BUF;++r)a.header[r].write_timing-=l;l=0}return 0},this.copy_buffer=function(e,t,a,n,s){var r=f+1;if(r<=0)return 0;if(0!=n&&n<r)return-1;if(I.arraycopy(_,0,t,a,r),f=-1,(c=0)!=s){var i=ve(1);i[0]=e.nMusicCRC,o.updateMusicCRC(i,t,a,r),e.nMusicCRC=i[0],0<r&&(e.VBR_seek_table.nBytesWritten+=r),e.decode_on_the_fly&&me()}return r},this.init_bit_stream_w=function(e){_=w(Y.LAME_MAXMP3BUFFER),e.h_ptr=e.w_ptr=0,e.header[e.h_ptr].write_timing=0,f=-1,l=c=0}}function e(e,t,a,n){this.xlen=e,this.linmax=t,this.table=a,this.hlen=n}D.MAX_VALUE=3.4028235e38,Me.vbr_off=new Me(0),Me.vbr_mt=new Me(1),Me.vbr_rh=new Me(2),Me.vbr_abr=new Me(3),Me.vbr_mtrh=new Me(4),Me.vbr_default=Me.vbr_mtrh,ye.STEREO=new ye(0),ye.JOINT_STEREO=new ye(1),ye.DUAL_CHANNEL=new ye(2),ye.MONO=new ye(3),ye.NOT_SET=new ye(4),O.STEPS_per_dB=100,O.MAX_dB=120,O.GAIN_NOT_ENOUGH_SAMPLES=-24601,O.GAIN_ANALYSIS_ERROR=0,O.GAIN_ANALYSIS_OK=1,O.INIT_GAIN_ANALYSIS_ERROR=0,O.INIT_GAIN_ANALYSIS_OK=1,O.MAX_ORDER=O.YULE_ORDER=10,O.MAX_SAMPLES_PER_WINDOW=(O.MAX_SAMP_FREQ=48e3)*(O.RMS_WINDOW_TIME_NUMERATOR=1)/(O.RMS_WINDOW_TIME_DENOMINATOR=20)+1,y.NUMTOCENTRIES=100,y.MAXFRAMESIZE=2880,N.EQ=function(e,t){return Math.abs(e)>Math.abs(t)?Math.abs(e-t)<=1e-6*Math.abs(e):Math.abs(e-t)<=1e-6*Math.abs(t)},N.NEQ=function(e,t){return!N.EQ(e,t)};var k={};function R(e){this.bits=e}function x(){this.over_noise=0,this.tot_noise=0,this.max_noise=0,this.over_count=0,this.over_SSD=0,this.bits=0}function r(e,t,a,n){this.l=ve(1+Ae.SBMAX_l),this.s=ve(1+Ae.SBMAX_s),this.psfb21=ve(1+Ae.PSFB21),this.psfb12=ve(1+Ae.PSFB12);var s=this.l,r=this.s;4==arguments.length&&(this.arrL=e,this.arrS=t,this.arr21=a,this.arr12=n,I.arraycopy(this.arrL,0,s,0,Math.min(this.arrL.length,this.l.length)),I.arraycopy(this.arrS,0,r,0,Math.min(this.arrS.length,this.s.length)),I.arraycopy(this.arr21,0,this.psfb21,0,Math.min(this.arr21.length,this.psfb21.length)),I.arraycopy(this.arr12,0,this.psfb12,0,Math.min(this.arr12.length,this.psfb12.length)))}function T(){var l=null,b=null,n=null;this.setModules=function(e,t,a){l=e,b=t,n=a},this.IPOW20=function(e){return h[e]};var B=2.220446049250313e-16,e=T.IXMAX_VAL,f=e+2,c=T.Q_MAX,u=T.Q_MAX2,s=100;this.nr_of_sfb_block=[[[6,5,5,5],[9,9,9,9],[6,9,9,9]],[[6,5,7,3],[9,9,12,6],[6,9,12,6]],[[11,10,0,0],[18,18,0,0],[15,18,0,0]],[[7,7,7,0],[12,12,12,0],[6,15,12,0]],[[6,6,6,3],[12,9,9,6],[6,12,9,6]],[[8,8,5,0],[15,12,9,0],[6,18,9,0]]];var M=[0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,2,2,3,3,3,2,0];this.pretab=M,this.sfBandIndex=[new r([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,24,32,42,56,74,100,132,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new r([0,6,12,18,24,30,36,44,54,66,80,96,114,136,162,194,232,278,332,394,464,540,576],[0,4,8,12,18,26,36,48,62,80,104,136,180,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new r([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,26,36,48,62,80,104,134,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new r([0,4,8,12,16,20,24,30,36,44,52,62,74,90,110,134,162,196,238,288,342,418,576],[0,4,8,12,16,22,30,40,52,66,84,106,136,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new r([0,4,8,12,16,20,24,30,36,42,50,60,72,88,106,128,156,190,230,276,330,384,576],[0,4,8,12,16,22,28,38,50,64,80,100,126,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new r([0,4,8,12,16,20,24,30,36,44,54,66,82,102,126,156,194,240,296,364,448,550,576],[0,4,8,12,16,22,30,42,58,78,104,138,180,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new r([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,26,36,48,62,80,104,134,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new r([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,26,36,48,62,80,104,134,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new r([0,12,24,36,48,60,72,88,108,132,160,192,232,280,336,400,476,566,568,570,572,574,576],[0,8,16,24,36,52,72,96,124,160,162,164,166,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0])];var y=de(c+u+1),h=de(c),p=de(f),m=de(f);function v(e,t){var a=n.ATHformula(t,e);return a-=s,a=Math.pow(10,a/10+e.ATHlower)}function A(e){this.s=e}this.adj43=m,this.iteration_init=function(e){var t,a=e.internal_flags,n=a.l3_side;if(0==a.iteration_init_init){for(a.iteration_init_init=1,n.main_data_begin=0,function(e){for(var t=e.internal_flags.ATH.l,a=e.internal_flags.ATH.psfb21,n=e.internal_flags.ATH.s,s=e.internal_flags.ATH.psfb12,r=e.internal_flags,i=e.out_samplerate,o=0;o<Ae.SBMAX_l;o++){var _=r.scalefac_band.l[o],l=r.scalefac_band.l[o+1];t[o]=D.MAX_VALUE;for(var f=_;f<l;f++){var c=f*i/1152,u=v(e,c);t[o]=Math.min(t[o],u)}}for(var o=0;o<Ae.PSFB21;o++){var _=r.scalefac_band.psfb21[o],l=r.scalefac_band.psfb21[o+1];a[o]=D.MAX_VALUE;for(var f=_;f<l;f++){var c=f*i/1152,u=v(e,c);a[o]=Math.min(a[o],u)}}for(var o=0;o<Ae.SBMAX_s;o++){var _=r.scalefac_band.s[o],l=r.scalefac_band.s[o+1];n[o]=D.MAX_VALUE;for(var f=_;f<l;f++){var c=f*i/384,u=v(e,c);n[o]=Math.min(n[o],u)}n[o]*=r.scalefac_band.s[o+1]-r.scalefac_band.s[o]}for(var o=0;o<Ae.PSFB12;o++){var _=r.scalefac_band.psfb12[o],l=r.scalefac_band.psfb12[o+1];s[o]=D.MAX_VALUE;for(var f=_;f<l;f++){var c=f*i/384,u=v(e,c);s[o]=Math.min(s[o],u)}s[o]*=r.scalefac_band.s[13]-r.scalefac_band.s[12]}e.noATH&&me(),r.ATH.floor=10*d(v(e,-1))}(e),p[0]=0,t=1;t<f;t++)p[t]=Math.pow(t,4/3);for(t=0;t<f-1;t++)m[t]=t+1-Math.pow(.5*(p[t]+p[t+1]),.75);for(m[t]=.5,t=0;t<c;t++)h[t]=Math.pow(2,-.1875*(t-210));for(t=0;t<=c+u;t++)y[t]=Math.pow(2,.25*(t-210-u));var s,r,i,o;for(l.huffman_init(a),32<=(t=e.exp_nspsytune>>2&63)&&(t-=64),s=Math.pow(10,t/4/10),32<=(t=e.exp_nspsytune>>8&63)&&(t-=64),r=Math.pow(10,t/4/10),32<=(t=e.exp_nspsytune>>14&63)&&(t-=64),i=Math.pow(10,t/4/10),32<=(t=e.exp_nspsytune>>20&63)&&(t-=64),o=i*Math.pow(10,t/4/10),t=0;t<Ae.SBMAX_l;t++)_=t<=6?s:t<=13?r:t<=20?i:o,a.nsPsy.longfact[t]=_;for(t=0;t<Ae.SBMAX_s;t++){var _;_=t<=5?s:t<=10?r:t<=11?i:o,a.nsPsy.shortfact[t]=_}}},this.on_pe=function(e,t,a,n,s,r){var i,o,_=e.internal_flags,l=0,f=ve(2),c=new R(l),u=b.ResvMaxBits(e,n,c,r),h=(l=c.bits)+u;for(j.MAX_BITS_PER_GRANULE<h&&(h=j.MAX_BITS_PER_GRANULE),o=i=0;o<_.channels_out;++o)a[o]=Math.min(j.MAX_BITS_PER_CHANNEL,l/_.channels_out),f[o]=0|a[o]*t[s][o]/700-a[o],f[o]>3*n/4&&(f[o]=3*n/4),f[o]<0&&(f[o]=0),f[o]+a[o]>j.MAX_BITS_PER_CHANNEL&&(f[o]=Math.max(0,j.MAX_BITS_PER_CHANNEL-a[o])),i+=f[o];if(u<i)for(o=0;o<_.channels_out;++o)f[o]=u*f[o]/i;for(o=0;o<_.channels_out;++o)a[o]+=f[o],u-=f[o];for(o=i=0;o<_.channels_out;++o)i+=a[o];return j.MAX_BITS_PER_GRANULE<i&&me(),h},this.athAdjust=function(e,t,a){var n=90.30873362,s=V.FAST_LOG10_X(t,10),r=e*e,i=0;return s-=a,1e-20<r&&(i=1+V.FAST_LOG10_X(r,10/n)),i<0&&(i=0),s*=i,s+=a+n-94.82444863,Math.pow(10,.1*s)},this.calc_xmin=function(e,t,a,n){var s,r=0,i=e.internal_flags,o=0,_=0,l=i.ATH,f=a.xr,c=e.VBR==Me.vbr_mtrh?1:0,u=i.masking_lower;for(e.VBR!=Me.vbr_mtrh&&e.VBR!=Me.vbr_mt||(u=1),s=0;s<a.psy_lmax;s++){for(S=e.VBR==Me.vbr_rh||e.VBR==Me.vbr_mtrh?athAdjust(l.adjust,l.l[s],l.floor):l.adjust*l.l[s],v=a.width[s],w=S/v,M=B,A=v>>1,y=0;k=f[o]*f[o],y+=k,M+=k<w?k:w,R=f[++o]*f[o],y+=R,M+=R<w?R:w,o++,0<--A;);if(S<y&&_++,s==Ae.SBPSY_l&&me(),0!=c&&(S=M),!e.ATHonly){var h=t.en.l[s];0<h&&(x=y*t.thm.l[s]*u/h,0!=c&&(x*=i.nsPsy.longfact[s]),S<x&&(S=x))}n[r++]=0!=c?S:S*i.nsPsy.longfact[s]}var b=575;if(a.block_type!=Ae.SHORT_TYPE)for(var p=576;0!=p--&&N.EQ(f[p],0);)b=p;a.max_nonzero_coeff=b;for(var m=a.sfb_smin;s<a.psymax;m++,s+=3){var v,d,g;for(g=e.VBR==Me.vbr_rh||e.VBR==Me.vbr_mtrh?athAdjust(l.adjust,l.s[m],l.floor):l.adjust*l.s[m],v=a.width[s],d=0;d<3;d++){var S,w,M,y=0,A=v>>1;w=g/v,M=B;do{var k,R;k=f[o]*f[o],y+=k,M+=k<w?k:w,R=f[++o]*f[o],y+=R,M+=R<w?R:w,o++}while(0<--A);if(g<y&&_++,m==Ae.SBPSY_s&&me(),S=0!=c?M:g,!e.ATHonly&&!e.ATHshort){var x,h=t.en.s[m][d];0<h&&(x=y*t.thm.s[m][d]*u/h,0!=c&&(x*=i.nsPsy.shortfact[m]),S<x&&(S=x))}n[r++]=0!=c?S:S*i.nsPsy.shortfact[m]}e.useTemporal&&(n[r-3]>n[r-3+1]&&(n[r-3+1]+=(n[r-3]-n[r-3+1])*i.decay),n[r-3+1]>n[r-3+2]&&(n[r-3+2]+=(n[r-3+1]-n[r-3+2])*i.decay))}return _},this.calc_noise_core=function(e,t,a,n){var s=0,r=t.s,i=e.l3_enc;if(r>e.count1)for(;0!=a--;)_=e.xr[r],r++,s+=_*_,_=e.xr[r],r++,s+=_*_;else if(r>e.big_values){var o=de(2);for(o[0]=0,o[1]=n;0!=a--;)_=Math.abs(e.xr[r])-o[i[r]],r++,s+=_*_,_=Math.abs(e.xr[r])-o[i[r]],r++,s+=_*_}else for(;0!=a--;){var _;_=Math.abs(e.xr[r])-p[i[r]]*n,r++,s+=_*_,_=Math.abs(e.xr[r])-p[i[r]]*n,r++,s+=_*_}return t.s=r,s},this.calc_noise=function(e,t,a,n,s){var r,i,o=0,_=0,l=0,f=0,c=0,u=-20,h=0,b=e.scalefac,p=0;for(n.over_SSD=0,r=0;r<e.psymax;r++){var m,v=e.global_gain-(b[p++]+(0!=e.preflag?M[r]:0)<<e.scalefac_scale+1)-8*e.subblock_gain[e.window[r]],d=0;if(null!=s&&s.step[r]==v)d=s.noise[r],h+=e.width[r],a[o++]=d/t[_++],d=s.noise_log[r];else{var g,S=y[v+T.Q_MAX2];i=e.width[r]>>1,h+e.width[r]>e.max_nonzero_coeff&&(g=e.max_nonzero_coeff-h+1,i=0<g?g>>1:0);var w=new A(h);d=this.calc_noise_core(e,w,i,S),h=w.s,null!=s&&(s.step[r]=v,s.noise[r]=d),d=a[o++]=d/t[_++],d=V.FAST_LOG10(Math.max(d,1e-20)),null!=s&&(s.noise_log[r]=d)}null!=s&&(s.global_gain=e.global_gain),c+=d,0<d&&(m=Math.max(0|10*d+.5,1),n.over_SSD+=m*m,l++,f+=d),u=Math.max(u,d)}return n.over_count=l,n.tot_noise=c,n.over_noise=f,n.max_noise=u,l}}function B(){this.xr=de(576),this.l3_enc=ve(576),this.scalefac=ve(F.SFBMAX),this.xrpow_max=0,this.part2_3_length=0,this.big_values=0,this.count1=0,this.global_gain=0,this.scalefac_compress=0,this.block_type=0,this.mixed_block_flag=0,this.table_select=ve(3),this.subblock_gain=ve(4),this.region0_count=0,this.region1_count=0,this.preflag=0,this.scalefac_scale=0,this.count1table_select=0,this.part2_length=0,this.sfb_lmax=0,this.sfb_smin=0,this.psy_lmax=0,this.sfbmax=0,this.psymax=0,this.sfbdivide=0,this.width=ve(F.SFBMAX),this.window=ve(F.SFBMAX),this.count1bits=0,this.sfb_partition_table=null,this.slen=ve(4),this.max_nonzero_coeff=0;var a=this;function n(e){return new Int32Array(e)}this.assign=function(e){var t;a.xr=(t=e.xr,new Float32Array(t)),a.l3_enc=n(e.l3_enc),a.scalefac=n(e.scalefac),a.xrpow_max=e.xrpow_max,a.part2_3_length=e.part2_3_length,a.big_values=e.big_values,a.count1=e.count1,a.global_gain=e.global_gain,a.scalefac_compress=e.scalefac_compress,a.block_type=e.block_type,a.mixed_block_flag=e.mixed_block_flag,a.table_select=n(e.table_select),a.subblock_gain=n(e.subblock_gain),a.region0_count=e.region0_count,a.region1_count=e.region1_count,a.preflag=e.preflag,a.scalefac_scale=e.scalefac_scale,a.count1table_select=e.count1table_select,a.part2_length=e.part2_length,a.sfb_lmax=e.sfb_lmax,a.sfb_smin=e.sfb_smin,a.psy_lmax=e.psy_lmax,a.sfbmax=e.sfbmax,a.psymax=e.psymax,a.sfbdivide=e.sfbdivide,a.width=n(e.width),a.window=n(e.window),a.count1bits=e.count1bits,a.sfb_partition_table=e.sfb_partition_table.slice(0),a.slen=n(e.slen),a.max_nonzero_coeff=e.max_nonzero_coeff}}k.t1HB=[1,1,1,0],k.t2HB=[1,2,1,3,1,1,3,2,0],k.t3HB=[3,2,1,1,1,1,3,2,0],k.t5HB=[1,2,6,5,3,1,4,4,7,5,7,1,6,1,1,0],k.t6HB=[7,3,5,1,6,2,3,2,5,4,4,1,3,3,2,0],k.t7HB=[1,2,10,19,16,10,3,3,7,10,5,3,11,4,13,17,8,4,12,11,18,15,11,2,7,6,9,14,3,1,6,4,5,3,2,0],k.t8HB=[3,4,6,18,12,5,5,1,2,16,9,3,7,3,5,14,7,3,19,17,15,13,10,4,13,5,8,11,5,1,12,4,4,1,1,0],k.t9HB=[7,5,9,14,15,7,6,4,5,5,6,7,7,6,8,8,8,5,15,6,9,10,5,1,11,7,9,6,4,1,14,4,6,2,6,0],k.t10HB=[1,2,10,23,35,30,12,17,3,3,8,12,18,21,12,7,11,9,15,21,32,40,19,6,14,13,22,34,46,23,18,7,20,19,33,47,27,22,9,3,31,22,41,26,21,20,5,3,14,13,10,11,16,6,5,1,9,8,7,8,4,4,2,0],k.t11HB=[3,4,10,24,34,33,21,15,5,3,4,10,32,17,11,10,11,7,13,18,30,31,20,5,25,11,19,59,27,18,12,5,35,33,31,58,30,16,7,5,28,26,32,19,17,15,8,14,14,12,9,13,14,9,4,1,11,4,6,6,6,3,2,0],k.t12HB=[9,6,16,33,41,39,38,26,7,5,6,9,23,16,26,11,17,7,11,14,21,30,10,7,17,10,15,12,18,28,14,5,32,13,22,19,18,16,9,5,40,17,31,29,17,13,4,2,27,12,11,15,10,7,4,1,27,12,8,12,6,3,1,0],k.t13HB=[1,5,14,21,34,51,46,71,42,52,68,52,67,44,43,19,3,4,12,19,31,26,44,33,31,24,32,24,31,35,22,14,15,13,23,36,59,49,77,65,29,40,30,40,27,33,42,16,22,20,37,61,56,79,73,64,43,76,56,37,26,31,25,14,35,16,60,57,97,75,114,91,54,73,55,41,48,53,23,24,58,27,50,96,76,70,93,84,77,58,79,29,74,49,41,17,47,45,78,74,115,94,90,79,69,83,71,50,59,38,36,15,72,34,56,95,92,85,91,90,86,73,77,65,51,44,43,42,43,20,30,44,55,78,72,87,78,61,46,54,37,30,20,16,53,25,41,37,44,59,54,81,66,76,57,54,37,18,39,11,35,33,31,57,42,82,72,80,47,58,55,21,22,26,38,22,53,25,23,38,70,60,51,36,55,26,34,23,27,14,9,7,34,32,28,39,49,75,30,52,48,40,52,28,18,17,9,5,45,21,34,64,56,50,49,45,31,19,12,15,10,7,6,3,48,23,20,39,36,35,53,21,16,23,13,10,6,1,4,2,16,15,17,27,25,20,29,11,17,12,16,8,1,1,0,1],k.t15HB=[7,12,18,53,47,76,124,108,89,123,108,119,107,81,122,63,13,5,16,27,46,36,61,51,42,70,52,83,65,41,59,36,19,17,15,24,41,34,59,48,40,64,50,78,62,80,56,33,29,28,25,43,39,63,55,93,76,59,93,72,54,75,50,29,52,22,42,40,67,57,95,79,72,57,89,69,49,66,46,27,77,37,35,66,58,52,91,74,62,48,79,63,90,62,40,38,125,32,60,56,50,92,78,65,55,87,71,51,73,51,70,30,109,53,49,94,88,75,66,122,91,73,56,42,64,44,21,25,90,43,41,77,73,63,56,92,77,66,47,67,48,53,36,20,71,34,67,60,58,49,88,76,67,106,71,54,38,39,23,15,109,53,51,47,90,82,58,57,48,72,57,41,23,27,62,9,86,42,40,37,70,64,52,43,70,55,42,25,29,18,11,11,118,68,30,55,50,46,74,65,49,39,24,16,22,13,14,7,91,44,39,38,34,63,52,45,31,52,28,19,14,8,9,3,123,60,58,53,47,43,32,22,37,24,17,12,15,10,2,1,71,37,34,30,28,20,17,26,21,16,10,6,8,6,2,0],k.t16HB=[1,5,14,44,74,63,110,93,172,149,138,242,225,195,376,17,3,4,12,20,35,62,53,47,83,75,68,119,201,107,207,9,15,13,23,38,67,58,103,90,161,72,127,117,110,209,206,16,45,21,39,69,64,114,99,87,158,140,252,212,199,387,365,26,75,36,68,65,115,101,179,164,155,264,246,226,395,382,362,9,66,30,59,56,102,185,173,265,142,253,232,400,388,378,445,16,111,54,52,100,184,178,160,133,257,244,228,217,385,366,715,10,98,48,91,88,165,157,148,261,248,407,397,372,380,889,884,8,85,84,81,159,156,143,260,249,427,401,392,383,727,713,708,7,154,76,73,141,131,256,245,426,406,394,384,735,359,710,352,11,139,129,67,125,247,233,229,219,393,743,737,720,885,882,439,4,243,120,118,115,227,223,396,746,742,736,721,712,706,223,436,6,202,224,222,218,216,389,386,381,364,888,443,707,440,437,1728,4,747,211,210,208,370,379,734,723,714,1735,883,877,876,3459,865,2,377,369,102,187,726,722,358,711,709,866,1734,871,3458,870,434,0,12,10,7,11,10,17,11,9,13,12,10,7,5,3,1,3],k.t24HB=[15,13,46,80,146,262,248,434,426,669,653,649,621,517,1032,88,14,12,21,38,71,130,122,216,209,198,327,345,319,297,279,42,47,22,41,74,68,128,120,221,207,194,182,340,315,295,541,18,81,39,75,70,134,125,116,220,204,190,178,325,311,293,271,16,147,72,69,135,127,118,112,210,200,188,352,323,306,285,540,14,263,66,129,126,119,114,214,202,192,180,341,317,301,281,262,12,249,123,121,117,113,215,206,195,185,347,330,308,291,272,520,10,435,115,111,109,211,203,196,187,353,332,313,298,283,531,381,17,427,212,208,205,201,193,186,177,169,320,303,286,268,514,377,16,335,199,197,191,189,181,174,333,321,305,289,275,521,379,371,11,668,184,183,179,175,344,331,314,304,290,277,530,383,373,366,10,652,346,171,168,164,318,309,299,287,276,263,513,375,368,362,6,648,322,316,312,307,302,292,284,269,261,512,376,370,364,359,4,620,300,296,294,288,282,273,266,515,380,374,369,365,361,357,2,1033,280,278,274,267,264,259,382,378,372,367,363,360,358,356,0,43,20,19,17,15,13,11,9,7,6,4,7,5,3,1,3],k.t32HB=[1,10,8,20,12,20,16,32,14,12,24,0,28,16,24,16],k.t33HB=[15,28,26,48,22,40,36,64,14,24,20,32,12,16,8,0],k.t1l=[1,4,3,5],k.t2l=[1,4,7,4,5,7,6,7,8],k.t3l=[2,3,7,4,4,7,6,7,8],k.t5l=[1,4,7,8,4,5,8,9,7,8,9,10,8,8,9,10],k.t6l=[3,4,6,8,4,4,6,7,5,6,7,8,7,7,8,9],k.t7l=[1,4,7,9,9,10,4,6,8,9,9,10,7,7,9,10,10,11,8,9,10,11,11,11,8,9,10,11,11,12,9,10,11,12,12,12],k.t8l=[2,4,7,9,9,10,4,4,6,10,10,10,7,6,8,10,10,11,9,10,10,11,11,12,9,9,10,11,12,12,10,10,11,11,13,13],k.t9l=[3,4,6,7,9,10,4,5,6,7,8,10,5,6,7,8,9,10,7,7,8,9,9,10,8,8,9,9,10,11,9,9,10,10,11,11],k.t10l=[1,4,7,9,10,10,10,11,4,6,8,9,10,11,10,10,7,8,9,10,11,12,11,11,8,9,10,11,12,12,11,12,9,10,11,12,12,12,12,12,10,11,12,12,13,13,12,13,9,10,11,12,12,12,13,13,10,10,11,12,12,13,13,13],k.t11l=[2,4,6,8,9,10,9,10,4,5,6,8,10,10,9,10,6,7,8,9,10,11,10,10,8,8,9,11,10,12,10,11,9,10,10,11,11,12,11,12,9,10,11,12,12,13,12,13,9,9,9,10,11,12,12,12,9,9,10,11,12,12,12,12],k.t12l=[4,4,6,8,9,10,10,10,4,5,6,7,9,9,10,10,6,6,7,8,9,10,9,10,7,7,8,8,9,10,10,10,8,8,9,9,10,10,10,11,9,9,10,10,10,11,10,11,9,9,9,10,10,11,11,12,10,10,10,11,11,11,11,12],k.t13l=[1,5,7,8,9,10,10,11,10,11,12,12,13,13,14,14,4,6,8,9,10,10,11,11,11,11,12,12,13,14,14,14,7,8,9,10,11,11,12,12,11,12,12,13,13,14,15,15,8,9,10,11,11,12,12,12,12,13,13,13,13,14,15,15,9,9,11,11,12,12,13,13,12,13,13,14,14,15,15,16,10,10,11,12,12,12,13,13,13,13,14,13,15,15,16,16,10,11,12,12,13,13,13,13,13,14,14,14,15,15,16,16,11,11,12,13,13,13,14,14,14,14,15,15,15,16,18,18,10,10,11,12,12,13,13,14,14,14,14,15,15,16,17,17,11,11,12,12,13,13,13,15,14,15,15,16,16,16,18,17,11,12,12,13,13,14,14,15,14,15,16,15,16,17,18,19,12,12,12,13,14,14,14,14,15,15,15,16,17,17,17,18,12,13,13,14,14,15,14,15,16,16,17,17,17,18,18,18,13,13,14,15,15,15,16,16,16,16,16,17,18,17,18,18,14,14,14,15,15,15,17,16,16,19,17,17,17,19,18,18,13,14,15,16,16,16,17,16,17,17,18,18,21,20,21,18],k.t15l=[3,5,6,8,8,9,10,10,10,11,11,12,12,12,13,14,5,5,7,8,9,9,10,10,10,11,11,12,12,12,13,13,6,7,7,8,9,9,10,10,10,11,11,12,12,13,13,13,7,8,8,9,9,10,10,11,11,11,12,12,12,13,13,13,8,8,9,9,10,10,11,11,11,11,12,12,12,13,13,13,9,9,9,10,10,10,11,11,11,11,12,12,13,13,13,14,10,9,10,10,10,11,11,11,11,12,12,12,13,13,14,14,10,10,10,11,11,11,11,12,12,12,12,12,13,13,13,14,10,10,10,11,11,11,11,12,12,12,12,13,13,14,14,14,10,10,11,11,11,11,12,12,12,13,13,13,13,14,14,14,11,11,11,11,12,12,12,12,12,13,13,13,13,14,15,14,11,11,11,11,12,12,12,12,13,13,13,13,14,14,14,15,12,12,11,12,12,12,13,13,13,13,13,13,14,14,15,15,12,12,12,12,12,13,13,13,13,14,14,14,14,14,15,15,13,13,13,13,13,13,13,13,14,14,14,14,15,15,14,15,13,13,13,13,13,13,13,14,14,14,14,14,15,15,15,15],k.t16_5l=[1,5,7,9,10,10,11,11,12,12,12,13,13,13,14,11,4,6,8,9,10,11,11,11,12,12,12,13,14,13,14,11,7,8,9,10,11,11,12,12,13,12,13,13,13,14,14,12,9,9,10,11,11,12,12,12,13,13,14,14,14,15,15,13,10,10,11,11,12,12,13,13,13,14,14,14,15,15,15,12,10,10,11,11,12,13,13,14,13,14,14,15,15,15,16,13,11,11,11,12,13,13,13,13,14,14,14,14,15,15,16,13,11,11,12,12,13,13,13,14,14,15,15,15,15,17,17,13,11,12,12,13,13,13,14,14,15,15,15,15,16,16,16,13,12,12,12,13,13,14,14,15,15,15,15,16,15,16,15,14,12,13,12,13,14,14,14,14,15,16,16,16,17,17,16,13,13,13,13,13,14,14,15,16,16,16,16,16,16,15,16,14,13,14,14,14,14,15,15,15,15,17,16,16,16,16,18,14,15,14,14,14,15,15,16,16,16,18,17,17,17,19,17,14,14,15,13,14,16,16,15,16,16,17,18,17,19,17,16,14,11,11,11,12,12,13,13,13,14,14,14,14,14,14,14,12],k.t16l=[1,5,7,9,10,10,11,11,12,12,12,13,13,13,14,10,4,6,8,9,10,11,11,11,12,12,12,13,14,13,14,10,7,8,9,10,11,11,12,12,13,12,13,13,13,14,14,11,9,9,10,11,11,12,12,12,13,13,14,14,14,15,15,12,10,10,11,11,12,12,13,13,13,14,14,14,15,15,15,11,10,10,11,11,12,13,13,14,13,14,14,15,15,15,16,12,11,11,11,12,13,13,13,13,14,14,14,14,15,15,16,12,11,11,12,12,13,13,13,14,14,15,15,15,15,17,17,12,11,12,12,13,13,13,14,14,15,15,15,15,16,16,16,12,12,12,12,13,13,14,14,15,15,15,15,16,15,16,15,13,12,13,12,13,14,14,14,14,15,16,16,16,17,17,16,12,13,13,13,13,14,14,15,16,16,16,16,16,16,15,16,13,13,14,14,14,14,15,15,15,15,17,16,16,16,16,18,13,15,14,14,14,15,15,16,16,16,18,17,17,17,19,17,13,14,15,13,14,16,16,15,16,16,17,18,17,19,17,16,13,10,10,10,11,11,12,12,12,13,13,13,13,13,13,13,10],k.t24l=[4,5,7,8,9,10,10,11,11,12,12,12,12,12,13,10,5,6,7,8,9,10,10,11,11,11,12,12,12,12,12,10,7,7,8,9,9,10,10,11,11,11,11,12,12,12,13,9,8,8,9,9,10,10,10,11,11,11,11,12,12,12,12,9,9,9,9,10,10,10,10,11,11,11,12,12,12,12,13,9,10,9,10,10,10,10,11,11,11,11,12,12,12,12,12,9,10,10,10,10,10,11,11,11,11,12,12,12,12,12,13,9,11,10,10,10,11,11,11,11,12,12,12,12,12,13,13,10,11,11,11,11,11,11,11,11,11,12,12,12,12,13,13,10,11,11,11,11,11,11,11,12,12,12,12,12,13,13,13,10,12,11,11,11,11,12,12,12,12,12,12,13,13,13,13,10,12,12,11,11,11,12,12,12,12,12,12,13,13,13,13,10,12,12,12,12,12,12,12,12,12,12,13,13,13,13,13,10,12,12,12,12,12,12,12,12,13,13,13,13,13,13,13,10,13,12,12,12,12,12,12,13,13,13,13,13,13,13,13,10,9,9,9,9,9,9,9,9,9,9,9,10,10,10,10,6],k.t32l=[1,5,5,7,5,8,7,9,5,7,7,9,7,9,9,10],k.t33l=[4,5,5,6,5,6,6,7,5,6,6,7,6,7,7,8],k.ht=[new e(0,0,null,null),new e(2,0,k.t1HB,k.t1l),new e(3,0,k.t2HB,k.t2l),new e(3,0,k.t3HB,k.t3l),new e(0,0,null,null),new e(4,0,k.t5HB,k.t5l),new e(4,0,k.t6HB,k.t6l),new e(6,0,k.t7HB,k.t7l),new e(6,0,k.t8HB,k.t8l),new e(6,0,k.t9HB,k.t9l),new e(8,0,k.t10HB,k.t10l),new e(8,0,k.t11HB,k.t11l),new e(8,0,k.t12HB,k.t12l),new e(16,0,k.t13HB,k.t13l),new e(0,0,null,k.t16_5l),new e(16,0,k.t15HB,k.t15l),new e(1,1,k.t16HB,k.t16l),new e(2,3,k.t16HB,k.t16l),new e(3,7,k.t16HB,k.t16l),new e(4,15,k.t16HB,k.t16l),new e(6,63,k.t16HB,k.t16l),new e(8,255,k.t16HB,k.t16l),new e(10,1023,k.t16HB,k.t16l),new e(13,8191,k.t16HB,k.t16l),new e(4,15,k.t24HB,k.t24l),new e(5,31,k.t24HB,k.t24l),new e(6,63,k.t24HB,k.t24l),new e(7,127,k.t24HB,k.t24l),new e(8,255,k.t24HB,k.t24l),new e(9,511,k.t24HB,k.t24l),new e(11,2047,k.t24HB,k.t24l),new e(13,8191,k.t24HB,k.t24l),new e(0,0,k.t32HB,k.t32l),new e(0,0,k.t33HB,k.t33l)],k.largetbl=[65540,327685,458759,589832,655369,655370,720906,720907,786443,786444,786444,851980,851980,851980,917517,655370,262149,393222,524295,589832,655369,720906,720906,720907,786443,786443,786444,851980,917516,851980,917516,655370,458759,524295,589832,655369,720905,720906,786442,786443,851979,786443,851979,851980,851980,917516,917517,720905,589832,589832,655369,720905,720906,786442,786442,786443,851979,851979,917515,917516,917516,983052,983052,786441,655369,655369,720905,720906,786442,786442,851978,851979,851979,917515,917516,917516,983052,983052,983053,720905,655370,655369,720906,720906,786442,851978,851979,917515,851979,917515,917516,983052,983052,983052,1048588,786441,720906,720906,720906,786442,851978,851979,851979,851979,917515,917516,917516,917516,983052,983052,1048589,786441,720907,720906,786442,786442,851979,851979,851979,917515,917516,983052,983052,983052,983052,1114125,1114125,786442,720907,786443,786443,851979,851979,851979,917515,917515,983051,983052,983052,983052,1048588,1048589,1048589,786442,786443,786443,786443,851979,851979,917515,917515,983052,983052,983052,983052,1048588,983053,1048589,983053,851978,786444,851979,786443,851979,917515,917516,917516,917516,983052,1048588,1048588,1048589,1114125,1114125,1048589,786442,851980,851980,851979,851979,917515,917516,983052,1048588,1048588,1048588,1048588,1048589,1048589,983053,1048589,851978,851980,917516,917516,917516,917516,983052,983052,983052,983052,1114124,1048589,1048589,1048589,1048589,1179661,851978,983052,917516,917516,917516,983052,983052,1048588,1048588,1048589,1179661,1114125,1114125,1114125,1245197,1114125,851978,917517,983052,851980,917516,1048588,1048588,983052,1048589,1048589,1114125,1179661,1114125,1245197,1114125,1048589,851978,655369,655369,655369,720905,720905,786441,786441,786441,851977,851977,851977,851978,851978,851978,851978,655366],k.table23=[65538,262147,458759,262148,327684,458759,393222,458759,524296],k.table56=[65539,262148,458758,524296,262148,327684,524294,589831,458757,524294,589831,655368,524295,524295,589832,655369],k.bitrate_table=[[0,8,16,24,32,40,48,56,64,80,96,112,128,144,160,-1],[0,32,40,48,56,64,80,96,112,128,160,192,224,256,320,-1],[0,8,16,24,32,40,48,56,64,-1,-1,-1,-1,-1,-1,-1]],k.samplerate_table=[[22050,24e3,16e3,-1],[44100,48e3,32e3,-1],[11025,12e3,8e3,-1]],k.scfsi_band=[0,6,11,16,21],T.Q_MAX=257,T.Q_MAX2=116,T.LARGE_BITS=1e5,T.IXMAX_VAL=8206;var F={};function C(){var r,g;this.rv=null,this.qupvt=null;var S,s=new function(){this.setModules=function(e,t){}};function w(e){this.ordinal=e}function o(e){for(var t=0;t<e.sfbmax;t++)if(e.scalefac[t]+e.subblock_gain[e.window[t]]==0)return!1;return!0}function M(e,t,a,n,s){var r;switch(e){default:case 9:0<t.over_count?(r=a.over_SSD<=t.over_SSD,a.over_SSD==t.over_SSD&&(r=a.bits<t.bits)):r=a.max_noise<0&&10*a.max_noise+a.bits<=10*t.max_noise+t.bits;break;case 0:r=a.over_count<t.over_count||a.over_count==t.over_count&&a.over_noise<t.over_noise||a.over_count==t.over_count&&N.EQ(a.over_noise,t.over_noise)&&a.tot_noise<t.tot_noise;break;case 8:me();case 1:r=a.max_noise<t.max_noise;break;case 2:r=a.tot_noise<t.tot_noise;break;case 3:r=a.tot_noise<t.tot_noise&&a.max_noise<t.max_noise;break;case 4:r=a.max_noise<=0&&.2<t.max_noise||a.max_noise<=0&&t.max_noise<0&&t.max_noise>a.max_noise-.2&&a.tot_noise<t.tot_noise||a.max_noise<=0&&0<t.max_noise&&t.max_noise>a.max_noise-.2&&a.tot_noise<t.tot_noise+t.over_noise||0<a.max_noise&&-.05<t.max_noise&&t.max_noise>a.max_noise-.1&&a.tot_noise+a.over_noise<t.tot_noise+t.over_noise||0<a.max_noise&&-.1<t.max_noise&&t.max_noise>a.max_noise-.15&&a.tot_noise+a.over_noise+a.over_noise<t.tot_noise+t.over_noise+t.over_noise;break;case 5:r=a.over_noise<t.over_noise||N.EQ(a.over_noise,t.over_noise)&&a.tot_noise<t.tot_noise;break;case 6:r=a.over_noise<t.over_noise||N.EQ(a.over_noise,t.over_noise)&&(a.max_noise<t.max_noise||N.EQ(a.max_noise,t.max_noise)&&a.tot_noise<=t.tot_noise);break;case 7:r=a.over_count<t.over_count||a.over_noise<t.over_noise}return 0==t.over_count&&(r=r&&a.bits<t.bits),r}function y(e,t,a,n,s){var r=e.internal_flags;!function(e,t,a,n,s){var r,i=e.internal_flags;r=0==t.scalefac_scale?1.2968395546510096:1.6817928305074292;for(var o=0,_=0;_<t.sfbmax;_++)o<a[_]&&(o=a[_]);var l=i.noise_shaping_amp;switch(3==l&&me(),l){case 2:break;case 1:1<o?o=Math.pow(o,.5):o*=.95;break;case 0:default:1<o?o=1:o*=.95}for(var f=0,_=0;_<t.sfbmax;_++){var c,u=t.width[_];if(f+=u,!(a[_]<o)){for(0!=(2&i.substep_shaping)&&me(),t.scalefac[_]++,c=-u;c<0;c++)n[f+c]*=r,n[f+c]>t.xrpow_max&&(t.xrpow_max=n[f+c]);if(2==i.noise_shaping_amp)return}}}(e,t,a,n);var i=o(t);return!(i||(i=2==r.mode_gr?S.scale_bitcount(t):S.scale_bitcount_lsf(r,t))&&(1<r.noise_shaping&&(Se.fill(r.pseudohalf,0),0==t.scalefac_scale?(function(e,t){for(var a=0,n=0;n<e.sfbmax;n++){var s=e.width[n],r=e.scalefac[n];if(0!=e.preflag&&(r+=g.pretab[n]),a+=s,0!=(1&r)){r++;for(var i=-s;i<0;i++)t[a+i]*=1.2968395546510096,t[a+i]>e.xrpow_max&&(e.xrpow_max=t[a+i])}e.scalefac[n]=r>>1}e.preflag=0,e.scalefac_scale=1}(t,n),i=!1):t.block_type==Ae.SHORT_TYPE&&0<r.subblock_gain&&(i=function(e,t,a){var n,s=t.scalefac;for(n=0;n<t.sfb_lmax;n++)if(16<=s[n])return!0;for(var r=0;r<3;r++){var i=0,o=0;for(n=t.sfb_lmax+r;n<t.sfbdivide;n+=3)i<s[n]&&(i=s[n]);for(;n<t.sfbmax;n+=3)o<s[n]&&(o=s[n]);if(!(i<16&&o<8)){if(7<=t.subblock_gain[r])return!0;t.subblock_gain[r]++;var _=e.scalefac_band.l[t.sfb_lmax];for(n=t.sfb_lmax+r;n<t.sfbmax;n+=3){var l=t.width[n],f=s[n];if(0<=(f-=4>>t.scalefac_scale))s[n]=f,_+=3*l;else{s[n]=0;var c=210+(f<<t.scalefac_scale+1);h=g.IPOW20(c),_+=l*(r+1);for(var u=-l;u<0;u++)a[_+u]*=h,a[_+u]>t.xrpow_max&&(t.xrpow_max=a[_+u]);_+=l*(3-r-1)}}var h=g.IPOW20(202);_+=t.width[n]*(r+1);for(var u=-t.width[n];u<0;u++)a[_+u]*=h,a[_+u]>t.xrpow_max&&(t.xrpow_max=a[_+u])}}return!1}(r,t,n)||o(t))),i||(i=2==r.mode_gr?S.scale_bitcount(t):S.scale_bitcount_lsf(r,t)),i))}this.setModules=function(e,t,a,n){r=t,this.rv=t,g=a,this.qupvt=a,S=n,s.setModules(g,S)},this.init_xrpow=function(e,t,a){var n=0,s=0|t.max_nonzero_coeff;if(t.xrpow_max=0,Se.fill(a,s,576,0),1e-20<(n=function(e,t,a,n){for(var s=n=0;s<=a;++s){var r=Math.abs(e.xr[s]);n+=r,t[s]=Math.sqrt(r*Math.sqrt(r)),t[s]>e.xrpow_max&&(e.xrpow_max=t[s])}return n}(t,a,s,n))){var r=0;0!=(2&e.substep_shaping)&&(r=1);for(var i=0;i<t.psymax;i++)e.pseudohalf[i]=r;return!0}return Se.fill(t.l3_enc,0,576,0),!1},this.init_outer_loop=function(e,t){t.part2_3_length=0,t.big_values=0,t.count1=0,t.global_gain=210,t.scalefac_compress=0,t.table_select[0]=0,t.table_select[1]=0,t.table_select[2]=0,t.subblock_gain[0]=0,t.subblock_gain[1]=0,t.subblock_gain[2]=0,t.subblock_gain[3]=0,t.region0_count=0,t.region1_count=0,t.preflag=0,t.scalefac_scale=0,t.count1table_select=0,t.part2_length=0,t.sfb_lmax=Ae.SBPSY_l,t.sfb_smin=Ae.SBPSY_s,t.psy_lmax=e.sfb21_extra?Ae.SBMAX_l:Ae.SBPSY_l,t.psymax=t.psy_lmax,t.sfbmax=t.sfb_lmax,t.sfbdivide=11;for(var a=0;a<Ae.SBMAX_l;a++)t.width[a]=e.scalefac_band.l[a+1]-e.scalefac_band.l[a],t.window[a]=3;if(t.block_type==Ae.SHORT_TYPE){var n=de(576);t.sfb_smin=0,(t.sfb_lmax=0)!=t.mixed_block_flag&&me(),t.psymax=t.sfb_lmax+3*((e.sfb21_extra?Ae.SBMAX_s:Ae.SBPSY_s)-t.sfb_smin),t.sfbmax=t.sfb_lmax+3*(Ae.SBPSY_s-t.sfb_smin),t.sfbdivide=t.sfbmax-18,t.psy_lmax=t.sfb_lmax;var s=e.scalefac_band.l[t.sfb_lmax];I.arraycopy(t.xr,0,n,0,576);for(var a=t.sfb_smin;a<Ae.SBMAX_s;a++)for(var r=e.scalefac_band.s[a],i=e.scalefac_band.s[a+1],o=0;o<3;o++)for(var _=r;_<i;_++)t.xr[s++]=n[3*_+o];for(var l=t.sfb_lmax,a=t.sfb_smin;a<Ae.SBMAX_s;a++)t.width[l]=t.width[l+1]=t.width[l+2]=e.scalefac_band.s[a+1]-e.scalefac_band.s[a],t.window[l]=0,t.window[l+1]=1,t.window[l+2]=2,l+=3}t.count1bits=0,t.sfb_partition_table=g.nr_of_sfb_block[0][0],t.slen[0]=0,t.slen[1]=0,t.slen[2]=0,t.slen[3]=0,t.max_nonzero_coeff=575,Se.fill(t.scalefac,0),function(e,t){var a=e.ATH,n=t.xr;if(t.block_type!=Ae.SHORT_TYPE)for(var s=!1,r=Ae.PSFB21-1;0<=r&&!s;r--){var i=e.scalefac_band.psfb21[r],o=e.scalefac_band.psfb21[r+1],_=g.athAdjust(a.adjust,a.psfb21[r],a.floor);1e-12<e.nsPsy.longfact[21]&&(_*=e.nsPsy.longfact[21]);for(var l=o-1;i<=l;l--){if(!(Math.abs(n[l])<_)){s=!0;break}n[l]=0}}else for(var f=0;f<3;f++)for(var s=!1,r=Ae.PSFB12-1;0<=r&&!s;r--){var i=3*e.scalefac_band.s[12]+(e.scalefac_band.s[13]-e.scalefac_band.s[12])*f+(e.scalefac_band.psfb12[r]-e.scalefac_band.psfb12[0]),o=i+(e.scalefac_band.psfb12[r+1]-e.scalefac_band.psfb12[r]),c=g.athAdjust(a.adjust,a.psfb12[r],a.floor);1e-12<e.nsPsy.shortfact[12]&&(c*=e.nsPsy.shortfact[12]);for(var l=o-1;i<=l;l--){if(!(Math.abs(n[l])<c)){s=!0;break}n[l]=0}}}(e,t)},w.BINSEARCH_NONE=new w(0),w.BINSEARCH_UP=new w(1),w.BINSEARCH_DOWN=new w(2),this.outer_loop=function(e,t,a,n,s,r){var i=e.internal_flags,o=new B,_=de(576),l=de(F.SFBMAX),f=new x,c=new function(){this.global_gain=0,this.sfb_count1=0,this.step=ve(39),this.noise=de(39),this.noise_log=de(39)},u=9999999,h=!1;if(function(e,t,a,n,s){var r,i=e.CurrentStep[n],o=!1,_=e.OldValue[n],l=w.BINSEARCH_NONE;for(t.global_gain=_,a-=t.part2_length;;){var f;if(r=S.count_bits(e,s,t,null),1==i||r==a)break;a<r?(l==w.BINSEARCH_DOWN&&(o=!0),o&&(i/=2),l=w.BINSEARCH_UP,f=i):(l==w.BINSEARCH_UP&&(o=!0),o&&(i/=2),l=w.BINSEARCH_DOWN,f=-i),t.global_gain+=f,t.global_gain<0&&me(),255<t.global_gain&&me()}for(;a<r&&t.global_gain<255;)t.global_gain++,r=S.count_bits(e,s,t,null);e.CurrentStep[n]=4<=_-t.global_gain?4:2,e.OldValue[n]=t.global_gain,t.part2_3_length=r}(i,t,r,s,n),0==i.noise_shaping)return 100;g.calc_noise(t,a,l,f,c),f.bits=t.part2_3_length,o.assign(t);var b=0;for(I.arraycopy(n,0,_,0,576);!h;){do{var p,m=new x,v=255;if(p=0!=(2&i.substep_shaping)?20:3,i.sfb21_extra&&me(),!y(e,o,l,n))break;0!=o.scalefac_scale&&(v=254);var d=r-o.part2_length;if(d<=0)break;for(;(o.part2_3_length=S.count_bits(i,n,o,c))>d&&o.global_gain<=v;)o.global_gain++;if(o.global_gain>v)break;if(0==f.over_count){for(;(o.part2_3_length=S.count_bits(i,n,o,c))>u&&o.global_gain<=v;)o.global_gain++;if(o.global_gain>v)break}if(g.calc_noise(o,a,l,m,c),m.bits=o.part2_3_length,0!=(M(t.block_type!=Ae.SHORT_TYPE?e.quant_comp:e.quant_comp_short,f,m)?1:0))u=t.part2_3_length,f=m,t.assign(o),b=0,I.arraycopy(n,0,_,0,576);else if(0==i.full_outer_loop){if(++b>p&&0==f.over_count)break;i.noise_shaping_amp,i.noise_shaping_amp}}while(o.global_gain+o.scalefac_scale<255);3==i.noise_shaping_amp?me():h=!0}return e.VBR==Me.vbr_rh||e.VBR==Me.vbr_mtrh?I.arraycopy(_,0,n,0,576):0!=(1&i.substep_shaping)&&me(),f.over_count},this.iteration_finish_one=function(e,t,a){var n=e.l3_side,s=n.tt[t][a];S.best_scalefac_store(e,t,a,n),1==e.use_best_huffman&&S.best_huffman_divide(e,s),r.ResvAdjust(e,s)}}function L(){this.thm=new i,this.en=new i}function Ae(){var k=Ae.MPG_MD_MS_LR,R=null,x=this.psy=null,B=null;this.setModules=function(e,t,a,n){R=e,this.psy=t,x=t,B=n};var T=new function(){var u=[-.1482523854003001,32.308141959636465,296.40344946382766,883.1344870032432,11113.947376231741,1057.2713659324597,305.7402417275812,30.825928907280012,3.8533188138216365,59.42900443849514,709.5899960123345,5281.91112291017,-5829.66483675846,-817.6293103748613,-76.91656988279972,-4.594269939176596,.9063471690191471,.1960342806591213,-.15466694054279598,34.324387823855965,301.8067566458425,817.599602898885,11573.795901679885,1181.2520595540152,321.59731579894424,31.232021761053772,3.7107095756221318,53.650946155329365,684.167428119626,5224.56624370173,-6366.391851890084,-908.9766368219582,-89.83068876699639,-5.411397422890401,.8206787908286602,.3901806440322567,-.16070888947830023,36.147034243915876,304.11815768187864,732.7429163887613,11989.60988270091,1300.012278487897,335.28490093152146,31.48816102859945,3.373875931311736,47.232241542899175,652.7371796173471,5132.414255594984,-6909.087078780055,-1001.9990371107289,-103.62185754286375,-6.104916304710272,.7416505462720353,.5805693545089249,-.16636367662261495,37.751650073343995,303.01103387567713,627.9747488785183,12358.763425278165,1412.2779918482834,346.7496836825721,31.598286663170416,3.1598635433980946,40.57878626349686,616.1671130880391,5007.833007176154,-7454.040671756168,-1095.7960341867115,-118.24411666465777,-6.818469345853504,.6681786379192989,.7653668647301797,-.1716176790982088,39.11551877123304,298.3413246578966,503.5259106886539,12679.589408408976,1516.5821921214542,355.9850766329023,31.395241710249053,2.9164211881972335,33.79716964664243,574.8943997801362,4853.234992253242,-7997.57021486075,-1189.7624067269965,-133.6444792601766,-7.7202770609839915,.5993769336819237,.9427934736519954,-.17645823955292173,40.21879108166477,289.9982036694474,359.3226160751053,12950.259102786438,1612.1013903507662,362.85067106591504,31.045922092242872,2.822222032597987,26.988862316190684,529.8996541764288,4671.371946949588,-8535.899136645805,-1282.5898586244496,-149.58553632943463,-8.643494270763135,.5345111359507916,1.111140466039205,-.36174739330527045,41.04429910497807,277.5463268268618,195.6386023135583,13169.43812144731,1697.6433561479398,367.40983966190305,30.557037410382826,2.531473372857427,20.070154905927314,481.50208566532336,4464.970341588308,-9065.36882077239,-1373.62841526722,-166.1660487028118,-9.58289321133207,.4729647758913199,1.268786568327291,-.36970682634889585,41.393213350082036,261.2935935556502,12.935476055240873,13336.131683328815,1772.508612059496,369.76534388639965,29.751323653701338,2.4023193045459172,13.304795348228817,430.5615775526625,4237.0568611071185,-9581.931701634761,-1461.6913552409758,-183.12733958476446,-10.718010163869403,.41421356237309503,1.414213562373095,-.37677560326535325,41.619486213528496,241.05423794991074,-187.94665032361226,13450.063605744153,1836.153896465782,369.4908799925761,29.001847876923147,2.0714759319987186,6.779591200894186,377.7767837205709,3990.386575512536,-10081.709459700915,-1545.947424837898,-200.3762958015653,-11.864482073055006,.3578057213145241,1.546020906725474,-.3829366947518991,41.1516456456653,216.47684307105183,-406.1569483347166,13511.136535077321,1887.8076599260432,367.3025214564151,28.136213436723654,1.913880671464418,.3829366947518991,323.85365704338597,3728.1472257487526,-10561.233882199509,-1625.2025997821418,-217.62525175416,-13.015432208941645,.3033466836073424,1.66293922460509,-.5822628872992417,40.35639251440489,188.20071124269245,-640.2706748618148,13519.21490106562,1927.6022433578062,362.8197642637487,26.968821921868447,1.7463817695935329,-5.62650678237171,269.3016715297017,3453.386536448852,-11016.145278780888,-1698.6569643425091,-234.7658734267683,-14.16351421663124,.2504869601913055,1.76384252869671,-.5887180101749253,39.23429103868072,155.76096234403798,-889.2492977967378,13475.470561874661,1955.0535223723712,356.4450994756727,25.894952980042156,1.5695032905781554,-11.181939564328772,214.80884394039484,3169.1640829158237,-11443.321309975563,-1765.1588461316153,-251.68908574481912,-15.49755935939164,.198912367379658,1.847759065022573,-.7912582233652842,37.39369355329111,119.699486012458,-1151.0956593239027,13380.446257078214,1970.3952110853447,348.01959814116185,24.731487364283044,1.3850130831637748,-16.421408865300393,161.05030052864092,2878.3322807850063,-11838.991423510031,-1823.985884688674,-268.2854986386903,-16.81724543849939,.1483359875383474,1.913880671464418,-.7960642926861912,35.2322109610459,80.01928065061526,-1424.0212633405113,13235.794061869668,1973.804052543835,337.9908651258184,23.289159354463873,1.3934255946442087,-21.099669467133474,108.48348407242611,2583.700758091299,-12199.726194855148,-1874.2780658979746,-284.2467154529415,-18.11369784385905,.09849140335716425,1.961570560806461,-.998795456205172,32.56307803611191,36.958364584370486,-1706.075448829146,13043.287458812016,1965.3831106103316,326.43182772364605,22.175018750622293,1.198638339011324,-25.371248002043963,57.53505923036915,2288.41886619975,-12522.674544337233,-1914.8400385312243,-299.26241273417224,-19.37805630698734,.04912684976946725,1.990369453344394,.035780907*V.SQRT2*.5/2384e-9,.017876148*V.SQRT2*.5/2384e-9,.003134727*V.SQRT2*.5/2384e-9,.002457142*V.SQRT2*.5/2384e-9,971317e-9*V.SQRT2*.5/2384e-9,218868e-9*V.SQRT2*.5/2384e-9,101566e-9*V.SQRT2*.5/2384e-9,13828e-9*V.SQRT2*.5/2384e-9,12804.797818791945,1945.5515939597317,313.4244966442953,49591e-9/2384e-9,1995.1556208053692,21458e-9/2384e-9,-69618e-9/2384e-9],A=[[2.382191739347913e-13,6.423305872147834e-13,9.400849094049688e-13,1.122435026096556e-12,1.183840321267481e-12,1.122435026096556e-12,9.40084909404969e-13,6.423305872147839e-13,2.382191739347918e-13,5.456116108943412e-12,4.878985199565852e-12,4.240448995017367e-12,3.559909094758252e-12,2.858043359288075e-12,2.156177623817898e-12,1.475637723558783e-12,8.371015190102974e-13,2.599706096327376e-13,-5.456116108943412e-12,-4.878985199565852e-12,-4.240448995017367e-12,-3.559909094758252e-12,-2.858043359288076e-12,-2.156177623817898e-12,-1.475637723558783e-12,-8.371015190102975e-13,-2.599706096327376e-13,-2.382191739347923e-13,-6.423305872147843e-13,-9.400849094049696e-13,-1.122435026096556e-12,-1.183840321267481e-12,-1.122435026096556e-12,-9.400849094049694e-13,-6.42330587214784e-13,-2.382191739347918e-13],[2.382191739347913e-13,6.423305872147834e-13,9.400849094049688e-13,1.122435026096556e-12,1.183840321267481e-12,1.122435026096556e-12,9.400849094049688e-13,6.423305872147841e-13,2.382191739347918e-13,5.456116108943413e-12,4.878985199565852e-12,4.240448995017367e-12,3.559909094758253e-12,2.858043359288075e-12,2.156177623817898e-12,1.475637723558782e-12,8.371015190102975e-13,2.599706096327376e-13,-5.461314069809755e-12,-4.921085770524055e-12,-4.343405037091838e-12,-3.732668368707687e-12,-3.093523840190885e-12,-2.430835727329465e-12,-1.734679010007751e-12,-9.74825365660928e-13,-2.797435120168326e-13,0,0,0,0,0,0,-2.283748241799531e-13,-4.037858874020686e-13,-2.146547464825323e-13],[.1316524975873958,.414213562373095,.7673269879789602,1.091308501069271,1.303225372841206,1.56968557711749,1.920982126971166,2.414213562373094,3.171594802363212,4.510708503662055,7.595754112725146,22.90376554843115,.984807753012208,.6427876096865394,.3420201433256688,.9396926207859084,-.1736481776669303,-.7660444431189779,.8660254037844387,.5,-.5144957554275265,-.4717319685649723,-.3133774542039019,-.1819131996109812,-.09457419252642064,-.04096558288530405,-.01419856857247115,-.003699974673760037,.8574929257125442,.8817419973177052,.9496286491027329,.9833145924917901,.9955178160675857,.9991605581781475,.999899195244447,.9999931550702802],[0,0,0,0,0,0,2.283748241799531e-13,4.037858874020686e-13,2.146547464825323e-13,5.461314069809755e-12,4.921085770524055e-12,4.343405037091838e-12,3.732668368707687e-12,3.093523840190885e-12,2.430835727329466e-12,1.734679010007751e-12,9.74825365660928e-13,2.797435120168326e-13,-5.456116108943413e-12,-4.878985199565852e-12,-4.240448995017367e-12,-3.559909094758253e-12,-2.858043359288075e-12,-2.156177623817898e-12,-1.475637723558782e-12,-8.371015190102975e-13,-2.599706096327376e-13,-2.382191739347913e-13,-6.423305872147834e-13,-9.400849094049688e-13,-1.122435026096556e-12,-1.183840321267481e-12,-1.122435026096556e-12,-9.400849094049688e-13,-6.423305872147841e-13,-2.382191739347918e-13]],k=A[Ae.SHORT_TYPE],w=A[Ae.SHORT_TYPE],R=A[Ae.SHORT_TYPE],x=A[Ae.SHORT_TYPE],B=[0,1,16,17,8,9,24,25,4,5,20,21,12,13,28,29,2,3,18,19,10,11,26,27,6,7,22,23,14,15,30,31];function T(e,t,a){for(var n,s,r,i=10,o=t+238-14-286,_=-15;_<0;_++){var l,f,c;l=u[i+-10],f=e[o+-224]*l,c=e[t+224]*l,l=u[i+-9],f+=e[o+-160]*l,c+=e[t+160]*l,l=u[i+-8],f+=e[o+-96]*l,c+=e[t+96]*l,l=u[i+-7],f+=e[o+-32]*l,c+=e[t+32]*l,l=u[i+-6],f+=e[o+32]*l,c+=e[t+-32]*l,l=u[i+-5],f+=e[o+96]*l,c+=e[t+-96]*l,l=u[i+-4],f+=e[o+160]*l,c+=e[t+-160]*l,l=u[i+-3],f+=e[o+224]*l,c+=e[t+-224]*l,l=u[i+-2],f+=e[t+-256]*l,c-=e[o+256]*l,l=u[i+-1],f+=e[t+-192]*l,c-=e[o+192]*l,l=u[i+0],f+=e[t+-128]*l,c-=e[o+128]*l,l=u[i+1],f+=e[t+-64]*l,c-=e[o+64]*l,l=u[i+2],f+=e[t+0]*l,c-=e[o+0]*l,l=u[i+3],f+=e[t+64]*l,c-=e[o+-64]*l,l=u[i+4],f+=e[t+128]*l,c-=e[o+-128]*l,l=u[i+5],f+=e[t+192]*l,c-=e[o+-192]*l,f*=u[i+6],l=c-f,a[30+2*_]=c+f,a[31+2*_]=u[i+7]*l,i+=18,t--,o++}c=e[t+-16]*u[i+-10],f=e[t+-32]*u[i+-2],c+=(e[t+-48]-e[t+16])*u[i+-9],f+=e[t+-96]*u[i+-1],c+=(e[t+-80]+e[t+48])*u[i+-8],f+=e[t+-160]*u[i+0],c+=(e[t+-112]-e[t+80])*u[i+-7],f+=e[t+-224]*u[i+1],c+=(e[t+-144]+e[t+112])*u[i+-6],f-=e[t+32]*u[i+2],c+=(e[t+-176]-e[t+144])*u[i+-5],f-=e[t+96]*u[i+3],c+=(e[t+-208]+e[t+176])*u[i+-4],f-=e[t+160]*u[i+4],c+=(e[t+-240]-e[t+208])*u[i+-3],f-=e[t+224],n=f-c,s=f+c,c=a[14],f=a[15]-c,a[31]=s+c,a[30]=n+f,a[15]=n-f,a[14]=s-c,r=a[28]-a[0],a[0]+=a[28],a[28]=r*u[i+-36+7],r=a[29]-a[1],a[1]+=a[29],a[29]=r*u[i+-36+7],r=a[26]-a[2],a[2]+=a[26],a[26]=r*u[i+-72+7],r=a[27]-a[3],a[3]+=a[27],a[27]=r*u[i+-72+7],r=a[24]-a[4],a[4]+=a[24],a[24]=r*u[i+-108+7],r=a[25]-a[5],a[5]+=a[25],a[25]=r*u[i+-108+7],r=a[22]-a[6],a[6]+=a[22],a[22]=r*V.SQRT2,r=a[23]-a[7],a[7]+=a[23],a[23]=r*V.SQRT2-a[7],a[7]-=a[6],a[22]-=a[7],a[23]-=a[22],r=a[6],a[6]=a[31]-r,a[31]=a[31]+r,r=a[7],a[7]=a[30]-r,a[30]=a[30]+r,r=a[22],a[22]=a[15]-r,a[15]=a[15]+r,r=a[23],a[23]=a[14]-r,a[14]=a[14]+r,r=a[20]-a[8],a[8]+=a[20],a[20]=r*u[i+-180+7],r=a[21]-a[9],a[9]+=a[21],a[21]=r*u[i+-180+7],r=a[18]-a[10],a[10]+=a[18],a[18]=r*u[i+-216+7],r=a[19]-a[11],a[11]+=a[19],a[19]=r*u[i+-216+7],r=a[16]-a[12],a[12]+=a[16],a[16]=r*u[i+-252+7],r=a[17]-a[13],a[13]+=a[17],a[17]=r*u[i+-252+7],r=-a[20]+a[24],a[20]+=a[24],a[24]=r*u[i+-216+7],r=-a[21]+a[25],a[21]+=a[25],a[25]=r*u[i+-216+7],r=a[4]-a[8],a[4]+=a[8],a[8]=r*u[i+-216+7],r=a[5]-a[9],a[5]+=a[9],a[9]=r*u[i+-216+7],r=a[0]-a[12],a[0]+=a[12],a[12]=r*u[i+-72+7],r=a[1]-a[13],a[1]+=a[13],a[13]=r*u[i+-72+7],r=a[16]-a[28],a[16]+=a[28],a[28]=r*u[i+-72+7],r=-a[17]+a[29],a[17]+=a[29],a[29]=r*u[i+-72+7],r=V.SQRT2*(a[2]-a[10]),a[2]+=a[10],a[10]=r,r=V.SQRT2*(a[3]-a[11]),a[3]+=a[11],a[11]=r,r=V.SQRT2*(-a[18]+a[26]),a[18]+=a[26],a[26]=r-a[18],r=V.SQRT2*(-a[19]+a[27]),a[19]+=a[27],a[27]=r-a[19],r=a[2],a[19]-=a[3],a[3]-=r,a[2]=a[31]-r,a[31]+=r,r=a[3],a[11]-=a[19],a[18]-=r,a[3]=a[30]-r,a[30]+=r,r=a[18],a[27]-=a[11],a[19]-=r,a[18]=a[15]-r,a[15]+=r,r=a[19],a[10]-=r,a[19]=a[14]-r,a[14]+=r,r=a[10],a[11]-=r,a[10]=a[23]-r,a[23]+=r,r=a[11],a[26]-=r,a[11]=a[22]-r,a[22]+=r,r=a[26],a[27]-=r,a[26]=a[7]-r,a[7]+=r,r=a[27],a[27]=a[6]-r,a[6]+=r,r=V.SQRT2*(a[0]-a[4]),a[0]+=a[4],a[4]=r,r=V.SQRT2*(a[1]-a[5]),a[1]+=a[5],a[5]=r,r=V.SQRT2*(a[16]-a[20]),a[16]+=a[20],a[20]=r,r=V.SQRT2*(a[17]-a[21]),a[17]+=a[21],a[21]=r,r=-V.SQRT2*(a[8]-a[12]),a[8]+=a[12],a[12]=r-a[8],r=-V.SQRT2*(a[9]-a[13]),a[9]+=a[13],a[13]=r-a[9],r=-V.SQRT2*(a[25]-a[29]),a[25]+=a[29],a[29]=r-a[25],r=-V.SQRT2*(a[24]+a[28]),a[24]-=a[28],a[28]=r-a[24],r=a[24]-a[16],a[24]=r,r=a[20]-r,a[20]=r,r=a[28]-r,a[28]=r,r=a[25]-a[17],a[25]=r,r=a[21]-r,a[21]=r,r=a[29]-r,a[29]=r,r=a[17]-a[1],a[17]=r,r=a[9]-r,a[9]=r,r=a[25]-r,a[25]=r,r=a[5]-r,a[5]=r,r=a[21]-r,a[21]=r,r=a[13]-r,a[13]=r,r=a[29]-r,a[29]=r,r=a[1]-a[0],a[1]=r,r=a[16]-r,a[16]=r,r=a[17]-r,a[17]=r,r=a[8]-r,a[8]=r,r=a[9]-r,a[9]=r,r=a[24]-r,a[24]=r,r=a[25]-r,a[25]=r,r=a[4]-r,a[4]=r,r=a[5]-r,a[5]=r,r=a[20]-r,a[20]=r,r=a[21]-r,a[21]=r,r=a[12]-r,a[12]=r,r=a[13]-r,a[13]=r,r=a[28]-r,a[28]=r,r=a[29]-r,a[29]=r,r=a[0],a[0]+=a[31],a[31]-=r,r=a[1],a[1]+=a[30],a[30]-=r,r=a[16],a[16]+=a[15],a[15]-=r,r=a[17],a[17]+=a[14],a[14]-=r,r=a[8],a[8]+=a[23],a[23]-=r,r=a[9],a[9]+=a[22],a[22]-=r,r=a[24],a[24]+=a[7],a[7]-=r,r=a[25],a[25]+=a[6],a[6]-=r,r=a[4],a[4]+=a[27],a[27]-=r,r=a[5],a[5]+=a[26],a[26]-=r,r=a[20],a[20]+=a[11],a[11]-=r,r=a[21],a[21]+=a[10],a[10]-=r,r=a[12],a[12]+=a[19],a[19]-=r,r=a[13],a[13]+=a[18],a[18]-=r,r=a[28],a[28]+=a[3],a[3]-=r,r=a[29],a[29]+=a[2],a[2]-=r}function E(e,t){for(var a=0;a<3;a++){var n,s,r,i,o,_;i=e[t+6]*A[Ae.SHORT_TYPE][0]-e[t+15],n=e[t+0]*A[Ae.SHORT_TYPE][2]-e[t+9],s=i+n,r=i-n,i=e[t+15]*A[Ae.SHORT_TYPE][0]+e[t+6],n=e[t+9]*A[Ae.SHORT_TYPE][2]+e[t+0],o=i+n,_=-i+n,n=2.069978111953089e-11*(e[t+3]*A[Ae.SHORT_TYPE][1]-e[t+12]),i=2.069978111953089e-11*(e[t+12]*A[Ae.SHORT_TYPE][1]+e[t+3]),e[t+0]=1.90752519173728e-11*s+n,e[t+15]=1.90752519173728e-11*-o+i,r=.8660254037844387*r*1.907525191737281e-11,o=.5*o*1.907525191737281e-11+i,e[t+3]=r-o,e[t+6]=r+o,s=.5*s*1.907525191737281e-11-n,_=.8660254037844387*_*1.907525191737281e-11,e[t+9]=s+_,e[t+12]=s-_,t++}}function C(e,t,a){var n,s,r,i,o,_,l,f,c,u,h,b,p,m,v,d,g,S;r=a[17]-a[9],o=a[15]-a[11],_=a[14]-a[12],l=a[0]+a[8],f=a[1]+a[7],c=a[2]+a[6],u=a[3]+a[5],e[t+17]=l+c-u-(f-a[4]),s=(l+c-u)*w[19]+(f-a[4]),n=(r-o-_)*w[18],e[t+5]=n+s,e[t+6]=n-s,i=(a[16]-a[10])*w[18],f=f*w[19]+a[4],n=r*w[12]+i+o*w[13]+_*w[14],s=-l*w[16]+f-c*w[17]+u*w[15],e[t+1]=n+s,e[t+2]=n-s,n=r*w[13]-i-o*w[14]+_*w[12],s=-l*w[17]+f-c*w[15]+u*w[16],e[t+9]=n+s,e[t+10]=n-s,n=r*w[14]-i+o*w[12]-_*w[13],s=l*w[15]-f+c*w[16]-u*w[17],e[t+13]=n+s,e[t+14]=n-s,h=a[8]-a[0],p=a[6]-a[2],m=a[5]-a[3],v=a[17]+a[9],d=a[16]+a[10],g=a[15]+a[11],S=a[14]+a[12],e[t+0]=v+g+S+(d+a[13]),n=(v+g+S)*w[19]-(d+a[13]),s=(h-p+m)*w[18],e[t+11]=n+s,e[t+12]=n-s,b=(a[7]-a[1])*w[18],d=a[13]-d*w[19],n=v*w[15]-d+g*w[16]+S*w[17],s=h*w[14]+b+p*w[12]+m*w[13],e[t+3]=n+s,e[t+4]=n-s,n=-v*w[17]+d-g*w[15]-S*w[16],s=h*w[13]+b-p*w[14]-m*w[12],e[t+7]=n+s,e[t+8]=n-s,n=-v*w[16]+d-g*w[17]-S*w[15],s=h*w[12]-b+p*w[13]-m*w[14],e[t+15]=n+s,e[t+16]=n-s}this.mdct_sub48=function(e,t,a){for(var n=t,s=286,r=0;r<e.channels_out;r++){for(var i=0;i<e.mode_gr;i++){for(var o,_=e.l3_side.tt[i][r],l=_.xr,f=0,c=e.sb_sample[r][1-i],u=0,h=0;h<9;h++)for(T(n,s,c[u]),T(n,s+32,c[u+1]),u+=2,s+=64,o=1;o<32;o+=2)c[u-1][o]*=-1;for(o=0;o<32;o++,f+=18){var b=_.block_type,p=e.sb_sample[r][i],m=e.sb_sample[r][1-i];if(0!=_.mixed_block_flag&&o<2&&(b=0),e.amp_filter[o]<1e-12)Se.fill(l,f+0,f+18,0);else if(e.amp_filter[o]<1&&me(),b==Ae.SHORT_TYPE){for(var h=-3;h<0;h++){var v=A[Ae.SHORT_TYPE][h+3];l[f+3*h+9]=p[9+h][B[o]]*v-p[8-h][B[o]],l[f+3*h+18]=p[14-h][B[o]]*v+p[15+h][B[o]],l[f+3*h+10]=p[15+h][B[o]]*v-p[14-h][B[o]],l[f+3*h+19]=m[2-h][B[o]]*v+m[3+h][B[o]],l[f+3*h+11]=m[3+h][B[o]]*v-m[2-h][B[o]],l[f+3*h+20]=m[8-h][B[o]]*v+m[9+h][B[o]]}E(l,f)}else{for(var d=de(18),h=-9;h<0;h++){var g,S;g=A[b][h+27]*m[h+9][B[o]]+A[b][h+36]*m[8-h][B[o]],S=A[b][h+9]*p[h+9][B[o]]-A[b][h+18]*p[8-h][B[o]],d[h+9]=g-S*k[3+h+9],d[h+18]=g*k[3+h+9]+S}C(l,f,d)}if(b!=Ae.SHORT_TYPE&&0!=o)for(var h=7;0<=h;--h){var w,M;w=l[f+h]*R[20+h]+l[f+-1-h]*x[28+h],M=l[f+h]*x[28+h]-l[f+-1-h]*R[20+h],l[f+-1-h]=w,l[f+h]=M}}}if(n=a,s=286,1==e.mode_gr)for(var y=0;y<18;y++)I.arraycopy(e.sb_sample[r][1][y],0,e.sb_sample[r][0][y],0,32)}}};this.lame_encode_mp3_frame=function(e,t,a,n,s,r){var i,o=E([2,2]);o[0][0]=new L,o[0][1]=new L,o[1][0]=new L,o[1][1]=new L;var _,l=E([2,2]);l[0][0]=new L,l[0][1]=new L,l[1][0]=new L,l[1][1]=new L;var f,c,u,h=[null,null],b=e.internal_flags,p=ge([2,4]),m=[[0,0],[0,0]],v=[[0,0],[0,0]];if(h[0]=t,h[1]=a,0==b.lame_encode_frame_init&&function(e,t){var a,n,s=e.internal_flags;if(0==s.lame_encode_frame_init){var r,i,o=de(2014),_=de(2014);for(s.lame_encode_frame_init=1,i=r=0;r<286+576*(1+s.mode_gr);++r)r<576*s.mode_gr?(o[r]=0,2==s.channels_out&&(_[r]=0)):(o[r]=t[0][i],2==s.channels_out&&(_[r]=t[1][i]),++i);for(n=0;n<s.mode_gr;n++)for(a=0;a<s.channels_out;a++)s.l3_side.tt[n][a].block_type=Ae.SHORT_TYPE;T.mdct_sub48(s,o,_)}}(e,h),b.padding=0,(b.slot_lag-=b.frac_SpF)<0&&(b.slot_lag+=e.out_samplerate,b.padding=1),0!=b.psymodel){var d,g=[null,null],S=0,w=ve(2);for(u=0;u<b.mode_gr;u++){for(c=0;c<b.channels_out;c++)g[c]=h[c],S=576+576*u-Ae.FFTOFFSET;if(e.VBR==Me.vbr_mtrh||e.VBR==Me.vbr_mt?me():d=x.L3psycho_anal_ns(e,g,S,u,o,l,m[u],v[u],p[u],w),0!=d)return-4;for(e.mode==ye.JOINT_STEREO&&me(),c=0;c<b.channels_out;c++){var M=b.l3_side.tt[u][c];M.block_type=w[c],M.mixed_block_flag=0}}}else me();if(function(e){var t,a;if(0==e.ATH.useAdjust)return e.ATH.adjust=1;if(a=e.loudness_sq[0][0],t=e.loudness_sq[1][0],2==e.channels_out?me():(a+=a,t+=t),2==e.mode_gr&&(a=Math.max(a,t)),a*=.5,.03125<(a*=e.ATH.aaSensitivityP))1<=e.ATH.adjust?e.ATH.adjust=1:e.ATH.adjust<e.ATH.adjustLimit&&(e.ATH.adjust=e.ATH.adjustLimit),e.ATH.adjustLimit=1;else{var n=31.98*a+625e-6;e.ATH.adjust>=n?(e.ATH.adjust*=.075*n+.925,e.ATH.adjust<n&&(e.ATH.adjust=n)):e.ATH.adjustLimit>=n?e.ATH.adjust=n:e.ATH.adjust<e.ATH.adjustLimit&&(e.ATH.adjust=e.ATH.adjustLimit),e.ATH.adjustLimit=n}}(b),T.mdct_sub48(b,h[0],h[1]),b.mode_ext=Ae.MPG_MD_LR_LR,e.force_ms?b.mode_ext=Ae.MPG_MD_MS_LR:e.mode==ye.JOINT_STEREO&&me(),b.mode_ext==k?(_=l,f=v):(_=o,f=m),e.analysis&&null!=b.pinfo&&me(),e.VBR==Me.vbr_off||e.VBR==Me.vbr_abr){var y,A;for(y=0;y<18;y++)b.nsPsy.pefirbuf[y]=b.nsPsy.pefirbuf[y+1];for(u=A=0;u<b.mode_gr;u++)for(c=0;c<b.channels_out;c++)A+=f[u][c];for(b.nsPsy.pefirbuf[18]=A,A=b.nsPsy.pefirbuf[9],y=0;y<9;y++)A+=(b.nsPsy.pefirbuf[y]+b.nsPsy.pefirbuf[18-y])*Ae.fircoef[y];for(A=3350*b.mode_gr*b.channels_out/A,u=0;u<b.mode_gr;u++)for(c=0;c<b.channels_out;c++)f[u][c]*=A}return b.iteration_loop.iteration_loop(e,f,[.5,.5],_),R.format_bitstream(e),i=R.copy_buffer(b,n,s,r,1),e.bWriteVbrTag&&B.addVbrFrame(e),e.analysis&&null!=b.pinfo&&me(),function(e){var t,a;for(e.bitrate_stereoMode_Hist[e.bitrate_index][4]++,e.bitrate_stereoMode_Hist[15][4]++,2==e.channels_out&&me(),t=0;t<e.mode_gr;++t)for(a=0;a<e.channels_out;++a){var n=0|e.l3_side.tt[t][a].block_type;0!=e.l3_side.tt[t][a].mixed_block_flag&&(n=4),e.bitrate_blockType_Hist[e.bitrate_index][n]++,e.bitrate_blockType_Hist[e.bitrate_index][5]++,e.bitrate_blockType_Hist[15][n]++,e.bitrate_blockType_Hist[15][5]++}}(b),i}}function i(){this.l=de(Ae.SBMAX_l),this.s=ge([Ae.SBMAX_s,3]);var n=this;this.assign=function(e){I.arraycopy(e.l,0,n.l,0,Ae.SBMAX_l);for(var t=0;t<Ae.SBMAX_s;t++)for(var a=0;a<3;a++)n.s[t][a]=e.s[t][a]}}function j(){var e=40;function t(){this.write_timing=0,this.ptr=0,this.buf=w(e)}this.Class_ID=0,this.lame_encode_frame_init=0,this.iteration_init_init=0,this.fill_buffer_resample_init=0,this.mfbuf=ge([2,j.MFSIZE]),this.mode_gr=0,this.channels_in=0,this.channels_out=0,this.resample_ratio=0,this.mf_samples_to_encode=0,this.mf_size=0,this.VBR_min_bitrate=0,this.VBR_max_bitrate=0,this.bitrate_index=0,this.samplerate_index=0,this.mode_ext=0,this.lowpass1=0,this.lowpass2=0,this.highpass1=0,this.highpass2=0,this.noise_shaping=0,this.noise_shaping_amp=0,this.substep_shaping=0,this.psymodel=0,this.noise_shaping_stop=0,this.subblock_gain=0,this.use_best_huffman=0,this.full_outer_loop=0,this.l3_side=new function(){this.tt=[[null,null],[null,null]],this.main_data_begin=0,this.private_bits=0,this.resvDrain_pre=0,this.resvDrain_post=0,this.scfsi=[ve(4),ve(4)];for(var e=0;e<2;e++)for(var t=0;t<2;t++)this.tt[e][t]=new B},this.ms_ratio=de(2),this.padding=0,this.frac_SpF=0,this.slot_lag=0,this.tag_spec=null,this.nMusicCRC=0,this.OldValue=ve(2),this.CurrentStep=ve(2),this.masking_lower=0,this.bv_scf=ve(576),this.pseudohalf=ve(F.SFBMAX),this.sfb21_extra=!1,this.inbuf_old=new Array(2),this.blackfilt=new Array(2*j.BPC+1),this.itime=n(2),this.sideinfo_len=0,this.sb_sample=ge([2,2,18,Ae.SBLIMIT]),this.amp_filter=de(32),this.header=new Array(j.MAX_HEADER_BUF),this.h_ptr=0,this.w_ptr=0,this.ancillary_flag=0,this.ResvSize=0,this.ResvMax=0,this.scalefac_band=new r,this.minval_l=de(Ae.CBANDS),this.minval_s=de(Ae.CBANDS),this.nb_1=ge([4,Ae.CBANDS]),this.nb_2=ge([4,Ae.CBANDS]),this.nb_s1=ge([4,Ae.CBANDS]),this.nb_s2=ge([4,Ae.CBANDS]),this.s3_ss=null,this.s3_ll=null,this.decay=0,this.thm=new Array(4),this.en=new Array(4),this.tot_ener=de(4),this.loudness_sq=ge([2,2]),this.loudness_sq_save=de(2),this.mld_l=de(Ae.SBMAX_l),this.mld_s=de(Ae.SBMAX_s),this.bm_l=ve(Ae.SBMAX_l),this.bo_l=ve(Ae.SBMAX_l),this.bm_s=ve(Ae.SBMAX_s),this.bo_s=ve(Ae.SBMAX_s),this.npart_l=0,this.npart_s=0,this.s3ind=S([Ae.CBANDS,2]),this.s3ind_s=S([Ae.CBANDS,2]),this.numlines_s=ve(Ae.CBANDS),this.numlines_l=ve(Ae.CBANDS),this.rnumlines_l=de(Ae.CBANDS),this.mld_cb_l=de(Ae.CBANDS),this.mld_cb_s=de(Ae.CBANDS),this.numlines_s_num1=0,this.numlines_l_num1=0,this.pe=de(4),this.ms_ratio_s_old=0,this.ms_ratio_l_old=0,this.ms_ener_ratio_old=0,this.blocktype_old=ve(2),this.nsPsy=new function(){this.last_en_subshort=ge([4,9]),this.lastAttacks=ve(4),this.pefirbuf=de(19),this.longfact=de(Ae.SBMAX_l),this.shortfact=de(Ae.SBMAX_s),this.attackthre=0,this.attackthre_s=0},this.VBR_seek_table=new function(){this.sum=0,this.seen=0,this.want=0,this.pos=0,this.size=0,this.bag=null,this.nVbrNumFrames=0,this.nBytesWritten=0,this.TotalFrameSize=0},this.ATH=null,this.PSY=null,this.nogap_total=0,this.nogap_current=0,this.decode_on_the_fly=!0,this.findReplayGain=!0,this.findPeakSample=!0,this.PeakSample=0,this.RadioGain=0,this.AudiophileGain=0,this.rgdata=null,this.noclipGainChange=0,this.noclipScale=0,this.bitrate_stereoMode_Hist=S([16,5]),this.bitrate_blockType_Hist=S([16,6]),this.pinfo=null,this.hip=null,this.in_buffer_nsamples=0,this.in_buffer_0=null,this.in_buffer_1=null,this.iteration_loop=null;for(var a=0;a<this.en.length;a++)this.en[a]=new i;for(var a=0;a<this.thm.length;a++)this.thm[a]=new i;for(var a=0;a<this.header.length;a++)this.header[a]=new t}function X(){var k=new function(){var h=de(Ae.BLKSIZE),p=de(Ae.BLKSIZE_s/2),x=[.9238795325112867,.3826834323650898,.9951847266721969,.0980171403295606,.9996988186962042,.02454122852291229,.9999811752826011,.006135884649154475];function m(e,t,a){var n,s,r,i=0,o=t+(a<<=1);n=4;do{var _,l,f,c,u,h,b;for(b=n>>1,h=(u=(c=n)<<1)+c,n=u<<1,r=(s=t)+b;w=e[s+0]-e[s+c],S=e[s+0]+e[s+c],k=e[s+u]-e[s+h],y=e[s+u]+e[s+h],e[s+u]=S-y,e[s+0]=S+y,e[s+h]=w-k,e[s+c]=w+k,w=e[r+0]-e[r+c],S=e[r+0]+e[r+c],k=V.SQRT2*e[r+h],y=V.SQRT2*e[r+u],e[r+u]=S-y,e[r+0]=S+y,e[r+h]=w-k,e[r+c]=w+k,r+=n,(s+=n)<o;);for(l=x[i+0],_=x[i+1],f=1;f<b;f++){var p,m;p=1-2*_*_,m=2*_*l,s=t+f,r=t+c-f;do{var v,d,g,S,w,M,y,A,k,R;d=m*e[s+c]-p*e[r+c],v=p*e[s+c]+m*e[r+c],w=e[s+0]-v,S=e[s+0]+v,M=e[r+0]-d,g=e[r+0]+d,d=m*e[s+h]-p*e[r+h],v=p*e[s+h]+m*e[r+h],k=e[s+u]-v,y=e[s+u]+v,R=e[r+u]-d,A=e[r+u]+d,d=_*y-l*R,v=l*y+_*R,e[s+u]=S-v,e[s+0]=S+v,e[r+h]=M-d,e[r+c]=M+d,d=l*A-_*k,v=_*A+l*k,e[r+u]=g-v,e[r+0]=g+v,e[s+h]=w-d,e[s+c]=w+d,r+=n,s+=n}while(s<o);l=(p=l)*x[i+0]-_*x[i+1],_=p*x[i+1]+_*x[i+0]}i+=2}while(n<a)}var v=[0,128,64,192,32,160,96,224,16,144,80,208,48,176,112,240,8,136,72,200,40,168,104,232,24,152,88,216,56,184,120,248,4,132,68,196,36,164,100,228,20,148,84,212,52,180,116,244,12,140,76,204,44,172,108,236,28,156,92,220,60,188,124,252,2,130,66,194,34,162,98,226,18,146,82,210,50,178,114,242,10,138,74,202,42,170,106,234,26,154,90,218,58,186,122,250,6,134,70,198,38,166,102,230,22,150,86,214,54,182,118,246,14,142,78,206,46,174,110,238,30,158,94,222,62,190,126,254];this.fft_short=function(e,t,a,n,s){for(var r=0;r<3;r++){var i=Ae.BLKSIZE_s/2,o=65535&192*(r+1),_=Ae.BLKSIZE_s/8-1;do{var l,f,c,u,h,b=255&v[_<<2];l=p[b]*n[a][s+b+o],h=p[127-b]*n[a][s+b+o+128],f=l-h,l+=h,c=p[b+64]*n[a][s+b+o+64],h=p[63-b]*n[a][s+b+o+192],u=c-h,c+=h,i-=4,t[r][i+0]=l+c,t[r][i+2]=l-c,t[r][i+1]=f+u,t[r][i+3]=f-u,l=p[b+1]*n[a][s+b+o+1],h=p[126-b]*n[a][s+b+o+129],f=l-h,l+=h,c=p[b+65]*n[a][s+b+o+65],h=p[62-b]*n[a][s+b+o+193],u=c-h,c+=h,t[r][i+Ae.BLKSIZE_s/2+0]=l+c,t[r][i+Ae.BLKSIZE_s/2+2]=l-c,t[r][i+Ae.BLKSIZE_s/2+1]=f+u,t[r][i+Ae.BLKSIZE_s/2+3]=f-u}while(0<=--_);m(t[r],i,Ae.BLKSIZE_s/2)}},this.fft_long=function(e,t,a,n,s){var r=Ae.BLKSIZE/8-1,i=Ae.BLKSIZE/2;do{var o,_,l,f,c,u=255&v[r];o=h[u]*n[a][s+u],c=h[u+512]*n[a][s+u+512],_=o-c,o+=c,l=h[u+256]*n[a][s+u+256],c=h[u+768]*n[a][s+u+768],f=l-c,l+=c,t[0+(i-=4)]=o+l,t[i+2]=o-l,t[i+1]=_+f,t[i+3]=_-f,o=h[u+1]*n[a][s+u+1],c=h[u+513]*n[a][s+u+513],_=o-c,o+=c,l=h[u+257]*n[a][s+u+257],c=h[u+769]*n[a][s+u+769],f=l-c,l+=c,t[i+Ae.BLKSIZE/2+0]=o+l,t[i+Ae.BLKSIZE/2+2]=o-l,t[i+Ae.BLKSIZE/2+1]=_+f,t[i+Ae.BLKSIZE/2+3]=_-f}while(0<=--r);m(t,i,Ae.BLKSIZE/2)},this.init_fft=function(e){for(var t=0;t<Ae.BLKSIZE;t++)h[t]=.42-.5*Math.cos(2*Math.PI*(t+.5)/Ae.BLKSIZE)+.08*Math.cos(4*Math.PI*(t+.5)/Ae.BLKSIZE);for(var t=0;t<Ae.BLKSIZE_s/2;t++)p[t]=.5*(1-Math.cos(2*Math.PI*(t+.5)/Ae.BLKSIZE_s))}},R=2.302585092994046,d=2,g=16,E=.34,v=1/217621504/(Ae.BLKSIZE/2),S=.2302585093;function se(e,t,a,n,s,r,i,o,_,l,f){var c=e.internal_flags;_<2?(k.fft_long(c,n[s],_,l,f),k.fft_short(c,r[i],_,l,f)):2==_&&me(),t[0]=n[s+0][0],t[0]*=t[0];for(var u=Ae.BLKSIZE/2-1;0<=u;--u){var h=n[s+0][Ae.BLKSIZE/2-u],b=n[s+0][Ae.BLKSIZE/2+u];t[Ae.BLKSIZE/2-u]=.5*(h*h+b*b)}for(var p=2;0<=p;--p){a[p][0]=r[i+0][p][0],a[p][0]*=a[p][0];for(var u=Ae.BLKSIZE_s/2-1;0<=u;--u){var h=r[i+0][p][Ae.BLKSIZE_s/2-u],b=r[i+0][p][Ae.BLKSIZE_s/2+u];a[p][Ae.BLKSIZE_s/2-u]=.5*(h*h+b*b)}}for(var m=0,u=11;u<Ae.HBLKSIZE;u++)m+=t[u];c.tot_ener[_]=m,e.analysis&&me(),2==e.athaa_loudapprox&&_<2&&(c.loudness_sq[o][_]=c.loudness_sq_save[_],c.loudness_sq_save[_]=function(e,t){for(var a=0,n=0;n<Ae.BLKSIZE/2;++n)a+=e[n]*t.ATH.eql_w[n];return a*=v}(t,c))}var x,B,T,C=8,I=23,L=15,re=[1,.79433,.63096,.63096,.63096,.63096,.63096,.25119,.11749],f=[3.3246*3.3246,3.23837*3.23837,9.9500500969,9.0247369744,8.1854926609,7.0440875649,2.46209*2.46209,2.284*2.284,4.4892710641,1.96552*1.96552,1.82335*1.82335,1.69146*1.69146,2.4621061921,2.1508568964,1.37074*1.37074,1.31036*1.31036,1.5691069696,1.4555939904,1.16203*1.16203,1.2715945225,1.09428*1.09428,1.0659*1.0659,1.0779838276,1.0382591025,1],c=[1.7782755904,1.35879*1.35879,1.38454*1.38454,1.39497*1.39497,1.40548*1.40548,1.3537*1.3537,1.6999465924,1.22321*1.22321,1.3169398564,1],u=[5.5396212496,2.29259*2.29259,4.9868695969,2.12675*2.12675,2.02545*2.02545,1.87894*1.87894,1.74303*1.74303,1.61695*1.61695,2.2499700001,1.39148*1.39148,1.29083*1.29083,1.19746*1.19746,1.2339655056,1.0779838276];function ie(e,t,a,n,s,r){var i;if(e<t){if(!(t<e*B))return e+t;i=t/e}else{if(t*B<=e)return e+t;i=e/t}if(e+=t,n+3<=6){if(x<=i)return e;var o=0|V.FAST_LOG10_X(i,16);return e*c[o]}var _,l,o=0|V.FAST_LOG10_X(i,16);return t=0!=r?s.ATH.cb_s[a]*s.ATH.adjust:s.ATH.cb_l[a]*s.ATH.adjust,e<T*t?t<e?(_=1,o<=13&&(_=u[o]),l=V.FAST_LOG10_X(e/t,10/15),e*((f[o]-_)*l+_)):13<o?e:e*u[o]:e*f[o]}function oe(e,t,a,n,s){var r,i,o=0,_=0;for(r=i=0;r<Ae.SBMAX_s;++i,++r){for(var l=e.bo_s[r],f=e.npart_s,c=l<f?l:f;i<c;)o+=t[i],_+=a[i],i++;if(e.en[n].s[r][s]=o,e.thm[n].s[r][s]=_,f<=i){++r;break}var u=e.PSY.bo_s_weight[r],h=1-u;o=u*t[i],_=u*a[i],e.en[n].s[r][s]+=o,e.thm[n].s[r][s]+=_,o=h*t[i],_=h*a[i]}for(;r<Ae.SBMAX_s;++r)e.en[n].s[r][s]=0,e.thm[n].s[r][s]=0}function _e(e,t,a,n){var s,r,i=0,o=0;for(s=r=0;s<Ae.SBMAX_l;++r,++s){for(var _=e.bo_l[s],l=e.npart_l,f=_<l?_:l;r<f;)i+=t[r],o+=a[r],r++;if(e.en[n].l[s]=i,e.thm[n].l[s]=o,l<=r){++s;break}var c=e.PSY.bo_l_weight[s],u=1-c;i=c*t[r],o=c*a[r],e.en[n].l[s]+=i,e.thm[n].l[s]+=o,i=u*t[r],o=u*a[r]}for(;s<Ae.SBMAX_l;++s)e.en[n].l[s]=0,e.thm[n].l[s]=0}function le(e,t,a,n,s,r){var i,o,_=e.internal_flags;for(o=i=0;o<_.npart_s;++o){for(var l=0,f=0,c=_.numlines_s[o],u=0;u<c;++u,++i){var h=t[r][i];l+=h,f<h&&(f=h)}a[o]=l}for(i=o=0;o<_.npart_s;o++){var b=_.s3ind_s[o][0],p=_.s3_ss[i++]*a[b];for(++b;b<=_.s3ind_s[o][1];)p+=_.s3_ss[i]*a[b],++i,++b;var m=d*_.nb_s1[s][o];if(n[o]=Math.min(p,m),_.blocktype_old[1&s]==Ae.SHORT_TYPE){var m=g*_.nb_s2[s][o],v=n[o];n[o]=Math.min(m,v)}_.nb_s2[s][o]=_.nb_s1[s][o],_.nb_s1[s][o]=p}for(;o<=Ae.CBANDS;++o)a[o]=0,n[o]=0}function fe(e,t,a){return 1<=a?e:a<=0?t:0<t?Math.pow(e/t,a)*t:0}var _=[11.8,13.6,17.2,32,46.5,51.3,57.5,67.1,71.5,84.6,97.6,130];function ce(e,t){for(var a=309.07,n=0;n<Ae.SBMAX_s-1;n++)for(var s=0;s<3;s++){var r=e.thm.s[n][s];if(0<r){var i=r*t,o=e.en.s[n][s];i<o&&(a+=1e10*i<o?_[n]*(10*R):_[n]*V.FAST_LOG10(o/i))}}return a}var o=[6.8,5.8,5.8,6.4,6.5,9.9,12.1,14.4,15,18.9,21.6,26.9,34.2,40.2,46.8,56.5,60.7,73.9,85.7,93.4,126.1];function ue(e,t){for(var a=281.0575,n=0;n<Ae.SBMAX_l-1;n++){var s=e.thm.l[n];if(0<s){var r=s*t,i=e.en.l[n];r<i&&(a+=1e10*r<i?o[n]*(10*R):o[n]*V.FAST_LOG10(i/r))}}return a}function he(e,t,a,n,s){var r,i;for(r=i=0;r<e.npart_l;++r){var o,_=0,l=0;for(o=0;o<e.numlines_l[r];++o,++i){var f=t[i];_+=f,l<f&&(l=f)}a[r]=_,n[r]=l,s[r]=_*e.rnumlines_l[r]}}function be(e,t,a,n){var s=re.length-1,r=0,i=a[r]+a[r+1];if(0<i){var o=t[r];o<t[r+1]&&(o=t[r+1]);var _=0|(i=20*(2*o-i)/(i*(e.numlines_l[r]+e.numlines_l[r+1]-1)));s<_&&(_=s),n[r]=_}else n[r]=0;for(r=1;r<e.npart_l-1;r++)if(0<(i=a[r-1]+a[r]+a[r+1])){var o=t[r-1];o<t[r]&&(o=t[r]),o<t[r+1]&&(o=t[r+1]);var _=0|(i=20*(3*o-i)/(i*(e.numlines_l[r-1]+e.numlines_l[r]+e.numlines_l[r+1]-1)));s<_&&(_=s),n[r]=_}else n[r]=0;if(0<(i=a[r-1]+a[r])){var o=t[r-1];o<t[r]&&(o=t[r]);var _=0|(i=20*(2*o-i)/(i*(e.numlines_l[r-1]+e.numlines_l[r]-1)));s<_&&(_=s),n[r]=_}else n[r]=0}var pe=[-1.730326e-17,-.01703172,-1.349528e-17,.0418072,-6.73278e-17,-.0876324,-3.0835e-17,.1863476,-1.104424e-16,-.627638];function P(e){return e<0&&(e=0),e*=.001,13*Math.atan(.76*e)+3.5*Math.atan(e*e/56.25)}function H(e,t,a,n,s,r,i,o,_,l,f,c){var u,h=de(Ae.CBANDS+1),b=o/(15<c?1152:384),p=ve(Ae.HBLKSIZE);o/=_;var m=0,v=0;for(u=0;u<Ae.CBANDS;u++){var d;for(x=P(o*m),h[u]=o*m,d=m;P(o*d)-x<E&&d<=_/2;d++);for(e[u]=d-m,v=u+1;m<d;)p[m++]=u;if(_/2<m){m=_/2,++u;break}}h[u]=o*m;for(var g=0;g<c;g++){var S,w,M,y,A;M=l[g],y=l[g+1],(S=0|Math.floor(.5+f*(M-.5)))<0&&(S=0),w=0|Math.floor(.5+f*(y-.5)),_/2<w&&(w=_/2),a[g]=(p[S]+p[w])/2,t[g]=p[w];var k=b*y;i[g]=(k-h[t[g]])/(h[t[g]+1]-h[t[g]]),i[g]<0?i[g]=0:1<i[g]&&(i[g]=1),A=P(o*l[g]*f),A=Math.min(A,15.5)/15.5,r[g]=Math.pow(10,1.25*(1-Math.cos(Math.PI*A))-2.5)}for(var R=m=0;R<v;R++){var x,B,T=e[R];x=P(o*m),B=P(o*(m+T-1)),n[R]=.5*(x+B),x=P(o*(m-.5)),B=P(o*(m+T-.5)),s[R]=B-x,m+=T}return v}function O(e,t,a,n,s,r){var i,o,_,l,f,c,u=ge([Ae.CBANDS,Ae.CBANDS]),h=0;if(r)for(var b=0;b<t;b++)for(i=0;i<t;i++){var p=(o=a[b]-a[i],c=f=l=_=void 0,_=o,l=.5<=(_*=0<=_?3:1.5)&&_<=2.5?8*((c=_-.5)*c-2*c):0,((f=15.811389+7.5*(_+=.474)-17.5*Math.sqrt(1+_*_))<=-60?0:(_=Math.exp((l+f)*S),_/=.6609193))*n[i]);u[b][i]=p*s[b]}else me();for(var b=0;b<t;b++){for(i=0;i<t&&!(0<u[b][i]);i++);for(e[b][0]=i,i=t-1;0<i&&!(0<u[b][i]);i--);e[b][1]=i,h+=e[b][1]-e[b][0]+1}for(var m=de(h),v=0,b=0;b<t;b++)for(i=e[b][0];i<=e[b][1];i++)m[v++]=u[b][i];return m}function N(e){var t=P(e);return t=Math.min(t,15.5)/15.5,Math.pow(10,1.25*(1-Math.cos(Math.PI*t))-2.5)}function n(e,t){e<-.3&&(e=3410),e/=1e3,e=Math.max(.1,e);var a=3.64*Math.pow(e,-.8)-6.8*Math.exp(-.6*Math.pow(e-3.4,2))+6*Math.exp(-.15*Math.pow(e-8.7,2))+.001*(.6+.04*t)*Math.pow(e,4);return a}this.L3psycho_anal_ns=function(e,t,a,n,s,r,i,o,_,l){var f,c,u,h,b,p,m,v,d,g=e.internal_flags,S=ge([2,Ae.BLKSIZE]),w=ge([2,3,Ae.BLKSIZE_s]),M=de(Ae.CBANDS+1),y=de(Ae.CBANDS+1),A=de(Ae.CBANDS+2),k=ve(2),R=ve(2),x=ge([2,576]),B=ve(Ae.CBANDS+2),T=ve(Ae.CBANDS+2);for(Se.fill(T,0),f=g.channels_out,e.mode==ye.JOINT_STEREO&&(f=4),d=e.VBR==Me.vbr_off?0==g.ResvMax?0:g.ResvSize/g.ResvMax*.5:e.VBR==Me.vbr_rh||e.VBR==Me.vbr_mtrh||e.VBR==Me.vbr_mt?.6:1,c=0;c<g.channels_out;c++){var E=t[c],C=a+576-350-21+192;for(h=0;h<576;h++){var I,L;for(I=E[C+h+10],b=L=0;b<9;b+=2)I+=pe[b]*(E[C+h+b]+E[C+h+21-b]),L+=pe[b+1]*(E[C+h+b+1]+E[C+h+21-b-1]);x[c][h]=I+L}s[n][c].en.assign(g.en[c]),s[n][c].thm.assign(g.thm[c]),2<f&&me()}for(c=0;c<f;c++){var P,H=de(12),O=[0,0,0,0],N=de(12),V=1,D=de(Ae.CBANDS),F=de(Ae.CBANDS),j=[0,0,0,0],X=de(Ae.HBLKSIZE),Y=ge([3,Ae.HBLKSIZE_s]);for(h=0;h<3;h++)H[h]=g.nsPsy.last_en_subshort[c][h+6],N[h]=H[h]/g.nsPsy.last_en_subshort[c][h+4],O[0]+=H[h];2==c&&me();var z=x[1&c],q=0;for(h=0;h<9;h++){for(var G=q+64,U=1;q<G;q++)U<Math.abs(z[q])&&(U=Math.abs(z[q]));g.nsPsy.last_en_subshort[c][h]=H[h+3]=U,O[1+h/3]+=U,U>H[h+3-2]?U/=H[h+3-2]:U=H[h+3-2]>10*U?H[h+3-2]/(10*U):0,N[h+3]=U}for(e.analysis&&me(),P=3==c?g.nsPsy.attackthre_s:g.nsPsy.attackthre,h=0;h<12;h++)0==j[h/3]&&N[h]>P&&(j[h/3]=h%3+1);for(h=1;h<4;h++)(O[h-1]>O[h]?O[h-1]/O[h]:O[h]/O[h-1])<1.7&&(j[h]=0,1==h&&(j[0]=0));for(0!=j[0]&&0!=g.nsPsy.lastAttacks[c]&&(j[0]=0),3!=g.nsPsy.lastAttacks[c]&&j[0]+j[1]+j[2]+j[3]==0||((V=0)!=j[1]&&0!=j[0]&&(j[1]=0),0!=j[2]&&0!=j[1]&&(j[2]=0),0!=j[3]&&0!=j[2]&&(j[3]=0)),c<2?R[c]=V:me(),_[c]=g.tot_ener[c],se(e,X,Y,S,1&c,w,1&c,n,c,t,a),he(g,X,M,D,F),be(g,D,F,B),v=0;v<3;v++){var K,Z;for(le(e,Y,y,A,c,v),oe(g,y,A,c,v),m=0;m<Ae.SBMAX_s;m++){if(Z=g.thm[c].s[m][v],Z*=.8,2<=j[v]||1==j[v+1]){var W=0!=v?v-1:2,U=fe(g.thm[c].s[m][W],Z,.6*d);Z=Math.min(Z,U)}if(1==j[v]){var W=0!=v?v-1:2,U=fe(g.thm[c].s[m][W],Z,.3*d);Z=Math.min(Z,U)}else if(0!=v&&3==j[v-1]||0==v&&3==g.nsPsy.lastAttacks[c]){var W=2!=v?v+1:0,U=fe(g.thm[c].s[m][W],Z,.3*d);Z=Math.min(Z,U)}K=H[3*v+3]+H[3*v+4]+H[3*v+5],6*H[3*v+5]<K&&(Z*=.5,6*H[3*v+4]<K&&(Z*=.5)),g.thm[c].s[m][v]=Z}}for(g.nsPsy.lastAttacks[c]=j[2],u=p=0;u<g.npart_l;u++){for(var Q=g.s3ind[u][0],$=M[Q]*re[B[Q]],J=g.s3_ll[p++]*$;++Q<=g.s3ind[u][1];)$=M[Q]*re[B[Q]],J=ie(J,g.s3_ll[p++]*$,Q,Q-u,g,0);J*=.158489319246111,g.blocktype_old[1&c]==Ae.SHORT_TYPE?A[u]=J:A[u]=fe(Math.min(J,Math.min(2*g.nb_1[c][u],16*g.nb_2[c][u])),J,d),g.nb_2[c][u]=g.nb_1[c][u],g.nb_1[c][u]=J}for(;u<=Ae.CBANDS;++u)M[u]=0,A[u]=0;_e(g,M,A,c)}for(e.mode!=ye.STEREO&&e.mode!=ye.JOINT_STEREO||me(),e.mode==ye.JOINT_STEREO&&me(),function(e,t,a,n){var s=e.internal_flags;e.short_blocks!=we.short_block_coupled||0!=t[0]&&0!=t[1]||(t[0]=t[1]=0);for(var r=0;r<s.channels_out;r++)n[r]=Ae.NORM_TYPE,e.short_blocks==we.short_block_dispensed&&(t[r]=1),e.short_blocks==we.short_block_forced&&(t[r]=0),0!=t[r]?s.blocktype_old[r]==Ae.SHORT_TYPE&&(n[r]=Ae.STOP_TYPE):(n[r]=Ae.SHORT_TYPE,s.blocktype_old[r]==Ae.NORM_TYPE&&(s.blocktype_old[r]=Ae.START_TYPE),s.blocktype_old[r]==Ae.STOP_TYPE&&(s.blocktype_old[r]=Ae.SHORT_TYPE)),a[r]=s.blocktype_old[r],s.blocktype_old[r]=n[r]}(e,R,l,k),c=0;c<f;c++){var ee,te,ae,ne=0;1<c?me():(ee=i,ne=0,te=l[c],ae=s[n][c]),ee[ne+c]=te==Ae.SHORT_TYPE?ce(ae,g.masking_lower):ue(ae,g.masking_lower),e.analysis&&(g.pinfo.pe[n][c]=ee[ne+c])}return 0},this.psymodel_init=function(e){var t,a=e.internal_flags,n=!0,s=13,r=0,i=0,o=-8.25,_=-4.5,l=de(Ae.CBANDS),f=de(Ae.CBANDS),c=de(Ae.CBANDS),u=e.out_samplerate;switch(e.experimentalZ){default:case 0:n=!0;break;case 1:n=e.VBR!=Me.vbr_mtrh&&e.VBR!=Me.vbr_mt;break;case 2:n=!1;break;case 3:s=8,r=-1.75,i=-.0125,o=-8.25,_=-2.25}for(a.ms_ener_ratio_old=.25,a.blocktype_old[0]=a.blocktype_old[1]=Ae.NORM_TYPE,t=0;t<4;++t){for(var h=0;h<Ae.CBANDS;++h)a.nb_1[t][h]=1e20,a.nb_2[t][h]=1e20,a.nb_s1[t][h]=a.nb_s2[t][h]=1;for(var b=0;b<Ae.SBMAX_l;b++)a.en[t].l[b]=1e20,a.thm[t].l[b]=1e20;for(var h=0;h<3;++h){for(var b=0;b<Ae.SBMAX_s;b++)a.en[t].s[b][h]=1e20,a.thm[t].s[b][h]=1e20;a.nsPsy.lastAttacks[t]=0}for(var h=0;h<9;h++)a.nsPsy.last_en_subshort[t][h]=10}for(a.loudness_sq_save[0]=a.loudness_sq_save[1]=0,a.npart_l=H(a.numlines_l,a.bo_l,a.bm_l,l,f,a.mld_l,a.PSY.bo_l_weight,u,Ae.BLKSIZE,a.scalefac_band.l,Ae.BLKSIZE/1152,Ae.SBMAX_l),t=0;t<a.npart_l;t++){var p=r;l[t]>=s&&(p=i*(l[t]-s)/(24-s)+r*(24-l[t])/(24-s)),c[t]=Math.pow(10,p/10),0<a.numlines_l[t]?a.rnumlines_l[t]=1/a.numlines_l[t]:a.rnumlines_l[t]=0}a.s3_ll=O(a.s3ind,a.npart_l,l,f,c,n);var m,h=0;for(t=0;t<a.npart_l;t++){g=D.MAX_VALUE;for(var v=0;v<a.numlines_l[t];v++,h++){var d=u*h/(1e3*Ae.BLKSIZE);S=this.ATHformula(1e3*d,e)-20,S=Math.pow(10,.1*S),(S*=a.numlines_l[t])<g&&(g=S)}a.ATH.cb_l[t]=g,6<(g=20*l[t]/10-20)&&(g=100),g<-15&&(g=-15),g-=8,a.minval_l[t]=Math.pow(10,g/10)*a.numlines_l[t]}for(a.npart_s=H(a.numlines_s,a.bo_s,a.bm_s,l,f,a.mld_s,a.PSY.bo_s_weight,u,Ae.BLKSIZE_s,a.scalefac_band.s,Ae.BLKSIZE_s/384,Ae.SBMAX_s),t=h=0;t<a.npart_s;t++){var g,p=o;l[t]>=s&&(p=_*(l[t]-s)/(24-s)+o*(24-l[t])/(24-s)),c[t]=Math.pow(10,p/10),g=D.MAX_VALUE;for(var v=0;v<a.numlines_s[t];v++,h++){var S,d=u*h/(1e3*Ae.BLKSIZE_s);S=this.ATHformula(1e3*d,e)-20,S=Math.pow(10,.1*S),(S*=a.numlines_s[t])<g&&(g=S)}a.ATH.cb_s[t]=g,g=7*l[t]/12-7,12<l[t]&&(g*=1+3.1*Math.log(1+g)),l[t]<12&&(g*=1+2.3*Math.log(1-g)),g<-15&&(g=-15),g-=8,a.minval_s[t]=Math.pow(10,g/10)*a.numlines_s[t]}a.s3_ss=O(a.s3ind_s,a.npart_s,l,f,c,n),x=Math.pow(10,(C+1)/16),B=Math.pow(10,(I+1)/16),T=Math.pow(10,L/10),k.init_fft(a),a.decay=Math.exp(-1*R/(.01*u/192)),m=3.5,0!=(2&e.exp_nspsytune)&&(m=1),0<Math.abs(e.msfix)&&(m=e.msfix),e.msfix=m;for(var w=0;w<a.npart_l;w++)a.s3ind[w][1]>a.npart_l-1&&(a.s3ind[w][1]=a.npart_l-1);var M=576*a.mode_gr/u;if(a.ATH.decay=Math.pow(10,-1.2*M),a.ATH.adjust=.01,-(a.ATH.adjustLimit=1)!=e.ATHtype){var y=e.out_samplerate/Ae.BLKSIZE,A=0;for(t=d=0;t<Ae.BLKSIZE/2;++t)d+=y,a.ATH.eql_w[t]=1/Math.pow(10,this.ATHformula(d,e)/10),A+=a.ATH.eql_w[t];for(A=1/A,t=Ae.BLKSIZE/2;0<=--t;)a.ATH.eql_w[t]*=A}for(var w=h=0;w<a.npart_s;++w)for(t=0;t<a.numlines_s[w];++t)++h;for(var w=h=0;w<a.npart_l;++w)for(t=0;t<a.numlines_l[w];++t)++h;for(t=h=0;t<a.npart_l;t++){var d=u*(h+a.numlines_l[t]/2)/(1*Ae.BLKSIZE);a.mld_cb_l[t]=N(d),h+=a.numlines_l[t]}for(;t<Ae.CBANDS;++t)a.mld_cb_l[t]=1;for(t=h=0;t<a.npart_s;t++){var d=u*(h+a.numlines_s[t]/2)/(1*Ae.BLKSIZE_s);a.mld_cb_s[t]=N(d),h+=a.numlines_s[t]}for(;t<Ae.CBANDS;++t)a.mld_cb_s[t]=1;return 0},this.ATHformula=function(e,t){var a;switch(t.ATHtype){case 0:a=n(e,9);break;case 1:a=n(e,-1);break;case 2:a=n(e,0);break;case 3:a=n(e,1)+6;break;case 4:a=n(e,t.ATHcurve);break;default:a=n(e,0)}return a}}function Y(){var T,E,u,h,b,C=this;Y.V9=410,Y.V8=420,Y.V7=430,Y.V6=440,Y.V5=450,Y.V4=460,Y.V3=470,Y.V2=480,Y.V1=490,Y.V0=500,Y.R3MIX=1e3,Y.STANDARD=1001,Y.EXTREME=1002,Y.INSANE=1003,Y.STANDARD_FAST=1004,Y.EXTREME_FAST=1005,Y.MEDIUM=1006,Y.MEDIUM_FAST=1007,Y.LAME_MAXMP3BUFFER=147456;var p,m,v=new X;function d(){this.lowerlimit=0}function s(e,t){this.lowpass=t}this.enc=new Ae,this.setModules=function(e,t,a,n,s,r,i,o,_){T=e,E=t,u=a,h=n,b=s,p=r,m=o,this.enc.setModules(E,v,h,p)};var I=4294479419;function g(e,t){var a=[new s(8,2e3),new s(16,3700),new s(24,3900),new s(32,5500),new s(40,7e3),new s(48,7500),new s(56,1e4),new s(64,11e3),new s(80,13500),new s(96,15100),new s(112,15600),new s(128,17e3),new s(160,17500),new s(192,18600),new s(224,19400),new s(256,19700),new s(320,20500)],n=C.nearestBitrateFullIndex(t);e.lowerlimit=a[n].lowpass}function L(e){var t=Ae.BLKSIZE+e.framesize-Ae.FFTOFFSET;return t=Math.max(t,512+e.framesize-32)}function P(){this.n_in=0,this.n_out=0}function H(e,t,a,n,s,r){var i=e.internal_flags;if(i.resample_ratio<.9999||1.0001<i.resample_ratio)me();else{r.n_out=Math.min(e.framesize,s),r.n_in=r.n_out;for(var o=0;o<r.n_out;++o)t[0][i.mf_size+o]=a[0][n+o],2==i.channels_out&&(t[1][i.mf_size+o]=a[1][n+o])}}this.lame_init=function(){var e,t,a=new function(){this.class_id=0,this.num_samples=0,this.num_channels=0,this.in_samplerate=0,this.out_samplerate=0,this.scale=0,this.scale_left=0,this.scale_right=0,this.analysis=!1,this.bWriteVbrTag=!1,this.decode_only=!1,this.quality=0,this.mode=ye.STEREO,this.force_ms=!1,this.free_format=!1,this.findReplayGain=!1,this.decode_on_the_fly=!1,this.write_id3tag_automatic=!1,this.brate=0,this.compression_ratio=0,this.copyright=0,this.original=0,this.extension=0,this.emphasis=0,this.error_protection=0,this.strict_ISO=!1,this.disable_reservoir=!1,this.quant_comp=0,this.quant_comp_short=0,this.experimentalY=!1,this.experimentalZ=0,this.exp_nspsytune=0,this.preset=0,this.VBR=null,this.VBR_q_frac=0,this.VBR_q=0,this.VBR_mean_bitrate_kbps=0,this.VBR_min_bitrate_kbps=0,this.VBR_max_bitrate_kbps=0,this.VBR_hard_min=0,this.lowpassfreq=0,this.highpassfreq=0,this.lowpasswidth=0,this.highpasswidth=0,this.maskingadjust=0,this.maskingadjust_short=0,this.ATHonly=!1,this.ATHshort=!1,this.noATH=!1,this.ATHtype=0,this.ATHcurve=0,this.ATHlower=0,this.athaa_type=0,this.athaa_loudapprox=0,this.athaa_sensitivity=0,this.short_blocks=null,this.useTemporal=!1,this.interChRatio=0,this.msfix=0,this.tune=!1,this.tune_value_a=0,this.version=0,this.encoder_delay=0,this.encoder_padding=0,this.framesize=0,this.frameNum=0,this.lame_allocated_gfp=0,this.internal_flags=null},n=((e=a).class_id=I,t=e.internal_flags=new j,e.mode=ye.NOT_SET,e.original=1,e.in_samplerate=44100,e.num_channels=2,e.num_samples=-1,e.bWriteVbrTag=!0,e.quality=-1,e.short_blocks=null,t.subblock_gain=-1,e.lowpassfreq=0,e.highpassfreq=0,e.lowpasswidth=-1,e.highpasswidth=-1,e.VBR=Me.vbr_off,e.VBR_q=4,e.ATHcurve=-1,e.VBR_mean_bitrate_kbps=128,e.VBR_min_bitrate_kbps=0,e.VBR_max_bitrate_kbps=0,e.VBR_hard_min=0,t.VBR_min_bitrate=1,t.VBR_max_bitrate=13,e.quant_comp=-1,e.quant_comp_short=-1,e.msfix=-1,t.resample_ratio=1,t.OldValue[0]=180,t.OldValue[1]=180,t.CurrentStep[0]=4,t.CurrentStep[1]=4,t.masking_lower=1,t.nsPsy.attackthre=-1,t.nsPsy.attackthre_s=-1,e.scale=-1,e.athaa_type=-1,e.ATHtype=-1,e.athaa_loudapprox=-1,e.athaa_sensitivity=0,e.useTemporal=null,e.interChRatio=-1,t.mf_samples_to_encode=Ae.ENCDELAY+Ae.POSTDELAY,e.encoder_padding=0,t.mf_size=Ae.ENCDELAY-Ae.MDCTDELAY,e.findReplayGain=!1,e.decode_on_the_fly=!1,t.decode_on_the_fly=!1,t.findReplayGain=!1,t.findPeakSample=!1,t.RadioGain=0,t.AudiophileGain=0,t.noclipGainChange=0,t.noclipScale=-1,e.preset=0,e.write_id3tag_automatic=!0,0);return 0!=n?null:(a.lame_allocated_gfp=1,a)},this.nearestBitrateFullIndex=function(e){var t=[8,16,24,32,40,48,56,64,80,96,112,128,160,192,224,256,320],a=0,n=0,s=0,r=0;r=t[16],n=t[s=16],a=16;for(var i=0;i<16;i++)if(Math.max(e,t[i+1])!=e){r=t[i+1],s=i+1,n=t[i],a=i;break}return e-n<r-e?a:s},this.lame_init_params=function(e){var t,a,n=e.internal_flags;if(n.Class_ID=0,null==n.ATH&&(n.ATH=new function(){this.useAdjust=0,this.aaSensitivityP=0,this.adjust=0,this.adjustLimit=0,this.decay=0,this.floor=0,this.l=de(Ae.SBMAX_l),this.s=de(Ae.SBMAX_s),this.psfb21=de(Ae.PSFB21),this.psfb12=de(Ae.PSFB12),this.cb_l=de(Ae.CBANDS),this.cb_s=de(Ae.CBANDS),this.eql_w=de(Ae.BLKSIZE/2)}),null==n.PSY&&(n.PSY=new function(){this.mask_adjust=0,this.mask_adjust_short=0,this.bo_l_weight=de(Ae.SBMAX_l),this.bo_s_weight=de(Ae.SBMAX_s)}),null==n.rgdata&&(n.rgdata=new function(){}),n.channels_in=e.num_channels,1==n.channels_in&&(e.mode=ye.MONO),n.channels_out=e.mode==ye.MONO?1:2,n.mode_ext=Ae.MPG_MD_MS_LR,e.mode==ye.MONO&&(e.force_ms=!1),e.VBR==Me.vbr_off&&128!=e.VBR_mean_bitrate_kbps&&0==e.brate&&(e.brate=e.VBR_mean_bitrate_kbps),e.VBR==Me.vbr_off||e.VBR==Me.vbr_mtrh||e.VBR==Me.vbr_mt||(e.free_format=!1),e.VBR==Me.vbr_off&&0==e.brate&&me(),e.VBR==Me.vbr_off&&0<e.compression_ratio&&me(),0!=e.out_samplerate&&(e.out_samplerate<16e3?(e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,8),e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,64)):e.out_samplerate<32e3?(e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,8),e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,160)):(e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,32),e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,320))),0==e.lowpassfreq){var s=16e3;switch(e.VBR){case Me.vbr_off:var r=new d;g(r,e.brate),s=r.lowerlimit;break;case Me.vbr_abr:var r=new d;g(r,e.VBR_mean_bitrate_kbps),s=r.lowerlimit;break;case Me.vbr_rh:me();default:me()}e.mode!=ye.MONO||e.VBR!=Me.vbr_off&&e.VBR!=Me.vbr_abr||(s*=1.5),e.lowpassfreq=0|s}switch(0==e.out_samplerate&&me(),e.lowpassfreq=Math.min(20500,e.lowpassfreq),e.lowpassfreq=Math.min(e.out_samplerate/2,e.lowpassfreq),e.VBR==Me.vbr_off&&(e.compression_ratio=16*e.out_samplerate*n.channels_out/(1e3*e.brate)),e.VBR==Me.vbr_abr&&me(),e.bWriteVbrTag||(e.findReplayGain=!1,e.decode_on_the_fly=!1,n.findPeakSample=!1),n.findReplayGain=e.findReplayGain,n.decode_on_the_fly=e.decode_on_the_fly,n.decode_on_the_fly&&(n.findPeakSample=!0),n.findReplayGain&&me(),n.decode_on_the_fly&&!e.decode_only&&me(),n.mode_gr=e.out_samplerate<=24e3?1:2,e.framesize=576*n.mode_gr,e.encoder_delay=Ae.ENCDELAY,n.resample_ratio=e.in_samplerate/e.out_samplerate,e.VBR){case Me.vbr_mt:case Me.vbr_rh:case Me.vbr_mtrh:e.compression_ratio=[5.7,6.5,7.3,8.2,10,11.9,13,14,15,16.5][e.VBR_q];break;case Me.vbr_abr:e.compression_ratio=16*e.out_samplerate*n.channels_out/(1e3*e.VBR_mean_bitrate_kbps);break;default:e.compression_ratio=16*e.out_samplerate*n.channels_out/(1e3*e.brate)}e.mode==ye.NOT_SET&&(e.mode=ye.JOINT_STEREO),0<e.highpassfreq?me():(n.highpass1=0,n.highpass2=0),0<e.lowpassfreq?(n.lowpass2=2*e.lowpassfreq,0<=e.lowpasswidth?me():n.lowpass1=2*e.lowpassfreq,n.lowpass1/=e.out_samplerate,n.lowpass2/=e.out_samplerate):me(),function(e){var t,a=e.internal_flags,n=32;if(0<a.lowpass1){for(var s=999,r=0;r<=31;r++){var i=r/31;i>=a.lowpass2&&(n=Math.min(n,r)),a.lowpass1<i&&i<a.lowpass2&&(s=Math.min(s,r))}a.lowpass1=999==s?(n-.75)/31:(s-.75)/31,a.lowpass2=n/31}0<a.highpass2&&me(),0<a.highpass2&&me();for(var r=0;r<32;r++){var o,_,i=r/31;a.highpass2>a.highpass1?me():o=1,_=a.lowpass2>a.lowpass1?1<(t=(i-a.lowpass1)/(a.lowpass2-a.lowpass1+1e-20))?0:t<=0?1:Math.cos(Math.PI/2*t):1,a.amp_filter[r]=o*_}}(e),n.samplerate_index=function(e,t){switch(e){case 44100:return t.version=1,0;case 48e3:return t.version=1;case 32e3:return t.version=1,2;case 22050:return t.version=0;case 24e3:return t.version=0,1;case 16e3:return t.version=0,2;case 11025:return t.version=0;case 12e3:return t.version=0,1;case 8e3:return t.version=0,2;default:return t.version=0,-1}}(e.out_samplerate,e),n.samplerate_index<0&&me(),e.VBR==Me.vbr_off?e.free_format?n.bitrate_index=0:(e.brate=function(e,t,a){a<16e3&&(t=2);for(var n=k.bitrate_table[t][1],s=2;s<=14;s++)0<k.bitrate_table[t][s]&&Math.abs(k.bitrate_table[t][s]-e)<Math.abs(n-e)&&(n=k.bitrate_table[t][s]);return n}(e.brate,e.version,e.out_samplerate),n.bitrate_index=function(e,t,a){a<16e3&&(t=2);for(var n=0;n<=14;n++)if(0<k.bitrate_table[t][n]&&k.bitrate_table[t][n]==e)return n;return-1}(e.brate,e.version,e.out_samplerate),n.bitrate_index<=0&&me()):n.bitrate_index=1,e.analysis&&(e.bWriteVbrTag=!1),null!=n.pinfo&&(e.bWriteVbrTag=!1),E.init_bit_stream_w(n);for(var i,o=n.samplerate_index+3*e.version+6*(e.out_samplerate<16e3?1:0),_=0;_<Ae.SBMAX_l+1;_++)n.scalefac_band.l[_]=h.sfBandIndex[o].l[_];for(var _=0;_<Ae.PSFB21+1;_++){var l=(n.scalefac_band.l[22]-n.scalefac_band.l[21])/Ae.PSFB21,f=n.scalefac_band.l[21]+_*l;n.scalefac_band.psfb21[_]=f}n.scalefac_band.psfb21[Ae.PSFB21]=576;for(var _=0;_<Ae.SBMAX_s+1;_++)n.scalefac_band.s[_]=h.sfBandIndex[o].s[_];for(var _=0;_<Ae.PSFB12+1;_++){var l=(n.scalefac_band.s[13]-n.scalefac_band.s[12])/Ae.PSFB12,f=n.scalefac_band.s[12]+_*l;n.scalefac_band.psfb12[_]=f}for(n.scalefac_band.psfb12[Ae.PSFB12]=192,1==e.version?n.sideinfo_len=1==n.channels_out?21:36:n.sideinfo_len=1==n.channels_out?13:21,e.error_protection&&(n.sideinfo_len+=2),a=void 0,a=(t=e).internal_flags,t.frameNum=0,t.write_id3tag_automatic&&m.id3tag_write_v2(t),a.bitrate_stereoMode_Hist=S([16,5]),a.bitrate_blockType_Hist=S([16,6]),a.PeakSample=0,t.bWriteVbrTag&&p.InitVbrTag(t),n.Class_ID=I,i=0;i<19;i++)n.nsPsy.pefirbuf[i]=700*n.mode_gr*n.channels_out;switch(-1==e.ATHtype&&(e.ATHtype=4),e.VBR){case Me.vbr_mt:e.VBR=Me.vbr_mtrh;case Me.vbr_mtrh:null==e.useTemporal&&(e.useTemporal=!1),u.apply_preset(e,500-10*e.VBR_q,0),e.quality<0&&(e.quality=LAME_DEFAULT_QUALITY),e.quality<5&&(e.quality=0),5<e.quality&&(e.quality=5),n.PSY.mask_adjust=e.maskingadjust,n.PSY.mask_adjust_short=e.maskingadjust_short,e.experimentalY?n.sfb21_extra=!1:n.sfb21_extra=44e3<e.out_samplerate,n.iteration_loop=new VBRNewIterationLoop(b);break;case Me.vbr_rh:u.apply_preset(e,500-10*e.VBR_q,0),n.PSY.mask_adjust=e.maskingadjust,n.PSY.mask_adjust_short=e.maskingadjust_short,e.experimentalY?n.sfb21_extra=!1:n.sfb21_extra=44e3<e.out_samplerate,6<e.quality&&(e.quality=6),e.quality<0&&(e.quality=LAME_DEFAULT_QUALITY),n.iteration_loop=new VBROldIterationLoop(b);break;default:var c;n.sfb21_extra=!1,e.quality<0&&(e.quality=LAME_DEFAULT_QUALITY),(c=e.VBR)==Me.vbr_off&&(e.VBR_mean_bitrate_kbps=e.brate),u.apply_preset(e,e.VBR_mean_bitrate_kbps,0),e.VBR=c,n.PSY.mask_adjust=e.maskingadjust,n.PSY.mask_adjust_short=e.maskingadjust_short,c==Me.vbr_off?n.iteration_loop=new function(e){var t=e;this.quantize=t,this.iteration_loop=function(e,t,a,n){var s=e.internal_flags,r=de(F.SFBMAX),i=de(576),o=ve(2),_=0,l=s.l3_side,f=new R(_);this.quantize.rv.ResvFrameBegin(e,f),_=f.bits;for(var c=0;c<s.mode_gr;c++){this.quantize.qupvt.on_pe(e,t,o,_,c,c),s.mode_ext==Ae.MPG_MD_MS_LR&&me();for(var u=0;u<s.channels_out;u++){var h,b,p=l.tt[c][u];p.block_type!=Ae.SHORT_TYPE?(h=0,b=s.PSY.mask_adjust-h):(h=0,b=s.PSY.mask_adjust_short-h),s.masking_lower=Math.pow(10,.1*b),this.quantize.init_outer_loop(s,p),this.quantize.init_xrpow(s,p,i)&&(this.quantize.qupvt.calc_xmin(e,n[c][u],p,r),this.quantize.outer_loop(e,p,r,i,u,o[u])),this.quantize.iteration_finish_one(s,c,u)}}this.quantize.rv.ResvFrameEnd(s,_)}}(b):me()}return e.VBR!=Me.vbr_off&&me(),e.tune&&me(),function(e){var t=e.internal_flags;switch(e.quality){default:case 9:t.psymodel=0,t.noise_shaping=0,t.noise_shaping_amp=0,t.noise_shaping_stop=0,t.use_best_huffman=0,t.full_outer_loop=0;break;case 8:e.quality=7;case 7:t.psymodel=1,t.noise_shaping=0,t.noise_shaping_amp=0,t.noise_shaping_stop=0,t.use_best_huffman=0,t.full_outer_loop=0;break;case 6:case 5:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),t.noise_shaping_amp=0,t.noise_shaping_stop=0,-1==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=0,t.full_outer_loop=0;break;case 4:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),t.noise_shaping_amp=0,t.noise_shaping_stop=0,-1==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=1,t.full_outer_loop=0;break;case 3:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),t.noise_shaping_amp=1,-(t.noise_shaping_stop=1)==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=1,t.full_outer_loop=0;break;case 2:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),0==t.substep_shaping&&(t.substep_shaping=2),t.noise_shaping_amp=1,-(t.noise_shaping_stop=1)==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=1,t.full_outer_loop=0;break;case 1:case 0:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),0==t.substep_shaping&&(t.substep_shaping=2),t.noise_shaping_amp=2,-(t.noise_shaping_stop=1)==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=1,t.full_outer_loop=0}}(e),e.athaa_type<0?n.ATH.useAdjust=3:n.ATH.useAdjust=e.athaa_type,n.ATH.aaSensitivityP=Math.pow(10,e.athaa_sensitivity/-10),null==e.short_blocks&&(e.short_blocks=we.short_block_allowed),e.short_blocks!=we.short_block_allowed||e.mode!=ye.JOINT_STEREO&&e.mode!=ye.STEREO||(e.short_blocks=we.short_block_coupled),e.quant_comp<0&&(e.quant_comp=1),e.quant_comp_short<0&&(e.quant_comp_short=0),e.msfix<0&&(e.msfix=0),e.exp_nspsytune=1|e.exp_nspsytune,e.internal_flags.nsPsy.attackthre<0&&(e.internal_flags.nsPsy.attackthre=X.NSATTACKTHRE),e.internal_flags.nsPsy.attackthre_s<0&&(e.internal_flags.nsPsy.attackthre_s=X.NSATTACKTHRE_S),e.scale<0&&(e.scale=1),e.ATHtype<0&&(e.ATHtype=4),e.ATHcurve<0&&(e.ATHcurve=4),e.athaa_loudapprox<0&&(e.athaa_loudapprox=2),e.interChRatio<0&&(e.interChRatio=0),null==e.useTemporal&&(e.useTemporal=!0),n.slot_lag=n.frac_SpF=0,e.VBR==Me.vbr_off&&(n.slot_lag=n.frac_SpF=72e3*(e.version+1)*e.brate%e.out_samplerate|0),h.iteration_init(e),v.psymodel_init(e),0},this.lame_encode_flush=function(e,t,a,n){var s,r,i,o,_=e.internal_flags,l=M([2,1152]),f=0,c=_.mf_samples_to_encode-Ae.POSTDELAY,u=L(e);if(_.mf_samples_to_encode<1)return 0;for(s=0,e.in_samplerate!=e.out_samplerate&&me(),(i=e.framesize-c%e.framesize)<576&&(i+=e.framesize),e.encoder_padding=i,o=(c+i)/e.framesize;0<o&&0<=f;){var h=u-_.mf_size,b=e.frameNum;h*=e.in_samplerate,1152<(h/=e.out_samplerate)&&(h=1152),h<1&&(h=1),r=n-s,0==n&&(r=0),f=this.lame_encode_buffer(e,l[0],l[1],h,t,a,r),a+=f,s+=f,o-=b!=e.frameNum?1:0}return _.mf_samples_to_encode=0,f<0?f:(r=n-s,0==n&&(r=0),E.flush_bitstream(e),(f=E.copy_buffer(_,t,a,r,1))<0?f:(a+=f,r=n-(s+=f),0==n&&(r=0),e.write_id3tag_automatic&&me(),s))},this.lame_encode_buffer=function(e,t,a,n,s,r,i){var o,_,l=e.internal_flags,f=[null,null];if(l.Class_ID!=I)return-3;if(0==n)return 0;_=n,(null==(o=l).in_buffer_0||o.in_buffer_nsamples<_)&&(o.in_buffer_0=de(_),o.in_buffer_1=de(_),o.in_buffer_nsamples=_),f[0]=l.in_buffer_0,f[1]=l.in_buffer_1;for(var c=0;c<n;c++)f[0][c]=t[c],1<l.channels_in&&(f[1][c]=a[c]);return function(e,t,a,n,s,r,i){var o,_,l,f,c,u=e.internal_flags,h=0,b=[null,null],p=[null,null];if(u.Class_ID!=I)return-3;if(0==n)return 0;if((c=E.copy_buffer(u,s,r,i,0))<0)return c;if(r+=c,h+=c,p[0]=t,p[1]=a,N.NEQ(e.scale,0)&&N.NEQ(e.scale,1))for(_=0;_<n;++_)p[0][_]*=e.scale,2==u.channels_out&&(p[1][_]*=e.scale);if(N.NEQ(e.scale_left,0)&&N.NEQ(e.scale_left,1))for(_=0;_<n;++_)p[0][_]*=e.scale_left;if(N.NEQ(e.scale_right,0)&&N.NEQ(e.scale_right,1))for(_=0;_<n;++_)p[1][_]*=e.scale_right;2==e.num_channels&&1==u.channels_out&&me(),f=L(e),b[0]=u.mfbuf[0],b[1]=u.mfbuf[1];for(var m,v,d,g,S,w,M,y=0;0<n;){var A=[null,null],k=0,R=0;A[0]=p[0],A[1]=p[1];var x=new P;if(H(e,b,A,y,n,x),k=x.n_in,R=x.n_out,u.findReplayGain&&!u.decode_on_the_fly&&T.AnalyzeSamples(u.rgdata,b[0],u.mf_size,b[1],u.mf_size,R,u.channels_out)==O.GAIN_ANALYSIS_ERROR)return-6;if(n-=k,y+=k,u.channels_out,u.mf_size+=R,u.mf_samples_to_encode<1&&me(),u.mf_samples_to_encode+=R,u.mf_size>=f){var B=i-h;if(0==i&&(B=0),m=e,v=b[0],d=b[1],g=s,S=r,w=B,void 0,M=C.enc.lame_encode_mp3_frame(m,v,d,g,S,w),m.frameNum++,(o=M)<0)return o;for(r+=o,h+=o,u.mf_size-=e.framesize,u.mf_samples_to_encode-=e.framesize,l=0;l<u.channels_out;l++)for(_=0;_<u.mf_size;_++)b[l][_]=b[l][_+e.framesize]}}return h}(e,f[0],f[1],n,s,r,i)}}F.SFBMAX=3*Ae.SBMAX_s,Ae.ENCDELAY=576,Ae.POSTDELAY=1152,Ae.FFTOFFSET=224+(Ae.MDCTDELAY=48),Ae.DECDELAY=528,Ae.SBLIMIT=32,Ae.CBANDS=64,Ae.SBPSY_l=21,Ae.SBPSY_s=12,Ae.SBMAX_l=22,Ae.SBMAX_s=13,Ae.PSFB21=6,Ae.PSFB12=6,Ae.HBLKSIZE=(Ae.BLKSIZE=1024)/2+1,Ae.HBLKSIZE_s=(Ae.BLKSIZE_s=256)/2+1,Ae.NORM_TYPE=0,Ae.START_TYPE=1,Ae.SHORT_TYPE=2,Ae.STOP_TYPE=3,Ae.MPG_MD_LR_LR=0,Ae.MPG_MD_LR_I=1,Ae.MPG_MD_MS_LR=2,Ae.MPG_MD_MS_I=3,Ae.fircoef=[-.1039435,-.1892065,5*-.0432472,-.155915,3.898045e-17,.0467745*5,.50455,.756825,.187098*5],j.MFSIZE=3456+Ae.ENCDELAY-Ae.MDCTDELAY,j.MAX_HEADER_BUF=256,j.MAX_BITS_PER_CHANNEL=4095,j.MAX_BITS_PER_GRANULE=7680,j.BPC=320,F.SFBMAX=3*Ae.SBMAX_s,t.Mp3Encoder=function(n,e,t){1!=n&&me("fix cc: only supports mono");var s=new Y,a=new function(){this.setModules=function(e,t){}},r=new O,i=new N,o=new function(){function e(e,t,a,n,s,r,i,o,_,l,f,c,u,h){this.quant_comp=t,this.quant_comp_s=a,this.safejoint=n,this.nsmsfix=s,this.st_lrm=r,this.st_s=i,this.nsbass=o,this.scale=_,this.masking_adj=l,this.ath_lower=f,this.ath_curve=c,this.interch=u,this.sfscale=h}var i;function n(e,t,a){me()}this.setModules=function(e){i=e};var o=[new e(8,9,9,0,0,6.6,145,0,.95,0,-30,11,.0012,1),new e(16,9,9,0,0,6.6,145,0,.95,0,-25,11,.001,1),new e(24,9,9,0,0,6.6,145,0,.95,0,-20,11,.001,1),new e(32,9,9,0,0,6.6,145,0,.95,0,-15,11,.001,1),new e(40,9,9,0,0,6.6,145,0,.95,0,-10,11,9e-4,1),new e(48,9,9,0,0,6.6,145,0,.95,0,-10,11,9e-4,1),new e(56,9,9,0,0,6.6,145,0,.95,0,-6,11,8e-4,1),new e(64,9,9,0,0,6.6,145,0,.95,0,-2,11,8e-4,1),new e(80,9,9,0,0,6.6,145,0,.95,0,0,8,7e-4,1),new e(96,9,9,0,2.5,6.6,145,0,.95,0,1,5.5,6e-4,1),new e(112,9,9,0,2.25,6.6,145,0,.95,0,2,4.5,5e-4,1),new e(128,9,9,0,1.95,6.4,140,0,.95,0,3,4,2e-4,1),new e(160,9,9,1,1.79,6,135,0,.95,-2,5,3.5,0,1),new e(192,9,9,1,1.49,5.6,125,0,.97,-4,7,3,0,0),new e(224,9,9,1,1.25,5.2,125,0,.98,-6,9,2,0,0),new e(256,9,9,1,.97,5.2,125,0,1,-8,10,1,0,0),new e(320,9,9,1,.9,5.2,125,0,1,-10,12,0,0,0)];function s(e,t,a){var n=t,s=i.nearestBitrateFullIndex(t);if(e.VBR=Me.vbr_abr,e.VBR_mean_bitrate_kbps=n,e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,320),e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,8),e.brate=e.VBR_mean_bitrate_kbps,320<e.VBR_mean_bitrate_kbps&&(e.disable_reservoir=!0),0<o[s].safejoint&&(e.exp_nspsytune=2|e.exp_nspsytune),0<o[s].sfscale&&(e.internal_flags.noise_shaping=2),0<Math.abs(o[s].nsbass)){var r=int(4*o[s].nsbass);r<0&&(r+=64),e.exp_nspsytune=e.exp_nspsytune|r<<2}return 0!=a?e.quant_comp=o[s].quant_comp:0<Math.abs(e.quant_comp- -1)||(e.quant_comp=o[s].quant_comp),0!=a?e.quant_comp_short=o[s].quant_comp_s:0<Math.abs(e.quant_comp_short- -1)||(e.quant_comp_short=o[s].quant_comp_s),0!=a?e.msfix=o[s].nsmsfix:0<Math.abs(e.msfix- -1)||(e.msfix=o[s].nsmsfix),0!=a?e.internal_flags.nsPsy.attackthre=o[s].st_lrm:0<Math.abs(e.internal_flags.nsPsy.attackthre- -1)||(e.internal_flags.nsPsy.attackthre=o[s].st_lrm),0!=a?e.internal_flags.nsPsy.attackthre_s=o[s].st_s:0<Math.abs(e.internal_flags.nsPsy.attackthre_s- -1)||(e.internal_flags.nsPsy.attackthre_s=o[s].st_s),0!=a?e.scale=o[s].scale:0<Math.abs(e.scale- -1)||(e.scale=o[s].scale),0!=a?e.maskingadjust=o[s].masking_adj:0<Math.abs(e.maskingadjust-0)||(e.maskingadjust=o[s].masking_adj),0<o[s].masking_adj?0!=a?e.maskingadjust_short=.9*o[s].masking_adj:0<Math.abs(e.maskingadjust_short-0)||(e.maskingadjust_short=.9*o[s].masking_adj):0!=a?e.maskingadjust_short=1.1*o[s].masking_adj:0<Math.abs(e.maskingadjust_short-0)||(e.maskingadjust_short=1.1*o[s].masking_adj),0!=a?e.ATHlower=-o[s].ath_lower/10:0<Math.abs(10*-e.ATHlower-0)||(e.ATHlower=-o[s].ath_lower/10),0!=a?e.ATHcurve=o[s].ath_curve:0<Math.abs(e.ATHcurve- -1)||(e.ATHcurve=o[s].ath_curve),0!=a?e.interChRatio=o[s].interch:0<Math.abs(e.interChRatio- -1)||(e.interChRatio=o[s].interch),t}this.apply_preset=function(e,t,a){switch(t){case Y.R3MIX:t=Y.V3,e.VBR=Me.vbr_mtrh;break;case Y.MEDIUM:t=Y.V4,e.VBR=Me.vbr_rh;break;case Y.MEDIUM_FAST:t=Y.V4,e.VBR=Me.vbr_mtrh;break;case Y.STANDARD:t=Y.V2,e.VBR=Me.vbr_rh;break;case Y.STANDARD_FAST:t=Y.V2,e.VBR=Me.vbr_mtrh;break;case Y.EXTREME:t=Y.V0,e.VBR=Me.vbr_rh;break;case Y.EXTREME_FAST:t=Y.V0,e.VBR=Me.vbr_mtrh;break;case Y.INSANE:return t=320,e.preset=t,s(e,t,a),e.VBR=Me.vbr_off,t}switch(e.preset=t){case Y.V9:return n(e,9,a),t;case Y.V8:return n(e,8,a),t;case Y.V7:return n(e,7,a),t;case Y.V6:return n(e,6,a),t;case Y.V5:return n(e,5,a),t;case Y.V4:return n(e,4,a),t;case Y.V3:return n(e,3,a),t;case Y.V2:return n(e,2,a),t;case Y.V1:return n(e,1,a),t;case Y.V0:return n(e,0,a),t}return 8<=t&&t<=320?s(e,t,a):(e.preset=0,t)}},_=new T,l=new C,f=new y,c=new function(){this.getLameShortVersion=function(){return"3.98.4"}},u=new function(){this.setModules=function(e,t){}},h=new function(){var _;this.setModules=function(e){_=e},this.ResvFrameBegin=function(e,t){var a,n=e.internal_flags,s=n.l3_side,r=_.getframebits(e);t.bits=(r-8*n.sideinfo_len)/n.mode_gr;var i=2048*n.mode_gr-8;320<e.brate?me():(a=11520,e.strict_ISO&&me()),n.ResvMax=a-r,n.ResvMax>i&&(n.ResvMax=i),(n.ResvMax<0||e.disable_reservoir)&&(n.ResvMax=0);var o=t.bits*n.mode_gr+Math.min(n.ResvSize,n.ResvMax);return a<o&&(o=a),s.resvDrain_pre=0,null!=n.pinfo&&me(),o},this.ResvMaxBits=function(e,t,a,n){var s,r=e.internal_flags,i=r.ResvSize,o=r.ResvMax;0!=n&&(i+=t),0!=(1&r.substep_shaping)&&(o*=.9),a.bits=t,9*o<10*i?(s=i-9*o/10,a.bits+=s,r.substep_shaping|=128):(s=0,r.substep_shaping&=127,e.disable_reservoir||0!=(1&r.substep_shaping)||(a.bits-=.1*t));var _=i<6*r.ResvMax/10?i:6*r.ResvMax/10;return(_-=s)<0&&(_=0),_},this.ResvAdjust=function(e,t){e.ResvSize-=t.part2_3_length+t.part2_length},this.ResvFrameEnd=function(e,t){var a,n=e.l3_side;e.ResvSize+=t*e.mode_gr;var s=0;n.resvDrain_post=0,(n.resvDrain_pre=0)!=(a=e.ResvSize%8)&&(s+=a),0<(a=e.ResvSize-s-e.ResvMax)&&(s+=a);var r=Math.min(8*n.main_data_begin,s)/8;n.resvDrain_pre+=8*r,s-=8*r,e.ResvSize-=8*r,n.main_data_begin-=r,n.resvDrain_post+=s,e.ResvSize-=s}},b=new A,p=new function(){this.setModules=function(e,t,a){}},m=new function(){};s.setModules(r,i,o,_,l,f,c,u,m),i.setModules(r,m,c,f),u.setModules(i,c),o.setModules(s),l.setModules(i,h,_,b),_.setModules(b,h,s.enc.psy),h.setModules(i),b.setModules(_),f.setModules(s,i,c),a.setModules(p,m),p.setModules(c,u,o);var v=s.lame_init();v.num_channels=n,v.in_samplerate=e,v.out_samplerate=e,v.brate=t,v.mode=ye.STEREO,v.quality=3,v.bWriteVbrTag=!1,v.disable_reservoir=!0,v.write_id3tag_automatic=!1,s.lame_init_params(v);var d=1152,g=0|1.25*d+7200,S=w(g);this.encodeBuffer=function(e,t){1==n&&(t=e),e.length>d&&(d=e.length,S=w(g=0|1.25*d+7200));var a=s.lame_encode_buffer(v,e,t,e.length,S,0,g);return new Int8Array(S.subarray(0,a))},this.flush=function(){var e=s.lame_encode_flush(v,S,0,g);return new Int8Array(S.subarray(0,e))}}}t(),e.lamejs=t}(("object"==typeof window&&window.document?window:Object).Recorder);