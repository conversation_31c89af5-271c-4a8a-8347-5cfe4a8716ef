/*
Recorder ../assets/page-i18n/app_index_html/en-US.js
https://github.com/xiangyuecn/Recorder

Usage: Recorder.i18n.lang="en-US" or "en"

Desc: English, 英语。The translation of the /app-support-sample/index.html page, this translation mainly comes from: google translation + Baidu translation, translated from Chinese to English. 页面/app-support-sample/index.html的翻译，此翻译主要来自：google翻译+百度翻译，由中文翻译成英文。

注意：请勿修改//@@打头的文本行；以下代码结构由/src/package-i18n.js自动生成，只允许在字符串中填写翻译后的文本，请勿改变代码结构；翻译的文本如果需要明确的空值，请填写"=Empty"；文本中的变量用{n}表示（n代表第几个变量），所有变量必须都出现至少一次，如果不要某变量用{n!}表示

Note: Do not modify the text lines starting with //@@; The following code structure is automatically generated by /src/package-i18n.js, only the translated text is allowed to be filled in the string, please do not change the code structure; If the translated text requires an explicit empty value, please fill in "=Empty"; Variables in the text are represented by {n} (n represents the number of variables), all variables must appear at least once, if a variable is not required, it is represented by {n!}
*/
(function(factory){
	var browser=typeof window=="object" && !!window.document;
	var win=browser?window:Object; //非浏览器环境，Recorder挂载在Object下面
	factory(win.Recorder,browser);
}(function(Recorder,isBrowser){
"use strict";
var i18n=Recorder.i18n;

//@@User Code-1 Begin 手写代码放这里 Put the handwritten code here @@

//@@User Code-1 End @@

//@@Exec i18n.lang="en-US";
Recorder.CLog('Import Page[app_index_html] lang="en-US"');

//@@Exec i18n.alias["en-US"]="en";

var putSet={lang:"en"};

//@@Exec i18n.data["rtl$en"]=false;
i18n.data["desc-page-app_index_html$en"]="English, 英语。The translation of the /app-support-sample/index.html page, this translation mainly comes from: google translation + Baidu translation, translated from Chinese to English. 页面/app-support-sample/index.html的翻译，此翻译主要来自：google翻译+百度翻译，由中文翻译成英文。";
//@@Exec i18n.GenerateDisplayEnglish=false;



//*************** Begin srcFile=../app-support-sample/index.html ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="RecordApp测试"
//@@Put0
 "PZM5:"+ //no args
       "RecordApp Test"

//@@zh="Recorder H5使用简单，功能丰富，支持PC、Android、iOS 14.3+"
,"a2Uc:"+ //no args
       "Recorder H5 is easy to use and rich in functions, supporting PC, Android, iOS 14.3+"

//@@zh="支持：浏览器WebView（H5）、各种使用js来构建的程序（App、小程序、UniApp、Electron、NodeJs）"
,"ptyK:"+ //no args
       "Support: browser WebView (H5), various programs built using js (App, miniProgram, UniApp, Electron, NodeJs)"

//@@zh="RecordApp：基于Recorder的跨平台录音解决方案"
,"v1fx:"+ //no args
       "RecordApp: Cross-platform recording solution based on Recorder"

//@@zh="- 支持H5页面录音（即本页面支持）："
,"kYvU:"+ //no args
       "- Support H5 page recording (that is, supported by this page): "

//@@zh="在浏览器中（含App WebView）默认使用Recorder H5进行录音"
,"Roj2:"+ //no args
       "Recorder H5 is used by default in the browser (including App WebView) for recording"

//@@zh="在开启了原生支持的App WebView中使用原生录音，App Demo:"
,"c7mM:"+ //no args
       "Using native recording in App WebView with native support enabled, App Demo:"

//@@zh="- 支持微信小程序录音，微信小程序Demo:"
,"up8g:"+ //no args
       "- Support WeChat miniProgram recording, WeChat miniProgram Demo:"

//@@zh="- 支持uni-app录音：H5、Android iOS App、微信小程序，含组件示例:"
,"K3sX:"+ //no args
       "- Supports uni-app recording: H5, Android iOS App, WeChat miniProgram, including component examples:"

//@@zh="- 支持自行适配到各种js运行环境中录音，比如：Electron、NodeJs、各种小程序"
,"sjDM:"+ //no args
       "- Supports self-adaptation to recording in various js running environments, such as: Electron, NodeJs, various miniPrograms"

//@@zh="- 以上环境中均可使用大部分的: 录音格式、实时处理、和可视化等扩展功能"
,"5agh:"+ //no args
       "- Most recording formats, real-time processing, and visualization extensions can be used in the above environments"

//@@zh="源码仓库 :"
,"lIPR:"+ //no args
       "Open Source :"

//@@zh="更多Demo :"
,"ZNjk:"+ //no args
       "More Demos :"

//@@zh="Demo列表(可编辑)"
,"Hkoo:"+ //no args
       "Demo List (editable)"

//@@zh="(Copy即用，更适合入门学习)"
,"paWp:"+ //no args
       "(Simple and easy to learn)"

//@@zh="类型 :"
,"5lOo:"+ //no args
       "Type :"

//@@zh="提示 :"
,"dsAF:"+ //no args
       "Hint :"

//@@zh="比特率 :"
,"u4jV:"+ //no args
       "bitRate :"

//@@zh="kbps，越大音质越好"
,"06ha:"+ //no args
       "kbps, The larger the value, the better the sound quality"

//@@zh="采样率 :"
,"4PRA:"+ //no args
       "sampleRate :"

//@@zh="hz，越大细节越丰富"
,"e0ec:"+ //no args
       "hz, The larger the value, the richer the details"

//@@zh="App里面总是使用Recorder H5录音"
,"Wpwv:"+ //no args
       "Always use Recorder H5 to record in the app"

//@@zh="请求权限"
,"aoOU:"+ //no args
       "Request permission"

//@@zh="录制"
,"3MeL:"+ //no args
       "Start recording"

//@@zh="停止"
,"9vcY:"+ //no args
       "Stop recording"

//@@zh="录制+定时停止"
,"6CdW:"+ //no args
       "Recording + timed stop"

//@@zh="暂停"
,"hxxb:"+ //no args
       "Pause"

//@@zh="继续"
,"Orkm:"+ //no args
       "Resume"

//@@zh="停止(仅清理)"
,"Yna5:"+ //no args
       "Stop (clean only)"

//@@zh="播放"
,"Wahr:"+ //no args
       "Play"

//@@zh="上传"
,"6Rqg:"+ //no args
       "Upload"

//@@zh="本地下载"
,"gXBH:"+ //no args
       "Local download"

//@@zh="批量编码"
,"wFvj:"+ //no args
       "Batch encoding"

//@@zh="kbps 测试音质用的，除比特率外其他参数可调整"
,"ozCS:"+ //no args
       "kbps, for testing sound quality, other parameters except bit rate can be adjusted"

//@@zh="接管编码器输出（takeoffEncodeChunk）"
,"Hyuy:"+ //no args
       "Take over the encoder output (takeoffEncodeChunk)"

//@@zh="尝试启用回声消除（echoCancellation）"
,"Kz8D:"+ //no args
       "Try enabling echoCancellation"

//@@zh="实时语音通话聊天对讲（WebSocket、WebRTC）"
,"zb16:"+ //no args
       "(Chinese) Real-time voice calls, chats, intercoms (WebSocket, WebRTC)"

//@@zh="实时语音识别、音频文件转文字，ASR"
,"jCKC:"+ //no args
       "(Chinese) Real-time speech recognition, audio file to text, ASR"

//@@zh="测试App :"
,"QFFW:"+ //no args
       "Test App :"

//@@zh="下载源码"
,"WUY9:"+ //no args
       "Download source code"

//@@zh="自行编译"
,"AeNc:"+ //no args
       "Compile yourself"

//@@zh="下载APK"
,"Uf5q:"+ //no args
       "Download APK"

//@@zh="40kb，删除.zip后缀，"
,"5zSj:"+ //no args
       "40kb, delete .zip suffix, "

//@@zh="源码"
,"G0HC:"+ //no args
       "Source code"

//@@zh="原生接口 :"
,"zIlH:"+ //no args
       "Native interface :"

//@@zh="显示内存占用"
,"dPz1:"+ //no args
       "Display memory usage"

//@@zh="显示后台录音保活通知(Android)"
,"dPz2:"+ //no args
       "Display background recording keep-alive notification (Android)"

//@@zh="关闭通知"
,"dPz3:"+ //no args
       "Turn off notifications"

//@@zh="切换成扬声器外放"
,"dPz4:"+ //no args
       "Switch to external speaker"

//@@zh="切换成听筒播放"
,"dPz5:"+ //no args
       "Switch to earpiece playback"

//@@zh="原生接口不一定可用，取决于原生app是否已实现对应接口"
,"dPz9:"+ //no args
       "The native interface may not be available, depending on whether the native app has implemented the corresponding interface"

//@@zh="如需录音功能定制开发，网站、App、小程序、前端后端开发等需求，请加QQ群：①群 781036591、②群 748359095、③群 450721519，口令recorder，联系群主（即作者），谢谢~"
,"Pea3:"+ //no args
       "If you need custom development of recording functions, websites, apps, miniProgram, front-end and back-end development, etc., please join the Tencent QQ group: ①group 781036591、②group 748359095、③group 450721519, password recorder, contact the group owner (ie the author), thank you~"

//@@zh="音乐播放测试 :"
,"2fWL:"+ //no args
       "Music playback test :"

//@@zh="合成5分钟wav"
,"uad6:"+ //no args
       "Generate 5 minutes wav"

//@@zh="Audio对录音的影响测试"
,"z9o0:"+ //no args
       "Audio's influence test on recording"

//@@zh="；低版本iOS Safari如果未开始过录音并且播放了音乐，然后后续录音可能会有问题；再现方法"
,"yStv:"+ //no args
       "; If the lower version of iOS Safari has not started recording and played music, then there may be problems with subsequent recordings; reproduction method"

//@@zh="刷新页面后首先先播放音乐，然后开始测试录音，会发现波形显示掉帧或者保持直线。另测试浏览器对音频的支持情况。"
,"4yox:"+ //no args
       "After refreshing the page, first play the music first, and then start the test recording, and you will find that the waveform shows frame drop or keeps a straight line. Also test the browser's support for audio. "

//@@zh="视频播放测试 :"
,"fFQq:"+ //no args
       "Video playback test :"

//@@zh="播放mp4"
,"6E53:"+ //no args
       "Play mp4"

//@@zh="Video对录音的影响测试"
,"ALiC:"+ //no args
       "Test of the influence of Video on recording"

//@@zh="；iOS Safari可能出现先播放视频，然后再开始录音，会自动播放视频的声音，但并未再现。"
,"yshJ:"+ //no args
       "; iOS Safari may appear to play the video first, and then start recording, and the sound of the video will be played automatically, but it is not reproduced. "

//@@zh="显示video"
,"CwnP:"+ //no args
       "Display Video"

//@@zh="隐藏video"
,"nFEO:"+ //no args
       "Hide Video"

//@@zh="移除video"
,"WXIx:"+ //no args
       "Remove Video"

//@@zh="循环播放"
,"iXZv:"+ //no args
       "Loop Playback"

//@@zh="静音循环播放"
,"2RpC:"+ //no args
       "Mute Loop"

//@@zh="浏览器环境情况 :"
,"IClu:"+ //no args
       "Browser environment :"

//@@zh="RecordApp库修改时间（有可能修改了忘改）："
,"mt71:"+ //no args
       "Modification time of the RecordApp library (it may be modified and forgotten): "

//@@zh="Recorder库修改时间（有可能修改了忘改）："
,"ySaK:"+ //no args
       "Modification time of the Recorder library (it may be modified and forgotten): "

//@@zh="本页面修改时间（有可能修改了忘改）："
,"Ls9o:"+ //no args
       "Modification time of this page (it may be modified and forgotten): "

//@@zh="不能获得错误堆栈"
,"jwqb:"+ //no args
       "can't get error stack"

//@@zh="AppUseH5选项变更，已重置RecordApp，请先进行权限测试"
,"T6A6:"+ //no args
       "AppUseH5 option changed, RecordApp has been reset, please test permissions first"

//@@zh="开始请求授权..."
,"LnQj:"+ //no args
       "Start requesting permissions..."

//@@zh="已启用audioTrackSet配置："
,"W5dU:"+ //no args
       "AudioTrackSet configuration enabled: "

//@@zh="已授权"
,"wnd4:"+ //no args
       "Permission granted"

//@@zh="授权失败："
,"oFqf:"+ //no args
       "Failed to obtain permission: "

//@@zh="定时不能小于100ms"
,"9ZjY:"+ //no args
       "Timing cannot be less than 100ms"

//@@zh="定时{1}ms后自动停止录音"
,"7Jp4:"+ //args: {1}
       "Automatically stop recording after {1}ms"

//@@zh="定时时间到，开始自动调用停止..."
,"MHeo:"+ //no args
       "When the time is up, start to call automatically to stop..."

//@@zh="已取消定时停止"
,"K57T:"+ //no args
       "Timed stop canceled"

//@@zh="需先调用RequestPermission"
,"dejy:"+ //no args
       "Need to call RequestPermission first"

//@@zh="正在使用Native录音，底层由App原生层提供支持"
,"Knhl:"+ //no args
       "Native recording is being used, and the bottom layer is supported by the App native layer"

//@@zh="正在使用H5录音，底层由Recorder直接提供支持"
,"mHN7:"+ //no args
       "H5 recording is being used, and the underlying layer is directly supported by Recorder"

//@@zh="当前环境{1}不支持实时回调，不能模拟实时编码传输"
,"GM5h:"+ //args: {1}
       "The current environment {1} does not support real-time callback and cannot simulate real-time encoding transmission"

//@@zh="当前环境{1}不支持实时回调，不能进行实时语音识别"
,"Vee6:"+ //args: {1}
       "The current environment {1} does not support real-time callback and cannot perform real-time speech recognition"

//@@zh="正在打开..."
,"wFnI:"+ //no args
       "Starting..."

//@@zh="录制中:"
,"bYp4:"+ //no args
       "Recording:"

//@@zh="录音被中断"
,"eWu1:"+ //no args
       "Recording interrupted"

//@@zh="录音未能正常开始"
,"eWu2:"+ //no args
       "Recording failed to start properly"

//@@zh="当前环境不支持onProcess回调，不启用watchDogTimer"
,"eWu3:"+ //no args
       "The current environment does not support onProcess callback, watchDogTimer will not be enabled"

//@@zh="开始录音失败："
,"EBjo:"+ //no args
       "Failed to start recording: "

//@@zh="已暂停"
,"z2B2:"+ //no args
       "Paused"

//@@zh="继续录音中..."
,"sTFX:"+ //no args
       "Resuming recording..."

//@@zh="已清理，错误信息："
,"C7au:"+ //no args
       "Cleaned, error message: "

//@@zh="启用takeoffEncodeChunk后Stop返回的blob长度为0不提供音频数据"
,"aS2G:"+ //no args
       "After enabling takeoffEncodeChunk, the length of the blob returned by Stop is 0 and no audio data is provided"

//@@zh="takeoffEncodeChunk接收到{1}片音频片段，正在合并成一个音频文件..."
,"Vsr4:"+ //args: {1}
       "takeoffEncodeChunk received {1} audio chunks, merging into one audio file..."

//@@zh="合并"
,"NcRr:"+ //no args
       "merge"

//@@zh="已录制"
,"SS8P:"+ //no args
       "Recorded"

//@@zh="失败："
,"Xnib:"+ //no args
       "Fail: "

//@@zh="正在结束"
,"s99V:"+ //no args
       "Stopping"

//@@zh="已录制"
,"jpEQ:"+ //no args
       "Recorded"

//@@zh="失败："
,"lenm:"+ //no args
       "Fail: "

//@@zh="花{1}ms编码{2}B"
,"aZ4T:"+ //args: {1}-{2}
       ", it took {1}ms to encode {2}B, "

//@@zh="下载"
,"9aiF:"+ //no args
       "Download"

//@@zh="播放"
,"56g1:"+ //no args
       "Play"

//@@zh="使用RecordApp.GetCurrentRecOrNull()方法，我们可以在停止录音时，得到RecordApp编码用的rec对象，因此可以进行二次编码。"
,"LZkZ:"+ //no args
       "Using the RecordApp.GetCurrentRecOrNull() method, we can get the rec object used for RecordApp encoding when we stop recording, so we can perform secondary encoding."

//@@zh="需先录个音"
,"qKJ9:"+ //no args
       "Need to record a sound first"

//@@zh="码率列表有误，需要? to ? step ?结构"
,"J1wQ:"+ //no args
       "The bitRate list is wrong, the ? to ? step ? structure is required"

//@@zh="开始批量编码，请勿进行其他操作~"
,"OQV2:"+ //no args
       "Start batch encoding, do not perform other operations~"

//@@zh="批量编码完成"
,"nUq7:"+ //no args
       "Batch encoding complete"

//@@zh="已编码"
,"A7H4:"+ //no args
       "Encoded"

//@@zh="失败："
,"mFBf:"+ //no args
       "Fail: "

//@@zh="播放失败"
,"kj0b:"+ //no args
       "Play failed"

//@@zh="正在转码成wav..."
,"Jysa:"+ //no args
       "transcoding to wav..."

//@@zh="已转码成wav播放"
,"zbUk:"+ //no args
       "Transcoded into wav playback"

//@@zh="转码成wav失败："
,"v8S9:"+ //no args
       "Transcoding to wav failed: "

//@@zh="正在合成wav..."
,"q2iG:"+ //no args
       "Generating wav..."

//@@zh="请先录音，然后停止后再播放"
,"U3nx:"+ //no args
       "Please record first, then stop and play"

//@@zh="请先录音，然后停止后再上传"
,"8y8L:"+ //no args
       "Please record first, then stop before uploading"

//@@zh="上传成功"
,"ebkz:"+ //no args
       "Successfully upload"

//@@zh="没有完成上传，演示上传地址无需关注上传结果，只要浏览器控制台内Network面板内看到的请求数据结构是预期的就ok了。"
,"p3cq:"+ //no args
       "If the upload is not completed, the demo upload address does not need to pay attention to the upload result. As long as the request data structure seen in the Network panel in the browser console is expected, it is ok."

//@@zh="上传失败"
,"rscd:"+ //no args
       "Upload failed"

//@@zh="开始上传到{1}，请稍候... （你可以先到源码 /assets/node-localServer 目录内执行 npm run start 来运行本地测试服务器）"
,"t0Vr:"+ //args: {1}
       "Starting upload to {1}, requesting later... (You can first go to the source code /assets/node-localServer directory and execute npm run start to run the local test server)"

//@@zh="上传方式一【Base64】"
,"3Auy:"+ //no args
       "Upload method 1 [Base64] "

//@@zh="上传方式二【FormData】"
,"GEBC:"+ //no args
       "Upload method 2 [FormData] "

//@@zh="请先录音，然后停止后再下载"
,"g1CD:"+ //no args
       "Please record first, then stop before downloading"

//@@zh="点击{1}"
,"Lw0c:"+ //args: {1}
       "Click {1}"

//@@zh="下载，或复制文本"
,"cs8P:"+ //no args
       "to download, or copy the text"

//@@zh="生成Base64文本"
,"hziK:"+ //no args
       "Generate Base64 text"

//@@zh="下载 "
,"rxa0:"+ //no args
       "download "

//@@zh="因移动端绝大部分国产浏览器未适配Blob Url的下载，所以本demo代码在移动端未调用downA.click()。请尝试点击日志中显示的下载链接下载，无法下载就复制Base64"
,"V48u:"+ //no args
       "Because the mobile browser may not be suitable for downloading Blob Url, this demo code does not call downA.click() on the mobile terminal. Please try to click the download link displayed in the log to download, if you cannot download, copy Base64"

//@@zh="动态波形"
,"RDIF:"+ //no args
       "Dynamic Waveform"

//@@zh="音频可视化波形"
,"08jd:"+ //no args
       "Visualization Waveform"

//@@zh="音频可视化频率直方图"
,"JrUV:"+ //no args
       "Visualize Spectrum"

//@@zh="已切换波形显示为："
,"GGPu:"+ //no args
       "The switched waveforms are displayed as: "

//@@zh="点击录制开始哦"
,"x6fO:"+ //no args
       "Click the Start Recording button to start"

//@@zh="、"
,"U67F:"+ //no args
       ", "

//@@zh="已启用Extensions："
,"4DQX:"+ //no args
       "Extensions enabled: "

//@@zh="当前页面处在在iframe中，但故意未进行任何处理，"
,"m96f:"+ //no args
       "The current page is in an iframe, but no processing is done on purpose, "

//@@zh="当前是同域"
,"9ywI:"+ //no args
       "Currently in the same domain"

//@@zh="并且已发生跨域，未设置相应策略H5录音权限永远是拒绝的，Native使用了postMessage转发兼容方案"
,"fLAB:"+ //no args
       "And cross-domain has occurred, and the corresponding policy permission is not set and the permission will always be denied, native uses a postMessage forwarding compatibility solution"

//@@zh="正在加载{1} ..."
,"bzvb:"+ //args: {1}
       "Loading {1} ..."

//@@zh="正在加载{1} ..."
,"pmQM:"+ //args: {1}
       "Loading {1} ..."

//@@zh="这个编码器无提示信息"
,"QNa8:"+ //no args
       "This encoder has no prompt information"

//@@zh="{1}编码器稳定版，"
,"JcTD:"+ //args: {1}
       "{1} encoder stable version, "

//@@zh="{1}编码器beta版，"
,"V3bs:"+ //args: {1}
       "{1} encoder beta version, "

//@@zh="{1}转码超快"
,"WKT6:"+ //args: {1}
       "{1} encoding is super fast"

//@@zh="支持边录边转码(Worker)"
,"Dnkb:"+ //no args
       "Support encoding while recording (Worker)"

//@@zh="仅支持标准UI线程转码"
,"YQGp:"+ //no args
       "Only standard UI thread encoding is supported"

//@@zh="（环境相应的支持文件，部分还需要配置文件）"
,"DPhZ:"+ //no args
       " (Corresponding support files for the environment, some also require configuration files) "

//@@zh="使用{1}录音需要加载的js："
,"mirC:"+ //args: {1}
       "The js that needs to be loaded when using {1} recording: "

//@@zh="【压缩版】："
,"GHsx:"+ //no args
       "[Distribution File]: "

//@@zh="【源文件】："
,"XCXI:"+ //no args
       "[Source File]: "

//@@zh="{1}已加载，可以录音了"
,"lZEO:"+ //args: {1}
       "{1} is loaded and ready to record"

//@@zh="{1}编码器压缩版"
,"FBTi:"+ //args: {1}
       "{1} encoder distribution version"

//@@zh="{1}编码器源码版"
,"c5BM:"+ //args: {1}
       "{1} encoder source version"

//@@zh="正在加载{1}"
,"IzN2:"+ //args: {1}
       "Loading {1}"

//@@zh="，请勿操作..."
,"m196:"+ //no args
       ", do not operate..."

//@@zh="js加载失败:"
,"iXrp:"+ //no args
       "js failed to load: "

//@@zh="正在执行Install，请勿操作..."
,"xEpa:"+ //no args
       "Install is in progress, please do not operate..."

//@@zh="Install成功，环境："
,"J58f:"+ //no args
       "Install successfully, environment: "

//@@zh="RecordApp.Install出错："
,"zLUe:"+ //no args
       "RecordApp.Install error: "

//@@zh="原生接口debugInfo调用出错："
,"uW1i:"+ //no args
       "Error in calling native interface debugInfo: "

//@@zh="占用内存大小(不一定准)："
,"uW2i:"+ //no args
       "Memory size occupied (not necessarily accurate): "

//@@zh="App中提升后台录音的稳定性：需要启用后台录音保活服务（iOS不需要），Android 9开始，锁屏或进入后台一段时间后App可能会被禁止访问麦克风导致录音静音、无法录音（App中H5录音也受影响），需要原生层提供搭配常驻通知的Android后台录音保活服务（Foreground services）"
,"zKd2:"+ //no args
       "Improve the stability of background recording in the App: You need to enable the background recording keep-alive service (not required for iOS). Starting from Android 9, after the screen is locked or the App enters the background for a period of time, the App may be prohibited from accessing the microphone, resulting in muted recording or failure to record (H5 recording in the App is also affected). The native layer needs to provide Android background recording keep-alive services (Foreground services) with permanent notifications."

//@@zh="原生接口androidNotifyService调用出错："
,"RkeW:"+ //no args
       "The native interface androidNotifyService calls an error: "

//@@zh="搭配常驻通知的Android后台录音保活服务已打开，ForegroundService已运行(通知可能不显示或会延迟显示，并不影响服务运行)，通知显示状态(1有通知权限 3可能无权限)"
,"kDxI:"+ //no args
       "Android background recording keep-alive service with resident notification is turned on, ForegroundService is running (notification may not be displayed or will be delayed, which does not affect service operation), notification display status (1 has notification permission 3 may not have permission)"

//@@zh="已关闭搭配常驻通知的Android后台录音保活服务"
,"KjdO:"+ //no args
       "Android background recording keep-alive service with resident notification has been disabled"

//@@zh="[外放]"
,"Tw1M:"+ //no args
       "[External speaker]"

//@@zh="[听筒播放]"
,"Tw2M:"+ //no args
       "[Handset Playing]"

//@@zh="切换失败："
,"Tw3M:"+ //no args
       "Switching failed: "

//@@zh="已切换"
,"Tw4M:"+ //no args
       "Switched"

]);
//*************** End srcFile=../app-support-sample/index.html ***************

//@@User Code-2 Begin 手写代码放这里 Put the handwritten code here @@

//@@User Code-2 End @@

}));