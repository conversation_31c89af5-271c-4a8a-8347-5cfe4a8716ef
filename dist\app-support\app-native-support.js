/*
录音
https://github.com/xiangyuecn/Recorder
src: app-support/app-native-support.js
*/
!function(e){var t="object"==typeof window&&!!window.document,r=(t?window:Object).Recorder,n=r.i18n;!function(i,e,p,t){"use strict";var o=i.RecordApp,u=o.CLog,v={Support:function(e){o.AlwaysAppUseH5?e(!1):s.<PERSON>(e)},CanProcess:function(){return!0},Config:{IsApp:function(e){n("IsApp"),e(!1)},JsBridgeRequestPermission:function(e,t){t(n("JsBridgeRequestPermission"))},JsBridgeStart:function(e,t,r){r(n("JsBridgeStart"))},JsBridgeStop:function(e,t){t(n("JsBridgeStop"))}}};o.RegisterPlatform("Native",v);var s=v.Config,n=function(e){var t=p("WWoj::{1}中的{2}方法未实现，请在{3}文件中或配置文件中实现此方法",0,"RecordApp.Platforms.Native.Config",e,"app-native-support.js");return u(t,3),t},d=function(e,t){var r=d.rec;if(r){r._appStart||r.envStart({envName:v.Key,canProcess:v.CanProcess()},t),r._appStart=1;var n=0;if(e instanceof Int16Array)for(var a=new Int16Array(e),i=0;i<a.length;i++)n+=Math.abs(a[i]);else for(var o,s=atob(e),c=s.length,a=new Int16Array(c/2),f=0,i=0;i+2<=c;f++,i+=2)o=(s.charCodeAt(i)|s.charCodeAt(i+1)<<8)<<16>>16,a[f]=o,n+=Math.abs(o);r.envIn(a,n)}else u(p("rCAM::未开始录音，但收到Native PCM数据"),3)};t||(o.NativeRecordReceivePCM=d);if(t){window.NativeRecordReceivePCM=d;try{window.top.NativeRecordReceivePCM=d}catch(e){var r=function(){u(p("t2OF::检测到跨域iframe，NativeRecordReceivePCM无法注入到顶层，已监听postMessage转发兼容传输数据，请自行实现将top层接收到数据转发到本iframe（不限层），不然无法接收到录音数据"),3)};setTimeout(r,8e3),r(),addEventListener("message",function(e){var t=e.data;t&&"NativeRecordReceivePCM"==t.type&&(t=t.data,d(t.pcmDataBase64,t.sampleRate))})}}v.RequestPermission=function(e,t,r){s.JsBridgeRequestPermission(t,r)},v.Start=function(e,t,r,n){d.param=t;var a=i(t);a.set.disableEnvInFix=!0,a.dataType="arraybuffer",d.rec=a,o.__Rec=a,s.JsBridgeStart(t,r,n)},v.Stop=function(r,a,t){var i=function(e){o.__Sync(r)&&(d.rec=null),t(e)};s.JsBridgeStop(function(){if(o.__Sync(r)){var t=d.rec;d.rec=null;var e=a?"":o.__StopOnlyClearMsg();if(t){u("rec encode: pcm:"+t.recSize+" srcSR:"+t.srcSampleRate+" set:"+JSON.stringify(d.param));var n=function(){if(o.__Sync(r))for(var e in t.set)d.param[e]=t.set[e]};if(!a)return n(),void i(e);t.stop(function(e,t,r){n(),a(e,t,r)},function(e){n(),i(e)})}else i(p("Z2y2::未开始录音")+(e?" ("+e+")":""))}else i("Incorrect sync status")},i)}}(r,0,n.$T,t)}();