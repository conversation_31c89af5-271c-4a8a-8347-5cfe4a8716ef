/*
Recorder ../assets/page-i18n/app_QuickStart_html/en-US.js
https://github.com/xiangyuecn/Recorder

Usage: Recorder.i18n.lang="en-US" or "en"

Desc: English, 英语。The translation of the /app-support-sample/QuickStart.html page, this translation mainly comes from: google translation + Baidu translation, translated from Chinese to English. 页面/app-support-sample/QuickStart.html的翻译，此翻译主要来自：google翻译+百度翻译，由中文翻译成英文。

注意：请勿修改//@@打头的文本行；以下代码结构由/src/package-i18n.js自动生成，只允许在字符串中填写翻译后的文本，请勿改变代码结构；翻译的文本如果需要明确的空值，请填写"=Empty"；文本中的变量用{n}表示（n代表第几个变量），所有变量必须都出现至少一次，如果不要某变量用{n!}表示

Note: Do not modify the text lines starting with //@@; The following code structure is automatically generated by /src/package-i18n.js, only the translated text is allowed to be filled in the string, please do not change the code structure; If the translated text requires an explicit empty value, please fill in "=Empty"; Variables in the text are represented by {n} (n represents the number of variables), all variables must appear at least once, if a variable is not required, it is represented by {n!}
*/
(function(factory){
	var browser=typeof window=="object" && !!window.document;
	var win=browser?window:Object; //非浏览器环境，Recorder挂载在Object下面
	factory(win.Recorder,browser);
}(function(Recorder,isBrowser){
"use strict";
var i18n=Recorder.i18n;

//@@User Code-1 Begin 手写代码放这里 Put the handwritten code here @@

//@@User Code-1 End @@

//@@Exec i18n.lang="en-US";
Recorder.CLog('Import Page[app_QuickStart_html] lang="en-US"');

//@@Exec i18n.alias["en-US"]="en";

var putSet={lang:"en"};

//@@Exec i18n.data["rtl$en"]=false;
i18n.data["desc-page-app_QuickStart_html$en"]="English, 英语。The translation of the /app-support-sample/QuickStart.html page, this translation mainly comes from: google translation + Baidu translation, translated from Chinese to English. 页面/app-support-sample/QuickStart.html的翻译，此翻译主要来自：google翻译+百度翻译，由中文翻译成英文。";
//@@Exec i18n.GenerateDisplayEnglish=false;



//*************** Begin srcFile=../app-support-sample/QuickStart.html ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="RecordApp QuickStart: 快速入门"
//@@Put0
 "CS4l:"+ //no args
       "Recorder App QuickStart: simple and easy to learn"

//@@zh="Recorder App QuickStart: 快速入门"
,"47ME:"+ //no args
       "Recorder App QuickStart"

//@@zh="更多Demo："
,"0KbD:"+ //no args
       "More Demos: "

//@@zh="请求权限"
,"65pE:"+ //no args
       "Request permission"

//@@zh="录制"
,"B3ly:"+ //no args
       "Start recording"

//@@zh="停止"
,"qpRJ:"+ //no args
       "Stop recording"

//@@zh="暂停"
,"hQ6r:"+ //no args
       "Pause"

//@@zh="继续"
,"ncRU:"+ //no args
       "Resume"

//@@zh="停止(仅清理)"
,"V0wV:"+ //no args
       "Stop recording (clean only)"

//@@zh="播放"
,"uwNo:"+ //no args
       "Play"

//@@zh="上传"
,"ziQt:"+ //no args
       "Upload"

//@@zh="本地下载"
,"HsFg:"+ //no args
       "Local download"

//@@zh="App里面总是使用Recorder H5录音"
,"iIYH:"+ //no args
       "Always use Recorder H5 in the App to record"

//@@zh="开始请求授权..."
,"Dy0d:"+ //no args
       "Start requesting permissions..."

//@@zh="已授权"
,"crXf:"+ //no args
       "Permission granted"

//@@zh="授权失败："
,"z8Lp:"+ //no args
       "Requesting permission failed: "

//@@zh="未请求权限"
,"38jY:"+ //no args
       "Permission not requested"

//@@zh="正在使用Native录音，底层由App原生层提供支持"
,"oZxa:"+ //no args
       "Native recording is being used, supported by the App native layer"

//@@zh="正在使用H5录音，底层由Recorder直接提供支持"
,"l9Th:"+ //no args
       "Currently using H5 recording, directly supported by Recorder"

//@@zh="正在打开..."
,"ULVy:"+ //no args
       "Opening..."

//@@zh="录制中："
,"qUcx:"+ //no args
       "Recording: "

//@@zh="录音被中断"
,"eWy1:"+ //no args
       "Recording interrupted"

//@@zh="录音未能正常开始"
,"eWy2:"+ //no args
       "Recording failed to start properly"

//@@zh="当前环境不支持onProcess回调，不启用watchDogTimer"
,"eWy3:"+ //no args
       "The current environment does not support onProcess callback, watchDogTimer will not be enabled"

//@@zh="开始录音失败："
,"0Rxw:"+ //no args
       "Failed to start recording: "

//@@zh="已暂停"
,"S27N:"+ //no args
       "Paused"

//@@zh="继续录音中..."
,"ChUt:"+ //no args
       "Resuming recording..."

//@@zh="未请求权限"
,"eHzv:"+ //no args
       "Recording permission not requested"

//@@zh="已录制mp3：{1}ms {2}字节，可以点击播放、上传、本地下载了"
,"YcB5:"+ //args: {1}-{2}
       "mp3 has been recorded: {1}ms {2}bytes, you can click on Play, Upload, Local download"

//@@zh="录音失败："
,"nIFF:"+ //no args
       "Recording failed: "

//@@zh="已清理，错误信息："
,"vHlR:"+ //no args
       "Cleaned, error message: "

//@@zh="请先录音，然后停止后再播放"
,"kWpA:"+ //no args
       "Please record first, then stop and play"

//@@zh="播放中: "
,"XCKe:"+ //no args
       "Playing: "

//@@zh="请先录音，然后停止后再上传"
,"SLaX:"+ //no args
       "Please record first, then stop before uploading"

//@@zh="上传成功"
,"DHfL:"+ //no args
       "Successfully upload"

//@@zh="没有完成上传，演示上传地址无需关注上传结果，只要浏览器控制台内Network面板内看到的请求数据结构是预期的就ok了。"
,"K1x7:"+ //no args
       "If the upload is not completed, the demo upload address does not need to pay attention to the upload result. As long as the request data structure seen in the Network panel in the browser console is expected, it is ok."

//@@zh="上传失败"
,"UKGO:"+ //no args
       "Upload failed"

//@@zh="开始上传到{1}，请稍候... （你可以先到源码 /assets/node-localServer 目录内执行 npm run start 来运行本地测试服务器）"
,"JIr4:"+ //args: {1}
       "Starting upload to {1}, requesting later... (You can first go to the source code /assets/node-localServer directory and execute npm run start to run the local test server)"

//@@zh="上传方式一【Base64】"
,"XVmd:"+ //no args
       "Upload method 1 [Base64] "

//@@zh="上传方式二【FormData】"
,"6rhE:"+ //no args
       "Upload method 2 [FormData] "

//@@zh="请先录音，然后停止后再下载"
,"hFmL:"+ //no args
       "Please record first, then stop before downloading"

//@@zh="点击 "
,"UrgF:"+ //no args
       "Click "

//@@zh=" 下载，或复制文本"
,"c7b7:"+ //no args
       " download, or copy text"

//@@zh="生成Base64文本"
,"6Bln:"+ //no args
       "Generate Base64 text"

//@@zh="下载 "
,"ROdY:"+ //no args
       "Download "

//@@zh="因移动端绝大部分国产浏览器未适配Blob Url的下载，所以本demo代码在移动端未调用downA.click()。请尝试点击日志中显示的下载链接下载"
,"3rPj:"+ //no args
       "Because the mobile browser may not be suitable for downloading Blob Url, this demo code does not call downA.click() on the mobile terminal. Please try to click the download link displayed in the log to download"

//@@zh="老的数据没有保存，只支持最新的一条"
,"weWp:"+ //no args
       "Old data is not saved, only the latest one is supported"

//@@zh="不能获得错误堆栈"
,"L6RO:"+ //no args
       "can't get error stack"

//@@zh="如需录音功能定制开发，网站、App、小程序、前端后端开发等需求，请加QQ群：①群 781036591、②群 748359095、③群 450721519，口令recorder，联系群主（即作者），谢谢~"
,"FaWz:"+ //no args
       "If you need custom development of recording functions, websites, apps, miniProgram, front-end and back-end development, etc., please join the Tencent QQ group: ①group 781036591、②group 748359095、③group 450721519, password recorder, contact the group owner (ie the author), thank you~"

//@@zh="Recorder App基于Recorder H5的跨平台录音，支持在浏览器环境中使用（H5）、各种使用js来构建的程序中使用（App、小程序、UniApp、Electron、NodeJs）"
,"98Ry:"+ //no args
       "Recorder App is based on Recorder H5's cross-platform recording and supports use in browser environments (H5) and various programs built using js (App, MiniProgram, UniApp, Electron, NodeJs)"

//@@zh="Recorder H5使用简单，功能丰富，支持PC、Android、iOS 14.3+"
,"p71N:"+ //no args
       "Recorder H5 is easy to use, rich in functions, and supports PC, Android, iOS 14.3+"

//@@zh="本页面修改时间（有可能修改了忘改）："
,"5hyY:"+ //no args
       "Modification time of this page (it may be modified and forgotten): "

//@@zh="RecordApp库修改时间（有可能修改了忘改）："
,"7GKB:"+ //no args
       "Modification time of the RecordApp library (it may be modified and forgotten): "

//@@zh="Recorder库修改时间（有可能修改了忘改）："
,"or9l:"+ //no args
       "Modification time of the Recorder library (it may be modified and forgotten): "

//@@zh="你可以直接将 "
,"veMB:"+ //no args
       "You can directly copy the "

//@@zh=" 文件copy到你的(https)网站中，无需其他文件，就能正常开始测试了，本文件更适合入门学习"
,"EA2M:"+ //no args
       " file to your (https) website, and you can start testing normally without other files; this file is more suitable for introductory learning"

//@@zh="AppUseH5选项变更，已重置RecordApp，请先进行权限测试"
,"Xvin:"+ //no args
       "AppUseH5 option changed, RecordApp has been reset, please test permissions first"

//@@zh="Install成功，环境："
,"1GmN:"+ //no args
       "Install successfully, environment: "

//@@zh="页面已准备好，请先点击请求权限，然后点击录制"
,"1ffD:"+ //no args
       "The page is ready, please click Request permission first, then click the Start recording"

//@@zh="RecordApp.Install出错："
,"Y5vJ:"+ //no args
       "RecordApp.Install error: "

//@@zh="js文件加载失败，请刷新重试！"
,"1ISU:"+ //no args
       "js file failed to load, please refresh and try again!"

]);
//*************** End srcFile=../app-support-sample/QuickStart.html ***************

//@@User Code-2 Begin 手写代码放这里 Put the handwritten code here @@

//@@User Code-2 End @@

}));