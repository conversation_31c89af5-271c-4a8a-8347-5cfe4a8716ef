/*
Recorder ../assets/page-i18n/widget_donate/en-US.js
https://github.com/xiangyuecn/Recorder

Usage: Recorder.i18n.lang="en-US" or "en"

Desc: English, 英语。The translation of the /assets/zdemo.widget.donate.js file, this translation mainly comes from: google translation + Baidu translation, translated from Chinese to English. 文件/assets/zdemo.widget.donate.js的翻译，此翻译主要来自：google翻译+百度翻译，由中文翻译成英文。

注意：请勿修改//@@打头的文本行；以下代码结构由/src/package-i18n.js自动生成，只允许在字符串中填写翻译后的文本，请勿改变代码结构；翻译的文本如果需要明确的空值，请填写"=Empty"；文本中的变量用{n}表示（n代表第几个变量），所有变量必须都出现至少一次，如果不要某变量用{n!}表示

Note: Do not modify the text lines starting with //@@; The following code structure is automatically generated by /src/package-i18n.js, only the translated text is allowed to be filled in the string, please do not change the code structure; If the translated text requires an explicit empty value, please fill in "=Empty"; Variables in the text are represented by {n} (n represents the number of variables), all variables must appear at least once, if a variable is not required, it is represented by {n!}
*/
(function(factory){
	var browser=typeof window=="object" && !!window.document;
	var win=browser?window:Object; //非浏览器环境，Recorder挂载在Object下面
	factory(win.Recorder,browser);
}(function(Recorder,isBrowser){
"use strict";
var i18n=Recorder.i18n;

//@@User Code-1 Begin 手写代码放这里 Put the handwritten code here @@

//@@User Code-1 End @@

//@@Exec i18n.lang="en-US";
Recorder.CLog('Import Page[widget_donate] lang="en-US"');

//@@Exec i18n.alias["en-US"]="en";

var putSet={lang:"en"};

//@@Exec i18n.data["rtl$en"]=false;
i18n.data["desc-page-widget_donate$en"]="English, 英语。The translation of the /assets/zdemo.widget.donate.js file, this translation mainly comes from: google translation + Baidu translation, translated from Chinese to English. 文件/assets/zdemo.widget.donate.js的翻译，此翻译主要来自：google翻译+百度翻译，由中文翻译成英文。";
//@@Exec i18n.GenerateDisplayEnglish=false;



//*************** Begin srcFile=../assets/zdemo.widget.donate.js ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="打赏挂件"
//@@Put0
 "4AP9:"+ //no args
       "Donation Widget"

//@@zh="已禁用打赏挂件一天，可通过命令开启："
,"NOaR:"+ //no args
       "The donation widget has been disabled for one day, it can be enabled by command: "

//@@zh="剩余{1}天就过年了，给大伙拜个早年吧~ 赏包辣条？"
,"e4F2:"+ //args: {1}
       "Donate{1!}"

//@@zh="新年快乐，给大伙拜年啦~ 赏个红包？"
,"qwYd:"+ //no args
       "Donate"

//@@zh="元宵节快乐~ 赏个红包？"
,"rZ6r:"+ //no args
       "Donate"

//@@zh="新年快乐，给大伙拜个晚年~ 赏包辣条？"
,"yA8s:"+ //no args
       "Donate"

//@@zh="赏包辣条？"
,"x2q9:"+ //no args
       "Donate"

//@@zh="再看吧，关掉先"
,"Fyh4:"+ //no args
       "Look again, close"

//@@zh="算了吧"
,"TQ2d:"+ //no args
       "Unwilling"

//@@zh="已打赏~ 壕气"
,"1LpD:"+ //no args
       "Donated~"

//@@zh="谢谢支持，看好你哟~"
,"NGKc:"+ //no args
       "Thank you for your support, you are awesome~"

//@@zh="emmm... 加油~"
,"6ifH:"+ //no args
       "emmm... come on~"

//@@zh="通过命令可禁用侧边打赏挂件一天: "
,"NSbf:"+ //no args
       "The side donation widget can be disabled for one day by command: "

//@@zh="emmm...已禁用打赏挂件，禁用时长为一天"
,"NaUj:"+ //no args
       "emmm...The donation widget has been disabled for one day"

]);
//*************** End srcFile=../assets/zdemo.widget.donate.js ***************

//@@User Code-2 Begin 手写代码放这里 Put the handwritten code here @@

//@@User Code-2 End @@

}));