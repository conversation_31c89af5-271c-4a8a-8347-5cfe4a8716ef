/**
本代码为RecordApp在uni-app下使用的适配代码，为压缩版（功能和源码版一致）
GitHub、详细文档、许可及服务协议: https://github.com/xiangyuecn/Recorder/tree/master/app-support-sample/demo_UniApp

【授权】
在uni-app中编译到App平台时仅供测试用（App平台包括：Android App、iOS App），不可用于正式发布或商用，正式发布或商用需先联系作者获取到商用授权许可

在uni-app中编译到其他平台时无此授权限制，比如：H5、小程序，均为免费授权

获取商用授权方式：到DCloud插件市场购买授权 https://ext.dcloud.net.cn/plugin?name=Recorder-NativePlugin-Android （会赠送Android版原生插件）；购买后可联系客服，同时提供订单信息，客服拉你进入VIP支持QQ群，入群后在群文件中可下载此js文件最新源码

客服联系方式：QQ ********** ，或者直接联系作者QQ ********* （回复可能没有客服及时）。
**/

/***
录音 RecordApp: uni-app支持文件，支持 H5、App vue、App nvue、微信小程序
GitHub、详细文档、许可及服务协议: https://github.com/xiangyuecn/Recorder/tree/master/app-support-sample/demo_UniApp

DCloud插件地址：https://ext.dcloud.net.cn/plugin?name=Recorder-UniCore
App配套原生插件：https://ext.dcloud.net.cn/plugin?name=Recorder-NativePlugin

全局配置参数：
	RecordApp.UniAppUseLicense:"" App中使用的授权许可，获得授权后请赋值为"我已获得UniAppID=***的商用授权"（***为你项目的uni-app应用标识），设置了UniNativeUtsPlugin时默认为已授权；如果未授权，将会在App打开后第一次调用`RecordApp.RequestPermission`请求录音权限时，弹出“未获得商用授权时，App上仅供测试”提示框。
	
	RecordApp.UniNativeUtsPlugin:null App中启用原生录音插件或uts插件，由App提供原生录音，将原生插件或uts插件赋值给这个变量即可开启支持；使用原生录音插件只需赋值为{nativePlugin:true}即可（提供nativePluginName可指定插件名字，默认为Recorder-NativePlugin），使用uts插件只需import插件后赋值即可（uts插件还未开发，目前不可集成）；如果未提供任何插件，App中将使用H5录音（在renderjs中提供H5录音）。
	
	RecordApp.UniWithoutAppRenderjs:false 不要使用或没有renderjs时，应当设为true，此时App中RecordApp完全运行在逻辑层，比如nvue页面，此时音频编码之类的操作全部在逻辑层，需要提供UniNativeUtsPlugin配置由原生插件进行录音，可视化绘制依旧可以在renderjs中进行。默认为false，RecordApp将在renderjs中进行实际的工作，然后将处理好的数据传回逻辑层，数据比较大时传输会比较慢。

不同平台环境下使用说明：
	【H5】 引入RecordApp和本js，按RecordApp的文档使用即可，和普通网页开发没有区别

	【微信小程序】 引入RecordApp和本js，同时引入RecordApp中的app-miniProgram-wx-support.js即可，录音操作和H5完全相同，其他可视化扩展等使用请参考RecordApp中的小程序说明
	
	【App vue】 引入RecordApp和本js，并创建一个<script module="xxx" lang="renderjs">，在renderjs中也引入RecordApp和本js，录音操作和H5大部分相同，部分回调需要多编写一个renderjs的处理代码，比如onProcess_renderjs，具体的请参考RecordApp文档中的app-support-sample/demo_UniApp文档
	
	【App nvue】 引入RecordApp和本js，配置RecordApp.UniWithoutAppRenderjs=true 和提供RecordApp.UniNativeUtsPlugin，录音操作和H5完全相同，但不支持可视化扩展
***/
!function(e){var t="object"==typeof window&&!!window.document,n=t?window:Object,i="https://github.com/xiangyuecn/Recorder/tree/master/app-support-sample/demo_UniApp";if(n.RecordApp){var r=n.Recorder,a=r.i18n;!function(S,h,e,W,B){"use strict";var P=h.RecordApp,j=P.CLog,I=function(){};P.UniSupportLM="2025-03-31 18:10";var N="app-uni-support.js",V=!1,M=!1,E=!1,O=!1,T=!1;(function(){/* #ifdef APP */if(B){V=!0;var e=navigator.userAgent.replace(/[_\d]/g," ");M=!/\bandroid\b/i.test(e)&&/\bios\b|\biphone\b/i.test(e)}else"object"==typeof plus&&("Android"==plus.os.name?V=!0:"iOS"==plus.os.name&&(M=V=!0)),(E=V)||j("App !plus",1)/* #endif */})(),V||((function(){/* #ifdef H5 */O=!0/* #endif */})(),(function(){/* #ifdef MP-WEIXIN */T=!0/* #endif */})());P.UniIsApp=function(){return V?M?2:1:0};var k=P.UniBtoa=function(e){if("object"==typeof uni&&uni.arrayBufferToBase64)return uni.arrayBufferToBase64(e);for(var t=new Uint8Array(e),n="",i=0,r=t.length;i<r;i++)n+=String.fromCharCode(t[i]);return btoa(n)},D=P.UniAtob=function(e){if("object"==typeof uni&&uni.base64ToArrayBuffer)return uni.base64ToArrayBuffer(e);for(var t=atob(e),n=new Uint8Array(t.length),i=0,r=t.length;i<r;i++)n[i]=t.charCodeAt(i);return n.buffer};P.UniB64Enc=function(e){if("object"==typeof uni&&uni.arrayBufferToBase64){var t=P.UniStr2Buf(e);return uni.arrayBufferToBase64(t)}return btoa(unescape(encodeURIComponent(e)))},P.UniB64Dec=function(e){if("object"==typeof uni&&uni.base64ToArrayBuffer){var t=uni.base64ToArrayBuffer(e);return P.UniBuf2Str(t)}return decodeURIComponent(escape(atob(e)))},P.UniStr2Buf=function(e){for(var t=unescape(encodeURIComponent(e)),n=new Uint8Array(t.length),i=0,r=t.length;i<r;i++)n[i]=t.charCodeAt(i);return n.buffer},P.UniBuf2Str=function(e){for(var t=new Uint8Array(e),n="",i=0,r=t.length;i<r;i++)n+=String.fromCharCode(t[i]);return decodeURIComponent(escape(n))};var x=P.UniJsSource={IsSource:!1,pcm_sum:function(e){for(var t=0,n=0;n<e.length;n++)t+=Math.abs(e[n]);return t}};(function(initMemory){!function(){var _=y;!function(e,t){for(var n=y,r=C();;)try{if(373421===-parseInt(n(321))/1*(-parseInt(n(410))/2)+parseInt(n(460))/3+parseInt(n(305))/4+parseInt(n(362))/5*(parseInt(n(644))/6)+parseInt(n(266))/7*(parseInt(n(543))/8)+-parseInt(n(590))/9*(-parseInt(n(393))/10)+-parseInt(n(399))/11)break;r.push(r.shift())}catch(e){r.push(r.shift())}}();var o={Support:function(e){var t=y;return T?(P[t(265)][t(527)]||j(W(t(643),0,t(250)),1),void e(!1)):O?void e(!1):V?void(!B||P[t(655)]?e(!0):e(!1)):(j(W(t(498)),3),void e(!1))},CanProcess:function(){return!0}};P[_(258)](B?_(278):_(603),o),V&&(j[_(586)]=B?_(247):_(401)),P[_(457)]||(P[_(457)]={id:0,pageShow:{}});var d=function(){return V&&!B&&!P[_(275)]};P[_(432)]=function(e){var t=_,n=P[t(457)][t(246)]={};if(T&&P[t(280)]&&P[t(280)](),d()){n[t(315)]=p(e);var r=P[t(343)];if(r){for(var i=getCurrentPages(),a=!0,o=0,s=i[t(621)];o<s;o++)if(i[o][t(486)].id==r){a=!1;break}a&&(P[t(682)]=null,P[t(343)]=null,P[t(322)]=null)}}},P[_(517)]=function(e){var t=_;if(d()){P[t(598)]=!0,P[t(683)]=1,setTimeout(function(){P[t(683)]=0});var n=v(e);if(n&&n[t(497)]&&n[t(497)][t(455)]){var r=e[t(450)]||e.$&&e.$[t(418)],i=s(e);i&&r?(r==P[t(682)]&&i==P[t(343)]||j(W(t(477))+t(256)+i+t(473)+r),P[t(682)]=r,P[t(343)]!=i&&(P[t(343)]=i,P[t(322)]=n[t(497)][t(455)]())):j(W(t(371))+t(546),1)}else j(W(t(284))+a(),1)}},P[_(537)]=function(e){var t=_;if(V&&B){if(e[t(361)])var n=window[t(529)],r=e[t(450)]||e[t(361)][t(400)][t(357)],i=e[t(361)][t(634)];if(i=w(1,i,t(387)))if(i[t(500)]=e,n&&r){var a=t(368)+n+t(473)+r;P[t(324)]=a,i[t(328)](t(624),a),P[t(457)][a]?j(W(t(627))+t(675)+a,3):(P[t(457)][a]=1,j(W(t(639))+t(665)+a))}else j(W(t(482))+t(546),1)}};var p=function(e,t,n){var r=_;if(e){if(e[r(573)])return e[r(573)];var i=s(e),a=e[r(450)]||e.$&&e.$[r(418)]}if(t)if(n||R(),r(512)==t)i=P[r(456)],a=P[r(670)];else i=P[r(343)],a=P[r(682)];return i&&a?r(368)+i+r(473)+a:""},f=function(e){var t=_;return t(298)===e||t(512)===e?{Rec_WvCid:p(null,e)}:{Rec_WvCid:e||"?"}},s=function(e){var t=_,n=v(e);return(n=n&&n[t(486)])&&n.id||0},v=function(e){var t=_,n=e[t(630)];return n&&n[t(497)]&&n[t(486)]?n:e[t(497)]&&e[t(486)]?e:void j(W(t(592)),1)},R=function(e){var t=_;if(!P[t(322)])return W(t(507));var n=p(null,1,1),r=P[t(457)][t(246)][t(315)];if(e){if(!P[t(456)])return W(t(492));if(p(null,t(512),1)!=n)return W(t(508))}return r&&r!=n&&j(W(t(333),0,r,n),3),""},w=P[_(424)]=function(e,t,n){var r=_,i=t&&t[r(568)];if(r(309)==i||3==e){var a=(t=t[r(462)])[r(267)],o=a[r(621)];900<o&&(a=a[r(289)](0,600)+r(555)+a[r(289)](o-300)),j(W(r(349),0,n)+"\n"+i+r(264)+a,3)}if(t&&i)return t;j(W(r(1==e?359:294),0,n),1)};P[_(413)]=function(e,i,a){var c=_,t="";t||E||(t=W(c(336)));var o=!t&&function(e,t){var n=c;if(e&&e[n(573)])var r=/^wv_(\d+)_/[n(423)](e[n(573)]),i=r&&r[1];else{var a=e&&v(e),o=a&&a[n(497)];i=(a=a&&a[n(486)])&&a.id}if(i){if(i==P[n(343)])return P[n(322)];if(o)return o[n(455)]();var s=plus[n(585)][n(350)](i);if(s)return s}return t?(R(),P[n(322)]):null}(e,null==e);if(t||o||(t=W(c(null==e?606:599))),t)return t+=W(c(239)),j(t+c(593)+i[c(289)](0,200),1),t;var n=P[c(457)][c(246)];if(n[c(591)]||(n[c(591)]=1,r()),a){a instanceof ArrayBuffer||(j(c(631),1),a[c(363)]instanceof ArrayBuffer&&(a=a[c(363)]));var s=("a"+Math[c(485)]())[c(577)](".",""),u=0,l=function(){var e=c;if(0!=u&&u>=a[e(652)])o[e(443)](e(429)+s+e(306)+s+e(663)+i+e(541));else{var t=P[e(470)](l),n=u;u+=524288;var r=a[e(417)](n,u);o[e(443)](e(510)+s+e(378)+s+e(520)+a[e(652)]+e(392)+k(r)+e(307)+t+e(419))}};l()}else o[c(443)](c(422)+i+c(272))},P[_(536)]=function(e,t,n){var r=_,i="";r(471)==typeof t&&(i=t[r(518)]||"",t=t[r(490)]||"");var a="";a||E||(a=W(r(550)));var o=!a&&p(e,null==e);if(a||o||(a=W(r(null==e?656:601))),a)return a+=W(r(596)),j(a+r(593)+t[r(289)](0,200),1),a;P[r(413)](e,r(638)+i+r(451)+JSON[r(677)](W(r(330)))+r(248)+o+r(452)+JSON[r(677)](W(r(660)))+r(604)+JSON[r(677)](W(r(255)))+r(611)+t+r(531),n)},P[_(625)]=function(d,f,v,A){return new Promise(function(n,r){var i=y,a=(f=f||{})[i(685)]||"",o=-1==f[i(345)],t="",s=setTimeout(function(){var e=i;c(),s=0;var t=new Error(W(e(o?671:673),0,a));t[e(415)]=1,r(t)},o?2e3:f[i(345)]||5e3),c=function(){var e=P[i(457)];delete e[u],delete e[t]};o&&(t=P[i(470)](function(){clearTimeout(s)}));var e=function(e){var t=i;if(c(),s)return clearTimeout(s),s=0,e[t(565)]?n({value:e[t(397)],bigBytes:P[t(525)](e[t(565)])}):e[t(382)]?n(e[t(397)]):void r(new Error(a+e[t(597)]))},u=P[i(470)](e),l=i(662)+u+i(323)+u+i(521)+m+i(251)+u+i(337)+(o?i(542)+m+i(251)+t+i(614):"")+i(241)+JSON[i(677)](W(i(466)))+i(479)+JSON[i(677)](W(i(475),0,i(360)+N+'"'))+i(408);f[i(356)]?l+=v:l={preCode:l+=i(447),jsCode:v};var p=P[f[i(356)]?i(413):i(536)](d,l,A);p&&e({errMsg:p})})};var m=_(469),r=function(){var i=_;if(E&&i(352)!=typeof UniServiceJSBridge){var e=P[i(501)];if(e){var t="";try{t=uni[i(398)](m)}catch(e){}if(e==t)return;j(W(i(583)),3)}e="r"+Math[i(485)]();try{uni[i(317)](m,e)}catch(e){}P[i(501)]=e,UniServiceJSBridge[i(463)](m),UniServiceJSBridge[i(434)](m,function(e){var t=i,n=e[t(566)]||"";if(t(681)!=n)if(t(668)!=n)if(-1==n[t(355)](t(532)))-1==n[t(355)](t(559))?j(W(t(489))+JSON[t(677)](e),1):P[t(339)](e);else{var r=P[t(457)][n];r?r(e):j(W(t(516))+JSON[t(677)](e),3)}else J(e);else F(e)})}};P[_(470)]=function(t){var e=_,n=P[e(457)],r=++n.id,i=e(532)+r;return n[i]=function(e){delete n[i],t(e)},i},P[_(381)]=function(e,t){var n=_,r=P[n(457)],i=n(293)+e;return t?r[i]=t:delete r[i],i},P[_(312)]=function(e){UniViewJSBridge[_(249)](m,e)},P[_(245)]=function(r,i,e){var a=_;if(B&&V){var o=P[a(324)];if(o){r instanceof ArrayBuffer||(j(a(480),1),r[a(363)]instanceof ArrayBuffer&&(r=r[a(363)]));var s=P[a(457)],c=0,u=++s.id;s[a(559)+u]=function(e){c=e,t()};var l=0,t=function(){var e=a;if(0!=l&&l>=r[e(652)])return delete s[e(559)+u],void i(c);var t=l;l+=524288;var n=r[e(417)](t,l);P[e(312)]({action:e(t?438:605),wvCid:o,wvID:u,mainID:c,b64:k(n)})};t()}else e(W(a(373)))}else e(W(a(295)))},P[_(339)]=function(e){var t=_,n=e[t(445)],r=P[t(457)],i=t(559);t(605)==e[t(566)]&&(n=++r.id,r[i+n]={memory:new Uint8Array(2097152),mOffset:0});var a=r[i+n];if(a){var o=new Uint8Array(D(e[t(580)])),s=o[t(621)];if(a[t(319)]+s>a[t(511)][t(621)]){var c=new Uint8Array(a[t(511)][t(621)]+Math[t(680)](2097152,s));c[t(623)](a[t(511)][t(488)](0,a[t(319)])),a[t(511)]=c}a[t(511)][t(623)](o,a[t(319)]),a[t(319)]+=s,P[t(413)](f(e[t(610)]),t(420)+i+e[t(513)]+t(461)+n+t(572))}else j(W(t(472)),3)},P[_(525)]=function(e){var t=_;if(!E)return null;var n=P[t(457)],r=n[t(559)+e];return delete n[t(559)+e],r?r[t(511)][t(363)][t(417)](0,r[t(319)]):null},P[_(374)]=function(n,i,a,r){var o=_;a=a||I,r=r||I;var s=function(e){var t=y;r(W(t(404),0,n)+(e[t(395)]||e[t(597)]))};if(T){var e=wx[o(435)][o(282)]+"/"+n;wx[o(346)]()[o(341)]({filePath:e,encoding:o(269),data:i,success:function(){a(e)},fail:s})}else E?plus.io[o(499)](plus.io[o(684)],function(e){var t=o;e[t(425)][t(254)](n,{create:!0},function(n){var r=t;n[r(487)](function(e){var t=r;e[t(635)]=function(){a(n[t(642)])},e[t(553)]=s;try{e[t(259)](k(i))}catch(e){s(e)}},s)},s)},s):r(W(o(296)))};var i=function(e){var t=_;if(A(),E){var n=W(t(608),0,S),r=P[t(539)];r&&(!e&&P[t(637)]||(P[t(637)]=1,r[t(514)]?j(W(t(535))+n):j(W(t(257))+n))),P[t(275)]?r?!e&&P[t(334)]||(P[t(334)]=1,j(W(t(533))+n)):j(W(t(465))+n,1):P[t(598)]&&(P[t(322)]?!e&&P[t(493)]||(P[t(493)]=1,j(W(t(616))+n)):j(W(t(519))+a()+n,1))}},a=function(){return W(_(286))};P[_(405)]=function(e,t,n,a){var r=_,o=[],s=function(e){return W(y(561),0,e)};if(T){var c=function(n){var r=y;if(n>=t[r(621)])a[r(629)](e,o);else{var i=t[n];e[r(276)]()[r(299)](i)[r(409)]({node:!0})[r(423)](function(e){var t=r;e[0]?(o[t(367)](e[0][t(448)]),c(n+1)):j(s(i),1)})}};c(0)}else if(O){for(var i=2;i<=3;i++){var u=w(i,e[r(634)],r(431));if(!u)return;for(var l=0,p=t[r(621)];l<p;l++){var d=t[l],f=u[r(380)](d+r(281)),v=f[0],A=f[1];if(!v&&0==l&&2==i)break;if(i=9,!v)return void j(s(d),1);A&&(A[r(633)](r(365))||(v=f[1],A=f[0]),A[r(462)][r(478)](A)),v[r(459)][r(379)]=r(563),(A=document[r(524)](r(310)))[r(328)](r(365),"1"),A[r(459)][r(288)]=A[r(459)][r(260)]=r(446),v[r(462)][r(687)](A),o[r(367)](A)}}a[r(629)](e,o)}else{if(E){var R=[r(354)];for(l=0,p=t[r(621)];l<p;l++){d=t[l];R[r(367)](r(304)+d+r(547)+l+r(686)+JSON[r(677)](s(d))+r(274)+(l+1)+r(375))}return R[r(367)]("}"),R[r(367)](n),void P[r(536)](e,R[r(421)]("\n"))}j(W(r(674)),1)}};var U=function(){var a=_;g(a(370),{},null,null,function(e){var t=a,n=e[t(566)];t(358)==n?e[t(496)]?j("["+o+"]["+e[t(685)]+"]"+e[t(395)],1):j("["+o+"]["+e[t(685)]+"]"+e[t(395)]):t(454)==n&&P[t(300)](e[t(383)],e[t(651)]);var r=P[t(457)][t(369)+n];if(r)for(var i in r)try{r[i](e)}catch(e){j(t(618)+n+"]["+i+t(403)+e[t(395)],1)}P[t(557)]&&P[t(557)](e)});var e=P[a(539)],o=e&&e[a(514)]?l:a(430);e&&(P[a(414)]=1)},c=_(271),u=_(646)+c,l=c;P[_(290)]=function(e,t,n){var r=_;U();var i=P[r(457)],a=r(369)+t,o=i[a];o||(o={},i[a]=o),n?o[e]=n:delete o[e]};var A=P[_(617)]=function(){var e=_,t=P[e(539)],n="";if(!V)return"";if(!t)return P[e(329)]||W(e(620));if(B&&(n=W(e(581))),!n&&t[e(514)]){if(!P[e(396)]){for(var r=0,i=l=t[e(654)]||l,a=0;!r&&a<2;a++){try{r=uni[e(440)](i)}catch(e){}if(r||i!=c)break;j(W(e(313),0,c,i=c+"-"+(M?e(243):e(567))))}if(P[e(396)]=r)j(W(e(658),0,i));else{i=l==c?u:l;n=W(e(588),0,i)}}}else n||t[e(484)]||(n=W(e(505)));return n&&(P[e(539)]=null,j(n,1)),P[e(329)]=n},g=function(e,t,n,r,i){var a=_,o=A(),s=P[a(539)];if(s){var c={action:e,args:t||{}};i||(i=function(e){var t=a;t(494)==e[t(468)]?n&&n(e[t(397)],e):r&&r(e[t(395)])}),s[a(514)]?P[a(396)][a(484)](c,i):s[a(484)](c,i)}else r&&r(o)};function C(){var e=initMemory;return(C=function(){return e})()}P[_(530)]=function(r,i){return new Promise(function(t,n){var e=y;if(!E)return n(new Error(W(e(327))));P[e(414)]||U(),g(r,i,function(e){t(e)},function(e){n(new Error(e))})})},o[_(237)]=function(e,t){i(),e()},o[_(291)]=function(){return e(_(291))},o[_(579)]=function(){return e(_(579))};var e=function(e){var t=_;if(!d())return!1;var n=q[t(347)];if(n){var r=R(1);r?j(r,1):P[t(413)](f(n[t(326)]),t(503)+e+"()")}else j(W(t(416),0,e),3)};o[_(641)]=function(e,t,n){var s=_,r=q[s(347)];q[s(347)]=null,r&&d()&&P[s(413)](f(r[s(326)]),s(602)),!d()||P[s(683)]?(P[s(456)]=P[s(343)],P[s(670)]=P[s(682)],i(!0),function(r){var i=s;if(!E)return r();var e=P[i(595)]=P[i(595)]||{},n=function(e,t,n){j(W(i(483),0,N)+e,t||0),n||r()},t=P[i(539)];if(t||e[i(552)])return e[i(552)]=e[i(552)]||(t[i(514)]?2:1),2==e[i(552)]?n(W(i(504))):n(W(i(609)));var a=i(688)+(e[i(411)]=e[i(411)]||uni[i(562)]()[i(411)]||"0")+i(444);if(P[i(270)]){if(P[i(270)]==a)return n(a);j(W(i(582),0,a),3)}var o=function(e){var t=i;n(t(659)+N+t(640)+a+t(551)+u+t(661)+S+" ",3,e)};if(e[i(648)])return o();o(1),e[i(648)]=1,uni[i(372)]({title:i(320),content:"文件"+N+i(344),showCancel:!1,confirmText:i(509),complete:function(){r()}})}(function(){b(e,t,n)})):n(W(s(407)))};var b=function(i,a,v){var o=_;if(B)return P[o(655)]?void a():void v(W(o(545)));var A=function(){v(W(o(458)),!0)},e=function(n){var r=o;U(),j(W(r(437))),g(r(522),{},function(e){var t=r;1==e?(j(W(t(556))),n()):(j(W(t(342))+t(366)+e,1),A())},v)};if(P[o(275)])e(a);else{var s=f(o(512)),t=function(e){var n=o,t=R(1),r=W(n(285));t?v(r+t):P[n(625)](s,{tag:r,timeout:2e3,useEval:!0},n(528))[n(240)](function(){e()})[n(515)](function(e){var t=n;v(e[t(415)]?r+W(t(594)):e[t(395)])})},n=function(e){var n=o;if(P[n(647)](i)){var t=n(540),r=P[t]||{};P[n(625)](s,{timeout:-1},n(613)+!!e+n(262)+t+"="+JSON[n(677)](r)+n(433))[n(240)](function(e){var t=n;e.ok?a():v(e[t(597)],e[t(481)])})[n(515)](function(e){v(e[n(395)])})}else v(n(301))};P[o(539)]?t(function(){e(function(){n(!0)})}):t(function(){!function(p){var d=y;if(M){j(W(d(558)));var f=function(){var e=d;if(P[e(325)])p();else{var t=[],n=P[e(548)],r=e(612);if(!n){var i=plus[e(626)][e(474)](e(560));t[e(367)](i);var a=i[e(645)]();t[e(367)](a);var o=a[e(338)]();t[e(367)](o),n=P[e(548)]=o[e(384)]({objectForKey:r})}if(n){var s=plus[e(626)][e(474)](e(394))[e(391)](),c=s[e(522)]();1970168948==c?s[e(426)](f):1735552628==c?(j(W(e(657))+" "+r+":"+n),p()):(j(W(e(238))+e(653)+c,1),A()),t[e(367)](s)}else v(W(e(283),0,r));for(var u=0,l=t[e(621)];u<l;u++)plus[e(626)][e(308)](t[u])}};f()}else j(W(d(678))),plus[d(353)][d(554)]([d(669)],function(e){var t=d;0<e[t(622)][t(621)]?(j(W(t(589))+JSON[t(677)](e)),p()):(j(W(t(412)),1,e),A())},function(e){var t=d;j(W(t(449))+e[t(395)],1,e),v(W(t(575))+e[t(395)])})}(function(){n()})})}};function y(e,t){var n=C();return(y=function(e,t){return n[e-=236]})(e,t)}o[_(464)]=function(t,o,n,s){var c=_,e=q[c(347)];if(q[c(347)]=null,e&&d()&&P[c(413)](f(e[c(326)]),c(602)),!d()||P[c(683)]){q[c(244)]=o;var u=h(o);if(u[c(623)][c(442)]=!0,u[c(292)]=c(441),q[c(332)]=!1,q[c(347)]=u,P[c(578)]=u,B)return P[c(655)]?void n():void s(W(c(376)));var r=function(t){var n=c,e=JSON[n(526)](JSON[n(677)](l));e[n(502)]=e[n(502)]||P[n(348)]||0,e[n(351)]=e[n(651)],e[n(651)]=48e3;var r=(e[n(649)]||{})[n(636)],i=e[n(666)];r&&null==i&&(i=1,e[n(666)]=!0),M||null!=e[n(584)]||(e[n(584)]=i?7:P[n(672)]||"0"),j(n(467)+JSON[n(677)](e)),U(),g(n(303),e,function(){var e=n;P[e(615)]=setInterval(function(){g(e(453),{},function(){})},5e3),t()},s)};clearInterval(P[c(615)]);var l={};for(var i in o)/_renderjs$/[c(600)](i)||(l[i]=o[i]);if(l=JSON[c(526)](JSON[c(677)](l)),P[c(275)])r(n);else{u[c(623)][c(476)]=c(538);var a=function(e,t){var n=c,r=R(1);if(r)s(W(n(331))+r);else{u[n(326)]=p(null,n(512)),q[n(332)]=t;var i=[n(364)+JSON[n(677)](l)+";"],a=n(619);i[n(367)](n(587)+(o[n(495)]||0)+n(506)+(o[n(302)]||0)+n(667)+(o[n(297)]||0)+n(263)+a+n(574)+a+n(427)),(o[n(268)]||o[n(316)])&&i[n(367)](n(679)+(o[n(316)]||0)+n(242)),i[n(367)](n(576)),P[n(625)](f(u[n(326)]),{timeout:-1},i[n(421)]("\n"))[n(240)](function(){e()})[n(515)](function(e){s(e[n(395)])})}};P[c(539)]?a(function(){var e=c;P[e(647)](t)?r(n):s(e(301))},!0):a(n)}}else s(W(c(534)))},o[_(406)]=function(e){return!!d()&&""},o[_(261)]=function(e){var t=_;if(!d())for(var n in e)/_renderjs$/[t(600)](n)&&delete e[n]};var F=function(e){var t=_,n=q[t(347)];n&&(n[t(623)][t(651)]=e[t(389)],n[t(623)][t(564)]=e[t(252)]);for(var r=e[t(676)],i=0,a=r[t(621)];i<a;i++)q(r[i],e[t(651)])},J=function(e){var t=_,n=q[t(347)];if(n){var r=new Uint8Array(D(e[t(523)]));n[t(623)][t(268)]&&n[t(623)][t(268)](r)}else j(W(t(439)),3)},q=function(e,t){var n=_,r=q[n(347)];if(r){if(r[n(277)]||r[n(377)]({envName:o[n(402)],canProcess:o[n(428)]()},t),r[n(277)]=1,e instanceof Int16Array)var i=new Int16Array(e);else i=new Int16Array(D(e));var a=x[n(544)](i);r[n(273)](i,a)}else j(W(n(436)),3)};P[_(300)]=function(e,t){var n=_;if(q[n(332)]){var r=q[n(347)];return r?void P[n(413)](f(r[n(326)]),n(570)+e+'",'+t+")"):void j(W(n(236)),3)}q(e,t)},o[_(664)]=function(n,i,r){var a=_,o=function(e){var t=y;P[t(647)](n)&&(q[t(347)]=null,s&&c&&d()&&P[t(413)](f(s[t(326)]),t(602))),r(e)},s=q[a(347)],c=!0,u=i?"":P[a(314)](),e=function(){var e=a;if(P[e(647)](n))if(q[e(347)]=null,s){if(j(e(318)+s[e(253)]+e(335)+s[e(650)]+e(607)+JSON[e(677)](q[e(244)])),!i)return l(),void o(u);s[e(628)](function(e,t,n){l(),i(e,t,n)},function(e){l(),o(e)})}else o(W(e(386))+(u?" ("+u+")":""));else o(e(301))},l=function(){var e=a;if(P[e(647)](n))for(var t in q[e(347)]=null,s[e(623)])q[e(244)][t]=s[e(623)][t]};if(B)return P[a(655)]?void e():void o(W(a(340)));var t=function(e){g(a(388),{},e,o)};if(clearInterval(P[a(615)]),P[a(275)])t(e);else{var p=function(e){var r=a;if(s){var t=R(1);if(t)o(W(r(279))+t);else{var n=r(491)+(i&&q[r(244)][r(569)]||0)+r(549)+!i+r(571);P[r(625)](f(s[r(326)]),{timeout:-1},n)[r(240)](function(e){var t=r;c=!1,s[t(623)][t(476)]=q[t(244)][t(476)],s[t(623)][t(651)]=e[t(389)],s[t(623)][t(564)]=e[t(252)],l();var n=P[t(525)](e[t(632)]);n?i(n,e[t(311)],e[t(390)]):o(W(t(385)))})[r(515)](function(e){c=!1,o(e[r(395)])})}}else o(W(r(287))+(u?" ("+u+")":""))};P[a(539)]?t(function(){var e=a;P[e(647)](n)?p():o(e(301))}):p()}}}();})(["UniAppUseLicense","Recorder-NativePlugin","\n})()","envIn",',1);\n\t\t\t\t\treturn;\n\t\t\t\t}else{\n\t\t\t\t\tif(el2){\n\t\t\t\t\t\tif(!el2.getAttribute("el2")){ el=els[1]; el2=els[0] }\n\t\t\t\t\t\tel2.parentNode.removeChild(el2);\n\t\t\t\t\t}\n\t\t\t\t\tel.style.display="none";\n\t\t\t\t\tel2=document.createElement("canvas");\n\t\t\t\t\tel2.setAttribute("el2","1"); el2.style.width=el2.style.height="100%";\n\t\t\t\t\tel.parentNode.appendChild(el2);\n\t\t\t\t}\n\t\t\t\tvar canvas',"UniWithoutAppRenderjs","createSelectorQuery","_appStart","UniApp-Renderjs","H6cq::无法调用Stop：","MiniProgramWx_onShow"," canvas","USER_DATA_PATH","9xoE::项目配置中未声明iOS录音权限{1}","GwCz::RecordApp.UniWebViewActivate 需要传入当前页面或组件的this对象作为参数","ksoA::无法调用RequestPermission：","e6Mo::，请检查此页面代码中是否编写了lang=renderjs的module，并且调用了RecordApp.UniRenderjsRegister；如果确实没有renderjs，比如nvue页面，请设置RecordApp.UniWithoutAppRenderjs=true并且搭配配套的原生插件在逻辑层中直接录音","pP4O::未开始录音","width","substr","UniNativeUtsPlugin_OnJsCall","Pause","dataType","mainCb_reg_","dX6B::{1}需要传入当前页面或组件的this对象作为参数","MujG::只允许在renderjs中调用RecordApp.UniWebViewSendBigBytesToMain","kxOd::当前环境未支持保存本地文件","start_renderjs","@act","select","UniNativeRecordReceivePCM","Incorrect sync status","onProcessBefore_renderjs","recordStart",'\n\t\t\t\tvar els=cpEl.querySelectorAll("',"2091116TrhjGs",".memory.buffer; delete window.",'"));\n\t\t\tcur.memory.set(buf,cur.mOffset);\n\t\t\tcur.mOffset+=buf.byteLength;\n\t\t\tRecordApp.UniWebViewSendToMain({action:"',"deleteObject","#text","canvas","duration","UniWebViewSendToMain","kSjQ::当前App未打包进双端原生插件[{1}]，尝试加载单端[{2}]","__StopOnlyClearMsg","sWvCid","takeoffEncodeChunk_renderjs","setStorageSync","rec encode: pcm:","mOffset","未获得商用授权时，App上仅供测试哈","10DOeuUS","__uniAppWebView",'", isOk:true, value:val, dataID:dataID});\n\t\t\t\t},CallFail)\n\t\t\t}else{\n\t\t\t\tRecordApp.UniWebViewSendToMain({action:"',"__UniWvCid","DisableIOSPlusReqPermission","__wvCid","MrBx::需在App逻辑层中调用原生插件功能","setAttribute","__uniNupErr","U1Be::renderjs中未import导入RecordApp","Bjx9::无法调用Start：","nativeToRjs","SWsy::检测到有其他页面或组件调用了RecordApp.UniPageOnShow（WvCid={1}），但未调用过RecordApp.UniWebViewActivate（当前WvCid={2}），部分功能会继续使用之前Activate的WebView和组件，请确保这是符合你的业务逻辑，不是因为忘记了调用UniWebViewActivate","__xYRb"," srcSR:","TfJX::当前不是App逻辑层",'",errMsg:err});\n\t\t};',"infoDictionary","__UniMainReceiveBigBytes","TPhg::不应当出现的非H5录音Stop","writeFile","Lx7r::无录音权限","__uniAppWebViewId","在uni-app中编译到App平台时仅供测试用，不可用于正式发布或商用，正式发布或商用需先获得授权许可（编译到其他平台时无此授权限制，比如：H5、小程序，均为免费授权）。本对话框仅在第一次请求录音权限时会弹出一次，如何去除本弹框、如何获取商用授权、更多信息请看控制台日志","timeout","getFileSystemManager","rec","Default_AppNativePlugin_SampleRate","dX7B::{1}未正确查询到节点，将使用传入的当前页面或组件this的$el.parentNode作为组件根节点。如果template下存在多个根节点(vue3 multi-root)，尽量在最外面再套一层view来避免兼容性问题","getWebviewById","sampleRate_set","undefined","android",'\n\t\t\tfor(var type=2;type<=3;type++){//@@Fast\n\t\t\t\tvar cpEl=RecordApp.__dX7B(type, this.$ownerInstance.$el, "RecordApp.UniFindCanvas.renderjs");\n\t\t\t\tif(!cpEl) return; //wvFixEl()\n\t\t',"indexOf","useEval","ownerId","onLog","dX5B::{1}需在renderjs中调用并且传入当前模块的this",'"@/uni_modules/Recorder-UniCore/',"$ownerInstance","610520HdwHhl","buffer","var set=","el2"," code=","push","wv_","nupOnJsCall_","jsCall","ipB3::RecordApp.UniWebViewActivate 发生不应该出现的错误（可能需要升级插件代码）：","showModal","kE91::renderjs中的mounted内需要调用RecordApp.UniRenderjsRegister才能调用RecordApp.UniWebViewSendBigBytesToMain","UniSaveLocalFile","=el2;\n\t\t\t","rSLO::不应当出现的非H5录音Start","envStart","=window.","display","querySelectorAll","UniMainCallBack_Register","isOk","pcmDataBase64","plusCallMethod","gomD::不应该出现的renderjs发回的文件数据丢失","YP4V::未开始录音","RecordApp.UniRenderjsRegister","recordStop","recSet_sr","mime","sharedInstance",'),mOffset:0};\n\t\t\tvar buf=new Uint8Array(RecordApp.UniAtob("',"10rPgQMX","AVAudioSession","message","__uniNP","value","getStorageSync","14683163WlLHoo","$vm","RecApp Main","Key","]: ","UqfI::保存文件{1}失败：","UniFindCanvas","Start_Check","PkQ2::需先调用RecordApp.UniWebViewActivate，然后才可以调用RequestPermission",");\n\t\t};\n\t","fields","25208UVqivC","appId","Ruxl::plus.android请求录音权限：无权限","UniWebViewEval","__uniNbjc","isTimeout","0FGq::未开始录音，不可以调用{1}","slice","uid",'"});\n\t\t})()','(function(){\n\t\tvar fn=RecordApp.__UniData["',"join","(function(){\n","exec","__dX7B","root","requestRecordPermission",');\n\t\t\tvar newBuffers=[],recSet=RecordApp.__Rec.set;\n\t\t\tfor(var i=newIdx;i<buffers.length;i++)newBuffers.push(RecordApp.UniBtoa(buffers[i].buffer));//@@Fast\n\t\t\tRecordApp.UniWebViewSendToMain({action:"recProcess",recSet_sr:recSet.sampleRate,recSet_bit:recSet.bitRate,sampleRate:sampleRate,newBuffers:newBuffers});\n\t\t\treturn procFn&&procFn.apply(This,arguments);\n\t\t};',"CanProcess","(function(){\n\t\t\t\tvar BigBytes=window.","RecorderUtsPlugin","RecordApp.UniFindCanvas.H5","UniPageOnShow",";\n\t\t\tRecordApp.RequestPermission(function(){\n\t\t\t\tCallSuccess({ok:1});\n\t\t\t},function(errMsg,isUserNotAllow){\n\t\t\t\tCallSuccess({errMsg:errMsg,isUserNotAllow:isUserNotAllow});\n\t\t\t});\n\t\t","subscribe","env","BjGP::未开始录音，但收到Uni Native PCM数据","Lx5r::正在调用原生插件请求录音权限","bigBytes_chunk","MTdp::未开始录音，但收到renderjs回传的onRecEncodeChunk","requireNativePlugin","arraybuffer","disableEnvInFix","evalJS","的商用授权","mainID","100%","CallErr=function(err){ CallFail(err) };","node","0JQw::plus.android请求录音权限出错：","_$id","\n\t\tif(!window.RecordApp){\n\t\t\tvar err=",'",vm=RecordApp.__uniWvCallVm;\n\t\tif(!vm || RecordApp.__uniWvCallWvCid!=wvCid){\n\t\t\tif(!RecordApp.__UniData[wvCid]){\n\t\t\t\tvar err=',"recordAlive","onRecord","$getAppWebview","__uniAppReqWebViewId","__UniData","0caE::用户拒绝了录音权限","style","1040175NqYSis",'"];\n\t\tif(fn)fn(',"parentNode","unsubscribe","Start","fqhr::当前已配置RecordApp.UniWithoutAppRenderjs，必须提供原生录音插件或uts插件才能录音，请参考RecordApp.UniNativeUtsPlugin配置","TSmQ::需要在页面中提供一个renderjs，在里面import导入RecordApp、录音格式编码器、可视化插件等","Native Start Set:","status","RecordApp__uniAppMainReceive","UniMainCallBack","object","CjMb::无效的BigBytes回传数据","_cid_","importClass","AN0e::需在renderjs中import {1}","type","WpKg::RecordApp.UniWebViewActivate 已切换当前页面或组件的renderjs所在的WebView","removeChild",');\n\t\t};\n\t\tif(!RecordApp.Platforms["UniApp-Renderjs"]){\n\t\t\treturn CallFail(',"UniWebViewSendBigBytesToMain buffer must be ArrayBuffer","isUserNotAllow","Uc9E::RecordApp.UniRenderjsRegister 发生不应该出现的错误（可能需要升级插件代码）：","FabE::【在App内使用{1}的授权许可】","request","random","$page","createWriter","subarray","ZHwv::[MainReceive]从renderjs发回未知数据：","jsCode","(function(){\n\t\t\tvar stopFn=","7ot0::需先调用RecordApp.RequestPermission方法","__0hyi","success","onProcess_renderjs","isError","$scope","4ATo::Recorder-UniCore目前只支持：H5、APP(Android iOS)、MP-WEIXIN，其他平台环境需要自行编写适配文件实现接入","requestFileSystem","__rModule","__uniAppMainReceiveBind","appNativePlugin_sampleRate","RecordApp.","w37G::已购买原生录音插件，获得授权许可","TGMm::提供的RecordApp.UniNativeUtsPlugin值不是RecordApp的uts原生录音插件",";\r\n\t\tvar procBefore=","AGd7::需要先调用RecordApp.UniWebViewActivate方法","VsdN::需重新调用RecordApp.RequestPermission方法","我知道啦","(function(){\n\t\t\tvar cur=window.","memory","@req","wvID","nativePlugin","catch","kZx6::从renderjs发回数据但UniMainCallBack回调不存在：","UniWebViewActivate","preCode","S3eF::未找到当前页面renderjs所在的WebView","||{memory:new Uint8Array(",'", isOk:true, value:val});\n\t\t\t}\n\t\t};\n\t\tvar CallFail=function(err){\n\t\t\tUniViewJSBridge.publishHandler("',"recordPermission","bytes","createElement","UniMainTakeBigBytes","parse","miniProgram-wx","CallSuccess(1)","__WebVieW_Id__","UniNativeUtsPluginCallAsync","\n\t\t}).call(vm);\n\t})()","mainCb_","xYRb::当前RecordApp运行在逻辑层中（性能会略低一些，可视化等插件不可用）","XCMU::需先调用RecordApp.UniWebViewActivate，然后才可以调用Start","XSYY::当前录音由原生录音插件提供支持","UniWebViewVueCall","UniRenderjsRegister","unknown","UniNativeUtsPlugin","RequestPermission_H5OpenSet","\n\t\t\t})()",'\n\t\t\tUniViewJSBridge.publishHandler("',"1433144TdzEXp","pcm_sum","Jk72::不应当出现的非H5权限请求","!id || !cid",' canvas"),el=els[0],el2=els[1];\n\t\t\t\tif(!el && ',"__9xoE",";\n\t\t\tvar clear=","lU1W::当前不是App逻辑层","' ，就不会弹提示框了；或者购买了配套的原生录音插件，设置RecordApp.UniNativeUtsPlugin参数后，也不会弹提示框。【获取授权方式】到DCloud插件市场购买授权: ","uts","onerror","requestPermissions","\n...\n","Lx6r::已获得录音权限","UniNativeUtsPlugin_JsCall","Y3rC::正在调用plus.ios@AVAudioSession请求iOS原生录音权限","bigBytes_","NSBundle","k7im::未找到Canvas：{1}，请确保此DOM已挂载（可尝试用$nextTick等待DOM更新）","getSystemInfoSync","none","bitRate","dataID","action","Android","nodeName","stop_renderjs",'RecordApp.UniNativeRecordReceivePCM("',';\n\t\t\tvar errFn=function(errMsg){\n\t\t\t\tCallFail(errMsg);\n\t\t\t};\n\t\t\tRecordApp.Stop(clear?null:function(arrBuf,duration,mime){\n\t\t\t\tstopFn&&stopFn.apply(This,arguments);\n\t\t\t\tvar recSet=RecordApp.__Rec.set,t1=Date.now();\n\t\t\t\tRecordApp.CLog("开始传输"+arrBuf.byteLength+"字节的数据回逻辑层，可能会比较慢，推荐使用takeoffEncodeChunk实时获取音频文件数据可避免Stop时产生超大数据回传");\n\t\t\t\tRecordApp.UniWebViewSendBigBytesToMain(arrBuf,function(dataId){//数据可能很大\n\t\t\t\t\tRecordApp.CLog("完成传输"+arrBuf.byteLength+"字节的数据回逻辑层，耗时"+(Date.now()-t1)+"ms");\n\t\t\t\t\tCallSuccess({recSet_sr:recSet.sampleRate,recSet_bit:recSet.bitRate,dataId:dataId,duration:duration,mime:mime});\n\t\t\t\t},errFn);\n\t\t\t},errFn);\n\t\t})()',");\n\t})()","Rec_WvCid","){\r\n\t\t\tprocBefore&&procBefore.call(This,","Mvl7::调用plus的权限请求出错：","RecordApp.Start(set,function(){\n\t\t\tstartFn&&startFn.call(This);\n\t\t\tCallSuccess();\n\t\t},function(errMsg){\n\t\t\tCallFail(errMsg);\n\t\t});","replace","__Rec","Resume","b64","l6sY::renderjs中不支持设置RecordApp.UniNativeUtsPlugin","aPoj::UniAppUseLicense填写无效，如果已获取到了商用授权，请填写：{1}，否则请使用空字符串","vEgr::不应该出现的MainReceiveBind重复绑定","android_audioSource","webview","Tag","var procFn=","SCW9::配置了RecordApp.UniNativeUtsPlugin，但当前App未打包进原生录音插件[{1}]","Bgls::已获得Android原生录音权限：","478593iJeHhj","mrBind","KpY6::严重兼容性问题：无法获取页面或组件this.$root.$scope或.$page","   jsCode=","KnF0::无法连接到renderjs","__FabE","TtoS::，不可以调用RecordApp.UniWebViewVueCall","errMsg","__hasWvActivate","qDo1::未找到此页面renderjs所在的WebView","test","6Iql::未找到此页面renderjs所在的WebView Cid","RecordApp.Stop()","UniApp-Main",';\n\t\t\t\tRecordApp.CLog(err,1); CallErr(err); return;\n\t\t\t};\n\t\t\tvar el=document.querySelector("[rec_wv_cid_key=\'"+wvCid+"\']");\n\t\t\tvm=el&&el.__rModule;\n\t\t\tif(!vm){\n\t\t\t\tvar err=',"bigBytes_start","peIm::当前还未调用过RecordApp.UniWebViewActivate"," set:","1f2V:: | RecordApp的uni-app支持文档和示例：{1} ","e71S::已购买uts插件，获得授权许可","wvCid",'+" WvCid="+wvCid;\n\t\t\t\tRecordApp.CLog(err,1); CallErr(err); return;\n\t\t\t};\n\t\t\tRecordApp.__uniWvCallVm=vm;\n\t\t\tRecordApp.__uniWvCallWvCid=wvCid;\n\t\t}; (function(){ var This=this;\n\t\t\t',"NSMicrophoneUsageDescription","\n\t\t\tRecordApp.UniAppUseNative=",'"});\n\t\t',"_X3Ij_alive","0hyi::当前RecordApp运行在renderjs所在的WebView中（逻辑层中只能做有限的实时处理，可视化等插件均需要在renderjs中进行调用）","UniCheckNativeUtsPluginConfig","OnJsCall[","buffers,power,duration,sampleRate,newIdx","H753::未配置RecordApp.UniNativeUtsPlugin原生录音插件","length","granted","set","rec_wv_cid_key","UniWebViewCallAsync","ios","mzKj::RecordApp.UniRenderjsRegister 重复注册当前页面renderjs模块，一个组件内只允许一个renderjs模块进行注册","stop","apply","$root","UniWebViewEval bigBytes must be ArrayBuffer","dataId","getAttribute","$el","onwrite","echoCancellation","__nnM6","(function(){\n\t\tvar CallErr=function(){};\n\t\t","7kJS::RecordApp.UniRenderjsRegister 已注册当前页面renderjs模块","在uni-app中编译到App平台时仅供测试用（App平台包括：Android App、iOS App），不可用于正式发布或商用，正式发布或商用需先获取到商用授权许可（编译到其他平台时无此授权限制，比如：H5、小程序，均为免费授权）。未获得授权时，在App打开后第一次调用RecordApp.RequestPermission请求录音权限时，会先弹出商用授权提示框；获取到授权许可后，请在调用RequestPermission前设置 RecordApp.UniAppUseLicense='","RequestPermission","fullPath","RXs7::微信小程序中需要：{1}","6DCsarq","mainBundle","https://ext.dcloud.net.cn/plugin?name=","__Sync","show","audioTrackSet","srcSampleRate","sampleRate","byteLength","denied ","nativePluginName","UniAppUseNative","mSbR::当前还未调用过RecordApp.UniWebViewActivate","j15C::已获得iOS原生录音权限","Xh1W::已加载原生录音插件[{1}]","当前未获得授权许可。文件","Bcgi::renderjs中的mounted内需要调用RecordApp.UniRenderjsRegister","-Android （会赠送Android版原生插件）；购买后可联系客服，同时提供订单信息，客服拉你进入VIP支持QQ群，入群后在群文件中可下载此js文件最新源码；客服联系方式：QQ ********** ，或者直接联系作者QQ ********* （回复可能没有客服及时）。详细请参考文档: ",'\n\t\tvar CallSuccess=function(val,buf){\n\t\t\tif(buf){\n\t\t\t\tRecordApp.UniWebViewSendBigBytesToMain(buf,function(dataID){\n\t\t\t\t\tRecordApp.UniWebViewSendToMain({action:"',";\n\t\t\t\t","Stop"," WvCid=","appNativePlugin_AEC_Enable",";\n\t\tvar startFn=","recEncodeChunk","android.permission.RECORD_AUDIO","__uniAppReqComponentId","KQhJ::{1}连接renderjs超时","Default_Android_AudioSource","RDcZ::{1}处理超时","yI24::RecordApp.UniFindCanvas未适配当前环境"," wvCid=","newBuffers","stringify","7Noe::正在调用plus.android.requestPermissions请求Android原生录音权限","var takeFn=","max","recProcess","__uniAppComponentId","__callWvActivate","PUBLIC_DOWNLOADS","tag","==0 && type==2) continue; type=9; //尝试获取el的上级来查询\n\t\t\t\tif(!el){\n\t\t\t\t\tRecordApp.CLog(","appendChild","我已获得UniAppID=","byzO::未开始录音，但收到UniNativeUtsPlugin PCM数据","Install","iKhe::plus.ios请求录音权限，状态值: ","igw2::，不可以调用RecordApp.UniWebViewEval","then","\n\t\tif(!window.RecordApp){\n\t\t\treturn CallFail(",';\n\t\t\tset.takeoffEncodeChunk=function(bytes){\n\t\t\t\tRecordApp.UniWebViewSendToMain({action:"recEncodeChunk",bytes:RecordApp.UniBtoa(bytes.buffer)});\n\t\t\t\ttakeFn&&takeFn.apply(This,arguments);\n\t\t\t};',"iOS","param","UniWebViewSendBigBytesToMain","pageShow","RecApp Renderjs",';\n\t\t\twindow["console"].error(err); CallErr(err); return;\n\t\t};\n\t\tvar wvCid="',"publishHandler","import 'recorder-core/src/app-support/app-miniProgram-wx-support.js'",'",{action:"',"recSet_bit","recSize","getFile","URyD::没有找到组件的renderjs模块"," WvCid=wv_","nnM6::当前录音由uts插件提供支持","RegisterPlatform","writeAsBinary","height","AllStart_Clean",";\r\n\t\t\tRecordApp.Current=null; //需先重置，不然native变化后install不一致\n\t\t\tRecordApp.",";\n\t\tset.onProcess=function("," parentNode:\n","Platforms","21RYYSQe","outerHTML","takeoffEncodeChunk","binary"])}(i,r,0,a.$T,t)}else console.error("需要先引入RecordApp，请按下面代码引入：\n1. 项目根目录 npm install recorder-core\n2. 页面中按顺序import\nimport Recorder from 'recorder-core'\nimport RecordApp from 'recorder-core/src/app-support/app.js'\nimport 你需要的音频格式编码器、可视化插件\n参考文档："+i)}();