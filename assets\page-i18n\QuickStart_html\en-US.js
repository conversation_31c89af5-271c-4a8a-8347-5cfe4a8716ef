/*
Recorder ../assets/page-i18n/QuickStart_html/en-US.js
https://github.com/xiangyuecn/Recorder

Usage: Recorder.i18n.lang="en-US" or "en"

Desc: English, 英语。The translation of the /QuickStart.html page, this translation mainly comes from: google translation + Baidu translation, translated from Chinese to English. 页面/QuickStart.html的翻译，此翻译主要来自：google翻译+百度翻译，由中文翻译成英文。

注意：请勿修改//@@打头的文本行；以下代码结构由/src/package-i18n.js自动生成，只允许在字符串中填写翻译后的文本，请勿改变代码结构；翻译的文本如果需要明确的空值，请填写"=Empty"；文本中的变量用{n}表示（n代表第几个变量），所有变量必须都出现至少一次，如果不要某变量用{n!}表示

Note: Do not modify the text lines starting with //@@; The following code structure is automatically generated by /src/package-i18n.js, only the translated text is allowed to be filled in the string, please do not change the code structure; If the translated text requires an explicit empty value, please fill in "=Empty"; Variables in the text are represented by {n} (n represents the number of variables), all variables must appear at least once, if a variable is not required, it is represented by {n!}
*/
(function(factory){
	var browser=typeof window=="object" && !!window.document;
	var win=browser?window:Object; //非浏览器环境，Recorder挂载在Object下面
	factory(win.Recorder,browser);
}(function(Recorder,isBrowser){
"use strict";
var i18n=Recorder.i18n;

//@@User Code-1 Begin 手写代码放这里 Put the handwritten code here @@

//@@User Code-1 End @@

//@@Exec i18n.lang="en-US";
Recorder.CLog('Import Page[QuickStart_html] lang="en-US"');

//@@Exec i18n.alias["en-US"]="en";

var putSet={lang:"en"};

//@@Exec i18n.data["rtl$en"]=false;
i18n.data["desc-page-QuickStart_html$en"]="English, 英语。The translation of the /QuickStart.html page, this translation mainly comes from: google translation + Baidu translation, translated from Chinese to English. 页面/QuickStart.html的翻译，此翻译主要来自：google翻译+百度翻译，由中文翻译成英文。";
//@@Exec i18n.GenerateDisplayEnglish=false;



//*************** Begin srcFile=../QuickStart.html ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="Recorder H5 QuickStart: 快速入门"
//@@Put0
 "XvFp:"+ //no args
       "Recorder H5 QuickStart: simple and easy to learn"

//@@zh="Recorder H5 QuickStart: 快速入门"
,"JM2s:"+ //no args
       "Recorder H5 QuickStart"

//@@zh="更多Demo："
,"FxZ3:"+ //no args
       "More Demos: "

//@@zh="切换到老版本测试"
,"UGOA:"+ //no args
       "Switch to old version"

//@@zh="打开录音,请求权限"
,"2KqN:"+ //no args
       "Open recording, request permission"

//@@zh="关闭录音,释放资源"
,"E0NB:"+ //no args
       "Close recording, release resources"

//@@zh="录制"
,"nlQN:"+ //no args
       "Start recording"

//@@zh="停止"
,"cEa1:"+ //no args
       "Stop recording"

//@@zh="暂停"
,"wQFS:"+ //no args
       "Pause"

//@@zh="继续"
,"hHDO:"+ //no args
       "Resume"

//@@zh="播放"
,"0TJm:"+ //no args
       "Play"

//@@zh="上传"
,"DOAp:"+ //no args
       "Upload"

//@@zh="本地下载"
,"GTE7:"+ //no args
       "Local download"

//@@zh="已打开录音，可以点击录制开始录音了"
,"GVCa:"+ //no args
       "Recording has been opened, you can click the Start recording"

//@@zh="打开录音失败："
,"TOOV:"+ //no args
       "Open the recording failed: "

//@@zh="已关闭"
,"jqOs:"+ //no args
       "Closed"

//@@zh="未打开录音"
,"VOOw:"+ //no args
       "Recording not open"

//@@zh="已开始录音..."
,"CGdy:"+ //no args
       "Started recording..."

//@@zh="录音被中断"
,"eWo1:"+ //no args
       "Recording interrupted"

//@@zh="录音未能正常开始"
,"eWo2:"+ //no args
       "Recording failed to start properly"

//@@zh="未打开录音"
,"ajKR:"+ //no args
       "Recording not open"

//@@zh="已暂停"
,"GvCy:"+ //no args
       "Paused"

//@@zh="未打开录音"
,"gCAR:"+ //no args
       "Recording not open"

//@@zh="继续录音中..."
,"5q1K:"+ //no args
       "Resuming recording..."

//@@zh="未打开录音"
,"Ob6S:"+ //no args
       "Recording not open"

//@@zh="未打开录音"
,"5JuL:"+ //no args
       "Recording not open"

//@@zh="时长:{1}ms"
,"gOix:"+ //args: {1}
       "duration: {1}ms"

//@@zh="已录制mp3：{1}ms {2}字节，可以点击播放、上传、本地下载了"
,"0LHf:"+ //args: {1}-{2}
       "mp3 has been recorded: {1}ms {2}bytes, you can click on Play, Upload, Local download"

//@@zh="录音失败:"
,"kGZO:"+ //no args
       "Recording failed: "

//@@zh="请先录音，然后停止后再播放"
,"tIke:"+ //no args
       "Please record first, then stop and play"

//@@zh="播放中: "
,"GlWb:"+ //no args
       "Playing: "

//@@zh="请先录音，然后停止后再上传"
,"DUTn:"+ //no args
       "Please record first, then stop before uploading"

//@@zh="上传成功"
,"G2MU:"+ //no args
       "Successfully upload"

//@@zh="没有完成上传，演示上传地址无需关注上传结果，只要浏览器控制台内Network面板内看到的请求数据结构是预期的就ok了。"
,"TUdi:"+ //no args
       "If the upload is not completed, the demo upload address does not need to pay attention to the upload result. As long as the request data structure seen in the Network panel in the browser console is expected, it is ok."

//@@zh="上传失败"
,"HjDi:"+ //no args
       "Upload failed"

//@@zh="开始上传到{1}，请稍候... （你可以先到源码 /assets/node-localServer 目录内执行 npm run start 来运行本地测试服务器）"
,"QnSI:"+ //args: {1}
       "Starting upload to {1}, requesting later... (You can first go to the source code /assets/node-localServer directory and execute npm run start to run the local test server)"

//@@zh="上传方式一【Base64】"
,"gG1f:"+ //no args
       "Upload method 1 [Base64] "

//@@zh="上传方式二【FormData】"
,"vDzB:"+ //no args
       "Upload method 2 [FormData] "

//@@zh="请先录音，然后停止后再下载"
,"M86h:"+ //no args
       "Please record first, then stop before downloading"

//@@zh="点击 "
,"vJPl:"+ //no args
       "Click "

//@@zh=" 下载，或复制文本"
,"Whtc:"+ //no args
       " download, or copy text"

//@@zh="生成Base64文本"
,"XK4l:"+ //no args
       "Generate Base64 text"

//@@zh="下载 "
,"g8Fy:"+ //no args
       "Download "

//@@zh="因移动端绝大部分国产浏览器未适配Blob Url的下载，所以本demo代码在移动端未调用downA.click()。请尝试点击日志中显示的下载链接下载"
,"DIEK:"+ //no args
       "Because the mobile browser may not be suitable for downloading Blob Url, this demo code does not call downA.click() on the mobile terminal. Please try to click the download link displayed in the log to download"

//@@zh="老的数据没有保存，只支持最新的一条"
,"eKKx:"+ //no args
       "Old data is not saved, only the latest one is supported"

//@@zh="不能获得错误堆栈"
,"kBaF:"+ //no args
       "can't get error stack"

//@@zh="如需录音功能定制开发，网站、App、小程序、前端后端开发等需求，请加QQ群：①群 781036591、②群 748359095、③群 450721519，口令recorder，联系群主（即作者），谢谢~"
,"Hzox:"+ //no args
       "If you need custom development of recording functions, websites, apps, miniProgram, front-end and back-end development, etc., please join the Tencent QQ group: ①group 781036591、②group 748359095、③group 450721519, password recorder, contact the group owner (ie the author), thank you~"

//@@zh="Recorder App基于Recorder H5的跨平台录音，支持在浏览器环境中使用（H5）、各种使用js来构建的程序中使用（App、小程序、UniApp、Electron、NodeJs）"
,"m0EU:"+ //no args
       "Recorder App is based on Recorder H5's cross-platform recording and supports use in browser environments (H5) and various programs built using js (App, MiniProgram, UniApp, Electron, NodeJs)"

//@@zh="Recorder H5使用简单，功能丰富，支持PC、Android、iOS 14.3+"
,"v17f:"+ //no args
       "Recorder H5 is easy to use, rich in functions, and supports PC, Android, iOS 14.3+"

//@@zh="本页面修改时间（有可能修改了忘改）："
,"EfeX:"+ //no args
       "Modification time of this page (it may be modified and forgotten): "

//@@zh="Recorder库修改时间（有可能修改了忘改）："
,"9Jy2:"+ //no args
       "Modification time of the Recorder library (it may be modified and forgotten): "

//@@zh="你可以直接将 "
,"7gIC:"+ //no args
       "You can directly copy the "

//@@zh=" 文件copy到你的(https)网站中，无需其他文件，就能正常开始测试了；相比 Recorder H5 (/index.html) 这个大而全(杂乱)的demo，本文件更适合入门学习"
,"s731:"+ //no args
       " file to your (https) website, and you can start testing normally without other files; compared to the large and complete (messy) demo of Recorder H5 (/index.html), this file is more suitable for introductory learning"

//@@zh="当前浏览器"
,"ERsK:"+ //no args
       "Current browser "

//@@zh="支持录音"
,"7tuo:"+ //no args
       "supports audio recording"

//@@zh="不支持录音"
,"8Z8O:"+ //no args
       "does not support audio recording"

//@@zh="页面已准备好，请先点击打开录音，然后点击录制"
,"BL9u:"+ //no args
       "The page is ready, please click to open the recording first, then click the Start recording"

//@@zh="js文件加载失败，请刷新重试！"
,"YzPd:"+ //no args
       "js file failed to load, please refresh and try again!"

]);
//*************** End srcFile=../QuickStart.html ***************

//@@User Code-2 Begin 手写代码放这里 Put the handwritten code here @@

//@@User Code-2 End @@

}));