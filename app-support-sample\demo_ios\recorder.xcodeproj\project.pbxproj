// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 50;
	objects = {

/* Begin PBXBuildFile section */
		222D69332305856D0095E833 /* RecordAppJsBridge.swift in Sources */ = {isa = PBXBuildFile; fileRef = 222D69322305856D0095E833 /* RecordAppJsBridge.swift */; };
		227CEBBA230584320076C1F7 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 227CEBB9230584320076C1F7 /* AppDelegate.swift */; };
		227CEBBC230584320076C1F7 /* MainView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 227CEBBB230584320076C1F7 /* MainView.swift */; };
		227CEBBF230584320076C1F7 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 227CEBBD230584320076C1F7 /* Main.storyboard */; };
		227CEBC1230584350076C1F7 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 227CEBC0230584350076C1F7 /* Assets.xcassets */; };
		227CEBC4230584350076C1F7 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 227CEBC2230584350076C1F7 /* LaunchScreen.storyboard */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		222D69322305856D0095E833 /* RecordAppJsBridge.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecordAppJsBridge.swift; sourceTree = "<group>"; };
		227CEBB6230584310076C1F7 /* recorder.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = recorder.app; sourceTree = BUILT_PRODUCTS_DIR; };
		227CEBB9230584320076C1F7 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		227CEBBB230584320076C1F7 /* MainView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainView.swift; sourceTree = "<group>"; };
		227CEBBE230584320076C1F7 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		227CEBC0230584350076C1F7 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		227CEBC3230584350076C1F7 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		227CEBC5230584350076C1F7 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		227CEBB3230584310076C1F7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		227CEBAD230584310076C1F7 = {
			isa = PBXGroup;
			children = (
				227CEBB8230584320076C1F7 /* recorder */,
				227CEBB7230584310076C1F7 /* Products */,
			);
			sourceTree = "<group>";
		};
		227CEBB7230584310076C1F7 /* Products */ = {
			isa = PBXGroup;
			children = (
				227CEBB6230584310076C1F7 /* recorder.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		227CEBB8230584320076C1F7 /* recorder */ = {
			isa = PBXGroup;
			children = (
				222D69322305856D0095E833 /* RecordAppJsBridge.swift */,
				227CEBBB230584320076C1F7 /* MainView.swift */,
				227CEBCB2305844E0076C1F7 /* ios */,
			);
			path = recorder;
			sourceTree = "<group>";
		};
		227CEBCB2305844E0076C1F7 /* ios */ = {
			isa = PBXGroup;
			children = (
				227CEBB9230584320076C1F7 /* AppDelegate.swift */,
				227CEBC0230584350076C1F7 /* Assets.xcassets */,
				227CEBC5230584350076C1F7 /* Info.plist */,
				227CEBC2230584350076C1F7 /* LaunchScreen.storyboard */,
				227CEBBD230584320076C1F7 /* Main.storyboard */,
			);
			path = ios;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		227CEBB5230584310076C1F7 /* recorder */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 227CEBC8230584350076C1F7 /* Build configuration list for PBXNativeTarget "recorder" */;
			buildPhases = (
				227CEBB2230584310076C1F7 /* Sources */,
				227CEBB3230584310076C1F7 /* Frameworks */,
				227CEBB4230584310076C1F7 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = recorder;
			productName = recorder;
			productReference = 227CEBB6230584310076C1F7 /* recorder.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		227CEBAE230584310076C1F7 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1020;
				LastUpgradeCheck = 1020;
				ORGANIZATIONNAME = macos;
				TargetAttributes = {
					227CEBB5230584310076C1F7 = {
						CreatedOnToolsVersion = 10.2.1;
					};
				};
			};
			buildConfigurationList = 227CEBB1230584310076C1F7 /* Build configuration list for PBXProject "recorder" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 227CEBAD230584310076C1F7;
			productRefGroup = 227CEBB7230584310076C1F7 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				227CEBB5230584310076C1F7 /* recorder */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		227CEBB4230584310076C1F7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				227CEBC4230584350076C1F7 /* LaunchScreen.storyboard in Resources */,
				227CEBC1230584350076C1F7 /* Assets.xcassets in Resources */,
				227CEBBF230584320076C1F7 /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		227CEBB2230584310076C1F7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				227CEBBC230584320076C1F7 /* MainView.swift in Sources */,
				227CEBBA230584320076C1F7 /* AppDelegate.swift in Sources */,
				222D69332305856D0095E833 /* RecordAppJsBridge.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		227CEBBD230584320076C1F7 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				227CEBBE230584320076C1F7 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		227CEBC2230584350076C1F7 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				227CEBC3230584350076C1F7 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		227CEBC6230584350076C1F7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		227CEBC7230584350076C1F7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		227CEBC9230584350076C1F7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = W55ZG96NPS;
				INFOPLIST_FILE = "$(SRCROOT)/recorder/ios/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.github.xianyuecn.recorder.edit-to-your-id;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		227CEBCA230584350076C1F7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = W55ZG96NPS;
				INFOPLIST_FILE = "$(SRCROOT)/recorder/ios/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.github.xianyuecn.recorder.edit-to-your-id;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		227CEBB1230584310076C1F7 /* Build configuration list for PBXProject "recorder" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				227CEBC6230584350076C1F7 /* Debug */,
				227CEBC7230584350076C1F7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		227CEBC8230584350076C1F7 /* Build configuration list for PBXNativeTarget "recorder" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				227CEBC9230584350076C1F7 /* Debug */,
				227CEBCA230584350076C1F7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 227CEBAE230584310076C1F7 /* Project object */;
}
