/*
录音
https://github.com/xiangyuecn/Recorder
src: engine/beta-amr.js,engine/beta-amr-engine.js,engine/wav.js
*/
!function(A){var e="object"==typeof window&&!!window.document,f=(e?window:Object).Recorder,i=f.i18n;!function(o,A,Q,e){"use strict";o.prototype.enc_amr={stable:!0,takeEC:"full",getTestMsg:function(){return Q("b2mN::AMR-NB(NarrowBand)，采样率设置无效（只提供8000hz），比特率范围：{1}（默认12.2kbps），一帧20ms、{2}字节；浏览器一般不支持播放amr格式，可用Recorder.amr2wav()转码成wav播放",0,"4.75, 5.15, 5.9, 6.7, 7.4, 7.95, 10.2, 12.2","Math.ceil(bitRate/8*20)+1")}};var g,v=function(A){var e=A.bitRate,f=o.AMR.BitRate(e),i=A.sampleRate;e==f&&8e3==i||o.CLog(Q("tQBv::AMR Info: 和设置的不匹配{1}，已更新成{2}",0,"set:"+e+"kbps "+i+"hz","set:"+f+"kbps 8000hz"),3),A.bitRate=f,A.sampleRate=8e3},l=function(){return Q.G("NeedImport-2",["beta-amr.js","src/engine/beta-amr-engine.js"])},u=e&&"function"==typeof Worker;o.amr2wav=function(A,i,r){if(o.AMR)if(o.prototype.wav){var e=function(A,f){var e=new Uint8Array(A);o.AMR.decode(e,function(A){var e=o({type:"wav"});f&&(e.dataType="arraybuffer"),e.mock(A,8e3).stop(function(A,e,f){i(A,e,f)},r)},r)};if(A instanceof ArrayBuffer)e(A,1);else{var f=new FileReader;f.onloadend=function(){e(f.result)},f.readAsArrayBuffer(A)}}else r(Q.G("NeedImport-2",["amr2wav","src/engine/wav.js"]));else r(l())},o.prototype.amr=function(A,e,f){var i=this,r=i.set,n=r.sampleRate,t=8e3;if(o.AMR){if(v(r),t<n)A=o.SampleData([A],n,t).data;else if(n<t)return void f(Q("q12D::数据采样率低于{1}",0,t));if(u){var w=i.amr_start(r);if(w){if(w.isW)return i.amr_encode(w,A),void i.amr_complete(w,e,f,1);i.amr_stop(w)}}o.AMR.encode(A,function(A){e(A.buffer,"audio/amr")},f,r.bitRate)}else f(l())},o.BindDestroy("amrWorker",function(){g&&(o.CLog("amrWorker Destroy"),g.terminate(),g=null)}),o.prototype.amr_envCheck=function(A,e){var f="";return e.takeoffEncodeChunk&&(C()||(f=Q("TxjV::当前浏览器版本太低，无法实时处理"))),f||o.AMR||(f=l()),f},o.prototype.amr_start=function(A){return C(A)};var c={id:0},C=function(f,A){var l,e=function(A){var e=A.data,f=l.wkScope.wk_ctxs,i=l.wkScope.wk_AMR,r=f[e.id];if("init"==e.action)f[e.id]={takeoff:e.takeoff,memory:new Uint8Array(5e5),mOffset:0,encObj:i.GetEncoder(e.bitRate)};else if(!r)return;switch(e.action){case"stop":if(!r.isCp)try{r.encObj.flush()}catch(A){console.error(A)}r.encObj=null,delete f[e.id];break;case"encode":if(r.isCp)break;try{var n=r.encObj.encode(e.pcm)}catch(A){r.err=A,console.error(A);break}if(!r._h){r._h=1;var t=i.GetHeader(),w=new Uint8Array(t.length+n.length);w.set(t),w.set(n,t.length),n=w}0<n.length&&(r.takeoff?B.onmessage({action:"takeoff",id:e.id,chunk:n}):function(A){var e=A.length;if(r.mOffset+e>r.memory.length){var f=new Uint8Array(r.memory.length+Math.max(5e5,e));f.set(r.memory.subarray(0,r.mOffset)),r.memory=f}r.memory.set(A,r.mOffset),r.mOffset+=e}(n));break;case"complete":r.isCp=1;try{r.encObj.flush()}catch(A){console.error(A)}if(r.err){B.onmessage({action:e.action,id:e.id,err:"AMR Encoder: "+r.err.message});break}B.onmessage({action:e.action,id:e.id,blob:r.memory.buffer.slice(0,r.mOffset)})}},i=function(i){B.onmessage=function(A){var e=A;i&&(e=A.data);var f=c[e.id];f&&("takeoff"==e.action?f.set.takeoffEncodeChunk(new Uint8Array(e.chunk.buffer)):(f.call&&f.call(e),f.call=null))}},r=function(){var A={worker:B,set:f};if(f){A.id=++c.id,c[A.id]=A,v(f);var e=!!f.takeoffEncodeChunk;e&&o.CLog(Q("Q7p7::takeoffEncodeChunk接管AMR编码器输出的二进制数据，只有首次回调数据（首帧）包含AMR头；在合并成AMR文件时，如果没有把首帧数据包含进去，则必须在文件开头添加上AMR头：Recorder.AMR.AMR_HEADER（转成二进制），否则无法播放"),3),B.postMessage({action:"init",id:A.id,sampleRate:f.sampleRate,bitRate:f.bitRate,takeoff:e,x:new Int16Array(5)})}else B.postMessage({x:new Int16Array(5)});return A},B=g;if(A||!u)return o.CLog(Q("6o9Z::当前环境不支持Web Worker，amr实时编码器运行在主线程中"),3),B={postMessage:function(A){e({data:A})}},l={wkScope:{wk_ctxs:{},wk_AMR:o.AMR}},i(),r();try{if(!B){var n=(e+"").replace(/[\w\$]+\.onmessage/g,"self.postMessage"),t=");self.onmessage="+(n=n.replace(/[\w\$]+\.wkScope/g,"wkScope"));t+=";var wkScope={ wk_ctxs:{},wk_AMR:Create() }";var w=o.AMR.Create.toString(),a=(window.URL||webkitURL).createObjectURL(new Blob(["var Create=(",w,t],{type:"text/javascript"}));B=new Worker(a),setTimeout(function(){(window.URL||webkitURL).revokeObjectURL(a)},1e4),i(1)}var s=r();return s.isW=1,g=B,s}catch(A){return B&&B.terminate(),console.error(A),C(f,1)}};o.prototype.amr_stop=function(A){if(A&&A.worker){A.worker.postMessage({action:"stop",id:A.id}),A.worker=null,delete c[A.id];var e=-1;for(var f in c)e++;e&&o.CLog(Q("yYWs::amr worker剩{1}个未stop",0,e),3)}},o.prototype.amr_encode=function(A,e){A&&A.worker&&A.worker.postMessage({action:"encode",id:A.id,pcm:e})},o.prototype.amr_complete=function(e,f,i,r){var n=this;e&&e.worker?(e.call=function(A){r&&n.amr_stop(e),A.err?i(A.err):f(A.blob,"audio/amr")},e.worker.postMessage({action:"complete",id:e.id})):i(Q("jOi8::amr编码器未start"))}}(f,0,i.$T,e)}(),function(A){"use strict";A.AMR=function r(){var A=function(){var w=u.Decoder_Interface_init(),A=n._malloc(u.AMR_BUFFER_COUNT),l=new Uint8Array(n.HEAPU8.buffer,A,u.AMR_BUFFER_COUNT);A=n._malloc(2*u.PCM_BUFFER_COUNT);var B=new Int16Array(n.HEAPU8.buffer,A,u.PCM_BUFFER_COUNT),a=[];this.decode=function(A){if(a.length){var e=new Uint8Array(a.length+A.length);e.set(a),e.set(A,a.length),A=e}for(var f=null,i=0,r=0;i+1<A.length;){var n=u.SIZES[A[i]>>3&15];if(null==n)throw new Error("Invalid amr frame type: "+A[i]);if(n+=1,f||(f=new Int16Array(Math.floor(A.length/Math.max(13,n)*u.PCM_BUFFER_COUNT))),i+n>A.length)break;if(l.set(A.subarray(i,i+n)),u.Decoder_Interface_Decode(w,l.byteOffset,B.byteOffset,0),r+u.PCM_BUFFER_COUNT>f.length){var t=new Int16Array(2*f.length);t.set(f.subarray(0,r)),f=t}i+=n,f.set(B,r),r+=u.PCM_BUFFER_COUNT}return a=A.subarray(i),new Int16Array(null==f?0:f.subarray(0,r))},this.flush=function(){return n._free(l.byteOffset),n._free(B.byteOffset),u.Decoder_Interface_exit(w),new Int16Array(0)}},e=function(A){var w=u.Mode[u.BitRate(A)],l=u.Encoder_Interface_init(),e=n._malloc(2*u.PCM_BUFFER_COUNT),B=new Int16Array(n.HEAPU8.buffer,e,u.PCM_BUFFER_COUNT);e=n._malloc(u.AMR_BUFFER_COUNT);var a=new Uint8Array(n.HEAPU8.buffer,e,u.AMR_BUFFER_COUNT),s=[];this.encode=function(A){if(s.length){var e=new Int16Array(s.length+A.length);e.set(s),e.set(A,s.length),A=e}for(var f=u.SIZES[w]+1,i=new Uint8Array(Math.ceil(A.length/u.PCM_BUFFER_COUNT*f)),r=0,n=0;r+u.PCM_BUFFER_COUNT<A.length;){B.set(A.subarray(r,r+u.PCM_BUFFER_COUNT));var t=u.Encoder_Interface_Encode(l,w,B.byteOffset,a.byteOffset,0);if(t!=f){console.error([t,f]);break}r+=u.PCM_BUFFER_COUNT,i.set(a.subarray(0,t),n),n+=t}return s=A.subarray(r),new Uint8Array(i.subarray(0,n))},this.flush=function(){return n._free(B.byteOffset),n._free(a.byteOffset),u.Encoder_Interface_exit(l),new Uint8Array(0)}},u={GetDecoder:function(){return r().DecG_()},GetEncoder:function(A){return r().EncG_(A)},decode:function(A,e,f){r().dec__(A,e,f)},encode:function(A,e,f,i){r().enc__(A,e,f,i)},DecG_:function(){return new A},dec__:function(n,t,w){var l=new A,B=[],a=0,s=function(){try{l.flush()}catch(A){console.error(A)}},o=0,Q=0;String.fromCharCode.apply(null,n.subarray(0,this.AMR_HEADER.length))==this.AMR_HEADER&&(o+=this.AMR_HEADER.length,Q=1);var g=(u.SIZES[n[o]>>3&15]||31)+1;g=1e3*Math.max(13,g)/20*5;var v=function(){try{var A=o;if(A<n.length){o+=g;var e=l.decode(n.subarray(A,o));B.push(e),a+=e.length}if(o<n.length)return void setTimeout(v)}catch(A){return console.error(A),s(),void w("AMR Decoder: "+(Q?A.message:"Not an amr audio file"))}s();for(var f=new Int16Array(a),i=0,r=0;i<B.length;i++){var e=B[i];f.set(e,r),r+=e.length}t(f)};v()},EncG_:function(A){return new e(A)},enc__:function(n,t,w,A){var l=new e(A),B=[this.GetHeader()],a=B[0].length,s=function(){try{l.flush()}catch(A){console.error(A)}},o=4e4;o-=o%this.PCM_BUFFER_COUNT;var Q=0,g=function(){try{var A=Q;if(A<n.length){Q+=o;var e=l.encode(n.subarray(A,Q));B.push(e),a+=e.length}if(Q<n.length)return void setTimeout(g)}catch(A){return console.error(A),s(),void w("AMR Encoder: "+A.message)}s();for(var f=new Uint8Array(a),i=0,r=0;i<B.length;i++){var e=B[i];f.set(e,r),r+=e.length}t(f)};g()},GetHeader:function(){for(var A=this.AMR_HEADER,e=new Uint8Array(A.length),f=0;f<A.length;f++)e[f]=A.charCodeAt(f);return e},BitRate:function(A){var e=this.Mode;if(A){if(null!=e[A])return A;var f=[];for(var i in e)f.push({v:+i,s:Math.abs(i-A||i-12.2)});return f.sort(function(A,e){return A.s-e.s}),f[0].v}return 12.2},Mode:{4.75:0,5.15:1,5.9:2,6.7:3,7.4:4,7.95:5,10.2:6,12.2:7},SIZES:[12,13,15,17,19,20,26,31,5,6,5,5,0,0,0,0],AMR_BUFFER_COUNT:32,PCM_BUFFER_COUNT:160,AMR_HEADER:"#!AMR\n",WAV_HEADER_SIZE:44},n={_main:function(){return u.Decoder_Interface_init=n._Decoder_Interface_init,u.Decoder_Interface_exit=n._Decoder_Interface_exit,u.Decoder_Interface_Decode=n._Decoder_Interface_Decode,u.Encoder_Interface_init=n._Encoder_Interface_init,u.Encoder_Interface_exit=n._Encoder_Interface_exit,u.Encoder_Interface_Encode=n._Encoder_Interface_Encode,0}};u.Module=n;var B={staticAlloc:function(A){var e=E;return E=15+(E=E+A|0)&-16,e},dynamicAlloc:function(A){var e=b;if(I<=(b=15+(b=b+A|0)&-16)){var f=void H("enlargeMemory");if(!f)return b=e,0}return e},alignMemory:function(A,e){var f=A=Math.ceil(A/(e||16))*(e||16);return f},GLOBAL_BASE:8};function a(A,e){A||H("Assertion failed: "+e)}n.Runtime=B;var s=2,o=4;function f(A,e,f,i){var r,n;"number"==typeof A?(r=!0,n=A):(r=!1,n=A.length);var t,w="string"==typeof e?e:null;if(f==o?t=i:(f!=s&&H("fix !ALLOC_STATIC"),t=B.staticAlloc(Math.max(n,w?1:e.length))),r){var l,i=t;for(a(0==(3&t)),l=t+(-4&n);i<l;i+=4)v[i>>2]=0;for(l=t+n;i<l;)Q[i++>>0]=0;return t}if("i8"===w)return A.subarray||A.slice?g.set(A,t):g.set(new Uint8Array(A),t),t;H("fix allocate")}n.ALLOC_STATIC=s,n.ALLOC_NONE=o,n.allocate=f;for(var Q,g,i,t,v,w,l,c,C,D=4096,E=0,h=0,P=0,k=0,b=0,d=n.TOTAL_STACK||65536,I=n.TOTAL_MEMORY||524288,M=65536;M<I||M<2*d;)M<16777216?M*=2:M+=16777216;M!==I&&H("fix t!==T"),C=new ArrayBuffer(I),Q=new Int8Array(C),i=new Int16Array(C),v=new Int32Array(C),g=new Uint8Array(C),t=new Uint16Array(C),w=new Uint32Array(C),l=new Float32Array(C),c=new Float64Array(C),a((v[0]=255)===g[0]&&0===g[3],"fix !LE"),n.HEAP=void 0,n.buffer=C,n.HEAP8=Q,n.HEAP16=i,n.HEAP32=v,n.HEAPU8=g,n.HEAPU16=t,n.HEAPU32=w,n.HEAPF32=l,n.HEAPF64=c,Math.imul&&-5===Math.imul(4294967295,5)||(Math.imul=function(A,e){var f=A>>>16,i=65535&A,r=e>>>16,n=65535&e;return i*n+(f*n+i*r<<16)|0}),Math.imul=Math.imul,Math.clz32||(Math.clz32=function(A){A>>>=0;for(var e=0;e<32;e++)if(A&1<<31-e)return e;return 32}),Math.clz32=Math.clz32,Math.abs,Math.cos,Math.sin,Math.tan,Math.acos,Math.asin,Math.atan,Math.atan2,Math.exp,Math.log,Math.sqrt,Math.ceil,Math.floor,Math.pow,Math.imul,Math.fround,Math.min,Math.clz32,E=31784,n.b64Atob=function(A){for(var e,f,i=0,r=0,n="";(f=A.charCodeAt(r++))&&61!=f;){if(64<f&&f<91)f-=65;else if(96<f&&f<123)f-=71;else if(47<f&&f<58)f+=4;else if(43==f)f=62;else{if(47!=f)continue;f=63}e=i%4?64*e+f:f,i++%4&&(n+=String.fromCharCode(255&e>>(-2*i&6)))}return n},n.b64Dec=function(A){for(var e="function"==typeof atob?atob(A):n.b64Atob(A),f=new Uint8Array(e.length),i=0;i<e.length;i++)f[i]=e.charCodeAt(i);return f},f(n.b64Dec("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"),"i8",o,B.GLOBAL_BASE),f(n.b64Dec("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"),"i8",o,B.GLOBAL_BASE+10240),f(n.b64Dec("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"),"i8",o,B.GLOBAL_BASE+20480),f(n.b64Dec("anwhe6d5/Hcidhh033F6b+dsKWpBZy9k9WCVXQ9aZVaZUqtOnkp0Ri1CzD1SOcE0GzBiK5cmvSHVHOIX5hLiDdgIywM9CkAKSQpYCmwKhwqnCs0K+QorC2MLoAvjCywMegzPDCgNiA3tDVcOxw48D7cPNxC9EEcR1xFsEgYTpRNJFPIUnxVSFgkXxBeFGEkZEhrgGrEbhxxhHT4eIB8FIO4g2yHLIr8jtiSwJa4mriexKLgpwSrMK9os6y3+LhMwKjFDMl4zezSaNbo22zf+OCI6RzttPJQ9vD7kPw1BNkJgQ4pEtEXdRgdIMElZSoJLqUzQTfZOG1A/UWJShFOkVMJV31b6VxNZK1pAW1NcY11xXn1fhmCMYY9ikGONZIdlfmZyZ2JoT2k4ah5r/2vdbLdtjG5ebytw9HC5cXlyNXPsc550THX1dZl2N3fRd2Z49niBeQZ6hnoBe3Z75ntRfLZ8FX1vfcN9EX5afp1+234Sf0R/cH+Wf7d/0X/mf/R//X//f/9/9H/Qf5V/Qn/XflV+vH0MfUV8aHt1emx5Tngcd9V1enQNc4xx+m9XbqJs3moLaShnOWU8YzNhHl//XNdapVhsVixU5VGaT0pN90qhSEpG80OcQUc/9DykOlg4EjbRM5gxZy8+LR8rCykCJwUlFSMzIV8fmx3nG0MasRgxF8MVaRQiE+8R0RDJD9YO+Q0yDYIM6AtmC/wKqQptCkkKPQo9Cj8KQwpKClQKYApvCoEKlgquCsgK5QoFCycLTQt1C58LzQv9CzAMZQydDNgMFg1WDZkN3g0mDnEOvg4ND2APtQ8MEGYQwhAhEYIR5hFMErQSHxOME/wTbhTiFFgV0RVMFskWSBfKF00Y0xhbGeUZcRr+Go4bIBy0HEod4R17HhYfsx9SIPIglSE5It4ihSMuJNgkhCUyJuAmkSdCKPUoqSlfKhYrziuHLEIt/S26LngvNjD2MLcxeDI7M/4zwjSHNU02EzfaN6E4ajkyOvw6xTuQPFo9JT7wPrw/iEBUQSBC7EK5Q4VEUkUeRutGt0eESFBJHErnSrNLfkxJTRNO3U6mT29QOFEAUsdSjlNUVBlV3VWhVmRXJljnWKdZZ1olW+JbnlxZXRNezF6DXzlg7mCiYVRiBWO1Y2NkD2W6ZWRmDGeyZ1do+mibaTtq2Wp1axBsqGw/bdNtZm73boZvE3CecCdxrnEycrVyNXOzcy90qXQhdZZ1CXZ6duh2VHe+dyV4injseEx5qnkFel56tHoHe1h7p3vyezx8gnzGfAh9R32Dfbx9830nfll+iH60ft1+BH8of0l/Z3+Df5x/sn/Ff9Z/5H/vf/d//X//f/9/YX2gdQ9pMFi1Q3QsYhNEZWNvZGVyAGVuY29kZXIA"),"i8",o,B.GLOBAL_BASE+30720);var F=B.alignMemory(f(12,"i8",s),8);function H(A){throw new Error("abort("+A+")")}a(F%8==0),n._memcpy=R,n._memmove=G,n._memset=L,n.abort=H,h=P=B.alignMemory(E),k=h+d,a((b=B.alignMemory(k))<I,"TOTAL_MEMORY not big enough for stack"),n.asmGlobalArg={Math:Math,Int8Array:Int8Array,Int16Array:Int16Array,Int32Array:Int32Array,Uint8Array:Uint8Array,Uint16Array:Uint16Array,Uint32Array:Uint32Array,Float32Array:Float32Array,Float64Array:Float64Array,NaN:NaN,Infinity:1/0},n.asmLibraryArg={abort:H,assert:a,_sysconf:function(A){if(30==A)return D;H("fix _sysconf")},_pthread_self:function(){return 0},_abort:function(A){n.abort(A)},___setErrNo:function(A){return n.___errno_location&&(v[n.___errno_location()>>2]=A),A},_sbrk:function A(e){var f,i=A;i.called||(0<(f=b)%4096&&(f+=4096-f%4096),b=f,i.called=!0,a(B.dynamicAlloc),i.alloc=B.dynamicAlloc,B.dynamicAlloc=function(){H("cannot dynamically allocate, sbrk now has control")});var r=b;if(0!=e){var n=i.alloc(e);if(!n)return-1>>>0}return r},_time:function(A){var e=Date.now()/1e3|0;return A&&(v[A>>2]=e),e},_emscripten_set_main_loop_timing:function(A,e){},_emscripten_memcpy_big:function(A,e,f){return g.set(g.subarray(e,e+f),A),A},_emscripten_set_main_loop:function(A,e,f,i,r){},STACKTOP:P,STACK_MAX:k,tempDoublePtr:F,ABORT:!1};var U=function(A,e,f){"use asm";var SA=new A.Int8Array(f);var jA=new A.Int16Array(f);var pA=new A.Int32Array(f);var w=new A.Uint8Array(f);var DA=new A.Uint16Array(f);var i=new A.Uint32Array(f);var r=new A.Float32Array(f);var n=new A.Float64Array(f);var WA=e.STACKTOP|0;var t=e.STACK_MAX|0;var l=e.tempDoublePtr|0;var B=e.ABORT|0;var a=0;var s=0;var o=0;var Q=0;var g=A.NaN,v=A.Infinity;var u=0,c=0,C=0,D=0,E=0.0,h=0,P=0,k=0,b=0.0;var d=0;var I=0;var M=0;var F=0;var H=0;var U=0;var G=0;var L=0;var R=0;var T=0;var y=A.Math.floor;var Y=A.Math.abs;var z=A.Math.sqrt;var X=A.Math.pow;var J=A.Math.cos;var O=A.Math.sin;var m=A.Math.tan;var N=A.Math.acos;var K=A.Math.asin;var x=A.Math.atan;var S=A.Math.atan2;var j=A.Math.exp;var p=A.Math.log;var W=A.Math.ceil;var VA=A.Math.imul;var V=A.Math.min;var Z=A.Math.clz32;var _=e.abort;var q=e.assert;var $=e._sysconf;var AA=e._pthread_self;var eA=e._abort;var ZA=e._abort;var fA=e.___setErrNo;var iA=e._sbrk;var rA=e._time;var nA=e._emscripten_set_main_loop_timing;var tA=e._emscripten_memcpy_big;var wA=e._emscripten_set_main_loop;var lA=0.0;function BA(){var A=0,e=0;e=WA;WA=WA+16|0;A=e;pA[A>>2]=0;zA(A,31756)|0;WA=e;return pA[A>>2]|0}function aA(A){A=A|0;var e=0,f=0;e=WA;WA=WA+16|0;f=e;pA[f>>2]=A;XA(f);WA=e;return}function sA(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;cA(A,(i|0)==0?(w[e>>0]|0)>>>3&15:15,e+1|0,f,2)|0;return}function oA(A){A=A|0;var e=0;e=lr(8)|0;mA(e,e+4|0,A)|0;return e|0}function QA(A){A=A|0;NA(A,A+4|0);Br(A);return}function gA(A,e,f,i,r){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;var n=0;r=WA;WA=WA+16|0;n=r;pA[n>>2]=e;f=(KA(pA[A>>2]|0,pA[A+4>>2]|0,e,f,i,n,3)|0)<<16>>16;SA[i>>0]=w[i>>0]|0|4;WA=r;return f|0}function vA(A){A=A|0;if(!A)A=-1;else{jA[A>>1]=4096;A=0}return A|0}function uA(A,e,f,i,r,n){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;var t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0;a=pA[n>>2]|0;g=r<<16>>16>0;if(g){t=0;w=0;do{B=jA[f+(t<<1)>>1]|0;B=VA(B,B)|0;if((B|0)!=1073741824){l=(B<<1)+w|0;if((B^w|0)>0&(l^w|0)<0){pA[n>>2]=1;w=(w>>>31)+2147483647|0}else w=l}else{pA[n>>2]=1;w=2147483647}t=t+1|0}while((t&65535)<<16>>16!=r<<16>>16);if((w|0)==2147483647){pA[n>>2]=a;B=0;l=0;do{w=jA[f+(B<<1)>>1]>>2;w=VA(w,w)|0;if((w|0)!=1073741824){t=(w<<1)+l|0;if((w^l|0)>0&(t^l|0)<0){pA[n>>2]=1;l=(l>>>31)+2147483647|0}else l=t}else{pA[n>>2]=1;l=2147483647}B=B+1|0}while((B&65535)<<16>>16!=r<<16>>16)}else Q=8}else{w=0;Q=8}if((Q|0)==8)l=w>>4;if(!l){jA[A>>1]=0;return}o=((Ni(l)|0)&65535)+65535|0;w=o<<16>>16;if((o&65535)<<16>>16>0){t=l<<w;if((t>>w|0)==(l|0))l=t;else l=l>>31^2147483647}else{w=0-w<<16;if((w|0)<2031616)l=l>>(w>>16);else l=0}s=er(l,n)|0;t=pA[n>>2]|0;if(g){w=0;l=0;do{a=jA[e+(w<<1)>>1]|0;a=VA(a,a)|0;if((a|0)!=1073741824){B=(a<<1)+l|0;if((a^l|0)>0&(B^l|0)<0){pA[n>>2]=1;l=(l>>>31)+2147483647|0}else l=B}else{pA[n>>2]=1;l=2147483647}w=w+1|0}while((w&65535)<<16>>16!=r<<16>>16);if((l|0)==2147483647){pA[n>>2]=t;a=0;l=0;do{B=jA[e+(a<<1)>>1]>>2;B=VA(B,B)|0;if((B|0)!=1073741824){w=(B<<1)+l|0;if((B^l|0)>0&(w^l|0)<0){pA[n>>2]=1;l=(l>>>31)+2147483647|0}else l=w}else{pA[n>>2]=1;l=2147483647}a=a+1|0}while((a&65535)<<16>>16!=r<<16>>16)}else Q=29}else{l=0;Q=29}if((Q|0)==29)l=l>>4;if(!l)B=0;else{w=(Ni(l)|0)<<16>>16;t=o-w|0;B=t&65535;l=(Ei(s,er(l<<w,n)|0)|0)<<16>>16;w=l<<7;t=t<<16>>16;if(B<<16>>16>0)t=B<<16>>16<31?w>>t:0;else{Q=0-t<<16>>16;t=w<<Q;t=(t>>Q|0)==(w|0)?t:l>>24^2147483647}B=(VA(((Ui(t,n)|0)<<9)+32768>>16,32767-(i&65535)<<16>>16)|0)>>>15<<16>>16}t=jA[A>>1]|0;if(g){l=i<<16>>16;w=0;while(1){i=((VA(t<<16>>16,l)|0)>>>15&65535)+B|0;t=i&65535;jA[f>>1]=(VA(jA[f>>1]|0,i<<16>>16)|0)>>>12;w=w+1<<16>>16;if(w<<16>>16>=r<<16>>16)break;else f=f+2|0}}jA[A>>1]=t;return}function _A(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var r=0,n=0,t=0,w=0,l=0,B=0,a=0,s=0,o=0;t=pA[i>>2]|0;r=f<<16>>16>0;if(r){w=0;n=0;do{B=jA[e+(w<<1)>>1]|0;B=VA(B,B)|0;if((B|0)!=1073741824){l=(B<<1)+n|0;if((B^n|0)>0&(l^n|0)<0){pA[i>>2]=1;n=(n>>>31)+2147483647|0}else n=l}else{pA[i>>2]=1;n=2147483647}w=w+1|0}while((w&65535)<<16>>16!=f<<16>>16);if((n|0)==2147483647){pA[i>>2]=t;B=0;t=0;do{l=jA[e+(B<<1)>>1]>>2;l=VA(l,l)|0;if((l|0)!=1073741824){w=(l<<1)+t|0;if((l^t|0)>0&(w^t|0)<0){pA[i>>2]=1;t=(t>>>31)+2147483647|0}else t=w}else{pA[i>>2]=1;t=2147483647}B=B+1|0}while((B&65535)<<16>>16!=f<<16>>16)}else o=8}else{n=0;o=8}if((o|0)==8)t=n>>4;if(!t)return;s=((Ni(t)|0)&65535)+65535|0;l=s<<16>>16;if((s&65535)<<16>>16>0){w=t<<l;if((w>>l|0)==(t|0))t=w;else t=t>>31^2147483647}else{l=0-l<<16;if((l|0)<2031616)t=t>>(l>>16);else t=0}a=er(t,i)|0;t=pA[i>>2]|0;if(r){w=0;n=0;do{B=jA[A+(w<<1)>>1]|0;B=VA(B,B)|0;if((B|0)!=1073741824){l=(B<<1)+n|0;if((B^n|0)>0&(l^n|0)<0){pA[i>>2]=1;n=(n>>>31)+2147483647|0}else n=l}else{pA[i>>2]=1;n=2147483647}w=w+1|0}while((w&65535)<<16>>16!=f<<16>>16);if((n|0)==2147483647){pA[i>>2]=t;t=0;w=0;do{B=jA[A+(t<<1)>>1]>>2;B=VA(B,B)|0;if((B|0)!=1073741824){l=(B<<1)+w|0;if((B^w|0)>0&(l^w|0)<0){pA[i>>2]=1;w=(w>>>31)+2147483647|0}else w=l}else{pA[i>>2]=1;w=2147483647}t=t+1|0}while((t&65535)<<16>>16!=f<<16>>16)}else o=28}else{n=0;o=28}if((o|0)==28)w=n>>4;if(!w)r=0;else{B=Ni(w)|0;l=B<<16>>16;if(B<<16>>16>0){t=w<<l;if((t>>l|0)==(w|0))w=t;else w=w>>31^2147483647}else{l=0-l<<16;if((l|0)<2031616)w=w>>(l>>16);else w=0}t=s-(B&65535)|0;l=t&65535;n=(Ei(a,er(w,i)|0)|0)<<16>>16;r=n<<7;t=t<<16>>16;if(l<<16>>16>0)r=l<<16>>16<31?r>>t:0;else{s=0-t<<16>>16;A=r<<s;r=(A>>s|0)==(r|0)?A:n>>24^2147483647}r=Ui(r,i)|0;if((r|0)>4194303)r=2147483647;else r=(r|0)<-4194304?-2147483648:r<<9;r=er(r,i)|0}n=(f&65535)+65535&65535;if(n<<16>>16<=-1)return;B=r<<16>>16;l=f+-1<<16>>16<<16>>16;while(1){t=e+(l<<1)|0;r=VA(jA[t>>1]|0,B)|0;do{if((r|0)!=1073741824){w=r<<1;if((w|0)<=268435455)if((w|0)<-268435456){jA[t>>1]=-32768;break}else{jA[t>>1]=r>>>12;break}else o=52}else{pA[i>>2]=1;o=52}}while(0);if((o|0)==52){o=0;jA[t>>1]=32767}n=n+-1<<16>>16;if(n<<16>>16<=-1)break;else l=l+-1|0}return}function cA(A,e,f,i,r){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;var n=0,t=0,w=0,l=0;l=WA;WA=WA+496|0;w=l;t=(r|0)==2;do{if(!(t&1|(r|0)==4)){ZA()}else{n=A+1168|0;if(t){OA(e,f,w,n);n=604}else{ZA()}r=jA[n+(e<<1)>>1]|0;do{if(e>>>0>=8){ZA()}else n=0}while(0);if(r<<16>>16==-1){A=-1;WA=l;return A|0}}}while(0);JA(A,e,w,n,i);pA[A+1760>>2]=e;A=r;WA=l;return A|0}function CA(A){A=A|0;var e=0;if(!A){e=-1;return e|0}e=A+122|0;do{jA[A>>1]=0;A=A+2|0}while((A|0)<(e|0));e=0;return e|0}function qA(A,e,f,i,r){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;var n=0,t=0,w=0,l=0,B=0,a=0,s=0;w=159;t=0;while(1){B=jA[f+(w<<1)>>1]|0;B=VA(B,B)|0;B=(B|0)==1073741824?2147483647:B<<1;n=B+t|0;if((B^t|0)>-1&(n^t|0)<0){pA[r>>2]=1;t=(t>>>31)+2147483647|0}else t=n;if((w|0)>0)w=w+-1|0;else{w=t;break}}r=w>>>14&65535;t=32767;n=59;while(1){B=jA[A+(n<<1)>>1]|0;t=B<<16>>16<t<<16>>16?B:t;if((n|0)>0)n=n+-1|0;else break}B=(w|0)>536870911?32767:r;r=t<<16>>16;n=r<<20>>16;w=t<<16>>16>0?32767:-32768;f=55;t=jA[A>>1]|0;while(1){l=jA[A+(f<<1)>>1]|0;t=t<<16>>16<l<<16>>16?l:t;if((f|0)>1)f=f+-1|0;else break}f=jA[A+80>>1]|0;l=jA[A+82>>1]|0;f=f<<16>>16<l<<16>>16?l:f;l=jA[A+84>>1]|0;f=f<<16>>16<l<<16>>16?l:f;l=jA[A+86>>1]|0;f=f<<16>>16<l<<16>>16?l:f;l=jA[A+88>>1]|0;f=f<<16>>16<l<<16>>16?l:f;l=jA[A+90>>1]|0;f=f<<16>>16<l<<16>>16?l:f;l=jA[A+92>>1]|0;f=f<<16>>16<l<<16>>16?l:f;l=jA[A+94>>1]|0;f=f<<16>>16<l<<16>>16?l:f;l=jA[A+96>>1]|0;f=f<<16>>16<l<<16>>16?l:f;l=jA[A+98>>1]|0;f=f<<16>>16<l<<16>>16?l:f;l=jA[A+100>>1]|0;f=f<<16>>16<l<<16>>16?l:f;l=jA[A+102>>1]|0;f=f<<16>>16<l<<16>>16?l:f;l=jA[A+104>>1]|0;f=f<<16>>16<l<<16>>16?l:f;l=jA[A+106>>1]|0;f=f<<16>>16<l<<16>>16?l:f;l=jA[A+108>>1]|0;f=f<<16>>16<l<<16>>16?l:f;l=jA[A+110>>1]|0;f=f<<16>>16<l<<16>>16?l:f;l=jA[A+112>>1]|0;f=f<<16>>16<l<<16>>16?l:f;l=jA[A+114>>1]|0;f=f<<16>>16<l<<16>>16?l:f;l=jA[A+116>>1]|0;f=f<<16>>16<l<<16>>16?l:f;l=A+118|0;s=jA[l>>1]|0;do{if((B+-21&65535)<17557&t<<16>>16>20?(B<<16>>16|0)<(((r<<4|0)==(n|0)?n:w)|0)?1:(f<<16>>16<s<<16>>16?s:f)<<16>>16<1953:0){t=A+120|0;n=jA[t>>1]|0;if(n<<16>>16>29){jA[t>>1]=30;f=t;w=1;break}else{w=(n&65535)+1&65535;jA[t>>1]=w;f=t;w=w<<16>>16>1&1;break}}else a=14}while(0);if((a|0)==14){f=A+120|0;jA[f>>1]=0;w=0}t=0;do{s=t;t=t+1|0;jA[A+(s<<1)>>1]=jA[A+(t<<1)>>1]|0}while((t|0)!=59);jA[l>>1]=B;t=jA[f>>1]|0;t=t<<16>>16>15?16383:t<<16>>16>8?15565:13926;n=di(e+8|0,5)|0;if((jA[f>>1]|0)>20){if(((di(e,9)|0)<<16>>16|0)>(t|0))a=20}else if((n<<16>>16|0)>(t|0))a=20;if((a|0)==20){jA[i>>1]=0;return w|0}n=(DA[i>>1]|0)+1&65535;if(n<<16>>16>10){jA[i>>1]=10;return w|0}else{jA[i>>1]=n;return w|0}return 0}function EA(A){A=A|0;var e=0;if(!A){e=-1;return e|0}e=A+18|0;do{jA[A>>1]=0;A=A+2|0}while((A|0)<(e|0));e=0;return e|0}function $A(A,e,f,i,r,n,t,w,l,B,a,s){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;l=l|0;B=B|0;a=a|0;s=s|0;var o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0;h=A+2|0;jA[A>>1]=jA[h>>1]|0;P=A+4|0;jA[h>>1]=jA[P>>1]|0;k=A+6|0;jA[P>>1]=jA[k>>1]|0;b=A+8|0;jA[k>>1]=jA[b>>1]|0;d=A+10|0;jA[b>>1]=jA[d>>1]|0;I=A+12|0;jA[d>>1]=jA[I>>1]|0;jA[I>>1]=f;u=0;E=0;do{o=r+(E<<1)|0;g=nr(jA[o>>1]|0,jA[i+(E<<1)>>1]|0,s)|0;g=(g&65535)-((g&65535)>>>15&65535)|0;g=g<<16>>31^g;D=((Ki(g&65535)|0)&65535)+65535|0;Q=D<<16>>16;if((D&65535)<<16>>16<0){v=0-Q<<16;if((v|0)<983040)c=g<<16>>16>>(v>>16)&65535;else c=0}else{v=g<<16>>16;g=v<<Q;if((g<<16>>16>>Q|0)==(v|0))c=g&65535;else c=(v>>>15^32767)&65535}C=Ki(jA[o>>1]|0)|0;g=jA[o>>1]|0;Q=C<<16>>16;if(C<<16>>16<0){ZA()}else{v=g<<16>>16;g=v<<Q;if((g<<16>>16>>Q|0)==(v|0))v=g&65535;else v=(v>>>15^32767)&65535}Q=Ei(c,v)|0;v=(D&65535)+2-(C&65535)|0;g=v&65535;do{if(v&32768){if(g<<16>>16!=-32768){D=0-v|0;v=D<<16>>16;if((D&65535)<<16>>16<0){ZA()}}else v=32767;g=Q<<16>>16;Q=g<<v;if((Q<<16>>16>>v|0)==(g|0))v=Q&65535;else v=(g>>>15^32767)&65535}else v=fr(Q,g,s)|0}while(0);u=Ci(u,v,s)|0;E=E+1|0}while((E|0)!=10);v=u&65535;g=u<<16>>16>5325;u=A+14|0;if(g){r=(DA[u>>1]|0)+1&65535;jA[u>>1]=r;if(r<<16>>16>10)jA[A+16>>1]=0}else jA[u>>1]=0;switch(e|0){case 0:case 1:case 2:case 3:case 6:break;default:{I=A+16|0;s=f;f=jA[I>>1]|0;f=f&65535;f=f+1|0;f=f&65535;jA[I>>1]=f;return s|0}}c=(t|n)<<16>>16==0;C=B<<16>>16==0;D=e>>>0<3;u=v+(D&((C|(c&(w<<16>>16==0|l<<16>>16==0)|a<<16>>16<2))^1)?61030:62259)&65535;u=u<<16>>16>0?u:0;if(u<<16>>16<=2048){u=u<<16>>16;if((u<<18>>18|0)==(u|0))l=u<<2;else l=u>>>15^32767}else l=8192;w=A+16|0;a=g|(jA[w>>1]|0)<40;u=jA[P>>1]|0;if((u*6554|0)==1073741824){pA[s>>2]=1;g=2147483647}else g=u*13108|0;u=jA[k>>1]|0;v=u*6554|0;if((v|0)!=1073741824){u=(u*13108|0)+g|0;if((v^g|0)>0&(u^g|0)<0){pA[s>>2]=1;u=(g>>>31)+2147483647|0}}else{pA[s>>2]=1;u=2147483647}v=jA[b>>1]|0;g=v*6554|0;if((g|0)!=1073741824){v=(v*13108|0)+u|0;if((g^u|0)>0&(v^u|0)<0){pA[s>>2]=1;v=(u>>>31)+2147483647|0}}else{pA[s>>2]=1;v=2147483647}u=jA[d>>1]|0;g=u*6554|0;if((g|0)!=1073741824){u=(u*13108|0)+v|0;if((g^v|0)>0&(u^v|0)<0){pA[s>>2]=1;g=(v>>>31)+2147483647|0}else g=u}else{pA[s>>2]=1;g=2147483647}u=jA[I>>1]|0;v=u*6554|0;if((v|0)!=1073741824){u=(u*13108|0)+g|0;if((v^g|0)>0&(u^g|0)<0){pA[s>>2]=1;u=(g>>>31)+2147483647|0}}else{pA[s>>2]=1;u=2147483647}g=er(u,s)|0;if(D&((c|C)^1)){ZA()}u=a?8192:l<<16>>16;o=VA(u,f<<16>>16)|0;if((o|0)==1073741824){pA[s>>2]=1;v=2147483647}else v=o<<1;g=g<<16>>16;Q=g<<13;if((Q|0)!=1073741824){o=v+(g<<14)|0;if((v^Q|0)>0&(o^v|0)<0){pA[s>>2]=1;v=(v>>>31)+2147483647|0}else v=o}else{pA[s>>2]=1;v=2147483647}o=VA(g,u)|0;if((o|0)==1073741824){pA[s>>2]=1;Q=2147483647}else Q=o<<1;o=v-Q|0;if(((o^v)&(Q^v)|0)<0){pA[s>>2]=1;o=(v>>>31)+2147483647|0}I=o<<2;f=w;s=er((I>>2|0)==(o|0)?I:o>>31^2147483647,s)|0;I=jA[f>>1]|0;I=I&65535;I=I+1|0;I=I&65535;jA[f>>1]=I;return s|0}function Ae(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,r=0,n=0,t=0;i=e;r=i+80|0;do{jA[i>>1]=0;i=i+2|0}while((i|0)<(r|0));i=0;do{t=jA[A+(i<<1)>>1]|0;r=((t&8)<<10&65535^8192)+-4096<<16>>16;n=i<<16;t=((jA[f+((t&7)<<1)>>1]|0)*327680|0)+n>>16;jA[e+(t<<1)>>1]=r;n=((jA[f+((DA[A+(i+5<<1)>>1]&7)<<1)>>1]|0)*327680|0)+n>>16;if((n|0)<(t|0))r=0-(r&65535)&65535;t=e+(n<<1)|0;jA[t>>1]=(DA[t>>1]|0)+(r&65535);i=i+1|0}while((i|0)!=5);return}function ee(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,r=0,n=0;r=e<<16>>16;i=(r<<1&2|1)+((r>>>1&7)*5|0)|0;e=r>>>4&3;e=((r>>>6&7)*5|0)+((e|0)==3?4:e)|0;r=f;n=r+80|0;do{jA[r>>1]=0;r=r+2|0}while((r|0)<(n|0));A=A<<16>>16;jA[f+(i<<1)>>1]=(0-(A&1)&16383)+57344;jA[f+(e<<1)>>1]=(0-(A>>>1&1)&16383)+57344;return}function fe(A,e,f,i,r,n){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;var t=0,w=0;n=f<<16>>16;w=n>>>3;A=A<<16>>16;A=((A<<17>>17|0)==(A|0)?A<<1:A>>>15^32767)+(w&8)<<16;w=(DA[i+(A+65536>>16<<1)>>1]|0)+((w&7)*5|0)|0;f=e<<16>>16;t=(0-(f&1)&16383)+57344&65535;A=r+((DA[i+(A>>16<<1)>>1]|0)+((n&7)*5|0)<<16>>16<<1)|0;e=r;n=e+80|0;do{jA[e>>1]=0;e=e+2|0}while((e|0)<(n|0));jA[A>>1]=t;jA[r+(w<<16>>16<<1)>>1]=(0-(f>>>1&1)&16383)+57344;return}function ie(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,r=0,n=0,t=0;e=e<<16>>16;i=(e&7)*5|0;r=(e>>>2&2|1)+((e>>>4&7)*5|0)|0;e=(e>>>6&2)+2+((e>>>8&7)*5|0)|0;n=f;t=n+80|0;do{jA[n>>1]=0;n=n+2|0}while((n|0)<(t|0));A=A<<16>>16;jA[f+(i<<1)>>1]=(0-(A&1)&16383)+57344;jA[f+(r<<1)>>1]=(0-(A>>>1&1)&16383)+57344;jA[f+(e<<1)>>1]=(0-(A>>>2&1)&16383)+57344;return}function re(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var r=0,n=0,t=0,w=0;e=e<<16>>16;t=jA[f+((e&7)<<1)>>1]|0;w=jA[f+((e>>>3&7)<<1)>>1]|0;n=jA[f+((e>>>6&7)<<1)>>1]|0;f=(e>>>9&1)+3+((jA[f+((e>>>10&7)<<1)>>1]|0)*5|0)|0;e=i;r=e+80|0;do{jA[e>>1]=0;e=e+2|0}while((e|0)<(r|0));A=A<<16>>16;jA[i+(t*327680>>16<<1)>>1]=(0-(A&1)&16383)+57344;jA[i+((w*327680|0)+65536>>16<<1)>>1]=(0-(A>>>1&1)&16383)+57344;jA[i+((n*327680|0)+131072>>16<<1)>>1]=(0-(A>>>2&1)&16383)+57344;jA[i+(f<<16>>16<<1)>>1]=(0-(A>>>3&1)&16383)+57344;return}function ne(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,r=0,n=0,t=0,w=0,l=0,B=0,a=0,s=0,o=0;o=WA;WA=WA+32|0;s=o+16|0;a=o;n=e;r=n+80|0;do{jA[n>>1]=0;n=n+2|0}while((n|0)<(r|0));r=jA[A>>1]|0;jA[s>>1]=r;jA[s+2>>1]=jA[A+2>>1]|0;jA[s+4>>1]=jA[A+4>>1]|0;jA[s+6>>1]=jA[A+6>>1]|0;l=jA[A+8>>1]|0;hA(l>>>3&65535,l&7,0,4,1,a,f);l=jA[A+10>>1]|0;hA(l>>>3&65535,l&7,2,6,5,a,f);l=jA[A+12>>1]|0;i=l>>2;do{if((i*25|0)!=1073741824){n=(VA(i,1638400)|0)+786432>>21;i=n*6554>>15;if((i|0)>32767){ZA()}A=(i<<16>>16)*5|0;t=i&1;if((A|0)==1073741824){pA[f>>2]=1;w=0;A=65535}else{w=0;B=6}}else{ZA()}}while(0);if((B|0)==6)A=A&65535;B=n-A|0;t=t<<16>>16==0?B:4-B|0;B=t<<16>>16;jA[a+6>>1]=Ci(((t<<17>>17|0)==(B|0)?t<<1:B>>>15^32767)&65535,l&1,f)|0;if(w){pA[f>>2]=1;i=32767}B=i<<16>>16;jA[a+14>>1]=((i<<17>>17|0)==(B|0)?i<<1:B>>>15^32767)+(l>>>1&1);i=0;while(1){r=r<<16>>16==0?8191:-8191;B=(jA[a+(i<<1)>>1]<<2)+i<<16;n=B>>16;if((B|0)<2621440)jA[e+(n<<1)>>1]=r;t=(jA[a+(i+4<<1)>>1]<<2)+i<<16;A=t>>16;if((A|0)<(n|0))r=0-(r&65535)&65535;if((t|0)<2621440){B=e+(A<<1)|0;jA[B>>1]=(DA[B>>1]|0)+(r&65535)}i=i+1|0;if((i|0)==4)break;r=jA[s+(i<<1)>>1]|0}WA=o;return}function hA(A,e,f,i,r,n,t){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;var w=0,l=0,B=0,a=0,s=0,o=0,Q=0;l=A<<16>>16>124?124:A;A=(l<<16>>16)*1311>>15;Q=(A|0)>32767;if(!Q){w=A<<16>>16;if((w*25|0)==1073741824){pA[t>>2]=1;w=1073741823}else o=4}else{pA[t>>2]=1;w=32767;o=4}if((o|0)==4)w=(w*50|0)>>>1;a=(l&65535)-w|0;w=(a<<16>>16)*6554>>15;s=(w|0)>32767;if(!s){l=w<<16>>16;if((l*5|0)==1073741824){pA[t>>2]=1;B=1073741823}else o=9}else{pA[t>>2]=1;l=32767;o=9}if((o|0)==9)B=(l*10|0)>>>1;a=a-B|0;o=a<<16>>16;l=e<<16>>16;B=l>>2;l=l-(B<<2)|0;jA[n+(f<<16>>16<<1)>>1]=((a<<17>>17|0)==(o|0)?a<<1:o>>>15^32767)+(l&1);if(s){pA[t>>2]=1;w=32767}f=w<<16>>16;jA[n+(i<<16>>16<<1)>>1]=((w<<17>>17|0)==(f|0)?w<<1:f>>>15^32767)+(l<<16>>17);if(Q){pA[t>>2]=1;A=32767}i=A<<16>>16;jA[n+(r<<16>>16<<1)>>1]=Ci(B&65535,((A<<17>>17|0)==(i|0)?A<<1:i>>>15^32767)&65535,t)|0;return}function PA(A){A=A|0;var e=0,f=0,i=0,r=0;if(!A){r=-1;return r|0}bi(A+1168|0);jA[A+460>>1]=40;pA[A+1164>>2]=0;e=A+646|0;f=A+1216|0;i=A+462|0;r=i+22|0;do{jA[i>>1]=0;i=i+2|0}while((i|0)<(r|0));dA(e,pA[f>>2]|0)|0;FA(A+686|0)|0;MA(A+700|0)|0;EA(A+608|0)|0;HA(A+626|0,pA[f>>2]|0)|0;CA(A+484|0)|0;UA(A+730|0)|0;IA(A+748|0)|0;hi(A+714|0)|0;kA(A,0)|0;r=0;return r|0}function kA(A,e){A=A|0;e=e|0;var f=0,i=0;if(!A){A=-1;return A|0}pA[A+388>>2]=A+308;gr(A|0,0,308)|0;e=(e|0)!=8;if(e){f=A+412|0;i=f+20|0;do{jA[f>>1]=0;f=f+2|0}while((f|0)<(i|0));jA[A+392>>1]=3e4;jA[A+394>>1]=26e3;jA[A+396>>1]=21e3;jA[A+398>>1]=15e3;jA[A+400>>1]=8e3;jA[A+402>>1]=0;jA[A+404>>1]=-8e3;jA[A+406>>1]=-15e3;jA[A+408>>1]=-21e3;jA[A+410>>1]=-26e3}jA[A+432>>1]=0;jA[A+434>>1]=40;pA[A+1164>>2]=0;jA[A+436>>1]=0;jA[A+438>>1]=0;jA[A+440>>1]=0;jA[A+460>>1]=40;jA[A+462>>1]=0;jA[A+464>>1]=0;if(e){f=A+442|0;i=f+18|0;do{jA[f>>1]=0;f=f+2|0}while((f|0)<(i|0));f=A+466|0;i=f+18|0;do{jA[f>>1]=0;f=f+2|0}while((f|0)<(i|0));EA(A+608|0)|0;i=A+1216|0;HA(A+626|0,pA[i>>2]|0)|0;dA(A+646|0,pA[i>>2]|0)|0;FA(A+686|0)|0;MA(A+700|0)|0;hi(A+714|0)|0}else{ZA()}CA(A+484|0)|0;jA[A+606>>1]=21845;UA(A+730|0)|0;if(!e){A=0;return A|0}IA(A+748|0)|0;A=0;return A|0}function bA(A,e,f,i,r,n){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;var t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0,T=0,y=0,Y=0,z=0,X=0,J=0,O=0,m=0,N=0,K=0,x=0,S=0,j=0,p=0,W=0,V=0,Z=0,_=0,q=0,$=0,AA=0,eA=0,fA=0,iA=0,rA=0,nA=0,tA=0,wA=0,lA=0,BA=0,aA=0,sA=0,oA=0,QA=0,gA=0,vA=0,uA=0,cA=0,CA=0,DA=0,EA=0,hA=0,PA=0,kA=0,bA=0,dA=0,IA=0,MA=0,FA=0,HA=0,UA=0,GA=0,LA=0,RA=0,TA=0,yA=0,YA=0,zA=0,XA=0,JA=0,OA=0,mA=0,NA=0,KA=0,xA=0;xA=WA;WA=WA+336|0;o=xA+236|0;s=xA+216|0;NA=xA+112|0;mA=xA+12|0;YA=xA+256|0;XA=xA+136|0;zA=xA+32|0;TA=xA+8|0;yA=xA+6|0;OA=xA+4|0;JA=xA+2|0;KA=xA;HA=A+1164|0;UA=A+748|0;GA=ge(UA,i,HA)|0;if(GA){ZA()}switch(i|0){case 1:{t=1;C=6;break}case 2:case 7:{ZA();break}case 3:{C=9;break}default:{t=0;C=6}}do{if((C|0)==6){i=A+440|0;if((jA[i>>1]|0)==6){ZA()}else{jA[i>>1]=0;MA=0;FA=0;break}}else if((C|0)==9){ZA()}}while(0);kA=A+1156|0;switch(pA[kA>>2]|0){case 1:{jA[i>>1]=5;jA[A+436>>1]=0;break}case 2:{jA[i>>1]=5;jA[A+436>>1]=1;break}default:{}}l=A+646|0;bA=A+666|0;w=NA;B=bA;a=w+20|0;do{SA[w>>0]=SA[B>>0]|0;w=w+1|0;B=B+1|0}while((w|0)<(a|0));dA=(e|0)!=7;IA=A+1168|0;if(dA){se(l,e,FA,f,IA,o,HA);w=A+392|0;Fi(w,o,n,HA);f=f+6|0}else{oe(l,FA,f,IA,s,o,HA);w=A+392|0;Ii(w,s,o,n,HA);f=f+10|0}B=o;a=w+20|0;do{jA[w>>1]=jA[B>>1]|0;w=w+2|0;B=B+2|0}while((w|0)<(a|0));PA=e>>>0>1;P=e>>>0<4&1;hA=(e|0)==5;EA=hA?10:5;hA=hA?19:9;d=A+434|0;I=143-hA&65535;M=A+460|0;F=A+462|0;H=A+464|0;k=e>>>0>2;U=A+388|0;G=(e|0)==0;L=e>>>0<2;R=A+1244|0;T=A+432|0;y=e>>>0<6;Y=A+1168|0;z=(e|0)==6;X=FA<<16>>16==0;J=A+714|0;O=A+686|0;m=A+436|0;N=A+700|0;K=(e|0)==7;x=A+482|0;S=e>>>0<3;j=A+608|0;p=A+626|0;W=A+438|0;V=e>>>0<7;Z=A+730|0;b=MA^1;_=t<<16>>16!=0;DA=_?FA^1:0;q=A+442|0;$=A+458|0;AA=A+412|0;eA=A+80|0;fA=A+1236|0;iA=A+1240|0;rA=A+468|0;nA=A+466|0;tA=A+470|0;wA=A+472|0;lA=A+474|0;BA=A+476|0;aA=A+478|0;sA=A+480|0;oA=A+444|0;QA=A+446|0;gA=A+448|0;vA=A+450|0;uA=A+452|0;cA=A+454|0;CA=A+456|0;D=0;E=0;Q=0;g=0;h=-1;while(1){h=(h<<16>>16)+1|0;a=h&65535;E=1-(E<<16>>16)|0;u=E&65535;s=PA&Q<<16>>16==80?0:Q;v=f+2|0;o=jA[f>>1]|0;A:do{if(dA){c=jA[d>>1]|0;w=(c&65535)-EA&65535;w=w<<16>>16<20?20:w;B=(w&65535)+hA&65535;l=B<<16>>16>143;we(o,l?I:w,l?143:B,s,c,TA,yA,P,HA);s=jA[TA>>1]|0;jA[M>>1]=s;if(MA){ZA()}else{o=s;s=jA[yA>>1]|0}Si(pA[U>>2]|0,o,s,40,1,HA);if(L){s=f+6|0;fe(a,jA[f+4>>1]|0,jA[v>>1]|0,pA[R>>2]|0,YA,HA);f=jA[T>>1]|0;c=f<<16>>16;o=c<<1;if((o|0)==(c<<17>>16|0)){B=G;break}B=G;o=f<<16>>16>0?32767:-32768;break}switch(e|0){case 2:{s=f+6|0;ee(jA[f+4>>1]|0,jA[v>>1]|0,YA);f=jA[T>>1]|0;c=f<<16>>16;o=c<<1;if((o|0)==(c<<17>>16|0)){B=G;break A}B=G;o=f<<16>>16>0?32767:-32768;break A}case 3:{s=f+6|0;ie(jA[f+4>>1]|0,jA[v>>1]|0,YA);f=jA[T>>1]|0;c=f<<16>>16;o=c<<1;if((o|0)==(c<<17>>16|0)){B=G;break A}B=G;o=f<<16>>16>0?32767:-32768;break A}default:{if(y){s=f+6|0;re(jA[f+4>>1]|0,jA[v>>1]|0,pA[Y>>2]|0,YA);f=jA[T>>1]|0;c=f<<16>>16;o=c<<1;if((o|0)==(c<<17>>16|0)){B=G;break A}B=G;o=f<<16>>16>0?32767:-32768;break A}if(!z){B=G;C=44;break A}ne(v,YA,HA);o=f+16|0;f=jA[T>>1]|0;c=f<<16>>16;a=c<<1;if((a|0)==(c<<17>>16|0)){s=o;B=G;o=a;break A}s=o;B=G;o=f<<16>>16>0?32767:-32768;break A}}}else{le(o,18,143,s,TA,yA,HA);if(X?s<<16>>16==0|o<<16>>16<61:0){o=jA[TA>>1]|0;s=jA[yA>>1]|0}else{jA[M>>1]=jA[TA>>1]|0;o=jA[d>>1]|0;jA[TA>>1]=o;jA[yA>>1]=0;s=0}Si(pA[U>>2]|0,o,s,40,0,HA);B=0;C=44}}while(0);if((C|0)==44){C=0;if(MA)ce(O,jA[i>>1]|0,OA,HA);else jA[OA>>1]=ae(e,jA[v>>1]|0,pA[iA>>2]|0)|0;Ce(O,FA,jA[m>>1]|0,OA,HA);Ae(f+4|0,YA,pA[Y>>2]|0);o=f+24|0;f=jA[OA>>1]|0;c=f<<16>>16;a=c<<1;if((a|0)==(c<<17>>16|0)){s=o;o=a}else{s=o;o=f<<16>>16>0?32767:-32768}}f=jA[TA>>1]|0;A:do{if(f<<16>>16<40){w=o<<16>>16;l=f;o=f<<16>>16;while(1){a=YA+(o<<1)|0;f=(VA(jA[YA+(o-(l<<16>>16)<<1)>>1]|0,w)|0)>>15;if((f|0)>32767){pA[HA>>2]=1;f=32767}c=f&65535;jA[KA>>1]=c;jA[a>>1]=Ci(jA[a>>1]|0,c,HA)|0;o=o+1|0;if((o&65535)<<16>>16==40)break A;l=jA[TA>>1]|0}}}while(0);A:do{if(B){B=(E&65535|0)==0;if(B){f=s;a=g}else{f=s+2|0;a=jA[s>>1]|0}if(X)te(J,e,a,YA,u,OA,JA,IA,HA);else{ZA()}Ce(O,FA,jA[m>>1]|0,OA,HA);ue(N,FA,jA[m>>1]|0,JA,HA);s=jA[OA>>1]|0;o=s<<16>>16>13017?13017:s;if(B)C=80;else c=a}else{f=s+2|0;o=jA[s>>1]|0;switch(e|0){case 1:case 2:case 3:case 4:case 6:{if(X)te(J,e,o,YA,u,OA,JA,IA,HA);else{ZA()}Ce(O,FA,jA[m>>1]|0,OA,HA);ue(N,FA,jA[m>>1]|0,JA,HA);s=jA[OA>>1]|0;o=s<<16>>16>13017?13017:s;if(!z){a=g;C=80;break A}if((jA[d>>1]|0)<=45){a=g;C=80;break A}a=g;o=o<<16>>16>>>2&65535;C=80;break A}case 5:{if(MA)ce(O,jA[i>>1]|0,OA,HA);else jA[OA>>1]=ae(5,o,pA[iA>>2]|0)|0;Ce(O,FA,jA[m>>1]|0,OA,HA);if(X)Be(J,5,jA[f>>1]|0,YA,pA[fA>>2]|0,JA,HA);else ve(N,J,jA[i>>1]|0,JA,HA);ue(N,FA,jA[m>>1]|0,JA,HA);o=jA[OA>>1]|0;f=s+4|0;s=o;a=g;o=o<<16>>16>13017?13017:o;C=80;break A}default:{if(X)Be(J,e,o,YA,pA[fA>>2]|0,JA,HA);else ve(N,J,jA[i>>1]|0,JA,HA);ue(N,FA,jA[m>>1]|0,JA,HA);o=jA[OA>>1]|0;s=o;a=g;C=80;break A}}}}while(0);if((C|0)==80){C=0;jA[T>>1]=s<<16>>16>13017?13017:s;c=a}o=o<<16>>16;o=(o<<17>>17|0)==(o|0)?o<<1:o>>>15^32767;u=(o&65535)<<16>>16>16384;A:do{if(u){v=o<<16>>16;if(K)s=0;else{s=0;while(1){o=(VA(jA[(pA[U>>2]|0)+(s<<1)>>1]|0,v)|0)>>15;if((o|0)>32767){pA[HA>>2]=1;o=32767}jA[KA>>1]=o;o=VA(jA[OA>>1]|0,o<<16>>16)|0;if((o|0)==1073741824){ZA()}else o=o<<1;jA[XA+(s<<1)>>1]=er(o,HA)|0;s=s+1|0;if((s|0)==40)break A}}do{o=(VA(jA[(pA[U>>2]|0)+(s<<1)>>1]|0,v)|0)>>15;if((o|0)>32767){pA[HA>>2]=1;o=32767}jA[KA>>1]=o;o=VA(jA[OA>>1]|0,o<<16>>16)|0;if((o|0)!=1073741824){o=o<<1;if((o|0)<0)o=~((o^-2)>>1);else C=88}else{ZA()}if((C|0)==88){C=0;o=o>>1}jA[XA+(s<<1)>>1]=er(o,HA)|0;s=s+1|0}while((s|0)!=40)}}while(0);if(X){jA[nA>>1]=jA[rA>>1]|0;jA[rA>>1]=jA[tA>>1]|0;jA[tA>>1]=jA[wA>>1]|0;jA[wA>>1]=jA[lA>>1]|0;jA[lA>>1]=jA[BA>>1]|0;jA[BA>>1]=jA[aA>>1]|0;jA[aA>>1]=jA[sA>>1]|0;jA[sA>>1]=jA[x>>1]|0;jA[x>>1]=jA[OA>>1]|0}if((MA|(jA[m>>1]|0)!=0?S&(jA[F>>1]|0)!=0:0)?(LA=jA[OA>>1]|0,LA<<16>>16>12288):0){ZA()}De(NA,bA,Q,mA,HA);o=$A(j,e,jA[JA>>1]|0,mA,p,FA,jA[m>>1]|0,t,jA[W>>1]|0,jA[F>>1]|0,jA[H>>1]|0,HA)|0;switch(e|0){case 0:case 1:case 2:case 3:case 6:{a=jA[OA>>1]|0;v=1;break}default:{o=jA[JA>>1]|0;a=jA[OA>>1]|0;if(V)v=1;else{s=a<<16>>16;if(a<<16>>16<0)s=~((s^-2)>>1);else s=s>>>1;a=s&65535;v=2}}}w=a<<16>>16;Q=v&65535;s=pA[U>>2]|0;g=0;do{s=s+(g<<1)|0;jA[zA+(g<<1)>>1]=jA[s>>1]|0;s=VA(jA[s>>1]|0,w)|0;if((s|0)==1073741824){pA[HA>>2]=1;l=2147483647}else l=s<<1;B=VA(jA[JA>>1]|0,jA[YA+(g<<1)>>1]|0)|0;if((B|0)!=1073741824){s=(B<<1)+l|0;if((B^l|0)>0&(s^l|0)<0){ZA()}}else{pA[HA>>2]=1;s=2147483647}C=s<<Q;C=er((C>>Q|0)==(s|0)?C:s>>31^2147483647,HA)|0;s=pA[U>>2]|0;jA[s+(g<<1)>>1]=C;g=g+1|0}while((g|0)!=40);Pe(Z);if((S?(jA[H>>1]|0)>3:0)?!((jA[F>>1]|0)==0|b):0)he(Z);ke(Z,e,zA,o,jA[OA>>1]|0,YA,a,v,IA,HA);o=0;B=0;do{s=jA[zA+(B<<1)>>1]|0;s=VA(s,s)|0;if((s|0)!=1073741824){a=(s<<1)+o|0;if((s^o|0)>0&(a^o|0)<0){pA[HA>>2]=1;o=(o>>>31)+2147483647|0}else o=a}else{pA[HA>>2]=1;o=2147483647}B=B+1|0}while((B|0)!=40);if((o|0)<0)o=~((o^-2)>>1);else o=o>>1;o=rr(o,KA,HA)|0;a=((jA[KA>>1]|0)>>>1)+15|0;s=a&65535;a=a<<16>>16;if(s<<16>>16>0)if(s<<16>>16<31){o=o>>a;C=135}else{o=0;C=137}else{ZA()}if((C|0)==135){C=0;if((o|0)<0)o=~((o^-4)>>2);else C=137}if((C|0)==137){C=0;o=o>>>2}o=o&65535;do{if(S?(RA=jA[H>>1]|0,RA<<16>>16>5):0)if(jA[F>>1]|0)if((jA[i>>1]|0)<4){if(_){if(!(MA|(jA[W>>1]|0)!=0))C=145}else if(!MA)C=145;if((C|0)==145?(0,(jA[m>>1]|0)==0):0){C=147;break}ZA()}else C=147;else C=151;else C=147}while(0);do{if((C|0)==147){C=0;if(jA[F>>1]|0){if(!MA?(jA[m>>1]|0)==0:0){C=151;break}if((jA[i>>1]|0)>=4)C=151}else C=151}}while(0);if((C|0)==151){C=0;jA[q>>1]=jA[oA>>1]|0;jA[oA>>1]=jA[QA>>1]|0;jA[QA>>1]=jA[gA>>1]|0;jA[gA>>1]=jA[vA>>1]|0;jA[vA>>1]=jA[uA>>1]|0;jA[uA>>1]=jA[cA>>1]|0;jA[cA>>1]=jA[CA>>1]|0;jA[CA>>1]=jA[$>>1]|0;jA[$>>1]=o}if(u){o=0;do{u=XA+(o<<1)|0;jA[u>>1]=Ci(jA[u>>1]|0,jA[zA+(o<<1)>>1]|0,HA)|0;o=o+1|0}while((o|0)!=40);_A(zA,XA,40,HA);pA[HA>>2]=0;tr(n,XA,r+(D<<1)|0,40,AA,0)}else{pA[HA>>2]=0;tr(n,zA,r+(D<<1)|0,40,AA,0)}if(!(pA[HA>>2]|0))Qr(AA|0,r+(D+30<<1)|0,20)|0;else{ZA()}Qr(A|0,eA|0,308)|0;jA[d>>1]=jA[TA>>1]|0;o=D+40|0;Q=o&65535;if(Q<<16>>16>=160)break;else{D=o<<16>>16;n=n+22|0;g=c}}jA[F>>1]=qA(A+484|0,A+466|0,r,H,HA)|0;Qe(UA,bA,r,HA);jA[m>>1]=FA;jA[W>>1]=t;Ee(A+626|0,bA,HA);KA=kA;pA[KA>>2]=GA;WA=xA;return}function te(A,e,f,i,r,n,t,w,l){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;l=l|0;var B=0,a=0,s=0,o=0,Q=0;Q=WA;WA=WA+16|0;s=Q+2|0;o=Q;f=f<<16>>16;f=(f<<18>>18|0)==(f|0)?f<<2:f>>>15^32767;switch(e|0){case 3:case 4:case 6:{a=f<<16>>16;f=pA[w+84>>2]|0;jA[n>>1]=jA[f+(a<<1)>>1]|0;w=jA[f+(a+1<<1)>>1]|0;B=jA[f+(a+3<<1)>>1]|0;n=jA[f+(a+2<<1)>>1]|0;break}case 0:{w=(f&65535)+(r<<16>>16<<1^2)|0;w=(w&65535)<<16>>16>1022?1022:w<<16>>16;jA[n>>1]=jA[782+(w<<1)>>1]|0;n=jA[782+(w+1<<1)>>1]|0;Gi(n<<16>>16,o,s,l);jA[o>>1]=(DA[o>>1]|0)+65524;w=ir(jA[s>>1]|0,5,l)|0;a=jA[o>>1]|0;a=Ci(w,((a<<26>>26|0)==(a|0)?a<<10:a>>>15^32767)&65535,l)|0;w=jA[s>>1]|0;f=jA[o>>1]|0;if((f*24660|0)==1073741824){pA[l>>2]=1;r=2147483647}else r=f*49320|0;B=(w<<16>>16)*24660>>15;f=r+(B<<1)|0;if((r^B|0)>0&(f^r|0)<0){pA[l>>2]=1;f=(r>>>31)+2147483647|0}B=f<<13;w=n;B=er((B>>13|0)==(f|0)?B:f>>31^2147483647,l)|0;n=a;break}default:{a=f<<16>>16;f=pA[w+80>>2]|0;jA[n>>1]=jA[f+(a<<1)>>1]|0;w=jA[f+(a+1<<1)>>1]|0;B=jA[f+(a+3<<1)>>1]|0;n=jA[f+(a+2<<1)>>1]|0}}Pi(A,e,i,o,s,0,0,l);r=VA((xi(14,jA[s>>1]|0,l)|0)<<16>>16,w<<16>>16)|0;if((r|0)==1073741824){pA[l>>2]=1;f=2147483647}else f=r<<1;w=10-(DA[o>>1]|0)|0;r=w&65535;w=w<<16>>16;if(r<<16>>16>0){o=r<<16>>16<31?f>>w:0;o=o>>>16;o=o&65535;jA[t>>1]=o;ki(A,n,B);WA=Q;return}else{l=0-w<<16>>16;o=f<<l;o=(o>>l|0)==(f|0)?o:f>>31^2147483647;o=o>>>16;o=o&65535;jA[t>>1]=o;ki(A,n,B);WA=Q;return}}function we(A,e,f,i,r,n,t,w,l){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;l=l|0;if(!(i<<16>>16)){w=A<<16>>16;if(A<<16>>16>=197){jA[n>>1]=w+65424;jA[t>>1]=0;return}r=((w<<16)+131072>>16)*10923>>15;if((r|0)>32767){pA[l>>2]=1;r=32767}A=(r&65535)+19|0;jA[n>>1]=A;jA[t>>1]=w+58-((A*196608|0)>>>16);return}if(!(w<<16>>16)){l=A<<16>>16<<16;A=((l+131072>>16)*21846|0)+-65536>>16;jA[n>>1]=A+(e&65535);jA[t>>1]=((l+-131072|0)>>>16)-((A*196608|0)>>>16);return}if((nr(r,e,l)|0)<<16>>16>5)r=(e&65535)+5&65535;w=f<<16>>16;w=(w-(r&65535)&65535)<<16>>16>4?w+65532&65535:r;r=A<<16>>16;if(A<<16>>16<4){jA[n>>1]=((((w&65535)<<16)+-327680|0)>>>16)+r;jA[t>>1]=0;return}r=r<<16;if(A<<16>>16<12){l=(((r+-327680>>16)*10923|0)>>>15<<16)+-65536|0;A=l>>16;jA[n>>1]=(w&65535)+A;jA[t>>1]=((r+-589824|0)>>>16)-(l>>>15)-A;return}else{jA[n>>1]=((r+-786432+((w&65535)<<16)|0)>>>16)+1;jA[t>>1]=0;return}}function le(A,e,f,i,r,n,t){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;if(i<<16>>16){t=(DA[r>>1]|0)+65531|0;t=(t<<16>>16|0)<(e<<16>>16|0)?e:t&65535;f=f<<16>>16;e=A<<16>>16<<16;A=((e+327680>>16)*10924|0)+-65536>>16;jA[r>>1]=(((((t&65535)<<16)+589824>>16|0)>(f|0)?f+65527&65535:t)&65535)+A;jA[n>>1]=((e+-196608|0)>>>16)-((A*393216|0)>>>16);return}i=A<<16>>16;if(A<<16>>16<463){A=((((i<<16)+327680>>16)*10924|0)>>>16)+17|0;jA[r>>1]=A;jA[n>>1]=i+105-((A*393216|0)>>>16);return}else{jA[r>>1]=i+65168;jA[n>>1]=0;return}}function Be(A,e,f,i,r,n,t){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;var w=0,l=0,B=0,a=0;a=WA;WA=WA+16|0;l=a+6|0;w=a+4|0;Pi(A,e,i,l,w,a+2|0,a,t);B=(f&31)*3|0;i=r+(B<<1)|0;if(!((nr(e&65535,7,t)|0)<<16>>16)){l=xi(jA[l>>1]|0,jA[w>>1]|0,t)|0;w=l<<16>>16;w=(VA(((l<<20>>20|0)==(w|0)?l<<4:w>>>15^32767)<<16>>16,jA[i>>1]|0)|0)>>15;if((w|0)>32767){pA[t>>2]=1;w=32767}i=w<<16;f=i>>16;if((w<<17>>17|0)==(f|0))w=i>>15;else w=f>>>15^32767}else{f=xi(14,jA[w>>1]|0,t)|0;f=VA(f<<16>>16,jA[i>>1]|0)|0;if((f|0)==1073741824){pA[t>>2]=1;i=2147483647}else i=f<<1;f=nr(9,jA[l>>1]|0,t)|0;w=f<<16>>16;if(f<<16>>16>0)w=f<<16>>16<31?i>>w:0;else{t=0-w<<16>>16;w=i<<t;w=(w>>t|0)==(i|0)?w:i>>31^2147483647}w=w>>>16}jA[n>>1]=w;ki(A,jA[r+(B+1<<1)>>1]|0,jA[r+(B+2<<1)>>1]|0);WA=a;return}function ae(A,e,f){A=A|0;e=e|0;f=f|0;e=jA[f+(e<<16>>16<<1)>>1]|0;if((A|0)!=7){A=e;return A|0}A=e&65532;return A|0}function se(A,e,f,i,r,n,t){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;var w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0;c=WA;WA=WA+48|0;Q=c+20|0;u=c;v=pA[r+44>>2]|0;g=pA[r+64>>2]|0;w=pA[r+4>>2]|0;o=pA[r+12>>2]|0;B=pA[r+20>>2]|0;l=pA[r+56>>2]|0;if(!(f<<16>>16)){a=e>>>0<2;if(a){f=765;s=508;B=pA[r+52>>2]|0}else{r=(e|0)==5;f=r?1533:765;s=2044;w=r?l:w}l=jA[i>>1]|0;f=((l*196608>>16|0)>(f&65535|0)?f:l*3&65535)<<16>>16;l=jA[w+(f<<1)>>1]|0;jA[Q>>1]=l;jA[Q+2>>1]=jA[w+(f+1<<1)>>1]|0;jA[Q+4>>1]=jA[w+(f+2<<1)>>1]|0;f=jA[i+2>>1]|0;if(a)f=f<<16>>16<<1&65535;a=(f<<16>>16)*196608|0;a=(a|0)>100466688?1533:a>>16;jA[Q+6>>1]=jA[o+(a<<1)>>1]|0;jA[Q+8>>1]=jA[o+(a+1<<1)>>1]|0;jA[Q+10>>1]=jA[o+(a+2<<1)>>1]|0;i=jA[i+4>>1]|0;i=((i<<18>>16|0)>(s&65535|0)?s:i<<2&65535)<<16>>16;jA[Q+12>>1]=jA[B+(i<<1)>>1]|0;jA[Q+14>>1]=jA[B+((i|1)<<1)>>1]|0;jA[Q+16>>1]=jA[B+((i|2)<<1)>>1]|0;jA[Q+18>>1]=jA[B+((i|3)<<1)>>1]|0;if((e|0)==8){ZA()}else w=0;do{l=A+(w<<1)|0;f=(VA(jA[g+(w<<1)>>1]|0,jA[l>>1]|0)|0)>>15;if((f|0)>32767){pA[t>>2]=1;f=32767}i=Ci(jA[v+(w<<1)>>1]|0,f&65535,t)|0;e=jA[Q+(w<<1)>>1]|0;jA[u+(w<<1)>>1]=Ci(e,i,t)|0;jA[l>>1]=e;w=w+1|0}while((w|0)!=10);$i(u,205,10,t);w=A+20|0;l=u;f=w+20|0;do{SA[w>>0]=SA[l>>0]|0;w=w+1|0;l=l+1|0}while((w|0)<(f|0));Oi(u,n,10,t);WA=c;return}else{ZA();return}}function oe(A,e,f,i,r,n,t){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;var w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0;c=WA;WA=WA+80|0;o=c+60|0;Q=c+40|0;v=c+20|0;u=c;g=pA[i+48>>2]|0;B=pA[i+24>>2]|0;a=pA[i+28>>2]|0;s=pA[i+32>>2]|0;if(e<<16>>16){ZA()}e=pA[i+16>>2]|0;i=pA[i+8>>2]|0;l=jA[f>>1]|0;l=((l<<18>>18|0)==(l|0)?l<<2:l>>>15^32767)<<16>>16;jA[o>>1]=jA[i+(l<<1)>>1]|0;jA[o+2>>1]=jA[i+(l+1<<1)>>1]|0;jA[Q>>1]=jA[i+(l+2<<1)>>1]|0;jA[Q+2>>1]=jA[i+(l+3<<1)>>1]|0;l=jA[f+2>>1]|0;l=((l<<18>>18|0)==(l|0)?l<<2:l>>>15^32767)<<16>>16;jA[o+4>>1]=jA[e+(l<<1)>>1]|0;jA[o+6>>1]=jA[e+(l+1<<1)>>1]|0;jA[Q+4>>1]=jA[e+(l+2<<1)>>1]|0;jA[Q+6>>1]=jA[e+(l+3<<1)>>1]|0;l=jA[f+4>>1]|0;i=l<<16>>16;if(l<<16>>16<0)e=~((i^-2)>>1);else e=i>>>1;l=e<<16>>16;l=((e<<18>>18|0)==(l|0)?e<<2:l>>>15^32767)<<16>>16;w=B+(l+1<<1)|0;e=jA[B+(l<<1)>>1]|0;if(!(i&1)){jA[o+8>>1]=e;jA[o+10>>1]=jA[w>>1]|0;jA[Q+8>>1]=jA[B+(l+2<<1)>>1]|0;jA[Q+10>>1]=jA[B+(l+3<<1)>>1]|0}else{if(e<<16>>16==-32768)e=32767;else e=0-(e&65535)&65535;jA[o+8>>1]=e;e=jA[w>>1]|0;if(e<<16>>16==-32768)e=32767;else e=0-(e&65535)&65535;jA[o+10>>1]=e;e=jA[B+(l+2<<1)>>1]|0;if(e<<16>>16==-32768)e=32767;else e=0-(e&65535)&65535;jA[Q+8>>1]=e;e=jA[B+(l+3<<1)>>1]|0;if(e<<16>>16==-32768)e=32767;else e=0-(e&65535)&65535;jA[Q+10>>1]=e}w=jA[f+6>>1]|0;w=((w<<18>>18|0)==(w|0)?w<<2:w>>>15^32767)<<16>>16;jA[o+12>>1]=jA[a+(w<<1)>>1]|0;jA[o+14>>1]=jA[a+(w+1<<1)>>1]|0;jA[Q+12>>1]=jA[a+(w+2<<1)>>1]|0;jA[Q+14>>1]=jA[a+(w+3<<1)>>1]|0;w=jA[f+8>>1]|0;w=((w<<18>>18|0)==(w|0)?w<<2:w>>>15^32767)<<16>>16;jA[o+16>>1]=jA[s+(w<<1)>>1]|0;jA[o+18>>1]=jA[s+(w+1<<1)>>1]|0;jA[Q+16>>1]=jA[s+(w+2<<1)>>1]|0;jA[Q+18>>1]=jA[s+(w+3<<1)>>1]|0;w=0;do{i=A+(w<<1)|0;e=(jA[i>>1]|0)*21299>>15;if((e|0)>32767){pA[t>>2]=1;e=32767}s=Ci(jA[g+(w<<1)>>1]|0,e&65535,t)|0;jA[v+(w<<1)>>1]=Ci(jA[o+(w<<1)>>1]|0,s,t)|0;f=jA[Q+(w<<1)>>1]|0;jA[u+(w<<1)>>1]=Ci(f,s,t)|0;jA[i>>1]=f;w=w+1|0}while((w|0)!=10);$i(v,205,10,t);$i(u,205,10,t);w=A+20|0;i=u;e=w+20|0;do{SA[w>>0]=SA[i>>0]|0;w=w+1|0;i=i+1|0}while((w|0)<(e|0));Oi(v,r,10,t);Oi(u,n,10,t);WA=c;return}function dA(A,e){A=A|0;e=e|0;var f=0,i=0;if(!A){i=-1;return i|0}f=A;i=f+20|0;do{jA[f>>1]=0;f=f+2|0}while((f|0)<(i|0));Qr(A+20|0,e|0,20)|0;i=0;return i|0}function IA(A){A=A|0;var e=0,f=0,i=0,r=0,n=0;if(!A){n=-1;return n|0}jA[A>>1]=0;jA[A+2>>1]=8192;e=A+4|0;jA[e>>1]=3500;jA[A+6>>1]=3500;pA[A+8>>2]=1887529304;jA[A+12>>1]=3e4;jA[A+14>>1]=26e3;jA[A+16>>1]=21e3;jA[A+18>>1]=15e3;jA[A+20>>1]=8e3;jA[A+22>>1]=0;jA[A+24>>1]=-8e3;jA[A+26>>1]=-15e3;jA[A+28>>1]=-21e3;jA[A+30>>1]=-26e3;jA[A+32>>1]=3e4;jA[A+34>>1]=26e3;jA[A+36>>1]=21e3;jA[A+38>>1]=15e3;jA[A+40>>1]=8e3;jA[A+42>>1]=0;jA[A+44>>1]=-8e3;jA[A+46>>1]=-15e3;jA[A+48>>1]=-21e3;jA[A+50>>1]=-26e3;jA[A+212>>1]=0;jA[A+374>>1]=0;jA[A+392>>1]=0;f=A+52|0;jA[f>>1]=1384;jA[A+54>>1]=2077;jA[A+56>>1]=3420;jA[A+58>>1]=5108;jA[A+60>>1]=6742;jA[A+62>>1]=8122;jA[A+64>>1]=9863;jA[A+66>>1]=11092;jA[A+68>>1]=12714;jA[A+70>>1]=13701;i=A+72|0;r=f;n=i+20|0;do{SA[i>>0]=SA[r>>0]|0;i=i+1|0;r=r+1|0}while((i|0)<(n|0));i=A+92|0;r=f;n=i+20|0;do{SA[i>>0]=SA[r>>0]|0;i=i+1|0;r=r+1|0}while((i|0)<(n|0));i=A+112|0;r=f;n=i+20|0;do{SA[i>>0]=SA[r>>0]|0;i=i+1|0;r=r+1|0}while((i|0)<(n|0));i=A+132|0;r=f;n=i+20|0;do{SA[i>>0]=SA[r>>0]|0;i=i+1|0;r=r+1|0}while((i|0)<(n|0));i=A+152|0;r=f;n=i+20|0;do{SA[i>>0]=SA[r>>0]|0;i=i+1|0;r=r+1|0}while((i|0)<(n|0));i=A+172|0;r=f;n=i+20|0;do{SA[i>>0]=SA[r>>0]|0;i=i+1|0;r=r+1|0}while((i|0)<(n|0));i=A+192|0;r=f;n=i+20|0;do{SA[i>>0]=SA[r>>0]|0;i=i+1|0;r=r+1|0}while((i|0)<(n|0));gr(A+214|0,0,160)|0;jA[A+376>>1]=3500;jA[A+378>>1]=3500;n=jA[e>>1]|0;jA[A+380>>1]=n;jA[A+382>>1]=n;jA[A+384>>1]=n;jA[A+386>>1]=n;jA[A+388>>1]=n;jA[A+390>>1]=n;jA[A+394>>1]=0;jA[A+396>>1]=7;jA[A+398>>1]=32767;jA[A+400>>1]=0;jA[A+402>>1]=0;jA[A+404>>1]=0;pA[A+408>>2]=1;jA[A+412>>1]=0;n=0;return n|0}function Qe(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var r=0,n=0,t=0,w=0,l=0,B=0;l=WA;WA=WA+16|0;t=l+2|0;w=l;jA[w>>1]=0;n=A+212|0;r=(DA[n>>1]|0)+10|0;r=(r&65535|0)==80?0:r&65535;jA[n>>1]=r;Qr(A+52+(r<<16>>16<<1)|0,e|0,20)|0;r=0;n=159;while(1){B=jA[f+(n<<1)>>1]|0;B=VA(B,B)|0;B=(B|0)==1073741824?2147483647:B<<1;e=B+r|0;if((B^r|0)>-1&(e^r|0)<0){pA[i>>2]=1;r=(r>>>31)+2147483647|0}else r=e;if((n|0)>0)n=n+-1|0;else break}Gi(r,t,w,i);r=jA[t>>1]|0;B=r<<16>>16;e=B<<10;if((e|0)!=(B<<26>>16|0)){pA[i>>2]=1;e=r<<16>>16>0?32767:-32768}jA[t>>1]=e;B=jA[w>>1]|0;r=B<<16>>16;if(B<<16>>16<0)r=~((r^-32)>>5);else r=r>>>5;w=A+392|0;B=(DA[w>>1]|0)+1|0;B=(B&65535|0)==8?0:B&65535;jA[w>>1]=B;jA[A+376+(B<<16>>16<<1)>>1]=r+57015+e;WA=l;return}function ge(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,r=0,n=0,t=0,w=0,l=0,B=0,a=0;l=(e|0)==4;B=(e|0)==5;a=(e|0)==6;i=pA[A+408>>2]|0;A:do{if((e+-4|0)>>>0<3)w=4;else{if((i+-1|0)>>>0<2)switch(e|0){case 2:case 3:case 7:{w=4;break A}default:{}}jA[A>>1]=0;t=0}}while(0);if((w|0)==4){ZA()}n=A+398|0;if(B&(jA[A+412>>1]|0)==0){jA[n>>1]=0;r=0}else r=jA[n>>1]|0;r=Ci(r,1,f)|0;jA[n>>1]=r;f=A+404|0;jA[f>>1]=0;A:do{switch(e|0){case 2:case 4:case 5:case 6:case 7:{if(!((e|0)==7&(t|0)==0)){ZA()}else w=14;break}default:w=14}}while(0);if((w|0)==14)jA[A+396>>1]=7;if(!t)return t|0;r=A+400|0;jA[r>>1]=0;i=A+402|0;jA[i>>1]=0;if(l){jA[r>>1]=1;return t|0}if(B){jA[r>>1]=1;jA[i>>1]=1;return t|0}if(!a)return t|0;jA[r>>1]=1;jA[f>>1]=0;return t|0}function MA(A){A=A|0;if(!A){A=-1;return A|0}jA[A>>1]=1;jA[A+2>>1]=1;jA[A+4>>1]=1;jA[A+6>>1]=1;jA[A+8>>1]=1;jA[A+10>>1]=0;jA[A+12>>1]=1;A=0;return A|0}function ve(A,e,f,i,r){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;ZA();return}function ue(A,e,f,i,r){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;if(!(e<<16>>16)){if(f<<16>>16){ZA()}else e=A+12|0;jA[e>>1]=jA[i>>1]|0}jA[A+10>>1]=jA[i>>1]|0;r=A+2|0;jA[A>>1]=jA[r>>1]|0;f=A+4|0;jA[r>>1]=jA[f>>1]|0;r=A+6|0;jA[f>>1]=jA[r>>1]|0;A=A+8|0;jA[r>>1]=jA[A>>1]|0;jA[A>>1]=jA[i>>1]|0;return}function ce(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;ZA();return}function FA(A){A=A|0;if(!A){A=-1;return A|0}jA[A>>1]=1640;jA[A+2>>1]=1640;jA[A+4>>1]=1640;jA[A+6>>1]=1640;jA[A+8>>1]=1640;jA[A+10>>1]=0;jA[A+12>>1]=16384;A=0;return A|0}function Ce(A,e,f,i,r){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;if(!(e<<16>>16)){if(f<<16>>16){ZA()}else e=A+12|0;jA[e>>1]=jA[i>>1]|0}i=jA[i>>1]|0;e=A+10|0;jA[e>>1]=i;if((nr(i,16384,r)|0)<<16>>16>0){jA[e>>1]=16384;e=16384}else e=jA[e>>1]|0;r=A+2|0;jA[A>>1]=jA[r>>1]|0;i=A+4|0;jA[r>>1]=jA[i>>1]|0;r=A+6|0;jA[i>>1]=jA[r>>1]|0;A=A+8|0;jA[r>>1]=jA[A>>1]|0;jA[A>>1]=e;return}function De(A,e,f,i,r){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;var n=0,t=0,w=0,l=0;switch(f<<16>>16){case 0:{l=9;while(1){w=jA[A+(l<<1)>>1]|0;f=w<<16>>16;if(w<<16>>16<0)f=~((f^-4)>>2);else f=f>>>2;t=jA[e+(l<<1)>>1]|0;n=t<<16>>16;if(t<<16>>16<0)t=~((n^-4)>>2);else t=n>>>2;jA[i+(l<<1)>>1]=Ci((w&65535)-f&65535,t&65535,r)|0;if((l|0)>0)l=l+-1|0;else break}return}case 40:{t=9;while(1){r=jA[A+(t<<1)>>1]|0;f=r<<16>>16;if(r<<16>>16<0)n=~((f^-2)>>1);else n=f>>>1;r=jA[e+(t<<1)>>1]|0;f=r<<16>>16;if(r<<16>>16<0)f=~((f^-2)>>1);else f=f>>>1;jA[i+(t<<1)>>1]=f+n;if((t|0)>0)t=t+-1|0;else break}return}case 80:{l=9;while(1){w=jA[A+(l<<1)>>1]|0;f=w<<16>>16;if(w<<16>>16<0)w=~((f^-4)>>2);else w=f>>>2;f=jA[e+(l<<1)>>1]|0;n=f<<16>>16;if(f<<16>>16<0)t=~((n^-4)>>2);else t=n>>>2;jA[i+(l<<1)>>1]=Ci(w&65535,(f&65535)-t&65535,r)|0;if((l|0)>0)l=l+-1|0;else break}return}case 120:{jA[i+18>>1]=jA[e+18>>1]|0;jA[i+16>>1]=jA[e+16>>1]|0;jA[i+14>>1]=jA[e+14>>1]|0;jA[i+12>>1]=jA[e+12>>1]|0;jA[i+10>>1]=jA[e+10>>1]|0;jA[i+8>>1]=jA[e+8>>1]|0;jA[i+6>>1]=jA[e+6>>1]|0;jA[i+4>>1]=jA[e+4>>1]|0;jA[i+2>>1]=jA[e+2>>1]|0;jA[i>>1]=jA[e>>1]|0;return}default:return}}function HA(A,e){A=A|0;e=e|0;if(!A){A=-1;return A|0}Qr(A|0,e|0,20)|0;A=0;return A|0}function Ee(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,r=0,n=0,t=0,w=0,l=0,B=0;B=0;do{l=A+(B<<1)|0;i=jA[l>>1]|0;t=i&65535;w=t<<16;i=i<<16>>16;if((i*5243|0)==1073741824){pA[f>>2]=1;n=2147483647}else n=i*10486|0;r=w-n|0;if(((r^w)&(n^w)|0)<0){pA[f>>2]=1;n=(t>>>15)+2147483647|0}else n=r;i=jA[e+(B<<1)>>1]|0;r=i*5243|0;if((r|0)!=1073741824){i=(i*10486|0)+n|0;if((r^n|0)>0&(i^n|0)<0){pA[f>>2]=1;i=(n>>>31)+2147483647|0}}else{pA[f>>2]=1;i=2147483647}jA[l>>1]=er(i,f)|0;B=B+1|0}while((B|0)!=10);return}function UA(A){A=A|0;var e=0;if(!A){e=-1;return e|0}e=A+18|0;do{jA[A>>1]=0;A=A+2|0}while((A|0)<(e|0));e=0;return e|0}function he(A){A=A|0;jA[A+14>>1]=1;return}function Pe(A){A=A|0;jA[A+14>>1]=0;return}function ke(A,e,f,i,r,n,t,w,l,B){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;l=l|0;B=B|0;var a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0;d=WA;WA=WA+160|0;P=d+80|0;k=d;C=pA[l+120>>2]|0;D=pA[l+124>>2]|0;E=pA[l+128>>2]|0;c=pA[l+132>>2]|0;s=A+6|0;u=A+8|0;jA[u>>1]=jA[s>>1]|0;g=A+4|0;jA[s>>1]=jA[g>>1]|0;v=A+2|0;jA[g>>1]=jA[v>>1]|0;jA[v>>1]=jA[A>>1]|0;jA[A>>1]=r;l=r<<16>>16<14746?r<<16>>16>9830&1:2;a=A+12|0;r=jA[a>>1]|0;o=r<<15;do{if((o|0)<=536870911)if((o|0)<-536870912){pA[B>>2]=1;r=-2147483648;break}else{r=r<<17;break}else{pA[B>>2]=1;r=2147483647}}while(0);h=i<<16>>16;Q=A+16|0;if((er(r,B)|0)<<16>>16>=i<<16>>16){o=jA[Q>>1]|0;if(o<<16>>16>0){o=(o&65535)+65535&65535;jA[Q>>1]=o}if(!(o<<16>>16)){r=(jA[A>>1]|0)<9830;r=(jA[v>>1]|0)<9830?r?2:1:r&1;if((jA[g>>1]|0)<9830)r=(r&65535)+1&65535;if((jA[s>>1]|0)<9830)r=(r&65535)+1&65535;if((jA[u>>1]|0)<9830)r=(r&65535)+1&65535;o=0;l=r<<16>>16>2?0:l}}else{jA[Q>>1]=2;o=2}v=l<<16>>16;u=A+10|0;v=(o<<16>>16==0?(v|0)>((jA[u>>1]|0)+1|0):0)?v+65535&65535:l;A=(jA[A+14>>1]|0)==1?0:i<<16>>16<10?2:v<<16>>16<2&o<<16>>16>0?(v&65535)+1&65535:v;jA[u>>1]=A;jA[a>>1]=i;switch(e|0){case 4:case 6:case 7:break;default:if(A<<16>>16<2){o=0;l=0;s=n;a=P;while(1){if(!(jA[s>>1]|0))r=0;else{l=l<<16>>16;jA[k+(l<<1)>>1]=o;r=jA[s>>1]|0;l=l+1&65535}jA[a>>1]=r;jA[s>>1]=0;o=o+1<<16>>16;if(o<<16>>16>=40){u=l;break}else{s=s+2|0;a=a+2|0}}v=A<<16>>16==0;v=(e|0)==5?v?C:D:v?E:c;if(u<<16>>16>0){g=0;do{Q=jA[k+(g<<1)>>1]|0;l=Q<<16>>16;A=jA[P+(l<<1)>>1]|0;if(Q<<16>>16<40){o=A<<16>>16;s=39-Q&65535;a=Q;l=n+(l<<1)|0;r=v;while(1){e=(VA(jA[r>>1]|0,o)|0)>>>15&65535;jA[l>>1]=Ci(jA[l>>1]|0,e,B)|0;a=a+1<<16>>16;if(a<<16>>16>=40)break;else{l=l+2|0;r=r+2|0}}if(Q<<16>>16>0){l=v+(s+1<<1)|0;b=36}}else{l=v;b=36}if((b|0)==36){b=0;r=A<<16>>16;o=0;s=n;while(1){e=(VA(jA[l>>1]|0,r)|0)>>>15&65535;jA[s>>1]=Ci(jA[s>>1]|0,e,B)|0;o=o+1<<16>>16;if(o<<16>>16>=Q<<16>>16)break;else{s=s+2|0;l=l+2|0}}}g=g+1|0}while((g&65535)<<16>>16!=u<<16>>16)}}}g=t<<16>>16;v=h<<1;r=w<<16>>16;a=0-r<<16;l=a>>16;if(w<<16>>16>0){o=0;s=f;while(1){A=VA(jA[f+(o<<1)>>1]|0,g)|0;if((A|0)==1073741824){pA[B>>2]=1;a=2147483647}else a=A<<1;w=VA(v,jA[n>>1]|0)|0;A=w+a|0;if((w^a|0)>-1&(A^a|0)<0){pA[B>>2]=1;A=(a>>>31)+2147483647|0}w=A<<r;jA[s>>1]=er((w>>r|0)==(A|0)?w:A>>31^2147483647,B)|0;o=o+1|0;if((o|0)==40)break;else{n=n+2|0;s=s+2|0}}WA=d;return}ZA();return}function GA(A){A=A|0;if(!A){A=-1;return A|0}jA[A>>1]=0;jA[A+2>>1]=0;jA[A+4>>1]=0;jA[A+6>>1]=0;jA[A+8>>1]=0;jA[A+10>>1]=0;A=0;return A|0}function LA(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var r=0,n=0,t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0;if(f<<16>>16<=0)return;r=A+10|0;l=A+8|0;a=A+4|0;s=A+6|0;o=A+2|0;n=jA[a>>1]|0;t=jA[s>>1]|0;w=jA[A>>1]|0;B=jA[o>>1]|0;Q=0;while(1){g=jA[r>>1]|0;v=jA[l>>1]|0;jA[r>>1]=v;u=jA[e>>1]|0;jA[l>>1]=u;g=((u<<16>>16)*7699|0)+((VA(w<<16>>16,-7667)|0)+(((n<<16>>16)*15836|0)+((t<<16>>16)*15836>>15))+((VA(B<<16>>16,-7667)|0)>>15))+(VA(v<<16>>16,-15398)|0)+((g<<16>>16)*7699|0)|0;v=g<<3;g=(v>>3|0)==(g|0)?v:g>>31^2147483647;v=g<<1;jA[e>>1]=er((v>>1|0)==(g|0)?v:g>>31^2147483647,i)|0;w=jA[a>>1]|0;jA[A>>1]=w;B=jA[s>>1]|0;jA[o>>1]=B;n=g>>>16&65535;jA[a>>1]=n;t=(g>>>1)-(g>>16<<15)&65535;jA[s>>1]=t;Q=Q+1<<16>>16;if(Q<<16>>16>=f<<16>>16)break;else e=e+2|0}return}function RA(A){A=A|0;if(!A)A=-1;else{jA[A>>1]=0;A=0}return A|0}function TA(A,e,f,i,r){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;var n=0,t=0,w=0,l=0;w=i<<16>>16;n=e+(w+-1<<1)|0;w=w+-2|0;l=jA[n>>1]|0;if(i<<16>>16<2)i=f<<16>>16;else{i=f<<16>>16;t=0;e=e+(w<<1)|0;while(1){f=(VA(jA[e>>1]|0,i)|0)>>15;if((f|0)>32767){pA[r>>2]=1;f=32767}jA[n>>1]=nr(jA[n>>1]|0,f&65535,r)|0;n=n+-2|0;t=t+1<<16>>16;if((t<<16>>16|0)>(w|0))break;else e=e+-2|0}}i=(VA(jA[A>>1]|0,i)|0)>>15;if((i|0)<=32767){w=i;w=w&65535;t=jA[n>>1]|0;r=nr(t,w,r)|0;jA[n>>1]=r;jA[A>>1]=l;return}pA[r>>2]=1;w=32767;w=w&65535;t=jA[n>>1]|0;r=nr(t,w,r)|0;jA[n>>1]=r;jA[A>>1]=l;return}function yA(A){A=A|0;var e=0,f=0,i=0;if(!A){i=-1;return i|0}gr(A+104|0,0,340)|0;e=A+102|0;f=A;i=f+100|0;do{jA[f>>1]=0;f=f+2|0}while((f|0)<(i|0));vA(e)|0;RA(A+100|0)|0;i=0;return i|0}function YA(A,e,f,i,r){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;var n=0,t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0;C=WA;WA=WA+96|0;g=C+22|0;v=C;u=C+44|0;Qr(A+124|0,f|0,320)|0;a=u+22|0;s=A+100|0;o=A+80|0;Q=A+102|0;if((e&-2|0)==6){B=0;while(1){wr(i,702,g);wr(i,722,v);l=A+104+(B+10<<1)|0;Ar(g,l,A,40);t=u;n=g;e=t+22|0;do{jA[t>>1]=jA[n>>1]|0;t=t+2|0;n=n+2|0}while((t|0)<(e|0));t=a;e=t+22|0;do{jA[t>>1]=0;t=t+2|0}while((t|0)<(e|0));tr(v,u,u,22,a,0);e=0;t=21;do{n=jA[u+(t<<16>>16<<1)>>1]|0;n=VA(n,n)|0;if((n|0)==1073741824){c=7;break}w=n<<1;n=w+e|0;if((w^e|0)>-1&(n^e|0)<0){ZA()}else e=n;t=t+-1<<16>>16}while(t<<16>>16>-1);if((c|0)==7){c=0;pA[r>>2]=1}w=e>>>16&65535;n=20;e=0;t=20;while(1){n=VA(jA[u+(n+1<<1)>>1]|0,jA[u+(n<<1)>>1]|0)|0;if((n|0)==1073741824){c=13;break}D=n<<1;n=D+e|0;if((D^e|0)>-1&(n^e|0)<0){ZA()}else e=n;n=(t&65535)+-1<<16>>16;if(n<<16>>16>-1){n=n<<16>>16;t=t+-1|0}else break}if((c|0)==13){c=0;pA[r>>2]=1}e=e>>16;if((e|0)<1)e=0;else e=Ei((e*26214|0)>>>15&65535,w)|0;TA(s,A,e,40,r);e=f+(B<<1)|0;tr(v,A,e,40,o,1);uA(Q,l,e,29491,40,r);e=(B<<16)+2621440|0;if((e|0)<10485760){B=e>>16;i=i+22|0}else break}t=A+104|0;n=A+424|0;e=t+20|0;do{SA[t>>0]=SA[n>>0]|0;t=t+1|0;n=n+1|0}while((t|0)<(e|0));WA=C;return}else{B=0;while(1){wr(i,742,g);wr(i,762,v);l=A+104+(B+10<<1)|0;Ar(g,l,A,40);t=u;n=g;e=t+22|0;do{jA[t>>1]=jA[n>>1]|0;t=t+2|0;n=n+2|0}while((t|0)<(e|0));t=a;e=t+22|0;do{jA[t>>1]=0;t=t+2|0}while((t|0)<(e|0));tr(v,u,u,22,a,0);e=0;t=21;do{n=jA[u+(t<<16>>16<<1)>>1]|0;n=VA(n,n)|0;if((n|0)==1073741824){c=22;break}D=n<<1;n=D+e|0;if((D^e|0)>-1&(n^e|0)<0){ZA()}else e=n;t=t+-1<<16>>16}while(t<<16>>16>-1);if((c|0)==22){c=0;pA[r>>2]=1}w=e>>>16&65535;n=20;e=0;t=20;while(1){n=VA(jA[u+(n+1<<1)>>1]|0,jA[u+(n<<1)>>1]|0)|0;if((n|0)==1073741824){c=28;break}D=n<<1;n=D+e|0;if((D^e|0)>-1&(n^e|0)<0){ZA()}else e=n;n=(t&65535)+-1<<16>>16;if(n<<16>>16>-1){n=n<<16>>16;t=t+-1|0}else break}if((c|0)==28){c=0;pA[r>>2]=1}e=e>>16;if((e|0)<1)e=0;else e=Ei((e*26214|0)>>>15&65535,w)|0;TA(s,A,e,40,r);e=f+(B<<1)|0;tr(v,A,e,40,o,1);uA(Q,l,e,29491,40,r);e=(B<<16)+2621440|0;if((e|0)<10485760){B=e>>16;i=i+22|0}else break}t=A+104|0;n=A+424|0;e=t+20|0;do{SA[t>>0]=SA[n>>0]|0;t=t+1|0;n=n+1|0}while((t|0)<(e|0));WA=C;return}}function zA(A,e){A=A|0;e=e|0;var f=0,i=0;if(!A){A=-1;return A|0}pA[A>>2]=0;f=lr(1764)|0;if(!f){A=-1;return A|0}if((PA(f)|0)<<16>>16==0?(i=f+1748|0,(GA(i)|0)<<16>>16==0):0){kA(f,0)|0;yA(f+1304|0)|0;GA(i)|0;pA[f+1760>>2]=0;pA[A>>2]=f;A=0;return A|0}e=pA[f>>2]|0;if(!e){A=-1;return A|0}Br(e);pA[f>>2]=0;A=-1;return A|0}function XA(A){A=A|0;var e=0;if(!A)return;e=pA[A>>2]|0;if(!e)return;Br(e);pA[A>>2]=0;return}function JA(A,e,f,i,r){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;var n=0,t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0;c=WA;WA=WA+208|0;u=c+88|0;v=c;g=A+1164|0;n=pA[A+1256>>2]|0;if((i+-5|0)>>>0<2){ZA()}else{o=n+(e<<1)|0;if((jA[o>>1]|0)>0){Q=pA[(pA[A+1260>>2]|0)+(e<<2)>>2]|0;a=0;n=0;while(1){s=Q+(a<<1)|0;l=jA[s>>1]|0;if(l<<16>>16>0){w=f;B=0;t=0;while(1){t=DA[w>>1]|t<<1&131070;B=B+1<<16>>16;if(B<<16>>16>=l<<16>>16)break;else w=w+2|0}t=t&65535}else t=0;jA[u+(a<<1)>>1]=t;n=n+1<<16>>16;if(n<<16>>16<(jA[o>>1]|0)){f=f+(jA[s>>1]<<1)|0;a=n<<16>>16}else break}}}bA(A,e,u,i,r,v);YA(A+1304|0,e,r,v,g);LA(A+1748|0,r,160,g);n=0;do{A=r+(n<<1)|0;jA[A>>1]=DA[A>>1]&65528;n=n+1|0}while((n|0)!=160);WA=c;return}function OA(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var r=0,n=0,t=0;n=pA[i+100>>2]|0;t=(DA[(pA[i+96>>2]|0)+(A<<1)>>1]|0)+65535|0;i=t&65535;r=i<<16>>16>-1;if(A>>>0<8){if(!r)return;n=pA[n+(A<<2)>>2]|0;r=t<<16>>16;while(1){jA[f+(jA[n+(r<<1)>>1]<<1)>>1]=(w[e+(r>>3)>>0]|0)>>>(r&7^7)&1;i=i+-1<<16>>16;if(i<<16>>16>-1)r=i<<16>>16;else break}return}else{ZA()}}function mA(A,e,f){A=A|0;e=e|0;f=f|0;A=ri(A,f,31764)|0;return((ei(e)|0|A)<<16>>16!=0)<<31>>31|0}function NA(A,e){A=A|0;e=e|0;ni(A);fi(e);return}function KA(A,e,f,i,r,n,t){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;var w=0,l=0,B=0,a=0,s=0;s=WA;WA=WA+512|0;w=s+8|0;l=s+4|0;B=s;pA[B>>2]=0;a=t<<16>>16==3;if(!((t&65535)<2|a&1)){ZA()}ti(A,f,i,w,B);ii(e,pA[B>>2]|0,l);i=pA[l>>2]|0;if((i|0)!=3){e=pA[B>>2]|0;pA[n>>2]=e;if((e|0)==8){ZA()}}else{pA[n>>2]=15;e=15}if(a){ff(e,w,r,(pA[A+4>>2]|0)+2392|0);r=jA[3404+(pA[n>>2]<<16>>16<<1)>>1]|0;WA=s;return r|0}switch(t<<16>>16){case 0:{ZA()}case 1:{ZA()}default:{r=-1;WA=s;return r|0}}return 0}function xA(A,e,f,i,r,n){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;var t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0;P=WA;WA=WA+480|0;h=P;n=240;B=r;l=A;w=h;t=0;while(1){E=((VA(jA[B>>1]|0,jA[l>>1]|0)|0)+16384|0)>>>15;jA[w>>1]=E;E=E<<16;t=(VA(E>>15,E>>16)|0)+t|0;if((t|0)<0){a=4;break}n=n+-1|0;if(!((n&65535)<<16>>16)){n=0;break}else{B=B+2|0;l=l+2|0;w=w+2|0}}if((a|0)==4){t=n&65535;w=240-n|0;if(!(t<<16>>16))n=0;else{B=t;l=r+(w<<1)|0;n=A+(w<<1)|0;t=h+(w<<1)|0;while(1){jA[t>>1]=((VA(jA[l>>1]|0,jA[n>>1]|0)|0)+16384|0)>>>15;B=B+-1<<16>>16;if(!(B<<16>>16)){n=0;break}else{l=l+2|0;n=n+2|0;t=t+2|0}}}do{l=n&65535;n=120;w=h;t=0;while(1){E=(jA[w>>1]|0)>>>2;C=w+2|0;jA[w>>1]=E;E=E<<16>>16;E=VA(E,E)|0;D=(jA[C>>1]|0)>>>2;jA[C>>1]=D;D=D<<16>>16;t=((VA(D,D)|0)+E<<1)+t|0;n=n+-1<<16>>16;if(!(n<<16>>16))break;else w=w+4|0}n=l+4|0}while((t|0)<1)}E=t+1|0;D=(Ni(E)|0)<<16>>16;E=E<<D;jA[f>>1]=E>>>16;jA[i>>1]=(E>>>1)-(E>>16<<15);E=h+478|0;B=e<<16>>16;if(e<<16>>16<=0){ZA()}v=h+476|0;u=D+1|0;c=239-B|0;C=h+(236-B<<1)|0;e=B;f=f+(B<<1)|0;i=i+(B<<1)|0;while(1){a=VA((c>>>1)+65535&65535,-2)|0;l=h+(a+236<<1)|0;a=C+(a<<1)|0;r=240-e|0;g=r+-1|0;w=h+(g<<1)|0;A=g>>>1&65535;r=h+(r+-2<<1)|0;B=VA(jA[E>>1]|0,jA[w>>1]|0)|0;if(!(A<<16>>16)){a=r;l=v}else{Q=v;o=E;while(1){t=w+-4|0;s=o+-4|0;B=(VA(jA[Q>>1]|0,jA[r>>1]|0)|0)+B|0;A=A+-1<<16>>16;B=(VA(jA[s>>1]|0,jA[t>>1]|0)|0)+B|0;if(!(A<<16>>16))break;else{r=w+-6|0;Q=o+-6|0;w=t;o=s}}}if(g&1)B=(VA(jA[l>>1]|0,jA[a>>1]|0)|0)+B|0;g=B<<u;jA[f>>1]=g>>>16;jA[i>>1]=(g>>>1)-(g>>16<<15);if((e&65535)+-1<<16>>16<<16>>16>0){c=c+1|0;C=C+2|0;e=e+-1|0;f=f+-2|0;i=i+-2|0}else break}h=D-n|0;h=h&65535;WA=P;return h|0}function be(A,e,f,i,r,n,t,w){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;var l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0;M=WA;WA=WA+3440|0;I=M+3420|0;P=M+3400|0;k=M+3224|0;d=M;E=M+3320|0;b=M+3240|0;h=M+24|0;We(f,A,E,2,w);Ai(E,e,b,k,5,P,5,w);je(f,b,h,w);qf(10,5,5,E,h,P,k,d,w);e=i;w=e+80|0;do{jA[e>>1]=0;e=e+2|0}while((e|0)<(w|0));jA[n>>1]=65535;jA[n+2>>1]=65535;jA[n+4>>1]=65535;jA[n+6>>1]=65535;jA[n+8>>1]=65535;o=0;Q=d;g=I;do{A=jA[Q>>1]|0;Q=Q+2|0;l=(A*6554|0)>>>15;B=l<<16>>16;e=i+(A<<1)|0;w=jA[e>>1]|0;if((jA[b+(A<<1)>>1]|0)>0){jA[e>>1]=w+4096;jA[g>>1]=8192;a=l}else{jA[e>>1]=w+61440;jA[g>>1]=-8192;a=B+8|0}g=g+2|0;s=a&65535;e=A-(l<<2)-B<<16>>16;l=n+(e<<1)|0;w=jA[l>>1]|0;A=w<<16>>16;do{if(w<<16>>16>=0){B=a<<16>>16;if(!((B^A)&8)){e=n+(e+5<<1)|0;if((A|0)>(B|0)){jA[e>>1]=w;jA[l>>1]=s;break}else{jA[e>>1]=s;break}}else{e=n+(e+5<<1)|0;if((A&7)>>>0>(B&7)>>>0){jA[e>>1]=s;break}else{jA[e>>1]=w;jA[l>>1]=s;break}}}else jA[l>>1]=s}while(0);o=o+1<<16>>16}while(o<<16>>16<10);g=I+2|0;o=I+4|0;a=I+6|0;B=I+8|0;l=I+10|0;e=I+12|0;w=I+14|0;A=I+16|0;v=I+18|0;u=40;c=f+(0-(jA[d>>1]|0)<<1)|0;C=f+(0-(jA[d+2>>1]|0)<<1)|0;D=f+(0-(jA[d+4>>1]|0)<<1)|0;E=f+(0-(jA[d+6>>1]|0)<<1)|0;h=f+(0-(jA[d+8>>1]|0)<<1)|0;P=f+(0-(jA[d+10>>1]|0)<<1)|0;k=f+(0-(jA[d+12>>1]|0)<<1)|0;b=f+(0-(jA[d+14>>1]|0)<<1)|0;i=f+(0-(jA[d+16>>1]|0)<<1)|0;Q=f+(0-(jA[d+18>>1]|0)<<1)|0;s=r;while(1){R=(VA(jA[I>>1]|0,jA[c>>1]|0)|0)>>7;L=(VA(jA[g>>1]|0,jA[C>>1]|0)|0)>>7;G=(VA(jA[o>>1]|0,jA[D>>1]|0)|0)>>7;U=(VA(jA[a>>1]|0,jA[E>>1]|0)|0)>>7;H=(VA(jA[B>>1]|0,jA[h>>1]|0)|0)>>7;F=(VA(jA[l>>1]|0,jA[P>>1]|0)|0)>>7;d=(VA(jA[e>>1]|0,jA[k>>1]|0)|0)>>7;f=(VA(jA[w>>1]|0,jA[b>>1]|0)|0)>>>7;r=(VA(jA[A>>1]|0,jA[i>>1]|0)|0)>>>7;jA[s>>1]=(R+128+L+G+U+H+F+d+f+r+((VA(jA[v>>1]|0,jA[Q>>1]|0)|0)>>>7)|0)>>>8;u=u+-1<<16>>16;if(!(u<<16>>16))break;else{c=c+2|0;C=C+2|0;D=D+2|0;E=E+2|0;h=h+2|0;P=P+2|0;k=k+2|0;b=b+2|0;i=i+2|0;Q=Q+2|0;s=s+2|0}}e=0;do{w=n+(e<<1)|0;A=jA[w>>1]|0;if((e|0)<5)A=(DA[t+((A&7)<<1)>>1]|A&8)&65535;else A=jA[t+((A&7)<<1)>>1]|0;jA[w>>1]=A;e=e+1|0}while((e|0)!=10);WA=M;return}function de(A,e,f,i,r,n,t,w){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;var l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0,T=0,y=0,Y=0;Y=WA;WA=WA+3456|0;G=Y+3448|0;H=Y+3360|0;M=Y+3368|0;o=Y+3280|0;U=Y+3200|0;F=Y;R=(i&65535)<<17;y=f<<16>>16;L=f<<16>>16<40;if(L){i=R>>16;f=y;do{B=(VA(jA[e+(f-y<<1)>>1]|0,i)|0)>>15;if((B|0)>32767){pA[w>>2]=1;B=32767}I=e+(f<<1)|0;jA[I>>1]=Ci(jA[I>>1]|0,B&65535,w)|0;f=f+1|0}while((f&65535)<<16>>16!=40)}We(e,A,M,1,w);$f(M,U,o,8);je(e,U,F,w);I=H+2|0;jA[H>>1]=0;jA[I>>1]=1;A=1;B=0;s=1;o=0;a=-1;do{b=jA[2830+(o<<1)>>1]|0;d=b<<16>>16;k=0;do{h=jA[2834+(k<<1)>>1]|0;P=h<<16>>16;E=A;C=d;c=s;D=b;u=a;while(1){l=jA[M+(C<<1)>>1]|0;g=jA[F+(C*80|0)+(C<<1)>>1]|0;f=P;s=1;v=h;A=h;a=-1;while(1){i=Ci(l,jA[M+(f<<1)>>1]|0,w)|0;i=i<<16>>16;i=(VA(i,i)|0)>>>15;Q=(jA[F+(C*80|0)+(f<<1)>>1]<<15)+32768+((jA[F+(f*80|0)+(f<<1)>>1]|0)+g<<14)|0;if(((VA(i<<16>>16,s<<16>>16)|0)-(VA(Q>>16,a<<16>>16)|0)<<1|0)>0){s=Q>>>16&65535;A=v;a=i&65535}Q=f+5|0;v=Q&65535;if(v<<16>>16>=40)break;else f=Q<<16>>16}if(((VA(a<<16>>16,c<<16>>16)|0)-(VA(s<<16>>16,u<<16>>16)|0)<<1|0)>0){jA[H>>1]=D;jA[I>>1]=A;B=D}else{A=E;s=c;a=u}Q=C+5|0;D=Q&65535;if(D<<16>>16>=40)break;else{E=A;C=Q<<16>>16;c=s;u=a}}k=k+1|0}while((k|0)!=4);o=o+1|0}while((o|0)!=2);g=A;v=B;i=r;f=i+80|0;do{jA[i>>1]=0;i=i+2|0}while((i|0)<(f|0));s=v;f=0;Q=0;i=0;while(1){B=s<<16>>16;l=jA[U+(B<<1)>>1]|0;A=(B*6554|0)>>>15;s=A<<16;o=s>>15;a=B-(o+(A<<3)<<16>>17)|0;switch(a<<16>>16|0){case 0:{o=s>>10;A=1;break}case 1:{if(!((f&65535)<<16>>16))A=0;else{o=A<<22>>16|16;A=1}break}case 2:{o=A<<22>>16|32;A=1;break}case 3:{o=A<<17>>16|1;A=0;break}case 4:{o=A<<22>>16|48;A=1;break}default:{o=A;A=a&65535}}o=o&65535;a=r+(B<<1)|0;if(l<<16>>16>0){jA[a>>1]=8191;jA[G+(f<<1)>>1]=32767;B=A<<16>>16;if(A<<16>>16<0){ZA()}else{F=1<<B;B=(F<<16>>16>>B|0)==1?F&65535:32767}i=Ci(i,B,w)|0}else{jA[a>>1]=-8192;jA[G+(f<<1)>>1]=-32768}B=Ci(Q,o,w)|0;f=f+1|0;if((f|0)==2){Q=B;break}s=jA[H+(f<<1)>>1]|0;Q=B}jA[t>>1]=i;o=G+2|0;s=jA[G>>1]|0;A=0;a=e+(0-(v<<16>>16)<<1)|0;B=e+(0-(g<<16>>16)<<1)|0;do{i=VA(jA[a>>1]|0,s)|0;a=a+2|0;if((i|0)!=1073741824?(T=i<<1,!((i|0)>0&(T|0)<0)):0)l=T;else{pA[w>>2]=1;l=2147483647}f=VA(jA[o>>1]|0,jA[B>>1]|0)|0;B=B+2|0;if((f|0)!=1073741824){i=(f<<1)+l|0;if((f^l|0)>0&(i^l|0)<0){pA[w>>2]=1;i=(l>>>31)+2147483647|0}}else{pA[w>>2]=1;i=2147483647}jA[n+(A<<1)>>1]=er(i,w)|0;A=A+1|0}while((A|0)!=40);if(!L){WA=Y;return Q|0}f=R>>16;i=y;do{l=(VA(jA[r+(i-y<<1)>>1]|0,f)|0)>>15;if((l|0)>32767){pA[w>>2]=1;l=32767}n=r+(i<<1)|0;jA[n>>1]=Ci(jA[n>>1]|0,l&65535,w)|0;i=i+1|0}while((i&65535)<<16>>16!=40);WA=Y;return Q|0}function Ie(A,e,f,i,r,n,t,w,l,B){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;l=l|0;B=B|0;var a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0;E=WA;WA=WA+3456|0;g=E+3360|0;v=E+3368|0;u=E+3280|0;c=E+3200|0;C=E;D=r<<16>>16;o=D<<1;if((o|0)==(D<<17>>16|0))Q=o;else{pA[B>>2]=1;Q=r<<16>>16>0?32767:-32768}D=i<<16>>16;a=i<<16>>16<40;if(a){r=Q<<16>>16;s=D;do{i=f+(s<<1)|0;o=(VA(jA[f+(s-D<<1)>>1]|0,r)|0)>>15;if((o|0)>32767){pA[B>>2]=1;o=32767}jA[i>>1]=Ci(jA[i>>1]|0,o&65535,B)|0;s=s+1|0}while((s&65535)<<16>>16!=40)}We(f,e,v,1,B);$f(v,c,u,8);je(f,c,C,B);Me(A,v,C,l,g);o=Fe(A,g,c,n,f,t,w,B)|0;if(!a){WA=E;return o|0}s=Q<<16>>16;r=D;do{i=n+(r<<1)|0;a=(VA(jA[n+(r-D<<1)>>1]|0,s)|0)>>15;if((a|0)>32767){pA[B>>2]=1;a=32767}jA[i>>1]=Ci(jA[i>>1]|0,a&65535,B)|0;r=r+1|0}while((r&65535)<<16>>16!=40);WA=E;return o|0}function Me(A,e,f,i,r){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;var n=0,t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0;h=r+2|0;jA[r>>1]=0;jA[h>>1]=1;D=A<<16>>16<<1;n=1;E=0;A=-1;do{C=(E<<3)+D<<16>>16;l=jA[i+(C<<1)>>1]|0;C=jA[i+((C|1)<<1)>>1]|0;t=l<<16>>16;A:do{if(l<<16>>16<40){c=C<<16>>16;if(C<<16>>16<40)u=n;else while(1){ZA()}while(1){g=jA[f+(t*80|0)+(t<<1)>>1]|0;Q=DA[e+(t<<1)>>1]|0;o=c;n=1;v=C;w=C;B=-1;while(1){s=(DA[e+(o<<1)>>1]|0)+Q<<16>>16;s=(VA(s,s)|0)>>>15;a=(jA[f+(t*80|0)+(o<<1)>>1]<<15)+32768+((jA[f+(o*80|0)+(o<<1)>>1]|0)+g<<14)|0;if(((VA(s<<16>>16,n<<16>>16)|0)-(VA(a>>16,B<<16>>16)|0)<<1|0)>0){n=a>>>16&65535;w=v;B=s&65535}a=o+5|0;v=a&65535;if(v<<16>>16>=40)break;else o=a<<16>>16}if(((VA(B<<16>>16,u<<16>>16)|0)-(VA(n<<16>>16,A<<16>>16)|0)<<1|0)>0){jA[r>>1]=l;jA[h>>1]=w;A=B}else n=u;t=t+5|0;l=t&65535;if(l<<16>>16>=40)break;else{t=t<<16>>16;u=n}}}}while(0);E=E+1|0}while((E|0)!=2);return}function Fe(A,e,f,i,r,n,t,w){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;var l=0,B=0,a=0,s=0,o=0,Q=0;l=i;B=l+80|0;do{jA[l>>1]=0;l=l+2|0}while((l|0)<(B|0));l=jA[e>>1]|0;o=(l*6554|0)>>>15;B=o<<16>>16;s=(748250>>>((l+(VA(B,-5)|0)<<16>>16)+((A<<16>>16)*5|0)|0)&1|0)==0;a=(jA[f+(l<<1)>>1]|0)>0;Q=a?32767:-32768;jA[i+(l<<1)>>1]=a?8191:-8192;l=e+2|0;A=jA[l>>1]|0;i=i+(A<<1)|0;if((jA[f+(A<<1)>>1]|0)>0){jA[i>>1]=8191;f=32767;i=(a&1|2)&65535}else{jA[i>>1]=-8192;f=-32768;i=a&1}o=((A*6554|0)>>>15<<3)+(s?o:B+64|0)&65535;jA[t>>1]=i;s=0;a=r+(0-(jA[e>>1]|0)<<1)|0;i=r+(0-(jA[l>>1]|0)<<1)|0;do{l=VA(Q,jA[a>>1]|0)|0;a=a+2|0;if((l|0)==1073741824){pA[w>>2]=1;A=2147483647}else A=l<<1;B=VA(f,jA[i>>1]|0)|0;i=i+2|0;if((B|0)!=1073741824){l=(B<<1)+A|0;if((B^A|0)>0&(l^A|0)<0){pA[w>>2]=1;l=(A>>>31)+2147483647|0}}else{pA[w>>2]=1;l=2147483647}jA[n+(s<<1)>>1]=er(l,w)|0;s=s+1|0}while((s|0)!=40);return o|0}function He(A,e,f,i,r,n,t,w){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;var l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0,T=0,y=0,Y=0,z=0,X=0,J=0,O=0,m=0,N=0;N=WA;WA=WA+3440|0;T=N+3360|0;y=N+3280|0;z=N+3200|0;Y=N;J=(i&65535)<<17;m=f<<16>>16;X=f<<16>>16<40;if(X){f=J>>16;l=m;do{i=(VA(jA[e+(l-m<<1)>>1]|0,f)|0)>>15;if((i|0)>32767){pA[w>>2]=1;i=32767}R=e+(l<<1)|0;jA[R>>1]=Ci(jA[R>>1]|0,i&65535,w)|0;l=l+1|0}while((l&65535)<<16>>16!=40)}We(e,A,T,1,w);$f(T,z,y,6);je(e,z,Y,w);R=1;B=2;a=1;i=0;l=1;A=-1;s=1;while(1){L=2;g=2;while(1){H=0;U=0;G=s;F=g;while(1){if(U<<16>>16<40){b=G<<16>>16;d=G<<16>>16<40;I=F<<16>>16;M=F<<16>>16<40;P=U<<16>>16;k=U;while(1){if((jA[y+(P<<1)>>1]|0)>-1){D=jA[Y+(P*80|0)+(P<<1)>>1]|0;if(d){E=DA[T+(P<<1)>>1]|0;C=b;Q=1;h=G;f=G;g=0;o=-1;while(1){u=(DA[T+(C<<1)>>1]|0)+E|0;c=u<<16>>16;c=(VA(c,c)|0)>>>15;v=(jA[Y+(P*80|0)+(C<<1)>>1]<<15)+32768+((jA[Y+(C*80|0)+(C<<1)>>1]|0)+D<<14)|0;if(((VA(c<<16>>16,Q<<16>>16)|0)-(VA(v>>16,o<<16>>16)|0)<<1|0)>0){Q=v>>>16&65535;f=h;g=u&65535;o=c&65535}v=C+5|0;h=v&65535;if(h<<16>>16>=40)break;else C=v<<16>>16}}else{Q=1;f=G;g=0}if(M){E=g&65535;h=f<<16>>16;C=(Q<<16>>16<<14)+32768|0;c=I;g=1;D=F;o=F;Q=-1;while(1){u=(DA[T+(c<<1)>>1]|0)+E<<16>>16;u=(VA(u,u)|0)>>>15;v=C+(jA[Y+(c*80|0)+(c<<1)>>1]<<12)+((jA[Y+(P*80|0)+(c<<1)>>1]|0)+(jA[Y+(h*80|0)+(c<<1)>>1]|0)<<13)|0;if(((VA(u<<16>>16,g<<16>>16)|0)-(VA(v>>16,Q<<16>>16)|0)<<1|0)>0){g=v>>>16&65535;o=D;Q=u&65535}v=c+5|0;D=v&65535;if(D<<16>>16>=40){C=g;c=Q;break}else c=v<<16>>16}}else{C=1;o=F;c=-1}g=VA(c<<16>>16,l<<16>>16)|0;if((g|0)==1073741824){ZA()}else v=g<<1;g=VA(C<<16>>16,A<<16>>16)|0;if((g|0)==1073741824){ZA()}else Q=g<<1;g=v-Q|0;if(((g^v)&(Q^v)|0)<0){ZA()}h=(g|0)>0;B=h?o:B;a=h?f:a;i=h?k:i;l=h?C:l;A=h?c:A}g=P+5|0;k=g&65535;if(k<<16>>16>=40)break;else P=g<<16>>16}}H=H+1<<16>>16;if(H<<16>>16>=3)break;else{M=F;F=G;G=U;U=M}}f=L+2|0;g=f&65535;if(g<<16>>16>=5)break;else L=f&65535}f=R+2|0;s=f&65535;if(s<<16>>16<4)R=f&65535;else{g=B;B=a;break}}f=r;l=f+80|0;do{jA[f>>1]=0;f=f+2|0}while((f|0)<(l|0));c=i<<16>>16;A=jA[z+(c<<1)>>1]|0;i=(c*6554|0)>>>15;f=i<<16;l=c-(((f>>16)*327680|0)>>>16)|0;switch(l<<16>>16|0){case 1:{i=f>>12;break}case 2:{i=f>>8;l=2;break}case 3:{i=i<<20>>16|8;l=1;break}case 4:{i=i<<24>>16|128;l=2;break}default:{}}f=r+(c<<1)|0;if(A<<16>>16>0){jA[f>>1]=8191;h=32767;a=65536<<(l<<16>>16)>>>16&65535}else{jA[f>>1]=-8192;h=-32768;a=0}v=B<<16>>16;B=jA[z+(v<<1)>>1]|0;f=(v*6554|0)>>>15;l=f<<16;A=v-(((l>>16)*327680|0)>>>16)|0;switch(A<<16>>16|0){case 1:{f=l>>12;break}case 2:{f=l>>8;A=2;break}case 3:{f=f<<20>>16|8;A=1;break}case 4:{f=f<<24>>16|128;A=2;break}default:{}}l=r+(v<<1)|0;if(B<<16>>16>0){jA[l>>1]=8191;u=32767;a=(65536<<(A<<16>>16)>>>16)+(a&65535)&65535}else{jA[l>>1]=-8192;u=-32768}s=f+i|0;Q=g<<16>>16;B=jA[z+(Q<<1)>>1]|0;i=(Q*6554|0)>>>15;f=i<<16;l=Q-(((f>>16)*327680|0)>>>16)|0;switch(l<<16>>16|0){case 1:{f=f>>12;break}case 2:{f=f>>8;l=2;break}case 3:{f=i<<20>>16|8;l=1;break}case 4:{f=i<<24>>16|128;l=2;break}default:f=i}i=r+(Q<<1)|0;if(B<<16>>16>0){jA[i>>1]=8191;g=32767;i=(65536<<(l<<16>>16)>>>16)+(a&65535)&65535}else{jA[i>>1]=-8192;g=-32768;i=a}o=s+f|0;jA[t>>1]=i;a=0;s=e+(0-c<<1)|0;A=e+(0-v<<1)|0;B=e+(0-Q<<1)|0;do{i=VA(jA[s>>1]|0,h)|0;s=s+2|0;if((i|0)!=1073741824?(O=i<<1,!((i|0)>0&(O|0)<0)):0)l=O;else{pA[w>>2]=1;l=2147483647}i=VA(jA[A>>1]|0,u)|0;A=A+2|0;if((i|0)!=1073741824){f=(i<<1)+l|0;if((i^l|0)>0&(f^l|0)<0){pA[w>>2]=1;f=(l>>>31)+2147483647|0}}else{pA[w>>2]=1;f=2147483647}l=VA(jA[B>>1]|0,g)|0;B=B+2|0;if((l|0)!=1073741824){i=(l<<1)+f|0;if((l^f|0)>0&(i^f|0)<0){pA[w>>2]=1;i=(f>>>31)+2147483647|0}}else{pA[w>>2]=1;i=2147483647}jA[n+(a<<1)>>1]=er(i,w)|0;a=a+1|0}while((a|0)!=40);i=o&65535;if(!X){WA=N;return i|0}l=J>>16;f=m;do{A=(VA(jA[r+(f-m<<1)>>1]|0,l)|0)>>15;if((A|0)>32767){pA[w>>2]=1;A=32767}n=r+(f<<1)|0;jA[n>>1]=Ci(jA[n>>1]|0,A&65535,w)|0;f=f+1|0}while((f&65535)<<16>>16!=40);WA=N;return i|0}function Ue(A,e,f,i,r,n,t,w,l){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;l=l|0;var B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0,T=0,y=0,Y=0,z=0,X=0,J=0,O=0,m=0,N=0,K=0,x=0,S=0,j=0,p=0,W=0,V=0,Z=0,_=0,q=0;q=WA;WA=WA+3456|0;p=q+3448|0;S=q+3360|0;N=q+3368|0;K=q+3280|0;j=q+3200|0;x=q;V=(i&65535)<<17;_=f<<16>>16;W=f<<16>>16<40;if(W){f=V>>16;B=_;do{i=(VA(jA[e+(B-_<<1)>>1]|0,f)|0)>>15;if((i|0)>32767){pA[l>>2]=1;i=32767}m=e+(B<<1)|0;jA[m>>1]=Ci(jA[m>>1]|0,i&65535,l)|0;B=B+1|0}while((B&65535)<<16>>16!=40)}We(e,A,N,1,l);$f(N,j,K,4);je(e,j,x,l);J=S+2|0;jA[S>>1]=0;O=S+4|0;jA[J>>1]=1;m=S+6|0;jA[O>>1]=2;jA[m>>1]=3;Q=3;s=2;a=1;i=0;f=1;B=-1;o=3;do{T=0;y=0;Y=o;z=1;X=2;while(1){if(y<<16>>16<40){F=z<<16>>16;H=z<<16>>16<40;U=X<<16>>16;G=X<<16>>16<40;L=Y<<16>>16;R=Y<<16>>16<40;M=y<<16>>16;I=s;b=a;k=f;d=y;while(1){if((jA[K+(M<<1)>>1]|0)>-1){v=jA[x+(M*80|0)+(M<<1)>>1]|0;if(H){g=DA[N+(M<<1)>>1]|0;u=F;h=1;s=z;a=z;D=0;E=-1;while(1){C=(DA[N+(u<<1)>>1]|0)+g|0;c=C<<16>>16;c=(VA(c,c)|0)>>>15;P=(jA[x+(M*80|0)+(u<<1)>>1]<<15)+32768+((jA[x+(u*80|0)+(u<<1)>>1]|0)+v<<14)|0;if(((VA(c<<16>>16,h<<16>>16)|0)-(VA(P>>16,E<<16>>16)|0)<<1|0)>0){h=P>>>16&65535;a=s;D=C&65535;E=c&65535}P=u+5|0;s=P&65535;if(s<<16>>16>=40)break;else u=P<<16>>16}}else{h=1;a=z;D=0}if(G){f=D&65535;A=a<<16>>16;v=(h<<16>>16<<14)+32768|0;u=U;P=1;g=X;s=X;E=0;D=-1;while(1){C=(DA[N+(u<<1)>>1]|0)+f|0;c=C<<16>>16;c=(VA(c,c)|0)>>>15;h=v+(jA[x+(u*80|0)+(u<<1)>>1]<<12)+((jA[x+(M*80|0)+(u<<1)>>1]|0)+(jA[x+(A*80|0)+(u<<1)>>1]|0)<<13)|0;if(((VA(c<<16>>16,P<<16>>16)|0)-(VA(h>>16,D<<16>>16)|0)<<1|0)>0){P=h>>>16&65535;s=g;E=C&65535;D=c&65535}h=u+5|0;g=h&65535;if(g<<16>>16>=40)break;else u=h<<16>>16}}else{P=1;s=X;E=0}if(R){v=E&65535;g=s<<16>>16;A=a<<16>>16;c=(P&65535)<<16|32768;C=L;f=1;u=Y;h=Y;P=-1;while(1){D=(DA[N+(C<<1)>>1]|0)+v<<16>>16;D=(VA(D,D)|0)>>>15;E=(jA[x+(C*80|0)+(C<<1)>>1]<<12)+c+((jA[x+(A*80|0)+(C<<1)>>1]|0)+(jA[x+(g*80|0)+(C<<1)>>1]|0)+(jA[x+(M*80|0)+(C<<1)>>1]|0)<<13)|0;if(((VA(D<<16>>16,f<<16>>16)|0)-(VA(E>>16,P<<16>>16)|0)<<1|0)>0){f=E>>>16&65535;h=u;P=D&65535}E=C+5|0;u=E&65535;if(u<<16>>16>=40)break;else C=E<<16>>16}}else{f=1;h=Y;P=-1}if(((VA(P<<16>>16,k<<16>>16)|0)-(VA(f<<16>>16,B<<16>>16)|0)<<1|0)>0){jA[S>>1]=d;jA[J>>1]=a;jA[O>>1]=s;jA[m>>1]=h;Q=h;i=d;B=P}else{s=I;a=b;f=k}}else{s=I;a=b;f=k}C=M+5|0;d=C&65535;if(d<<16>>16>=40)break;else{M=C<<16>>16;I=s;b=a;k=f}}}T=T+1<<16>>16;if(T<<16>>16>=4)break;else{L=X;R=Y;X=z;z=y;Y=L;y=R}}o=o+1<<16>>16}while(o<<16>>16<5);P=Q;h=s;E=a;D=i;i=r;f=i+80|0;do{jA[i>>1]=0;i=i+2|0}while((i|0)<(f|0));A=D;f=0;B=0;i=0;while(1){s=A<<16>>16;o=jA[j+(s<<1)>>1]|0;A=s*13108>>16;a=s-((A*327680|0)>>>16)|0;A=jA[w+(A<<1)>>1]|0;switch(a<<16>>16|0){case 1:{Q=A<<16>>16<<3&65535;break}case 2:{Q=A<<16>>16<<6&65535;break}case 3:{Q=A<<16>>16<<10&65535;break}case 4:{Q=((A&65535)<<10|512)&65535;a=3;break}default:Q=A}A=r+(s<<1)|0;if(o<<16>>16>0){jA[A>>1]=8191;A=32767;i=(65536<<(a<<16>>16)>>>16)+(i&65535)&65535}else{jA[A>>1]=-8192;A=-32768}jA[p+(f<<1)>>1]=A;B=(Q&65535)+(B&65535)|0;f=f+1|0;if((f|0)==4){C=B;break}A=jA[S+(f<<1)>>1]|0}jA[t>>1]=i;v=p+2|0;u=p+4|0;c=p+6|0;A=jA[p>>1]|0;g=0;a=e+(0-(D<<16>>16)<<1)|0;s=e+(0-(E<<16>>16)<<1)|0;o=e+(0-(h<<16>>16)<<1)|0;Q=e+(0-(P<<16>>16)<<1)|0;do{i=VA(jA[a>>1]|0,A)|0;a=a+2|0;if((i|0)!=1073741824?(Z=i<<1,!((i|0)>0&(Z|0)<0)):0)B=Z;else{pA[l>>2]=1;B=2147483647}i=VA(jA[v>>1]|0,jA[s>>1]|0)|0;s=s+2|0;if((i|0)!=1073741824){f=(i<<1)+B|0;if((i^B|0)>0&(f^B|0)<0){pA[l>>2]=1;f=(B>>>31)+2147483647|0}}else{pA[l>>2]=1;f=2147483647}i=VA(jA[u>>1]|0,jA[o>>1]|0)|0;o=o+2|0;if((i|0)!=1073741824){B=(i<<1)+f|0;if((i^f|0)>0&(B^f|0)<0){pA[l>>2]=1;B=(f>>>31)+2147483647|0}}else{pA[l>>2]=1;B=2147483647}f=VA(jA[c>>1]|0,jA[Q>>1]|0)|0;Q=Q+2|0;if((f|0)!=1073741824){i=(f<<1)+B|0;if((f^B|0)>0&(i^B|0)<0){pA[l>>2]=1;i=(B>>>31)+2147483647|0}}else{pA[l>>2]=1;i=2147483647}jA[n+(g<<1)>>1]=er(i,l)|0;g=g+1|0}while((g|0)!=40);i=C&65535;if(((_<<16)+-2621440|0)>-1|W^1){WA=q;return i|0}B=V>>16;f=_;do{A=(VA(jA[r+(f-_<<1)>>1]|0,B)|0)>>15;if((A|0)>32767){pA[l>>2]=1;A=32767}n=r+(f<<1)|0;jA[n>>1]=Ci(jA[n>>1]|0,A&65535,l)|0;f=f+1|0}while((f&65535)<<16>>16!=40);WA=q;return i|0}function Ge(A,e,f,i,r,n,t){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;var w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0;R=WA;WA=WA+3440|0;v=R+3424|0;F=R+3408|0;H=R+3240|0;u=R+3224|0;I=R+3328|0;g=R+3248|0;M=R+24|0;L=R+16|0;G=R;pe(f,A,I,2,4,4,t);Ai(I,e,g,H,4,F,4,t);je(f,g,M,t);qf(8,4,4,I,M,F,H,u,t);e=i;A=e+80|0;do{jA[e>>1]=0;e=e+2|0}while((e|0)<(A|0));jA[G>>1]=-1;jA[L>>1]=-1;b=G+2|0;jA[b>>1]=-1;d=L+2|0;jA[d>>1]=-1;I=G+4|0;jA[I>>1]=-1;M=L+4|0;jA[M>>1]=-1;H=G+6|0;jA[H>>1]=-1;F=L+6|0;jA[F>>1]=-1;o=0;do{a=jA[u+(o<<1)>>1]|0;e=a>>>2;l=e&65535;A=a&3;B=(jA[g+(a<<1)>>1]|0)>0;a=i+(a<<1)|0;Q=B&1^1;jA[a>>1]=(DA[a>>1]|0)+(B?8191:57345);jA[v+(o<<1)>>1]=B?32767:-32768;B=G+(A<<1)|0;a=jA[B>>1]|0;do{if(a<<16>>16>=0){s=L+(A<<1)|0;w=(a<<16>>16|0)<=(e<<16>>16|0);e=G+((A|4)<<1)|0;if((Q&65535|0)==(DA[s>>1]&1|0))if(w){jA[e>>1]=l;break}else{jA[e>>1]=a;jA[B>>1]=l;jA[s>>1]=Q;break}else if(w){jA[e>>1]=a;jA[B>>1]=l;jA[s>>1]=Q;break}else{jA[e>>1]=l;break}}else{jA[B>>1]=l;jA[L+(A<<1)>>1]=Q}}while(0);o=o+1|0}while((o|0)!=8);c=v+2|0;C=v+4|0;D=v+6|0;E=v+8|0;h=v+10|0;P=v+12|0;k=v+14|0;v=jA[v>>1]|0;o=0;s=f+(0-(jA[u>>1]|0)<<1)|0;a=f+(0-(jA[u+2>>1]|0)<<1)|0;B=f+(0-(jA[u+4>>1]|0)<<1)|0;l=f+(0-(jA[u+6>>1]|0)<<1)|0;e=f+(0-(jA[u+8>>1]|0)<<1)|0;A=f+(0-(jA[u+10>>1]|0)<<1)|0;w=f+(0-(jA[u+12>>1]|0)<<1)|0;f=f+(0-(jA[u+14>>1]|0)<<1)|0;do{Q=VA(jA[s>>1]|0,v)|0;s=s+2|0;if((Q|0)!=1073741824?(U=Q<<1,!((Q|0)>0&(U|0)<0)):0)Q=U;else{pA[t>>2]=1;Q=2147483647}g=VA(jA[c>>1]|0,jA[a>>1]|0)|0;a=a+2|0;if((g|0)!=1073741824){i=(g<<1)+Q|0;if((g^Q|0)>0&(i^Q|0)<0){pA[t>>2]=1;Q=(Q>>>31)+2147483647|0}else Q=i}else{pA[t>>2]=1;Q=2147483647}g=VA(jA[C>>1]|0,jA[B>>1]|0)|0;B=B+2|0;if((g|0)!=1073741824){i=(g<<1)+Q|0;if((g^Q|0)>0&(i^Q|0)<0){pA[t>>2]=1;i=(Q>>>31)+2147483647|0}}else{pA[t>>2]=1;i=2147483647}g=VA(jA[D>>1]|0,jA[l>>1]|0)|0;l=l+2|0;if((g|0)!=1073741824){Q=(g<<1)+i|0;if((g^i|0)>0&(Q^i|0)<0){pA[t>>2]=1;Q=(i>>>31)+2147483647|0}}else{pA[t>>2]=1;Q=2147483647}g=VA(jA[E>>1]|0,jA[e>>1]|0)|0;e=e+2|0;if((g|0)!=1073741824){i=(g<<1)+Q|0;if((g^Q|0)>0&(i^Q|0)<0){pA[t>>2]=1;i=(Q>>>31)+2147483647|0}}else{pA[t>>2]=1;i=2147483647}g=VA(jA[h>>1]|0,jA[A>>1]|0)|0;A=A+2|0;if((g|0)!=1073741824){Q=(g<<1)+i|0;if((g^i|0)>0&(Q^i|0)<0){pA[t>>2]=1;Q=(i>>>31)+2147483647|0}}else{pA[t>>2]=1;Q=2147483647}g=VA(jA[P>>1]|0,jA[w>>1]|0)|0;w=w+2|0;if((g|0)!=1073741824){i=(g<<1)+Q|0;if((g^Q|0)>0&(i^Q|0)<0){pA[t>>2]=1;i=(Q>>>31)+2147483647|0}}else{pA[t>>2]=1;i=2147483647}g=VA(jA[k>>1]|0,jA[f>>1]|0)|0;f=f+2|0;if((g|0)!=1073741824){Q=(g<<1)+i|0;if((g^i|0)>0&(Q^i|0)<0){pA[t>>2]=1;Q=(i>>>31)+2147483647|0}}else{pA[t>>2]=1;Q=2147483647}jA[r+(o<<1)>>1]=er(Q,t)|0;o=o+1|0}while((o|0)!=40);jA[n>>1]=jA[L>>1]|0;jA[n+2>>1]=jA[d>>1]|0;jA[n+4>>1]=jA[M>>1]|0;jA[n+6>>1]=jA[F>>1]|0;A=jA[G>>1]|0;e=jA[G+8>>1]|0;w=jA[b>>1]|0;jA[n+8>>1]=e<<1&2|A&1|w<<2&4|(((e>>1)*327680|0)+(A>>>1<<16)+(VA(w>>1,1638400)|0)|0)>>>13&65528;w=jA[I>>1]|0;A=jA[G+12>>1]|0;e=jA[G+10>>1]|0;jA[n+10>>1]=A<<1&2|w&1|e<<2&4|(((A>>1)*327680|0)+(w>>>1<<16)+(VA(e>>1,1638400)|0)|0)>>>13&65528;e=jA[G+14>>1]|0;w=jA[H>>1]|0;A=w<<16>>16>>>1;if(!(e&2)){r=A;t=e<<16>>16;L=t>>1;L=L*327680|0;r=r<<16;L=r+L|0;L=L<<5;L=L>>16;L=L|12;L=L*2622|0;L=L>>>16;r=w&65535;r=r&1;t=t<<17;t=t&131072;L=L<<18;t=L|t;t=t>>>16;r=t|r;r=r&65535;n=n+12|0;jA[n>>1]=r;WA=R;return}r=4-(A<<16>>16)|0;t=e<<16>>16;L=t>>1;L=L*327680|0;r=r<<16;L=r+L|0;L=L<<5;L=L>>16;L=L|12;L=L*2622|0;L=L>>>16;r=w&65535;r=r&1;t=t<<17;t=t&131072;L=L<<18;t=L|t;t=t>>>16;r=t|r;r=r&65535;n=n+12|0;jA[n>>1]=r;WA=R;return}function Le(A,e,f,i,r){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;var n=0,t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0;v=f<<16>>16;n=0-v|0;f=r+(n<<2)|0;r=((v-(i<<16>>16)|0)>>>2)+1&65535;if(r<<16>>16<=0)return;v=e<<16>>16>>>1&65535;if(!(v<<16>>16)){ZA()}g=A+(n<<1)|0;while(1){B=g+4|0;s=jA[B>>1]|0;w=jA[g>>1]|0;a=s;l=v;o=A;Q=g;g=g+8|0;t=0;n=0;i=0;e=0;while(1){c=jA[o>>1]|0;u=(VA(w<<16>>16,c)|0)+t|0;t=jA[Q+2>>1]|0;n=(VA(t,c)|0)+n|0;w=(VA(a<<16>>16,c)|0)+i|0;i=jA[Q+6>>1]|0;a=(VA(i,c)|0)+e|0;e=jA[o+2>>1]|0;t=u+(VA(e,t)|0)|0;n=n+(VA(s<<16>>16,e)|0)|0;B=B+4|0;i=w+(VA(e,i)|0)|0;w=jA[B>>1]|0;e=a+(VA(w<<16>>16,e)|0)|0;l=l+-1<<16>>16;if(!(l<<16>>16))break;c=s;a=w;s=jA[Q+8>>1]|0;o=o+4|0;Q=Q+4|0;w=c}pA[f>>2]=t<<1;pA[f+4>>2]=n<<1;pA[f+8>>2]=i<<1;pA[f+12>>2]=e<<1;if(r<<16>>16<=1)break;else{f=f+16|0;r=r+-1<<16>>16}}return}function Re(A,e,f,i,r,n,t,w,l){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;l=l|0;var B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0;E=WA;WA=WA+16|0;C=E+2|0;D=E;do{if(r<<16>>16>0){g=i<<16>>16;u=0;s=0;i=0;a=0;v=0;while(1){B=jA[A+(u<<1)>>1]|0;o=B<<16>>16;s=(VA(o,o)|0)+s|0;o=jA[e+(u<<1)>>1]|0;i=(VA(o,o)|0)+i|0;a=(VA(jA[f+(u<<1)>>1]|0,o)|0)+a|0;o=VA(o,g)|0;if((o|0)==1073741824){pA[l>>2]=1;Q=2147483647}else Q=o<<1;o=Q<<1;o=(nr(B,er((o>>1|0)==(Q|0)?o:Q>>31^2147483647,l)|0,l)|0)<<16>>16;o=VA(o,o)|0;if((o|0)!=1073741824){B=(o<<1)+v|0;if((o^v|0)>0&(B^v|0)<0){pA[l>>2]=1;B=(v>>>31)+2147483647|0}}else{pA[l>>2]=1;B=2147483647}u=u+1|0;if((u&65535)<<16>>16==r<<16>>16){v=B;break}else v=B}s=s<<1;i=i<<1;a=a<<1;if((s|0)>=0){if((s|0)<400){B=v;c=14;break}}else{pA[l>>2]=1;s=2147483647}Q=Ni(s)|0;o=Q<<16>>16;if(Q<<16>>16>0){B=s<<o;if((B>>o|0)!=(s|0))B=s>>31^2147483647}else{B=0-o<<16;if((B|0)<2031616)B=s>>(B>>16);else B=0}jA[n>>1]=B>>>16;s=i;g=a;B=v;i=15-(Q&65535)&65535}else{i=0;a=0;B=0;c=14}}while(0);if((c|0)==14){jA[n>>1]=0;s=i;g=a;i=-15}jA[t>>1]=i;if((s|0)<0){pA[l>>2]=1;s=2147483647}o=Ni(s)|0;a=o<<16>>16;if(o<<16>>16>0){i=s<<a;if((i>>a|0)!=(s|0))i=s>>31^2147483647}else{i=0-a<<16;if((i|0)<2031616)i=s>>(i>>16);else i=0}jA[n+2>>1]=i>>>16;jA[t+2>>1]=15-(o&65535);s=Ni(g)|0;a=s<<16>>16;if(s<<16>>16>0){i=g<<a;if((i>>a|0)!=(g|0))i=g>>31^2147483647}else{i=0-a<<16;if((i|0)<2031616)i=g>>(i>>16);else i=0}jA[n+4>>1]=i>>>16;jA[t+4>>1]=2-(s&65535);s=Ni(B)|0;i=s<<16>>16;if(s<<16>>16>0){a=B<<i;if((a>>i|0)!=(B|0))a=B>>31^2147483647}else{i=0-i<<16;if((i|0)<2031616)a=B>>(i>>16);else a=0}i=a>>>16&65535;B=15-(s&65535)&65535;jA[n+6>>1]=i;jA[t+6>>1]=B;if((a>>16|0)<=0){l=0;jA[w>>1]=l;WA=E;return}a=jA[n>>1]|0;if(!(a<<16>>16)){l=0;jA[w>>1]=l;WA=E;return}i=Ei(fr(a,1,l)|0,i)|0;i=(i&65535)<<16;a=((nr(B,jA[t>>1]|0,l)|0)&65535)+3|0;B=a&65535;a=a<<16>>16;if(B<<16>>16>0)B=B<<16>>16<31?i>>a:0;else{t=0-a<<16>>16;B=i<<t;B=(B>>t|0)==(i|0)?B:i>>31^2147483647}Gi(B,C,D,l);D=cf((DA[C>>1]|0)+65509&65535,jA[D>>1]|0,l)|0;C=D<<13;l=er((C>>13|0)==(D|0)?C:D>>31^2147483647,l)|0;jA[w>>1]=l;WA=E;return}function Te(A,e,f,i,r,n,t,w,l,B,a){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;l=l|0;B=B|0;a=a|0;var s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0;E=WA;WA=WA+80|0;c=E;jA[t>>1]=jA[n>>1]|0;jA[w>>1]=jA[n+2>>1]|0;Q=jA[n+4>>1]|0;if(Q<<16>>16==-32768)Q=32767;else Q=0-(Q&65535)&65535;jA[t+2>>1]=Q;jA[w+2>>1]=(DA[n+6>>1]|0)+1;switch(A|0){case 0:case 5:{u=0;o=0;s=0;v=0;break}default:{u=0;o=1;s=1;v=1}}while(1){g=(jA[r+(u<<1)>>1]|0)>>>3;jA[c+(u<<1)>>1]=g;g=g<<16>>16;Q=VA(g,g)|0;if((Q|0)!=1073741824){n=(Q<<1)+o|0;if((Q^o|0)>0&(n^o|0)<0){pA[a>>2]=1;o=(o>>>31)+2147483647|0}else o=n}else{pA[a>>2]=1;o=2147483647}Q=VA(jA[e+(u<<1)>>1]|0,g)|0;if((Q|0)!=1073741824){n=(Q<<1)+s|0;if((Q^s|0)>0&(n^s|0)<0){pA[a>>2]=1;s=(s>>>31)+2147483647|0}else s=n}else{pA[a>>2]=1;s=2147483647}Q=VA(jA[i+(u<<1)>>1]|0,g)|0;if((Q|0)!=1073741824){n=(Q<<1)+v|0;if((Q^v|0)>0&(n^v|0)<0){pA[a>>2]=1;n=(v>>>31)+2147483647|0}}else{pA[a>>2]=1;n=2147483647}u=u+1|0;if((u|0)==40){i=n;g=s;break}else v=n}s=Ni(o)|0;n=s<<16>>16;if(s<<16>>16>0){Q=o<<n;if((Q>>n|0)!=(o|0))Q=o>>31^2147483647}else{Q=0-n<<16;if((Q|0)<2031616)Q=o>>(Q>>16);else Q=0}r=t+4|0;jA[r>>1]=Q>>>16;e=w+4|0;jA[e>>1]=-3-(s&65535);o=Ni(g)|0;n=o<<16>>16;if(o<<16>>16>0){Q=g<<n;if((Q>>n|0)!=(g|0))Q=g>>31^2147483647}else{Q=0-n<<16;if((Q|0)<2031616)Q=g>>(Q>>16);else Q=0}n=Q>>>16;jA[t+6>>1]=(n|0)==32768?32767:0-n&65535;jA[w+6>>1]=7-(o&65535);o=Ni(i)|0;n=o<<16>>16;if(o<<16>>16>0){Q=i<<n;if((Q>>n|0)!=(i|0))Q=i>>31^2147483647}else{Q=0-n<<16;if((Q|0)<2031616)Q=i>>(Q>>16);else Q=0}jA[t+8>>1]=Q>>>16;jA[w+8>>1]=7-(o&65535);switch(A|0){case 0:case 5:{Q=0;s=0;break}default:{WA=E;return}}do{s=(VA(jA[c+(Q<<1)>>1]|0,jA[f+(Q<<1)>>1]|0)|0)+s|0;Q=Q+1|0}while((Q|0)!=40);n=s<<1;Q=Ni(n)|0;o=Q<<16>>16;if(Q<<16>>16>0){s=n<<o;if((s>>o|0)==(n|0)){C=s;D=40}else{C=n>>31^2147483647;D=40}}else{s=0-o<<16;if((s|0)<2031616){C=n>>(s>>16);D=40}}if((D|0)==40?(C>>16|0)>=1:0){a=fr(C>>>16&65535,1,a)|0;jA[l>>1]=Ei(a,jA[r>>1]|0)|0;jA[B>>1]=65528-(Q&65535)-(DA[e>>1]|0);WA=E;return}jA[l>>1]=0;jA[B>>1]=0;WA=E;return}function ye(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var r=0,n=0,t=0;n=0;r=0;do{t=jA[A+(n<<1)>>1]|0;r=(VA(t,t)|0)+r|0;n=n+1|0}while((n|0)!=40);if((r|0)<0){pA[i>>2]=1;r=2147483647}i=Ni(r)|0;A=i<<16>>16;if(i<<16>>16>0){n=r<<A;if((n>>A|0)==(r|0))r=n;else r=r>>31^2147483647}else{A=0-A<<16;if((A|0)<2031616)r=r>>(A>>16);else r=0}jA[f>>1]=r>>>16;jA[e>>1]=16-(i&65535);return}function Ye(A,e,f,i,r,n,t,w,l,B,a,s,o){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;l=l|0;B=B|0;a=a|0;s=s|0;o=o|0;var Q=0,g=0,v=0,u=0;g=WA;WA=WA+16|0;Q=g;if(B>>>0<2){t=Ie(a,A,e,f,i,t,w,Q,pA[s+76>>2]|0,o)|0;o=pA[l>>2]|0;jA[o>>1]=t;t=jA[Q>>1]|0;pA[l>>2]=o+4;jA[o+2>>1]=t;WA=g;return}switch(B|0){case 2:{t=de(A,e,f,i,t,w,Q,o)|0;o=pA[l>>2]|0;jA[o>>1]=t;t=jA[Q>>1]|0;pA[l>>2]=o+4;jA[o+2>>1]=t;WA=g;return}case 3:{t=He(A,e,f,i,t,w,Q,o)|0;o=pA[l>>2]|0;jA[o>>1]=t;t=jA[Q>>1]|0;pA[l>>2]=o+4;jA[o+2>>1]=t;WA=g;return}default:{if((B&-2|0)==4){t=Ue(A,e,f,i,t,w,Q,pA[s+36>>2]|0,o)|0;o=pA[l>>2]|0;jA[o>>1]=t;t=jA[Q>>1]|0;pA[l>>2]=o+4;jA[o+2>>1]=t;WA=g;return}if((B|0)!=6){a=r<<16>>16;a=(a<<17>>17|0)==(a|0)?a<<1:a>>>15^32767;r=f<<16>>16<40;if(!r){be(A,n,e,t,w,pA[l>>2]|0,pA[s+36>>2]|0,o);pA[l>>2]=(pA[l>>2]|0)+20;WA=g;return}Q=f<<16>>16;B=a<<16>>16;i=Q;do{u=(VA(jA[e+(i-Q<<1)>>1]|0,B)|0)>>>15&65535;v=e+(i<<1)|0;jA[v>>1]=Ci(jA[v>>1]|0,u,o)|0;i=i+1|0}while((i&65535)<<16>>16!=40);be(A,n,e,t,w,pA[l>>2]|0,pA[s+36>>2]|0,o);pA[l>>2]=(pA[l>>2]|0)+20;if(!r){WA=g;return}r=f<<16>>16;B=a<<16>>16;Q=r;do{i=(VA(jA[t+(Q-r<<1)>>1]|0,B)|0)>>15;if((i|0)>32767){pA[o>>2]=1;i=32767}u=t+(Q<<1)|0;jA[u>>1]=Ci(jA[u>>1]|0,i&65535,o)|0;Q=Q+1|0}while((Q&65535)<<16>>16!=40);WA=g;return}s=i<<16>>16;s=(s<<17>>17|0)==(s|0)?s<<1:s>>>15^32767;a=f<<16>>16<40;if(!a){Ge(A,n,e,t,w,pA[l>>2]|0,o);pA[l>>2]=(pA[l>>2]|0)+14;WA=g;return}Q=f<<16>>16;B=s<<16>>16;i=Q;do{r=(VA(jA[e+(i-Q<<1)>>1]|0,B)|0)>>15;if((r|0)>32767){pA[o>>2]=1;r=32767}u=e+(i<<1)|0;jA[u>>1]=Ci(jA[u>>1]|0,r&65535,o)|0;i=i+1|0}while((i&65535)<<16>>16!=40);Ge(A,n,e,t,w,pA[l>>2]|0,o);pA[l>>2]=(pA[l>>2]|0)+14;if(!a){WA=g;return}r=f<<16>>16;B=s<<16>>16;Q=r;do{i=(VA(jA[t+(Q-r<<1)>>1]|0,B)|0)>>15;if((i|0)>32767){pA[o>>2]=1;i=32767}u=t+(Q<<1)|0;jA[u>>1]=Ci(jA[u>>1]|0,i&65535,o)|0;Q=Q+1|0}while((Q&65535)<<16>>16!=40);WA=g;return}}}function ze(A){A=A|0;var e=0;if(!A){A=-1;return A|0}pA[A>>2]=0;e=lr(4)|0;if(!e){A=-1;return A|0}if(!((Hf(e)|0)<<16>>16)){Uf(pA[e>>2]|0)|0;pA[A>>2]=e;A=0;return A|0}else{Gf(e);Br(e);A=-1;return A|0}return 0}function Xe(A){A=A|0;var e=0;if(!A)return;e=pA[A>>2]|0;if(!e)return;Gf(e);Br(pA[A>>2]|0);pA[A>>2]=0;return}function Je(A){A=A|0;if(!A){A=-1;return A|0}Uf(pA[A>>2]|0)|0;A=0;return A|0}function Oe(A,e,f,i,r,n,t,w,l,B,a,s,o,Q,g,v,u,c,C,D){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;l=l|0;B=B|0;a=a|0;s=s|0;o=o|0;Q=Q|0;g=g|0;v=v|0;u=u|0;c=c|0;C=C|0;D=D|0;var E=0,h=0,P=0,k=0;h=WA;WA=WA+16|0;k=h+2|0;P=h;jA[o>>1]=Lf(pA[A>>2]|0,f,r,t,l,n,40,i,Q,P,k,D)|0;A=jA[k>>1]|0;i=pA[u>>2]|0;pA[u>>2]=i+2;jA[i>>1]=A;Si(t,jA[o>>1]|0,jA[Q>>1]|0,40,jA[P>>1]|0,D);Se(t,n,s,40);jA[g>>1]=Qf(f,l,s,v,40,D)|0;jA[c>>1]=32767;if(B<<16>>16!=0?(E=jA[g>>1]|0,E<<16>>16>15565):0)E=Qi(e,E,D)|0;else E=0;if(f>>>0<2){k=jA[g>>1]|0;jA[g>>1]=k<<16>>16>13926?13926:k;if(E<<16>>16)jA[c>>1]=15565}else{if(E<<16>>16){jA[c>>1]=15565;jA[g>>1]=15565}if((f|0)==7){P=Zf(7,jA[c>>1]|0,g,0,0,C,D)|0;k=pA[u>>2]|0;pA[u>>2]=k+2;jA[k>>1]=P}}o=jA[g>>1]|0;E=0;while(1){P=VA(jA[s>>1]|0,o)|0;jA[a>>1]=(DA[l>>1]|0)-(P>>>14);P=(VA(jA[t>>1]|0,o)|0)>>>14;k=w+(E<<1)|0;jA[k>>1]=(DA[k>>1]|0)-P;E=E+1|0;if((E|0)==40)break;else{t=t+2|0;l=l+2|0;a=a+2|0;s=s+2|0}}WA=h;return}function me(A,e){A=A|0;e=e|0;var f=0,i=0,r=0,n=0;n=WA;WA=WA+16|0;r=n;if(!A){A=-1;WA=n;return A|0}pA[A>>2]=0;f=lr(2532)|0;pA[r>>2]=f;if(!f){A=-1;WA=n;return A|0}bi(f+2392|0);pA[f+2188>>2]=0;pA[(pA[r>>2]|0)+2192>>2]=0;pA[(pA[r>>2]|0)+2196>>2]=0;pA[(pA[r>>2]|0)+2200>>2]=0;pA[(pA[r>>2]|0)+2204>>2]=0;pA[(pA[r>>2]|0)+2208>>2]=0;pA[(pA[r>>2]|0)+2212>>2]=0;pA[(pA[r>>2]|0)+2220>>2]=0;i=pA[r>>2]|0;pA[i+2216>>2]=e;pA[i+2528>>2]=0;f=i;if((((((((ze(i+2196|0)|0)<<16>>16==0?(Yi(i+2192|0)|0)<<16>>16==0:0)?(lf(i+2200|0)|0)<<16>>16==0:0)?(yf(i+2204|0)|0)<<16>>16==0:0)?(Bi(i+2208|0)|0)<<16>>16==0:0)?(vi(i+2212|0)|0)<<16>>16==0:0)?(Ze(i+2220|0,pA[i+2432>>2]|0)|0)<<16>>16==0:0)?(bf(i+2188|0)|0)<<16>>16==0:0){Ke(i)|0;pA[A>>2]=f;A=0;WA=n;return A|0}Ne(r);A=-1;WA=n;return A|0}function Ne(A){A=A|0;var e=0;if(!A)return;e=pA[A>>2]|0;if(!e)return;df(e+2188|0);Xi((pA[A>>2]|0)+2192|0);Bf((pA[A>>2]|0)+2200|0);Xe((pA[A>>2]|0)+2196|0);zf((pA[A>>2]|0)+2204|0);si((pA[A>>2]|0)+2208|0);ci((pA[A>>2]|0)+2212|0);qe((pA[A>>2]|0)+2220|0);Br(pA[A>>2]|0);pA[A>>2]=0;return}function Ke(A){A=A|0;var e=0,f=0,i=0,r=0;if(!A){r=-1;return r|0}pA[A+652>>2]=A+320;pA[A+640>>2]=A+240;pA[A+644>>2]=A+160;pA[A+648>>2]=A+80;pA[A+1264>>2]=A+942;pA[A+1912>>2]=A+1590;i=A+1938|0;pA[A+2020>>2]=i;pA[A+2384>>2]=A+2304;e=A+2028|0;pA[A+2024>>2]=A+2108;pA[A+2528>>2]=0;gr(A|0,0,640)|0;gr(A+1282|0,0,308)|0;gr(A+656|0,0,286)|0;f=A+2224|0;r=i+80|0;do{jA[i>>1]=0;i=i+2|0}while((i|0)<(r|0));i=e;r=i+80|0;do{jA[i>>1]=0;i=i+2|0}while((i|0)<(r|0));e=A+1268|0;i=f;r=i+80|0;do{jA[i>>1]=0;i=i+2|0}while((i|0)<(r|0));jA[e>>1]=40;jA[A+1270>>1]=40;jA[A+1272>>1]=40;jA[A+1274>>1]=40;jA[A+1276>>1]=40;If(pA[A+2188>>2]|0)|0;zi(pA[A+2192>>2]|0)|0;Je(pA[A+2196>>2]|0)|0;af(pA[A+2200>>2]|0)|0;Yf(pA[A+2204>>2]|0)|0;ai(pA[A+2208>>2]|0)|0;ui(pA[A+2212>>2]|0)|0;_e(pA[A+2220>>2]|0,pA[A+2432>>2]|0)|0;jA[A+2388>>1]=0;r=0;return r|0}function xe(A,e,f,i,r,n){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;var t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0,T=0,y=0,Y=0,z=0,X=0,J=0,O=0,m=0,N=0,K=0,x=0,S=0,j=0,p=0,W=0,V=0,Z=0,_=0,q=0,$=0,AA=0,eA=0,fA=0,iA=0,rA=0,nA=0,tA=0,wA=0,lA=0,BA=0,aA=0,sA=0,oA=0,QA=0;QA=WA;WA=WA+1184|0;N=QA;a=QA+1096|0;s=QA+1008|0;l=QA+904|0;wA=QA+928|0;lA=QA+824|0;j=QA+744|0;aA=QA+664|0;sA=QA+584|0;W=QA+328|0;rA=QA+504|0;nA=QA+424|0;BA=QA+344|0;oA=QA+248|0;p=QA+168|0;AA=QA+88|0;fA=QA+68|0;iA=QA+48|0;eA=QA+28|0;tA=QA+24|0;q=QA+22|0;Z=QA+20|0;S=QA+16|0;K=QA+12|0;x=QA+10|0;_=QA+8|0;V=QA+6|0;$=QA+4|0;pA[N>>2]=i;m=A+2528|0;t=A+652|0;or(pA[t>>2]|0,f|0,320)|0;pA[r>>2]=e;B=A+2216|0;if(!(pA[B>>2]|0)){f=A+2220|0;i=0}else{ZA()}O=A+2392|0;Mf(pA[A+2188>>2]|0,e,pA[A+644>>2]|0,pA[A+648>>2]|0,a,O,m);w=A+2192|0;Ji(pA[w>>2]|0,e,pA[r>>2]|0,a,s,l,N,m);$e(pA[f>>2]|0,l,pA[t>>2]|0,m);if((pA[r>>2]|0)==8){ZA()}else J=oi(pA[A+2208>>2]|0,pA[w>>2]|0,m)|0;Y=A+640|0;w=A+2264|0;t=A+1264|0;f=A+2204|0;i=A+2212|0;z=A+1268|0;X=A+1278|0;Jf(e,2842,2862,2882,a,0,pA[Y>>2]|0,w,pA[t>>2]|0,m);if(e>>>0>1){Ff(pA[f>>2]|0,pA[i>>2]|0,e,pA[t>>2]|0,S,z,X,0,pA[B>>2]|0,m);Jf(e,2842,2862,2882,a,80,pA[Y>>2]|0,w,pA[t>>2]|0,m);Ff(pA[f>>2]|0,pA[i>>2]|0,e,(pA[t>>2]|0)+160|0,S+2|0,z,X,1,pA[B>>2]|0,m)}else{Jf(e,2842,2862,2882,a,80,pA[Y>>2]|0,w,pA[t>>2]|0,m);Ff(pA[f>>2]|0,pA[i>>2]|0,e,pA[t>>2]|0,S,z,X,1,pA[B>>2]|0,m);jA[S+2>>1]=jA[S>>1]|0}if(pA[B>>2]|0)ZA();if((pA[r>>2]|0)==8){ZA()}P=A+2224|0;k=A+2244|0;b=A+2284|0;d=A+2388|0;I=A+2020|0;M=A+1916|0;F=A+1912|0;H=A+2024|0;U=A+2384|0;G=A+2196|0;L=A+2208|0;R=A+2464|0;T=A+2200|0;y=A+2224|0;D=A+2244|0;E=A+1270|0;h=A+1280|0;C=0;B=0;l=0;v=0;u=0;w=0;c=-1;while(1){o=c;c=c+1<<16>>16;v=1-(v<<16>>16)|0;i=v&65535;g=(v&65535|0)!=0;f=pA[r>>2]|0;t=(f|0)==0;do{if(g)if(t){t=fA;f=P;Q=t+20|0;do{jA[t>>1]=jA[f>>1]|0;t=t+2|0;f=f+2|0}while((t|0)<(Q|0));t=iA;f=k;Q=t+20|0;do{jA[t>>1]=jA[f>>1]|0;t=t+2|0;f=f+2|0}while((t|0)<(Q|0));t=eA;f=b;Q=t+20|0;do{jA[t>>1]=jA[f>>1]|0;t=t+2|0;f=f+2|0}while((t|0)<(Q|0));jA[tA>>1]=jA[d>>1]|0;e=(pA[Y>>2]|0)+(C<<1)|0;t=20;break}else{e=(pA[Y>>2]|0)+(C<<1)|0;t=19;break}else{e=(pA[Y>>2]|0)+(C<<1)|0;if(t)t=20;else t=19}}while(0);if((t|0)==19)wi(f,2842,2862,2882,a,s,e,b,D,pA[I>>2]|0,M,(pA[F>>2]|0)+(C<<1)|0,pA[H>>2]|0,wA,rA,pA[U>>2]|0);else if((t|0)==20?(0,wi(0,2842,2862,2882,a,s,e,b,iA,pA[I>>2]|0,M,(pA[F>>2]|0)+(C<<1)|0,pA[H>>2]|0,wA,rA,pA[U>>2]|0),g):0){t=AA;f=pA[H>>2]|0;Q=t+80|0;do{jA[t>>1]=jA[f>>1]|0;t=t+2|0;f=f+2|0}while((t|0)<(Q|0))}t=nA;f=rA;Q=t+80|0;do{jA[t>>1]=jA[f>>1]|0;t=t+2|0;f=f+2|0}while((t|0)<(Q|0));Oe(pA[G>>2]|0,pA[L>>2]|0,pA[r>>2]|0,u,S,pA[H>>2]|0,(pA[F>>2]|0)+(C<<1)|0,nA,wA,J,lA,aA,K,x,_,W,N,$,pA[R>>2]|0,m);switch(o<<16>>16){case-1:{if((jA[X>>1]|0)>0)jA[E>>1]=jA[K>>1]|0;break}case 2:{if((jA[h>>1]|0)>0)jA[z>>1]=jA[K>>1]|0;break}default:{}}Ye(lA,pA[H>>2]|0,jA[K>>1]|0,jA[d>>1]|0,jA[_>>1]|0,nA,j,sA,N,pA[r>>2]|0,c,O,m);sf(pA[T>>2]|0,pA[r>>2]|0,rA,(pA[F>>2]|0)+(C<<1)|0,j,wA,lA,aA,sA,W,i,jA[$>>1]|0,q,Z,_,V,N,O,m);gi(pA[L>>2]|0,jA[_>>1]|0,m);e=pA[r>>2]|0;do{if(!e)if(g){t=BA;f=wA;Q=t+80|0;do{jA[t>>1]=jA[f>>1]|0;t=t+2|0;f=f+2|0}while((t|0)<(Q|0));t=oA;f=sA;Q=t+80|0;do{jA[t>>1]=jA[f>>1]|0;t=t+2|0;f=f+2|0}while((t|0)<(Q|0));t=p;f=j;Q=t+80|0;do{jA[t>>1]=jA[f>>1]|0;t=t+2|0;f=f+2|0}while((t|0)<(Q|0));l=jA[K>>1]|0;B=jA[x>>1]|0;li(pA[Y>>2]|0,0,u,jA[_>>1]|0,jA[V>>1]|0,s,n,wA,j,aA,sA,fA,b,iA,pA[F>>2]|0,d,m);jA[d>>1]=jA[tA>>1]|0;w=u;break}else{t=b;f=eA;Q=t+20|0;do{jA[t>>1]=jA[f>>1]|0;t=t+2|0;f=f+2|0}while((t|0)<(Q|0));g=w<<16>>16;Si((pA[F>>2]|0)+(g<<1)|0,l,B,40,1,m);Se((pA[F>>2]|0)+(g<<1)|0,AA,aA,40);li(pA[Y>>2]|0,pA[r>>2]|0,w,jA[q>>1]|0,jA[Z>>1]|0,s+-22|0,n,BA,p,aA,oA,y,b,D,pA[F>>2]|0,tA,m);wi(pA[r>>2]|0,2842,2862,2882,a,s,(pA[Y>>2]|0)+(C<<1)|0,b,D,pA[I>>2]|0,M,(pA[F>>2]|0)+(C<<1)|0,pA[H>>2]|0,wA,rA,pA[U>>2]|0);Si((pA[F>>2]|0)+(C<<1)|0,jA[K>>1]|0,jA[x>>1]|0,40,1,m);Se((pA[F>>2]|0)+(C<<1)|0,pA[H>>2]|0,aA,40);li(pA[Y>>2]|0,pA[r>>2]|0,u,jA[_>>1]|0,jA[V>>1]|0,s,n,wA,j,aA,sA,y,b,D,pA[F>>2]|0,d,m);break}else li(pA[Y>>2]|0,e,u,jA[_>>1]|0,jA[V>>1]|0,s,n,wA,j,aA,sA,y,b,D,pA[F>>2]|0,d,m)}while(0);e=C+40|0;u=e&65535;if(u<<16>>16>=160)break;else{C=e<<16>>16;a=a+22|0;s=s+22|0}}or(A+1282|0,A+1602|0,308)|0;sA=A+656|0;oA=A+976|0;or(sA|0,oA|0,286)|0;oA=A+320|0;or(A|0,oA|0,320)|0;WA=QA;return 0}function Se(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var r=0,n=0,t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0;Q=i<<16>>16;if(i<<16>>16>1)o=1;else return;while(1){r=jA[A>>1]|0;w=e+(o+-1<<1)|0;i=VA(jA[e+(o<<1)>>1]|0,r)|0;B=jA[w>>1]|0;r=VA(B<<16>>16,r)|0;t=(o+131071|0)>>>1;l=t&65535;n=jA[A+2>>1]|0;if(!(l<<16>>16)){e=w;t=B}else{a=(t<<1)+131070&131070;s=o-a|0;t=A;do{v=(VA(B<<16>>16,n)|0)+i|0;g=t;t=t+4|0;i=jA[w+-2>>1]|0;n=(VA(i,n)|0)+r|0;r=jA[t>>1]|0;w=w+-4|0;i=v+(VA(r,i)|0)|0;B=jA[w>>1]|0;r=n+(VA(B<<16>>16,r)|0)|0;l=l+-1<<16>>16;n=jA[g+6>>1]|0}while(l<<16>>16!=0);t=e+(s+-3<<1)|0;A=A+(a+2<<1)|0;e=t;t=jA[t>>1]|0}i=(VA(t<<16>>16,n)|0)+i|0;jA[f>>1]=r>>>12;jA[f+2>>1]=i>>>12;i=(o<<16)+131072>>16;if((i|0)<(Q|0)){f=f+4|0;A=A+(1-o<<1)|0;o=i}else break}return}function je(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var r=0,n=0,t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0;k=WA;WA=WA+80|0;P=k;t=20;n=A;r=1;while(1){h=jA[n>>1]|0;h=(VA(h,h)|0)+r|0;r=jA[n+2>>1]|0;r=h+(VA(r,r)|0)|0;t=t+-1<<16>>16;if(!(t<<16>>16))break;else n=n+4|0}r=r<<1;if((r|0)<0){n=20;r=A;i=P;while(1){jA[i>>1]=(jA[r>>1]|0)>>>1;jA[i+2>>1]=(jA[r+2>>1]|0)>>>1;n=n+-1<<16>>16;if(!(n<<16>>16)){h=P;break}else{r=r+4|0;i=i+4|0}}}else{r=Ui(r>>1,i)|0;if((r|0)<16777215)r=((r>>9)*32440|0)>>>15<<16>>16;else r=32440;t=20;n=A;i=P;while(1){jA[i>>1]=((VA(jA[n>>1]|0,r)|0)+32|0)>>>6;jA[i+2>>1]=((VA(jA[n+2>>1]|0,r)|0)+32|0)>>>6;t=t+-1<<16>>16;if(!(t<<16>>16)){h=P;break}else{n=n+4|0;i=i+4|0}}}t=20;n=h;i=f+3198|0;r=0;while(1){E=jA[n>>1]|0;E=(VA(E,E)|0)+r|0;jA[i>>1]=(E+16384|0)>>>15;D=jA[n+2>>1]|0;r=(VA(D,D)|0)+E|0;jA[i+-82>>1]=(r+16384|0)>>>15;t=t+-1<<16>>16;if(!(t<<16>>16))break;else{n=n+4|0;i=i+-164|0}}E=e+78|0;D=1;while(1){r=39-D|0;A=f+3120+(r<<1)|0;i=f+(r*80|0)+78|0;r=e+(r<<1)|0;l=P+(D<<1)|0;n=65575-D|0;w=n&65535;t=jA[h>>1]|0;if(!(w<<16>>16)){w=E;n=0}else{v=n+65535&65535;c=v*41|0;C=(VA(D,-40)|0)-c|0;u=0-D|0;c=u-c|0;u=u-v|0;g=D+v|0;Q=jA[l>>1]|0;s=h;o=E;B=f+((38-D|0)*80|0)+78|0;n=0;a=0;while(1){l=l+2|0;n=(VA(Q<<16>>16,t)|0)+n|0;s=s+2|0;Q=jA[l>>1]|0;a=(VA(Q<<16>>16,t)|0)+a|0;d=r;r=r+-2|0;t=jA[r>>1]|0;b=jA[o>>1]<<1;d=(VA((VA(b,jA[d>>1]|0)|0)>>16,(n<<1)+32768>>16)|0)>>>15&65535;jA[i>>1]=d;jA[A>>1]=d;t=(VA((VA(b,t)|0)>>16,(a<<1)+32768>>16)|0)>>>15&65535;jA[A+-2>>1]=t;jA[B>>1]=t;w=w+-1<<16>>16;t=jA[s>>1]|0;if(!(w<<16>>16))break;else{o=o+-2|0;A=A+-82|0;i=i+-82|0;B=B+-82|0}}l=P+(g+1<<1)|0;w=e+(38-v<<1)|0;r=e+(u+38<<1)|0;A=f+3040+(c+38<<1)|0;i=f+3040+(C+38<<1)|0}d=(VA(jA[l>>1]|0,t)|0)+n|0;d=(VA((d<<1)+32768>>16,(VA(jA[w>>1]<<1,jA[r>>1]|0)|0)>>16)|0)>>>15&65535;jA[A>>1]=d;jA[i>>1]=d;i=(D<<16)+131072|0;if((i|0)<2621440)D=i>>16;else break}WA=k;return}function pe(A,e,f,i,r,n,t){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;var w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0;g=WA;WA=WA+160|0;Q=g;if(r<<16>>16>0){s=n&65535;o=0;w=5;do{if((o|0)<40){a=o;B=o&65535;n=0;while(1){if(B<<16>>16<40){B=B<<16>>16;l=0;do{l=(VA(jA[A+(B-a<<1)>>1]|0,jA[e+(B<<1)>>1]|0)|0)+l|0;B=B+1|0}while((B&65535)<<16>>16!=40)}else l=0;l=l<<1;pA[Q+(a<<2)>>2]=l;l=vf(l)|0;n=(l|0)>(n|0)?l:n;l=a+s|0;B=l&65535;if(B<<16>>16>=40)break;else a=l<<16>>16}}else n=0;w=(n>>1)+w|0;o=o+1|0}while((o&65535)<<16>>16!=r<<16>>16)}else w=5;i=((Ni(w)|0)&65535)-(i&65535)|0;n=i<<16>>16;l=0-n<<16;w=(l|0)<2031616;l=l>>16;if((i&65535)<<16>>16>0)if(w){w=0;do{i=pA[Q+(w<<2)>>2]|0;e=i<<n;jA[f+(w<<1)>>1]=er((e>>n|0)==(i|0)?e:i>>31^2147483647,t)|0;w=w+1|0}while((w|0)!=40);WA=g;return}else{ZA()}else if(w){w=0;do{jA[f+(w<<1)>>1]=er(pA[Q+(w<<2)>>2]>>l,t)|0;w=w+1|0}while((w|0)!=40);WA=g;return}else{ZA()}}function We(A,e,f,i,r){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;var n=0,t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0;P=WA;WA=WA+160|0;h=P;C=A+2|0;D=jA[A>>1]|0;E=0;r=5;do{c=E;w=0;while(1){a=e+(c<<1)|0;u=40-c|0;n=(u+131071|0)>>>1&65535;l=e+(c+1<<1)|0;t=VA(jA[a>>1]<<1,D)|0;if(!(n<<16>>16))n=C;else{v=131111-c+131070&131070;g=c+v|0;Q=C;o=A;s=a;while(1){B=s+4|0;a=o+4|0;t=(VA(jA[l>>1]<<1,jA[Q>>1]|0)|0)+t|0;n=n+-1<<16>>16;t=(VA(jA[B>>1]<<1,jA[a>>1]|0)|0)+t|0;if(!(n<<16>>16))break;else{l=s+6|0;Q=o+6|0;o=a;s=B}}l=e+(g+3<<1)|0;n=A+(v+3<<1)|0}if(!(u&1))t=(VA(jA[l>>1]<<1,jA[n>>1]|0)|0)+t|0;pA[h+(c<<2)>>2]=t;t=(t|0)<0?0-t|0:t;w=(t|0)>(w|0)?t:w;t=c+5|0;if((t&65535)<<16>>16<40)c=t<<16>>16;else break}r=(w>>1)+r|0;E=E+1|0}while((E|0)!=5);i=((Ni(r)|0)&65535)-(i&65535)|0;t=i<<16>>16;r=0-t<<16;w=r>>16;if((i&65535)<<16>>16>0){n=20;r=h;while(1){h=pA[r>>2]|0;i=h<<t;jA[f>>1]=(((i>>t|0)==(h|0)?i:h>>31^2147483647)+32768|0)>>>16;h=pA[r+4>>2]|0;i=h<<t;jA[f+2>>1]=(((i>>t|0)==(h|0)?i:h>>31^2147483647)+32768|0)>>>16;n=n+-1<<16>>16;if(!(n<<16>>16))break;else{f=f+4|0;r=r+8|0}}WA=P;return}if((r|0)<2031616){n=20;r=h;while(1){jA[f>>1]=((pA[r>>2]>>w)+32768|0)>>>16;jA[f+2>>1]=((pA[r+4>>2]>>w)+32768|0)>>>16;n=n+-1<<16>>16;if(!(n<<16>>16))break;else{f=f+4|0;r=r+8|0}}WA=P;return}else{ZA()}}function Ve(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var r=0,n=0,t=0;t=(Ei(16383,e)|0)<<16>>16;e=VA(t,e<<16>>16)|0;if((e|0)==1073741824){pA[i>>2]=1;r=2147483647}else r=e<<1;n=(VA(t,f<<16>>16)|0)>>15;e=r+(n<<1)|0;if((r^n|0)>0&(e^r|0)<0){pA[i>>2]=1;e=(r>>>31)+2147483647|0}r=2147483647-e|0;f=r>>16;e=VA(f,t)|0;if((e|0)==1073741824){pA[i>>2]=1;n=2147483647}else n=e<<1;t=(VA((r>>>1)-(f<<15)<<16>>16,t)|0)>>15;e=n+(t<<1)|0;if((n^t|0)>0&(e^n|0)<0){pA[i>>2]=1;e=(n>>>31)+2147483647|0}n=e>>16;t=A>>16;f=VA(n,t)|0;f=(f|0)==1073741824?2147483647:f<<1;r=(VA((e>>>1)-(n<<15)<<16>>16,t)|0)>>15;i=(r<<1)+f|0;i=(r^f|0)>0&(i^f|0)<0?(f>>>31)+2147483647|0:i;t=(VA(n,(A>>>1)-(t<<15)<<16>>16)|0)>>15;A=i+(t<<1)|0;A=(i^t|0)>0&(A^i|0)<0?(i>>>31)+2147483647|0:A;i=A<<2;return((i>>2|0)==(A|0)?i:A>>31^2147483647)|0}function Ze(A,e){A=A|0;e=e|0;var f=0,i=0,r=0,n=0;if(!A){n=-1;return n|0}pA[A>>2]=0;f=lr(192)|0;if(!f){n=-1;return n|0}i=f+176|0;jA[i>>1]=0;jA[i+2>>1]=0;jA[i+4>>1]=0;jA[i+6>>1]=0;jA[i+8>>1]=0;jA[i+10>>1]=0;i=f;r=e;n=i+20|0;do{jA[i>>1]=jA[r>>1]|0;i=i+2|0;r=r+2|0}while((i|0)<(n|0));i=f+20|0;r=e;n=i+20|0;do{jA[i>>1]=jA[r>>1]|0;i=i+2|0;r=r+2|0}while((i|0)<(n|0));i=f+40|0;r=e;n=i+20|0;do{jA[i>>1]=jA[r>>1]|0;i=i+2|0;r=r+2|0}while((i|0)<(n|0));i=f+60|0;r=e;n=i+20|0;do{jA[i>>1]=jA[r>>1]|0;i=i+2|0;r=r+2|0}while((i|0)<(n|0));i=f+80|0;r=e;n=i+20|0;do{jA[i>>1]=jA[r>>1]|0;i=i+2|0;r=r+2|0}while((i|0)<(n|0));i=f+100|0;r=e;n=i+20|0;do{jA[i>>1]=jA[r>>1]|0;i=i+2|0;r=r+2|0}while((i|0)<(n|0));i=f+120|0;r=e;n=i+20|0;do{jA[i>>1]=jA[r>>1]|0;i=i+2|0;r=r+2|0}while((i|0)<(n|0));i=f+140|0;r=e;n=i+20|0;do{jA[i>>1]=jA[r>>1]|0;i=i+2|0;r=r+2|0}while((i|0)<(n|0));i=f+160|0;n=i+20|0;do{jA[i>>1]=0;i=i+2|0}while((i|0)<(n|0));jA[f+188>>1]=7;jA[f+190>>1]=32767;pA[A>>2]=f;n=0;return n|0}function _e(A,e){A=A|0;e=e|0;var f=0,i=0,r=0;if(!A){r=-1;return r|0}f=A+176|0;jA[f>>1]=0;jA[f+2>>1]=0;jA[f+4>>1]=0;jA[f+6>>1]=0;jA[f+8>>1]=0;jA[f+10>>1]=0;f=A;i=e;r=f+20|0;do{jA[f>>1]=jA[i>>1]|0;f=f+2|0;i=i+2|0}while((f|0)<(r|0));f=A+20|0;i=e;r=f+20|0;do{jA[f>>1]=jA[i>>1]|0;f=f+2|0;i=i+2|0}while((f|0)<(r|0));f=A+40|0;i=e;r=f+20|0;do{jA[f>>1]=jA[i>>1]|0;f=f+2|0;i=i+2|0}while((f|0)<(r|0));f=A+60|0;i=e;r=f+20|0;do{jA[f>>1]=jA[i>>1]|0;f=f+2|0;i=i+2|0}while((f|0)<(r|0));f=A+80|0;i=e;r=f+20|0;do{jA[f>>1]=jA[i>>1]|0;f=f+2|0;i=i+2|0}while((f|0)<(r|0));f=A+100|0;i=e;r=f+20|0;do{jA[f>>1]=jA[i>>1]|0;f=f+2|0;i=i+2|0}while((f|0)<(r|0));f=A+120|0;i=e;r=f+20|0;do{jA[f>>1]=jA[i>>1]|0;f=f+2|0;i=i+2|0}while((f|0)<(r|0));f=A+140|0;i=e;r=f+20|0;do{jA[f>>1]=jA[i>>1]|0;f=f+2|0;i=i+2|0}while((f|0)<(r|0));f=A+160|0;r=f+20|0;do{jA[f>>1]=0;f=f+2|0}while((f|0)<(r|0));jA[A+188>>1]=7;jA[A+190>>1]=32767;r=1;return r|0}function qe(A){A=A|0;var e=0;if(!A)return;e=pA[A>>2]|0;if(!e)return;Br(e);pA[A>>2]=0;return}function $e(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var r=0,n=0,t=0,w=0,l=0,B=0;B=WA;WA=WA+16|0;t=B+2|0;l=B;w=A+176|0;n=(DA[w>>1]|0)+1|0;n=(n&65535|0)==8?0:n&65535;jA[w>>1]=n;n=A+((n<<16>>16)*10<<1)|0;r=n+20|0;do{jA[n>>1]=jA[e>>1]|0;n=n+2|0;e=e+2|0}while((n|0)<(r|0));e=0;r=160;while(1){n=jA[f>>1]|0;e=(VA(n<<1,n)|0)+e|0;if((e|0)<0){e=2147483647;break}r=r+-1<<16>>16;if(!(r<<16>>16))break;else f=f+2|0}Gi(e,t,l,i);e=jA[t>>1]|0;t=e<<16>>16;f=t<<10;if((f|0)!=(t<<26>>16|0)){pA[i>>2]=1;f=e<<16>>16>0?32767:-32768}jA[A+160+(jA[w>>1]<<1)>>1]=(((jA[l>>1]|0)>>>5)+f<<16)+-558432256>>17;WA=B;return}function Af(A,e,f,i,r,n,t,w){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;if(!(n<<16>>16)){n=A<<16>>16;if(((n<<16)+-5570560|0)<65536){e=(n*3|0)+-58+(e<<16>>16)|0;e=e&65535;return e|0}else{e=n+112|0;e=e&65535;return e|0}}if(!(t<<16>>16)){w=(A&65535)-(i&65535)<<16;e=(e<<16>>16)+2+(w>>15)+(w>>16)|0;e=e&65535;return e|0}i=i<<16>>16;i=(((f&65535)-i<<16)+-327680|0)>0?i+5&65535:f;r=r<<16>>16;f=A<<16>>16;i=(((r-(i&65535)<<16)+-262144|0)>0?r+65532&65535:i)<<16>>16;r=i*196608|0;A=r+-393216>>16;n=((e&65535)<<16)+(f*196608|0)>>16;if(!(A-n&32768)){e=f+5-i|0;e=e&65535;return e|0}if((r+196608>>16|0)>(n|0)){e=n+3-A|0;e=e&65535;return e|0}else{e=f+11-i|0;e=e&65535;return e|0}return 0}function ef(A,e,f,i,r){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;r=A<<16>>16;do{if(!(i<<16>>16))if(A<<16>>16<95){r=((r*393216|0)+-6881280>>16)+(e<<16>>16)|0;break}else{r=r+368|0;break}else r=((((r-(f&65535)|0)*393216|0)+196608|0)>>>16)+(e&65535)|0}while(0);return r&65535|0}function ff(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var r=0,n=0,t=0,w=0,l=0,B=0,a=0,s=0;a=pA[i+100>>2]|0;B=pA[i+96>>2]|0;SA[f>>0]=A<<3;B=B+(A<<1)|0;r=jA[B>>1]|0;if(A>>>0>=8){ZA()}t=r<<16>>16;if(r<<16>>16>7){r=a+(A<<2)|0;i=0;l=0;n=1;while(1){s=DA[e+(jA[(pA[r>>2]|0)+(i<<1)>>1]<<1)>>1]<<7;t=f+(n<<16>>16)|0;SA[t>>0]=s;s=DA[e+(jA[(pA[r>>2]|0)+((l|1)<<16>>16<<1)>>1]<<1)>>1]<<6|s;SA[t>>0]=s;s=DA[e+(jA[(pA[r>>2]|0)+((l|2)<<16>>16<<1)>>1]<<1)>>1]<<5|s;SA[t>>0]=s;s=DA[e+(jA[(pA[r>>2]|0)+((l|3)<<16>>16<<1)>>1]<<1)>>1]<<4|s;SA[t>>0]=s;s=DA[e+(jA[(pA[r>>2]|0)+((l|4)<<16>>16<<1)>>1]<<1)>>1]<<3|s&240;SA[t>>0]=s;s=DA[e+(jA[(pA[r>>2]|0)+((l|5)<<16>>16<<1)>>1]<<1)>>1]<<2|s;SA[t>>0]=s;s=DA[e+(jA[(pA[r>>2]|0)+((l|6)<<16>>16<<1)>>1]<<1)>>1]<<1|s;SA[t>>0]=s;w=l+8<<16>>16;n=n+1<<16>>16;SA[t>>0]=s&254|DA[e+(jA[(pA[r>>2]|0)+((l|7)<<16>>16<<1)>>1]<<1)>>1];i=w<<16>>16;t=jA[B>>1]|0;if((i|0)>=(t+-7|0))break;else l=w}}else{w=0;n=1}B=t&7;l=f+(n<<16>>16)|0;SA[l>>0]=0;if(!B)return;n=a+(A<<2)|0;r=0;i=0;t=0;while(1){i=(DA[e+(jA[(pA[n>>2]|0)+(w<<16>>16<<1)>>1]<<1)>>1]&255)<<7-r|i&255;SA[l>>0]=i;t=t+1<<16>>16;r=t<<16>>16;if((r|0)>=(B|0))break;else w=w+1<<16>>16}return}function rf(A){A=A|0;var e=0;if(!A){A=-1;return A|0}pA[A>>2]=0;e=lr(16)|0;if(!e){A=-1;return A|0}jA[e>>1]=0;jA[e+2>>1]=0;jA[e+4>>1]=0;jA[e+6>>1]=0;jA[e+8>>1]=0;jA[e+10>>1]=0;jA[e+12>>1]=0;jA[e+14>>1]=0;pA[A>>2]=e;A=0;return A|0}function nf(A){A=A|0;if(!A){A=-1;return A|0}jA[A>>1]=0;jA[A+2>>1]=0;jA[A+4>>1]=0;jA[A+6>>1]=0;jA[A+8>>1]=0;jA[A+10>>1]=0;jA[A+12>>1]=0;jA[A+14>>1]=0;A=0;return A|0}function tf(A){A=A|0;var e=0;if(!A)return;e=pA[A>>2]|0;if(!e)return;Br(e);pA[A>>2]=0;return}function wf(A,e,f,i,r){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;var n=0,t=0,w=0,l=0,B=0;w=e<<16>>16<2722?0:e<<16>>16<5444?1:2;t=ir(f,1,r)|0;B=A+4|0;if(!(f<<16>>16>200?t<<16>>16>(jA[B>>1]|0):0)){t=jA[A>>1]|0;if(t<<16>>16){n=t+-1<<16>>16;jA[A>>1]=n;n=n<<16>>16!=0;l=5}}else{jA[A>>1]=8;n=1;l=5}if((l|0)==5)if((w&65535)<2&n)w=(w&65535)+1&65535;l=A+6|0;jA[l>>1]=e;n=di(l,5)|0;if(!(w<<16>>16!=0|n<<16>>16>5443))if(n<<16>>16<0)n=16384;else{n=n<<16>>16;n=(((n<<18>>18|0)==(n|0)?n<<2:n>>>15^32767)<<16>>16)*24660>>15;if((n|0)>32767){pA[r>>2]=1;n=32767}n=16384-n&65535}else n=0;t=A+2|0;if(!(jA[t>>1]|0))n=fr(n,1,r)|0;jA[i>>1]=n;jA[t>>1]=n;jA[B>>1]=f;i=A+12|0;jA[A+14>>1]=jA[i>>1]|0;f=A+10|0;jA[i>>1]=jA[f>>1]|0;A=A+8|0;jA[f>>1]=jA[A>>1]|0;jA[A>>1]=jA[l>>1]|0;return}function lf(A){A=A|0;var e=0,f=0,i=0,r=0,n=0,t=0;if(!A){A=-1;return A|0}pA[A>>2]=0;e=lr(68)|0;i=e;if(!e){A=-1;return A|0}pA[e+28>>2]=0;r=e+64|0;pA[r>>2]=0;n=e+32|0;if(((hi(n)|0)<<16>>16==0?(t=e+48|0,(hi(t)|0)<<16>>16==0):0)?(rf(r)|0)<<16>>16==0:0){f=e+32|0;do{jA[e>>1]=0;e=e+2|0}while((e|0)<(f|0));hi(n)|0;hi(t)|0;nf(pA[r>>2]|0)|0;pA[A>>2]=i;A=0;return A|0}tf(r);Br(e);A=-1;return A|0}function Bf(A){A=A|0;var e=0;if(!A)return;e=pA[A>>2]|0;if(!e)return;tf(e+64|0);Br(pA[A>>2]|0);pA[A>>2]=0;return}function af(A){A=A|0;var e=0,f=0,i=0;if(!A){i=-1;return i|0}e=A+32|0;f=A;i=f+32|0;do{jA[f>>1]=0;f=f+2|0}while((f|0)<(i|0));hi(e)|0;hi(A+48|0)|0;nf(pA[A+64>>2]|0)|0;i=0;return i|0}function sf(A,e,f,i,r,n,t,w,l,B,a,s,o,Q,g,v,u,c,C){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;l=l|0;B=B|0;a=a|0;s=s|0;o=o|0;Q=Q|0;g=g|0;v=v|0;u=u|0;c=c|0;C=C|0;var D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0;H=WA;WA=WA+48|0;E=H+34|0;P=H+32|0;b=H+30|0;k=H+28|0;h=H+18|0;D=H+8|0;d=H+6|0;I=H+4|0;M=H+2|0;F=H;if(e){a=A+32|0;Pi(a,e,r,E,P,d,I,C);do{if((e|0)!=7){Te(e,n,t,w,l,B,h,D,F,M,C);if((e|0)==5){Wf(pA[A+64>>2]|0,f,i,r,h,D,jA[d>>1]|0,jA[I>>1]|0,jA[E>>1]|0,jA[P>>1]|0,40,jA[F>>1]|0,jA[M>>1]|0,s,g,v,b,k,u,c,C);break}else{A=_f(e,jA[E>>1]|0,jA[P>>1]|0,h,D,s,g,v,b,k,c,C)|0;n=pA[u>>2]|0;pA[u>>2]=n+2;jA[n>>1]=A;break}}else{jA[v>>1]=of(t,l,C)|0;A=Vf(7,jA[E>>1]|0,jA[P>>1]|0,v,b,k,pA[c+68>>2]|0,C)|0;n=pA[u>>2]|0;pA[u>>2]=n+2;jA[n>>1]=A}}while(0);ki(a,jA[b>>1]|0,jA[k>>1]|0);WA=H;return}if(!(a<<16>>16)){Pi(A+48|0,0,r,E,P,d,I,C);Te(0,n,t,w,l,B,h,D,F,M,C);ye(n,d,I,C);n=jf(A+32|0,jA[A>>1]|0,jA[A+2>>1]|0,A+8|0,A+18|0,jA[A+4>>1]|0,jA[A+6>>1]|0,r,jA[E>>1]|0,jA[P>>1]|0,D,h,jA[d>>1]|0,jA[I>>1]|0,s,o,Q,g,v,C)|0;jA[pA[A+28>>2]>>1]=n;WA=H;return}a=pA[u>>2]|0;pA[u>>2]=a+2;pA[A+28>>2]=a;a=A+48|0;f=A+32|0;o=f;o=DA[o>>1]|DA[o+2>>1]<<16;f=f+4|0;f=DA[f>>1]|DA[f+2>>1]<<16;u=a;Q=u;jA[Q>>1]=o;jA[Q+2>>1]=o>>>16;u=u+4|0;jA[u>>1]=f;jA[u+2>>1]=f>>>16;u=A+40|0;f=u;f=DA[f>>1]|DA[f+2>>1]<<16;u=u+4|0;u=DA[u>>1]|DA[u+2>>1]<<16;Q=A+56|0;o=Q;jA[o>>1]=f;jA[o+2>>1]=f>>>16;Q=Q+4|0;jA[Q>>1]=u;jA[Q+2>>1]=u>>>16;Q=A+2|0;Pi(a,0,r,A,Q,d,I,C);Te(0,n,t,w,l,B,A+18|0,A+8|0,F,M,C);w=(DA[M>>1]|0)+1|0;u=jA[F>>1]|0;o=w<<16>>16;if((w&65535)<<16>>16<0){c=0-o<<16;if((c|0)<983040)c=u<<16>>16>>(c>>16)&65535;else c=0}else{u=u<<16>>16;c=u<<o;if((c<<16>>16>>o|0)==(u|0))c=c&65535;else c=(u>>>15^32767)&65535}jA[v>>1]=c;ye(n,A+4|0,A+6|0,C);Sf(a,jA[A>>1]|0,jA[Q>>1]|0,jA[M>>1]|0,jA[F>>1]|0,C);WA=H;return}function of(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,r=0,n=0;r=10;f=A;i=e;A=0;while(1){A=(VA(jA[i>>1]>>1,jA[f>>1]|0)|0)+A|0;A=A+(VA(jA[i+2>>1]>>1,jA[f+2>>1]|0)|0)|0;A=A+(VA(jA[i+4>>1]>>1,jA[f+4>>1]|0)|0)|0;A=A+(VA(jA[i+6>>1]>>1,jA[f+6>>1]|0)|0)|0;r=r+-1<<16>>16;if(!(r<<16>>16))break;else{f=f+8|0;i=i+8|0}}f=A<<1;r=Ni(f|1)|0;n=r<<16>>16;f=(r<<16>>16<17?f>>17-n:f<<n+-17)&65535;if(f<<16>>16<1){e=0;return e|0}else{r=20;i=e;A=0}while(1){e=jA[i>>1]>>1;e=((VA(e,e)|0)>>>2)+A|0;A=jA[i+2>>1]>>1;A=e+((VA(A,A)|0)>>>2)|0;r=r+-1<<16>>16;if(!(r<<16>>16))break;else i=i+4|0}A=A<<3;r=Ni(A)|0;e=r<<16>>16;f=Ei(f,(r<<16>>16<16?A>>16-e:A<<e+-16)&65535)|0;e=(n<<16)+327680-(e<<16)|0;A=e>>16;if((e|0)>65536)A=f<<16>>16>>A+-1;else A=f<<16>>16<<1-A;e=A&65535;return e|0}function Qf(A,e,f,i,r,n){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;var t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0;pA[n>>2]=0;a=r<<16>>16;l=a>>>2&65535;o=l<<16>>16==0;if(o)w=0;else{B=l;t=f;w=0;while(1){Q=jA[t>>1]|0;Q=(VA(Q,Q)|0)+w|0;w=jA[t+2>>1]|0;w=Q+(VA(w,w)|0)|0;Q=jA[t+4>>1]|0;Q=w+(VA(Q,Q)|0)|0;w=jA[t+6>>1]|0;w=Q+(VA(w,w)|0)|0;B=B+-1<<16>>16;if(!(B<<16>>16))break;else t=t+8|0}}if(!((w>>>31^1)&(w|0)<1073741824)){w=a>>>1&65535;if(!(w<<16>>16))w=1;else{t=w;B=f;w=0;while(1){Q=jA[B>>1]>>2;Q=(VA(Q,Q)|0)+w|0;w=jA[B+2>>1]>>2;w=Q+(VA(w,w)|0)|0;t=t+-1<<16>>16;if(!(t<<16>>16))break;else B=B+4|0}w=w<<1|1}Q=(Ni(w)|0)<<16>>16;s=Q+65532&65535;Q=er(w<<Q,n)|0}else{a=w<<1|1;Q=Ni(a)|0;s=Q;Q=er(a<<(Q<<16>>16),n)|0}pA[n>>2]=0;do{if(!(r<<16>>16)){w=1;g=14}else{a=r;B=e;w=f;r=0;while(1){v=VA(jA[w>>1]|0,jA[B>>1]|0)|0;t=v+r|0;if((v^r|0)>0&(t^r|0)<0)break;a=a+-1<<16>>16;if(!(a<<16>>16)){g=13;break}else{B=B+2|0;w=w+2|0;r=t}}if((g|0)==13){w=t<<1|1;g=14;break}pA[n>>2]=1;if(o)w=1;else{w=e;t=0;while(1){t=(VA(jA[f>>1]>>2,jA[w>>1]|0)|0)+t|0;t=t+(VA(jA[f+2>>1]>>2,jA[w+2>>1]|0)|0)|0;t=t+(VA(jA[f+4>>1]>>2,jA[w+4>>1]|0)|0)|0;t=t+(VA(jA[f+6>>1]>>2,jA[w+6>>1]|0)|0)|0;l=l+-1<<16>>16;if(!(l<<16>>16))break;else{w=w+8|0;f=f+8|0}}w=t<<1|1}f=(Ni(w)|0)<<16>>16;t=f+65532&65535;f=er(w<<f,n)|0}}while(0);if((g|0)==14){f=Ni(w)|0;t=f;f=er(w<<(f<<16>>16),n)|0}jA[i>>1]=Q;w=s<<16>>16;jA[i+2>>1]=15-w;jA[i+4>>1]=f;t=t<<16>>16;jA[i+6>>1]=15-t;if(f<<16>>16<4){v=0;return v|0}t=fr(Ei(f<<16>>16>>>1&65535,Q)|0,t-w&65535,n)|0;t=t<<16>>16>19661?19661:t;if((A|0)!=7){v=t;return v|0}v=t&65532;return v|0}function gf(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;if(f<<16>>16)e=e<<16>>16<<1&65535;if(e<<16>>16<0){A=A+-2|0;e=(e&65535)+6&65535}f=e<<16>>16;i=6-f<<16>>16;e=(VA(jA[3468+(f<<1)>>1]|0,jA[A>>1]|0)|0)+16384|0;e=e+(VA(jA[3468+(i<<1)>>1]|0,jA[A+2>>1]|0)|0)|0;e=e+(VA(jA[3468+(f+6<<1)>>1]|0,jA[A+-2>>1]|0)|0)|0;e=e+(VA(jA[3468+(i+6<<1)>>1]|0,jA[A+4>>1]|0)|0)|0;e=(VA(jA[3468+(f+12<<1)>>1]|0,jA[A+-4>>1]|0)|0)+e|0;e=e+(VA(jA[3468+(i+12<<1)>>1]|0,jA[A+6>>1]|0)|0)|0;f=e+(VA(jA[3468+(f+18<<1)>>1]|0,jA[A+-6>>1]|0)|0)|0;return(f+(VA(jA[3468+(i+18<<1)>>1]|0,jA[A+8>>1]|0)|0)|0)>>>15&65535|0}function vf(A){A=A|0;A=A-(A>>>31)|0;return A>>31^A|0}function uf(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var r=0,n=0,t=0,w=0,l=0;if(!(A<<16>>16))return;else{r=3518;n=3538;i=f}while(1){i=i+2|0;e=e+2|0;l=jA[e>>1]|0;w=jA[r>>1]|0;f=VA(w,l)|0;f=(f|0)==1073741824?2147483647:f<<1;l=(VA(jA[n>>1]|0,l)|0)>>15;t=(l<<1)+f|0;t=(f^l|0)>0&(t^f|0)<0?(f>>>31)+2147483647|0:t;w=(VA(w,jA[i>>1]|0)|0)>>15;f=t+(w<<1)|0;f=(t^w|0)>0&(f^t|0)<0?(t>>>31)+2147483647|0:f;jA[e>>1]=f>>>16;jA[i>>1]=(f>>>1)-(f>>16<<15);A=A+-1<<16>>16;if(!(A<<16>>16))break;else{r=r+2|0;n=n+2|0}}return}function cf(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,r=0;i=A&65535;r=i<<16;e=e<<16>>16;A=(e<<1)+r|0;if(!((e^r|0)>0&(A^r|0)<0)){r=A;return r|0}pA[f>>2]=1;r=(i>>>15)+2147483647|0;return r|0}function Cf(A){A=A|0;var e=0,f=0,i=0;if(!A){i=-1;return i|0}pA[A>>2]=0;e=lr(22)|0;if(!e){i=-1;return i|0}jA[e>>1]=4096;f=e+2|0;i=f+20|0;do{jA[f>>1]=0;f=f+2|0}while((f|0)<(i|0));pA[A>>2]=e;i=0;return i|0}function Df(A){A=A|0;var e=0;if(!A){e=-1;return e|0}jA[A>>1]=4096;A=A+2|0;e=A+20|0;do{jA[A>>1]=0;A=A+2|0}while((A|0)<(e|0));e=0;return e|0}function Ef(A){A=A|0;var e=0;if(!A)return;e=pA[A>>2]|0;if(!e)return;Br(e);pA[A>>2]=0;return}function hf(A,e,f,i,r,n){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;var t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0,T=0,y=0;R=WA;WA=WA+96|0;G=R+66|0;L=R+44|0;U=R+22|0;w=R;I=e+2|0;H=f+2|0;F=(jA[H>>1]<<1)+(DA[I>>1]<<16)|0;t=vf(F)|0;t=Ve(t,jA[e>>1]|0,jA[f>>1]|0,n)|0;if((F|0)>0)t=kf(t)|0;b=t>>16;jA[r>>1]=er(t,n)|0;C=t>>20;M=G+2|0;jA[M>>1]=C;F=L+2|0;jA[F>>1]=(t>>>5)-(C<<15);C=VA(b,b)|0;C=(C|0)==1073741824?2147483647:C<<1;b=(VA((t>>>1)-(b<<15)<<16>>16,b)|0)>>15;d=b<<1;k=d+C|0;k=(b^C|0)>0&(k^C|0)<0?(C>>>31)+2147483647|0:k;d=k+d|0;d=2147483647-(vf((k^b|0)>0&(d^k|0)<0?(k>>>31)+2147483647|0:d)|0)|0;k=d>>16;b=jA[e>>1]|0;C=VA(k,b)|0;C=(C|0)==1073741824?2147483647:C<<1;b=(VA((d>>>1)-(k<<15)<<16>>16,b)|0)>>15;d=(b<<1)+C|0;d=(b^C|0)>0&(d^C|0)<0?(C>>>31)+2147483647|0:d;k=(VA(jA[f>>1]|0,k)|0)>>15;C=d+(k<<1)|0;C=(d^k|0)>0&(C^d|0)<0?(d>>>31)+2147483647|0:C;d=Ni(C)|0;C=C<<(d<<16>>16);k=U+2|0;b=w+2|0;l=C;C=(C>>>1)-(C>>16<<15)|0;D=w+4|0;E=U+4|0;h=2;P=2;while(1){c=l>>>16;t=c&65535;g=C&65535;v=P+-1|0;a=G+(v<<1)|0;u=L+(v<<1)|0;Q=1;o=a;s=u;B=I;w=H;l=0;while(1){T=jA[B>>1]|0;y=((VA(jA[s>>1]|0,T)|0)>>15)+l|0;l=jA[o>>1]|0;l=y+(VA(l,T)|0)+((VA(l,jA[w>>1]|0)|0)>>15)|0;Q=Q+1<<16>>16;if((Q<<16>>16|0)>=(P|0))break;else{o=o+-2|0;s=s+-2|0;B=B+2|0;w=w+2|0}}y=(DA[e+(P<<1)>>1]<<16)+(l<<5)+(jA[f+(P<<1)>>1]<<1)|0;l=Ve(vf(y)|0,t,g,n)|0;if((y|0)>0)l=kf(l)|0;w=d<<16>>16;if(d<<16>>16>0){t=l<<w;if((t>>w|0)!=(l|0))t=l>>31^2147483647}else{w=0-w<<16;if((w|0)<2031616)t=l>>(w>>16);else t=0}Q=t>>16;if((P|0)<5)jA[r+(v<<1)>>1]=(t+32768|0)>>>16;y=(t>>>16)-(t>>>31)|0;if(((y<<16>>31^y)&65535)<<16>>16>32750){t=16;break}s=(t>>>1)-(Q<<15)<<16>>16;o=1;l=u;w=k;B=b;while(1){T=(VA(jA[l>>1]|0,Q)|0)>>15;u=jA[a>>1]|0;y=(VA(u,s)|0)>>15;u=VA(u,Q)|0;y=u+T+(jA[L+(o<<1)>>1]|0)+(jA[G+(o<<1)>>1]<<15)+y|0;jA[w>>1]=y>>>15;jA[B>>1]=y&32767;o=o+1|0;if((o&65535)<<16>>16==h<<16>>16)break;else{a=a+-2|0;l=l+-2|0;w=w+2|0;B=B+2|0}}jA[E>>1]=t>>20;jA[D>>1]=(t>>>5)-(jA[U+(P<<1)>>1]<<15);T=VA(Q,Q)|0;T=(T|0)==1073741824?2147483647:T<<1;t=(VA(s,Q)|0)>>15;y=t<<1;w=y+T|0;w=(t^T|0)>0&(w^T|0)<0?(T>>>31)+2147483647|0:w;y=w+y|0;y=2147483647-(vf((w^t|0)>0&(y^w|0)<0?(w>>>31)+2147483647|0:y)|0)|0;w=y>>16;t=c<<16>>16;t=((VA(w,C<<16>>16)|0)>>15)+(VA(w,t)|0)+((VA((y>>>1)-(w<<15)<<16>>16,t)|0)>>15)<<1;w=(Ni(t)|0)<<16>>16;t=t<<w;y=P<<1;or(M|0,k|0,y|0)|0;or(F|0,b|0,y|0)|0;P=P+1|0;if((P|0)>=11){t=20;break}else{d=w+(d&65535)&65535;l=t;C=(t>>1)-(t>>16<<15)|0;D=D+2|0;E=E+2|0;h=h+1<<16>>16}}if((t|0)==16){ZA()}else if((t|0)==20){jA[i>>1]=4096;y=((jA[F>>1]|0)+8192+(jA[M>>1]<<15)|0)>>>14&65535;jA[i+2>>1]=y;jA[A+2>>1]=y;y=((jA[L+4>>1]|0)+8192+(jA[G+4>>1]<<15)|0)>>>14&65535;jA[i+4>>1]=y;jA[A+4>>1]=y;y=((jA[L+6>>1]|0)+8192+(jA[G+6>>1]<<15)|0)>>>14&65535;jA[i+6>>1]=y;jA[A+6>>1]=y;y=((jA[L+8>>1]|0)+8192+(jA[G+8>>1]<<15)|0)>>>14&65535;jA[i+8>>1]=y;jA[A+8>>1]=y;y=((jA[L+10>>1]|0)+8192+(jA[G+10>>1]<<15)|0)>>>14&65535;jA[i+10>>1]=y;jA[A+10>>1]=y;y=((jA[L+12>>1]|0)+8192+(jA[G+12>>1]<<15)|0)>>>14&65535;jA[i+12>>1]=y;jA[A+12>>1]=y;y=((jA[L+14>>1]|0)+8192+(jA[G+14>>1]<<15)|0)>>>14&65535;jA[i+14>>1]=y;jA[A+14>>1]=y;y=((jA[L+16>>1]|0)+8192+(jA[G+16>>1]<<15)|0)>>>14&65535;jA[i+16>>1]=y;jA[A+16>>1]=y;y=((jA[L+18>>1]|0)+8192+(jA[G+18>>1]<<15)|0)>>>14&65535;jA[i+18>>1]=y;jA[A+18>>1]=y;y=((jA[L+20>>1]|0)+8192+(jA[G+20>>1]<<15)|0)>>>14&65535;jA[i+20>>1]=y;jA[A+20>>1]=y;WA=R;return 0}return 0}function Pf(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;i=A>>16;jA[e>>1]=i;jA[f>>1]=(A>>>1)-(i<<15);return}function kf(A){A=A|0;return((A|0)==-2147483648?2147483647:0-A|0)|0}function bf(A){A=A|0;var e=0;if(!A){A=-1;return A|0}pA[A>>2]=0;e=lr(4)|0;if(!e){A=-1;return A|0}pA[e>>2]=0;if(!((Cf(e)|0)<<16>>16)){Df(pA[e>>2]|0)|0;pA[A>>2]=e;A=0;return A|0}else{Ef(e);Br(e);A=-1;return A|0}return 0}function df(A){A=A|0;var e=0;if(!A)return;e=pA[A>>2]|0;if(!e)return;Ef(e);Br(pA[A>>2]|0);pA[A>>2]=0;return}function If(A){A=A|0;if(!A){A=-1;return A|0}Df(pA[A>>2]|0)|0;A=0;return A|0}function Mf(A,e,f,i,r,n,t){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;var w=0,l=0,B=0,a=0;a=WA;WA=WA+64|0;B=a+48|0;l=a+22|0;w=a;if((e|0)==7){f=pA[n+116>>2]|0;xA(i,10,w,l,pA[n+112>>2]|0,t)|0;uf(10,w,l,t);hf(pA[A>>2]|0,w,l,r+22|0,B,t)|0;xA(i,10,w,l,f,t)|0;uf(10,w,l,t);hf(pA[A>>2]|0,w,l,r+66|0,B,t)|0;WA=a;return}else{xA(f,10,w,l,pA[n+108>>2]|0,t)|0;uf(10,w,l,t);hf(pA[A>>2]|0,w,l,r+66|0,B,t)|0;WA=a;return}}function Ff(A,e,f,i,r,n,t,w,l,B){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;l=l|0;B=B|0;if((f|0)==6){jA[r>>1]=Xf(A,e,i,20,143,80,n,t,w,l,B)|0;return}jA[t>>1]=0;jA[t+2>>1]=0;if(f>>>0<2){jA[r>>1]=Rf(e,f,i,20,143,160,w,l,B)|0;return}if(f>>>0<6){jA[r>>1]=Rf(e,f,i,20,143,80,w,l,B)|0;return}else{jA[r>>1]=Rf(e,f,i,18,143,80,w,l,B)|0;return}}function Hf(A){A=A|0;var e=0;if((A|0)!=0?(pA[A>>2]=0,e=lr(2)|0,(e|0)!=0):0){jA[e>>1]=0;pA[A>>2]=e;e=0}else e=-1;return e|0}function Uf(A){A=A|0;if(!A)A=-1;else{jA[A>>1]=0;A=0}return A|0}function Gf(A){A=A|0;var e=0;if(!A)return;e=pA[A>>2]|0;if(!e)return;Br(e);pA[A>>2]=0;return}function Lf(A,e,f,i,r,n,t,w,l,B,a,s){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;l=l|0;B=B|0;a=a|0;s=s|0;var o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0,T=0,y=0,Y=0,z=0,X=0,J=0,O=0,m=0,N=0,K=0;K=WA;WA=WA+240|0;c=K+160|0;C=K+80|0;z=K;Y=jA[3558+(e*18|0)>>1]|0;N=jA[3558+(e*18|0)+2>>1]|0;o=jA[3558+(e*18|0)+4>>1]|0;X=jA[3558+(e*18|0)+6>>1]|0;v=jA[3558+(e*18|0)+12>>1]|0;g=jA[3558+(e*18|0)+14>>1]|0;Q=jA[3558+(e*18|0)+16>>1]|0;A:do{switch(w<<16>>16){case 0:case 80:if(e>>>0<2&w<<16>>16==80){J=(DA[A>>1]|0)-(v&65535)|0;J=(J<<16>>16|0)<(Q<<16>>16|0)?Q:J&65535;y=g<<16>>16;O=(J&65535)+y&65535;m=O<<16>>16>143;J=m?143-y&65535:J;O=m?143:O;m=1;break A}else{J=(DA[f+((w<<16>>16!=0&1)<<1)>>1]|0)-(DA[3558+(e*18|0)+8>>1]|0)|0;J=(J<<16>>16|0)<(Q<<16>>16|0)?Q:J&65535;y=jA[3558+(e*18|0)+10>>1]|0;O=(J&65535)+y&65535;m=O<<16>>16>143;J=m?143-y&65535:J;O=m?143:O;m=0;break A}default:{J=(DA[A>>1]|0)-(v&65535)|0;J=(J<<16>>16|0)<(Q<<16>>16|0)?Q:J&65535;y=g<<16>>16;O=(J&65535)+y&65535;m=O<<16>>16>143;J=m?143-y&65535:J;O=m?143:O;m=1}}}while(0);T=J&65535;w=T+65532|0;u=w&65535;R=(O&65535)+4&65535;y=w<<16>>16;w=0-(w&65535)|0;v=w&65535;Se(i+(w<<16>>16<<1)|0,n,c,t);w=t<<16>>16;b=w>>>1&65535;D=b<<16>>16==0;if(D)t=1;else{t=b;Q=c;f=C;g=0;while(1){L=jA[Q>>1]|0;jA[f>>1]=L>>>2;L=(VA(L,L)|0)+g|0;g=jA[Q+2>>1]|0;jA[f+2>>1]=g>>>2;g=L+(VA(g,g)|0)|0;t=t+-1<<16>>16;if(!(t<<16>>16))break;else{Q=Q+4|0;f=f+4|0}}t=(g|0)<33554433}L=t?0:2;k=t?c:C;E=t?c:C;A:do{if(u<<16>>16<=R<<16>>16){h=w+-1|0;H=k+(h<<1)|0;U=n+(h<<1)|0;G=k+(w+-2<<1)|0;I=h>>>1;M=I&65535;P=M<<16>>16==0;F=t?12:14;I=(I<<1)+131070&131070;f=w+-3-I|0;d=k+(f<<1)|0;I=k+(w+-4-I<<1)|0;n=n+(f<<1)|0;if(!D){D=y;while(1){C=b;c=E;Q=r;g=0;t=0;while(1){C=C+-1<<16>>16;w=jA[c>>1]|0;g=(VA(w,jA[Q>>1]|0)|0)+g|0;w=(VA(w,w)|0)+t|0;t=jA[c+2>>1]|0;g=g+(VA(t,jA[Q+2>>1]|0)|0)|0;t=w+(VA(t,t)|0)|0;if(!(C<<16>>16))break;else{c=c+4|0;Q=Q+4|0}}c=Ui(t<<1,s)|0;t=c>>16;Q=g<<1>>16;C=VA(t,Q)|0;C=(C|0)==1073741824?2147483647:C<<1;Q=(VA((c>>>1)-(t<<15)<<16>>16,Q)|0)>>15;c=(Q<<1)+C|0;c=(Q^C|0)>0&(c^C|0)<0?(C>>>31)+2147483647|0:c;t=(VA(t,g&32767)|0)>>15;C=c+(t<<1)|0;jA[z+(D-y<<1)>>1]=(c^t|0)>0&(C^c|0)<0?(c>>>31)+65535|0:C;if(u<<16>>16!=R<<16>>16){v=v+-1<<16>>16;C=jA[i+(v<<16>>16<<1)>>1]|0;if(P){c=h;t=G;g=U;Q=H}else{c=M;t=G;g=U;Q=H;while(1){D=(VA(jA[g>>1]|0,C)|0)>>F;jA[Q>>1]=D+(DA[t>>1]|0);D=(VA(jA[g+-2>>1]|0,C)|0)>>F;jA[Q+-2>>1]=D+(DA[t+-2>>1]|0);c=c+-1<<16>>16;if(!(c<<16>>16)){c=f;t=I;g=n;Q=d;break}else{t=t+-4|0;g=g+-4|0;Q=Q+-4|0}}}D=(VA(jA[g>>1]|0,C)|0)>>F;jA[Q>>1]=D+(DA[t>>1]|0);jA[k+(c+-1<<1)>>1]=C>>L}u=u+1<<16>>16;if(u<<16>>16>R<<16>>16)break A;else D=u<<16>>16}}if(P){ZA()}c=k+(f+-1<<1)|0;t=y;while(1){Ui(0,s)|0;jA[z+(t-y<<1)>>1]=0;if(u<<16>>16!=R<<16>>16){ZA()}u=u+1<<16>>16;if(u<<16>>16>R<<16>>16)break;else t=u<<16>>16}}}while(0);u=J<<16>>16;f=T+1&65535;if(f<<16>>16>O<<16>>16)n=J;else{v=J;w=jA[z+(u-y<<1)>>1]|0;while(1){g=jA[z+((f<<16>>16)-y<<1)>>1]|0;Q=g<<16>>16<w<<16>>16;v=Q?v:f;f=f+1<<16>>16;if(f<<16>>16>O<<16>>16){n=v;break}else w=Q?w:g}}A:do{if(!(m<<16>>16==0?n<<16>>16>Y<<16>>16:0)){if(!(e>>>0<4&m<<16>>16!=0)){v=z+((n<<16>>16)-y<<1)|0;g=gf(v,o,N,s)|0;f=(o&65535)+1&65535;if(f<<16>>16<=X<<16>>16)while(1){Q=gf(v,f,N,s)|0;w=Q<<16>>16>g<<16>>16;o=w?f:o;f=f+1<<16>>16;if(f<<16>>16>X<<16>>16)break;else g=w?Q:g}if((e+-7|0)>>>0<2){X=o<<16>>16==-3;f=(X<<31>>31)+n<<16>>16;o=X?3:o;break}switch(o<<16>>16){case-2:{f=n+-1<<16>>16;o=1;break A}case 2:{f=n+1<<16>>16;o=-1;break A}default:{f=n;break A}}}Y=jA[A>>1]|0;Y=((Y<<16>>16)-u|0)>5?u+5&65535:Y;w=O<<16>>16;Y=(w-(Y<<16>>16)|0)>4?w+65532&65535:Y;w=n<<16>>16;f=Y<<16>>16;if((w|0)==(f+-1|0)?1:n<<16>>16==Y<<16>>16){v=z+(w-y<<1)|0;w=gf(v,o,N,s)|0;f=(o&65535)+1&65535;if(f<<16>>16<=X<<16>>16)while(1){g=gf(v,f,N,s)|0;Q=g<<16>>16>w<<16>>16;o=Q?f:o;f=f+1<<16>>16;if(f<<16>>16>X<<16>>16)break;else w=Q?g:w}if((e+-7|0)>>>0<2){ZA()}switch(o<<16>>16){case-2:{f=n+-1<<16>>16;o=1;break A}case 2:{f=n+1<<16>>16;o=-1;break A}default:{f=n;break A}}}if((w|0)==(f+-2|0)){f=z+(w-y<<1)|0;w=gf(f,0,N,s)|0;if((e|0)!=8){o=0;v=1;while(1){g=gf(f,v,N,s)|0;Q=g<<16>>16>w<<16>>16;o=Q?v:o;v=v+1<<16>>16;if(v<<16>>16>X<<16>>16)break;else w=Q?g:w}if((e+-7|0)>>>0>=2)switch(o<<16>>16){case-2:{f=n+-1<<16>>16;o=1;break A}case 2:{f=n+1<<16>>16;o=-1;break A}default:{f=n;break A}}}else o=0;X=o<<16>>16==-3;f=(X<<31>>31)+n<<16>>16;o=X?3:o;break}if((w|0)==(f+1|0)){v=z+(w-y<<1)|0;f=gf(v,o,N,s)|0;w=(o&65535)+1&65535;if(w<<16>>16<=0)while(1){Q=gf(v,w,N,s)|0;g=Q<<16>>16>f<<16>>16;o=g?w:o;w=w+1<<16>>16;if(w<<16>>16>0)break;else f=g?Q:f}if((e+-7|0)>>>0<2){ZA()}switch(o<<16>>16){case-2:{f=n+-1<<16>>16;o=1;break A}case 2:{f=n+1<<16>>16;o=-1;break A}default:{f=n;break A}}}else{f=n;o=0}}else{f=n;o=0}}while(0);if((e+-7|0)>>>0>1){X=A;A=Af(f,o,jA[A>>1]|0,J,O,m,e>>>0<4&1,s)|0;jA[a>>1]=A;jA[X>>1]=f;jA[B>>1]=N;jA[l>>1]=o;WA=K;return f|0}else{s=ef(f,o,J,m,s)|0;jA[a>>1]=s;jA[A>>1]=f;jA[B>>1]=N;jA[l>>1]=o;WA=K;return f|0}return 0}function Rf(A,e,f,i,r,n,t,w,l){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;l=l|0;var B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0;I=WA;WA=WA+1200|0;b=I+1188|0;k=I+580|0;d=I+578|0;P=I+576|0;C=I;E=I+582|0;h=(w|0)!=0;if(h){ZA()}D=r<<16>>16;s=0-D|0;a=f+(s<<1)|0;s=s&65535;v=n<<16>>16;do{if(s<<16>>16<n<<16>>16){g=s;Q=a;s=0;while(1){u=jA[Q>>1]|0;s=(VA(u<<1,u)|0)+s|0;if((s|0)<0)break;g=g+1<<16>>16;if(g<<16>>16>=n<<16>>16){c=14;break}else Q=Q+2|0}if((c|0)==14){if((s|0)<1048576){c=15;break}or(E|0,a|0,v+D<<1|0)|0;u=0;break}B=v+D|0;o=B>>>1;g=o&65535;if(!(g<<16>>16))s=E;else{u=((o<<1)+131070&131070)+2|0;v=u-D|0;Q=E;while(1){jA[Q>>1]=(jA[a>>1]|0)>>>3;jA[Q+2>>1]=(jA[a+2>>1]|0)>>>3;g=g+-1<<16>>16;if(!(g<<16>>16))break;else{a=a+4|0;Q=Q+4|0}}a=f+(v<<1)|0;s=E+(u<<1)|0}if(!(B&1))u=3;else{jA[s>>1]=(jA[a>>1]|0)>>>3;u=3}}else c=15}while(0);if((c|0)==15){u=v+D|0;s=u>>>1;o=s&65535;if(!(o<<16>>16))s=E;else{v=((s<<1)+131070&131070)+2|0;Q=v-D|0;g=E;while(1){jA[g>>1]=jA[a>>1]<<3;jA[g+2>>1]=jA[a+2>>1]<<3;o=o+-1<<16>>16;if(!(o<<16>>16))break;else{a=a+4|0;g=g+4|0}}a=f+(Q<<1)|0;s=E+(v<<1)|0}if(!(u&1))u=-3;else{jA[s>>1]=jA[a>>1]<<3;u=-3}}v=C+(D<<2)|0;Q=E+(D<<1)|0;Le(Q,n,r,i,v);B=(e|0)==7&1;s=i<<16>>16;a=s<<2;if((a|0)!=(s<<18>>16|0)){pA[l>>2]=1;a=i<<16>>16>0?32767:-32768}g=Tf(A,v,Q,u,B,n,r,a&65535,b,w,l)|0;s=s<<1;o=Tf(A,v,Q,u,B,n,a+65535&65535,s&65535,k,w,l)|0;s=Tf(A,v,Q,u,B,n,s+65535&65535,i,d,w,l)|0;if(t<<16>>16==1&h){ZA()}a=jA[b>>1]|0;B=jA[k>>1]|0;if(((a<<16>>16)*55706>>16|0)>=(B<<16>>16|0)){k=a;b=g;k=k<<16>>16;k=k*55706|0;k=k>>16;d=jA[d>>1]|0;d=d<<16>>16;d=(k|0)<(d|0);d=d?s:b;WA=I;return d|0}jA[b>>1]=B;k=B;b=o;k=k<<16>>16;k=k*55706|0;k=k>>16;d=jA[d>>1]|0;d=d<<16>>16;d=(k|0)<(d|0);d=d?s:b;WA=I;return d|0}function Tf(A,e,f,i,r,n,t,w,l,B,a){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;l=l|0;B=B|0;a=a|0;var s=0,o=0,Q=0,g=0,v=0;if(t<<16>>16<w<<16>>16){w=-2147483648;Q=t}else{Q=t;s=-2147483648;o=e+(0-(t<<16>>16)<<2)|0;e=t;while(1){t=pA[o>>2]|0;v=(t|0)<(s|0);e=v?e:Q;s=v?s:t;Q=Q+-1<<16>>16;if(Q<<16>>16<w<<16>>16){w=s;Q=e;break}else o=o+4|0}}e=n<<16>>16>>>2&65535;if(!(e<<16>>16))e=0;else{s=e;t=f+(0-(Q<<16>>16)<<1)|0;e=0;while(1){v=jA[t>>1]|0;v=(VA(v,v)|0)+e|0;e=jA[t+2>>1]|0;e=v+(VA(e,e)|0)|0;v=jA[t+4>>1]|0;v=e+(VA(v,v)|0)|0;e=jA[t+6>>1]|0;e=v+(VA(e,e)|0)|0;s=s+-1<<16>>16;if(!(s<<16>>16))break;else t=t+8|0}e=e<<1}if(B)ZA();e=Ui(e,a)|0;t=r<<16>>16!=0;if(t)e=(e|0)>1073741823?2147483647:e<<1;r=w>>16;A=e>>16;a=VA(A,r)|0;a=(a|0)==1073741824?2147483647:a<<1;e=(VA((e>>>1)-(A<<15)<<16>>16,r)|0)>>15;v=(e<<1)+a|0;v=(e^a|0)>0&(v^a|0)<0?(a>>>31)+2147483647|0:v;r=(VA(A,(w>>>1)-(r<<15)<<16>>16)|0)>>15;e=v+(r<<1)|0;e=(v^r|0)>0&(e^v|0)<0?(v>>>31)+2147483647|0:e;if(!t){jA[l>>1]=e;return Q|0}t=i<<16>>16;if(i<<16>>16>0)if(i<<16>>16<31){t=e>>t;g=16}else t=0;else{g=0-t<<16>>16;t=e<<g;t=(t>>g|0)==(e|0)?t:e>>31^2147483647;g=16}if((g|0)==16){if((t|0)>65535){jA[l>>1]=32767;return Q|0}if((t|0)<-65536){jA[l>>1]=-32768;return Q|0}}jA[l>>1]=t>>>1;return Q|0}function yf(A){A=A|0;var e=0;if(!A){A=-1;return A|0}pA[A>>2]=0;e=lr(6)|0;if(!e){A=-1;return A|0}jA[e>>1]=40;jA[e+2>>1]=0;jA[e+4>>1]=0;pA[A>>2]=e;A=0;return A|0}function Yf(A){A=A|0;if(!A){A=-1;return A|0}jA[A>>1]=40;jA[A+2>>1]=0;jA[A+4>>1]=0;A=0;return A|0}function zf(A){A=A|0;var e=0;if(!A)return;e=pA[A>>2]|0;if(!e)return;Br(e);pA[A>>2]=0;return}function Xf(A,e,f,i,r,n,t,w,l,B,a){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;l=l|0;B=B|0;a=a|0;var s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0;F=WA;WA=WA+1200|0;D=F+1186|0;E=F+1184|0;M=F+1182|0;C=F;P=F+576|0;h=r<<16>>16;I=P+(h<<1)|0;s=(0-h&65535)<<16>>16<n<<16>>16;if(s){v=0-r<<16>>16<<16>>16;o=0;do{g=jA[f+(v<<1)>>1]|0;g=VA(g,g)|0;if((g|0)!=1073741824){Q=(g<<1)+o|0;if((g^o|0)>0&(Q^o|0)<0){pA[a>>2]=1;o=(o>>>31)+2147483647|0}else o=Q}else{pA[a>>2]=1;o=2147483647}v=v+1|0}while((v&65535)<<16>>16!=n<<16>>16)}else o=0;if((2147483646-o&o|0)>=0)if((o|0)==2147483647){if(s){o=0-r<<16>>16<<16>>16;do{jA[P+(o+h<<1)>>1]=fr(jA[f+(o<<1)>>1]|0,3,a)|0;o=o+1|0}while((o&65535)<<16>>16!=n<<16>>16)}}else u=14;else{pA[a>>2]=1;u=14}do{if((u|0)==14){if((1048575-o&o|0)<0){pA[a>>2]=1;o=(o>>>31)+2147483647|0}else o=o+-1048576|0;if((o|0)>=0){if(!s)break;d=0-r<<16>>16<<16>>16;or(P+(h+d<<1)|0,f+(d<<1)|0,(((n+r<<16>>16)+-1&65535)<<1)+2|0)|0;break}if(s){o=0-r<<16>>16<<16>>16;do{d=jA[f+(o<<1)>>1]|0;jA[P+(o+h<<1)>>1]=(d<<19>>19|0)==(d|0)?d<<3:d>>>15^32767;o=o+1|0}while((o&65535)<<16>>16!=n<<16>>16)}}}while(0);b=C+(h<<2)|0;Le(I,n,r,i,b);v=jA[A>>1]|0;d=A+4|0;k=w+(l<<16>>16<<1)|0;A:do{if(r<<16>>16<i<<16>>16)c=r;else{if((jA[d>>1]|0)<=0){f=r;w=-2147483648;g=r;u=3402;while(1){Pf(pA[C+(h-(f<<16>>16)<<2)>>2]|0,D,E,a);Q=jA[E>>1]|0;o=jA[u>>1]|0;v=VA(o,jA[D>>1]|0)|0;if((v|0)==1073741824){pA[a>>2]=1;s=2147483647}else s=v<<1;c=(VA(o,Q<<16>>16)|0)>>15;v=s+(c<<1)|0;if((s^c|0)>0&(v^s|0)<0){ZA()}Q=(v|0)<(w|0);g=Q?g:f;f=f+-1<<16>>16;if(f<<16>>16<i<<16>>16){c=g;break A}else{w=Q?w:v;u=u+-2|0}}}w=r;s=-2147483648;g=r;c=2902+(h+123-(v<<16>>16)<<1)|0;f=3402;while(1){Pf(pA[C+(h-(w<<16>>16)<<2)>>2]|0,D,E,a);u=jA[E>>1]|0;Q=jA[f>>1]|0;v=VA(Q,jA[D>>1]|0)|0;if((v|0)==1073741824){pA[a>>2]=1;o=2147483647}else o=v<<1;u=(VA(Q,u<<16>>16)|0)>>15;v=o+(u<<1)|0;if((o^u|0)>0&(v^o|0)<0){pA[a>>2]=1;v=(o>>>31)+2147483647|0}Pf(v,D,E,a);u=jA[E>>1]|0;Q=jA[c>>1]|0;v=VA(Q,jA[D>>1]|0)|0;if((v|0)==1073741824){pA[a>>2]=1;o=2147483647}else o=v<<1;u=(VA(Q,u<<16>>16)|0)>>15;v=o+(u<<1)|0;if((o^u|0)>0&(v^o|0)<0){pA[a>>2]=1;v=(o>>>31)+2147483647|0}Q=(v|0)<(s|0);g=Q?g:w;w=w+-1<<16>>16;if(w<<16>>16<i<<16>>16){c=g;break}else{s=Q?s:v;c=c+-2|0;f=f+-2|0}}}}while(0);if(n<<16>>16>0){w=0;f=I;u=P+(h-(c<<16>>16)<<1)|0;g=0;o=0;while(1){v=jA[u>>1]|0;Q=VA(v,jA[f>>1]|0)|0;if((Q|0)!=1073741824){s=(Q<<1)+g|0;if((Q^g|0)>0&(s^g|0)<0){ZA()}else g=s}else{pA[a>>2]=1;g=2147483647}s=VA(v,v)|0;if((s|0)!=1073741824){Q=(s<<1)+o|0;if((s^o|0)>0&(Q^o|0)<0){ZA()}else o=Q}else{pA[a>>2]=1;o=2147483647}w=w+1<<16>>16;if(w<<16>>16>=n<<16>>16)break;else{f=f+2|0;u=u+2|0}}}else{g=0;o=0}Q=(B|0)==0;if(!Q){ZA()}s=(er(o,a)|0)<<16>>16;if((s*13107|0)==1073741824){pA[a>>2]=1;o=2147483647}else o=s*26214|0;s=g-o|0;if(((s^g)&(o^g)|0)<0){pA[a>>2]=1;s=(g>>>31)+2147483647|0}B=er(s,a)|0;jA[k>>1]=B;if(B<<16>>16>0){s=t+6|0;jA[t+8>>1]=jA[s>>1]|0;B=t+4|0;jA[s>>1]=jA[B>>1]|0;s=t+2|0;jA[B>>1]=jA[s>>1]|0;jA[s>>1]=jA[t>>1]|0;jA[t>>1]=c;jA[A>>1]=di(t,5)|0;jA[A+2>>1]=32767;s=32767}else{jA[A>>1]=c;A=A+2|0;s=((jA[A>>1]|0)*29491|0)>>>15&65535;jA[A>>1]=s}jA[d>>1]=((nr(s,9830,a)|0)&65535)>>>15^1;if(Q){WA=F;return c|0}if((nr(l,1,a)|0)<<16>>16){WA=F;return c|0}ZA();return c|0}function Jf(A,e,f,i,r,n,t,w,l,B){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;l=l|0;B=B|0;var a=0,s=0;B=WA;WA=WA+48|0;s=B+22|0;a=B;e=A>>>0<6?e:f;f=n<<16>>16>0?22:0;A=r+(f<<1)|0;wr(A,e,s);wr(A,i,a);A=n<<16>>16;n=l+(A<<1)|0;Ar(s,t+(A<<1)|0,n,40);tr(a,n,n,40,w,1);f=r+(((f<<16)+720896|0)>>>16<<1)|0;wr(f,e,s);wr(f,i,a);A=(A<<16)+2621440>>16;l=l+(A<<1)|0;Ar(s,t+(A<<1)|0,l,40);tr(a,l,l,40,w,1);WA=B;return}function Of(A){A=A|0;var e=0;if(!A){A=-1;return A|0}pA[A>>2]=0;e=lr(12)|0;if(!e){A=-1;return A|0}jA[e>>1]=0;jA[e+2>>1]=0;jA[e+4>>1]=0;jA[e+6>>1]=0;jA[e+8>>1]=0;jA[e+10>>1]=0;pA[A>>2]=e;A=0;return A|0}function mf(A){A=A|0;if(!A){A=-1;return A|0}jA[A>>1]=0;jA[A+2>>1]=0;jA[A+4>>1]=0;jA[A+6>>1]=0;jA[A+8>>1]=0;jA[A+10>>1]=0;A=0;return A|0}function Nf(A){A=A|0;var e=0;if(!A)return;e=pA[A>>2]|0;if(!e)return;Br(e);pA[A>>2]=0;return}function Kf(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,r=0,n=0,t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0;s=A+10|0;r=jA[s>>1]|0;o=A+8|0;i=jA[o>>1]|0;if(!(f<<16>>16)){ZA()}w=A+4|0;l=A+6|0;B=A+2|0;t=jA[l>>1]|0;a=jA[w>>1]|0;n=f;f=r;while(1){Q=(VA(jA[A>>1]|0,-3733)|0)+(((a<<16>>16)*7807|0)+((t<<16>>16)*7807>>15))|0;jA[A>>1]=a;Q=Q+((VA(jA[B>>1]|0,-3733)|0)>>15)|0;jA[B>>1]=t;Q=((f<<16>>16)*1899|0)+Q+(VA(i<<16>>16,-3798)|0)|0;f=jA[e>>1]|0;Q=Q+((f<<16>>16)*1899|0)|0;jA[e>>1]=(Q+2048|0)>>>12;r=Q>>>12;a=r&65535;jA[w>>1]=a;t=(Q<<3)-(r<<15)&65535;jA[l>>1]=t;n=n+-1<<16>>16;if(!(n<<16>>16))break;else{Q=i;e=e+2|0;i=f;f=Q}}jA[s>>1]=i;jA[o>>1]=f;return}function xf(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var r=0,n=0,t=0,w=0;r=jA[(pA[i+88>>2]|0)+(A<<1)>>1]|0;if(!(r<<16>>16))return;w=f;t=pA[(pA[i+92>>2]|0)+(A<<2)>>2]|0;while(1){f=jA[t>>1]|0;if(!(f<<16>>16))f=0;else{A=jA[e>>1]|0;n=f;i=w+((f<<16>>16)+-1<<1)|0;while(1){f=A<<16>>16;jA[i>>1]=f&1;n=n+-1<<16>>16;if(!(n<<16>>16))break;else{A=f>>>1&65535;i=i+-2|0}}f=jA[t>>1]|0}e=e+2|0;r=r+-1<<16>>16;if(!(r<<16>>16))break;else{w=w+(f<<16>>16<<1)|0;t=t+2|0}}return}function Sf(A,e,f,i,r,n){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;var t=0,w=0,l=0,B=0,a=0;a=WA;WA=WA+16|0;l=a+2|0;B=a;t=r<<16>>16;if(r<<16>>16<1){n=-5443;B=-32768;ki(A,B,n);WA=a;return}w=xi(14,f,n)|0;if((t|0)<(w<<16>>16|0))f=i;else{f=(i&65535)+1&65535;r=t>>>1&65535}i=Ei(r,w&65535)|0;jA[B>>1]=i;Gi(i<<16>>16,l,B,n);jA[l>>1]=((((f&65535)-(e&65535)<<16)+-65536|0)>>>16)+(DA[l>>1]|0);i=ir(jA[B>>1]|0,5,n)|0;t=jA[l>>1]|0;i=((t&65535)<<10)+(i&65535)&65535;if(i<<16>>16>18284){ZA()}r=jA[B>>1]|0;t=t<<16>>16;if((t*24660|0)==1073741824){pA[n>>2]=1;f=2147483647}else f=t*49320|0;B=(r<<16>>16)*24660>>15;t=f+(B<<1)|0;if((f^B|0)>0&(t^f|0)<0){pA[n>>2]=1;t=(f>>>31)+2147483647|0}B=t<<13;n=er((B>>13|0)==(t|0)?B:t>>31^2147483647,n)|0;B=i;ki(A,B,n);WA=a;return}function jf(A,e,f,i,r,n,t,w,l,B,a,s,o,Q,g,v,u,c,C,D){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;l=l|0;B=B|0;a=a|0;s=s|0;o=o|0;Q=Q|0;g=g|0;v=v|0;u=u|0;c=c|0;C=C|0;D=D|0;var E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0,T=0,y=0,Y=0,z=0,X=0,J=0,O=0,m=0,N=0,K=0,x=0,S=0,j=0,p=0,W=0,V=0,Z=0,_=0,q=0,$=0,AA=0,eA=0,fA=0;fA=WA;WA=WA+80|0;q=fA+66|0;$=fA+64|0;AA=fA+62|0;eA=fA+60|0;Y=fA+40|0;z=fA+20|0;T=fA;jA[q>>1]=e;jA[$>>1]=l;jA[AA>>1]=B;R=xi(14,f,D)|0;_=R&65535;jA[eA>>1]=_;y=xi(14,B,D)|0;L=(DA[i>>1]|0)+65523|0;jA[T>>1]=L;I=(DA[i+2>>1]|0)+65522|0;M=T+2|0;jA[M>>1]=I;F=((e&65535)<<16)+-720896|0;k=F>>16;F=(F>>>15)+15+(DA[i+4>>1]|0)|0;H=T+4|0;jA[H>>1]=F;U=(DA[i+6>>1]|0)+k|0;G=T+6|0;jA[G>>1]=U;k=k+1+(DA[i+8>>1]|0)|0;b=T+8|0;jA[b>>1]=k;E=(DA[a>>1]|0)+65523&65535;jA[T+10>>1]=E;d=(DA[a+2>>1]|0)+65522&65535;jA[T+12>>1]=d;h=((l&65535)<<16)+-720896|0;i=h>>16;h=(h>>>15)+15+(DA[a+4>>1]|0)&65535;jA[T+14>>1]=h;P=(DA[a+6>>1]|0)+i&65535;jA[T+16>>1]=P;i=i+1+(DA[a+8>>1]|0)&65535;jA[T+18>>1]=i;V=(n&65535)-(o&65535)<<16;l=V>>16;if((V|0)>0){B=t;f=Q<<16>>16>>l&65535}else{B=t<<16>>16>>0-l&65535;f=Q}if((ir(f,1,D)|0)<<16>>16>B<<16>>16)f=1;else f=(((B<<16>>16)+3>>2|0)>(f<<16>>16|0))<<31>>31;a=L+f&65535;jA[T>>1]=a;V=I+f&65535;jA[M>>1]=V;W=F+f&65535;jA[H>>1]=W;p=U+f&65535;jA[G>>1]=p;j=k+f&65535;jA[b>>1]=j;l=i<<16>>16>a<<16>>16?i:a;l=P<<16>>16>l<<16>>16?P:l;l=h<<16>>16>l<<16>>16?h:l;l=d<<16>>16>l<<16>>16?d:l;l=E<<16>>16>l<<16>>16?E:l;l=j<<16>>16>l<<16>>16?j:l;l=p<<16>>16>l<<16>>16?p:l;l=W<<16>>16>l<<16>>16?W:l;l=(V<<16>>16>l<<16>>16?V:l)+1&65535;i=0;while(1){f=l-(a&65535)|0;a=f&65535;B=DA[r>>1]<<16;f=f<<16>>16;if(a<<16>>16>0)a=a<<16>>16<31?B>>f:0;else{ZA()}V=a>>16;jA[Y+(i<<1)>>1]=V;jA[z+(i<<1)>>1]=(a>>>1)-(V<<15);i=i+1|0;if((i|0)==5){f=5;B=s;break}a=jA[T+(i<<1)>>1]|0;r=r+2|0}while(1){i=l-(E&65535)|0;E=i&65535;a=DA[B>>1]<<16;i=i<<16>>16;if(E<<16>>16>0)a=E<<16>>16<31?a>>i:0;else{ZA()}V=a>>16;jA[Y+(f<<1)>>1]=V;jA[z+(f<<1)>>1]=(a>>>1)-(V<<15);a=f+1|0;if((a&65535)<<16>>16==10)break;E=jA[T+(a<<1)>>1]|0;f=a;B=B+2|0}X=R<<16>>16;J=jA[Y>>1]|0;O=jA[z>>1]|0;m=jA[Y+2>>1]|0;N=jA[z+2>>1]|0;K=jA[Y+4>>1]|0;x=jA[z+4>>1]|0;S=jA[Y+6>>1]|0;j=jA[z+6>>1]|0;p=jA[Y+8>>1]|0;W=jA[z+8>>1]|0;V=g&65535;o=y<<16>>16;n=jA[Y+10>>1]|0;P=jA[z+10>>1]|0;h=jA[Y+12>>1]|0;r=jA[z+12>>1]|0;f=jA[Y+14>>1]|0;B=jA[z+14>>1]|0;i=jA[Y+16>>1]|0;E=jA[z+16>>1]|0;k=jA[Y+18>>1]|0;z=jA[z+18>>1]|0;l=2147483647;Y=0;a=0;b=782;do{T=jA[b>>1]|0;U=(VA(X,jA[b+2>>1]|0)|0)>>>15<<16;s=U>>16;F=T<<1;L=(VA(F,T)|0)>>16;Q=VA(L,J)|0;if((Q|0)==1073741824){pA[D>>2]=1;G=2147483647}else G=Q<<1;y=(VA(O,L)|0)>>15;Q=G+(y<<1)|0;if((G^y|0)>0&(Q^G|0)<0){pA[D>>2]=1;Q=(G>>>31)+2147483647|0}L=VA(m,T)|0;if((L|0)==1073741824){pA[D>>2]=1;G=2147483647}else G=L<<1;y=(VA(N,T)|0)>>15;L=G+(y<<1)|0;if((G^y|0)>0&(L^G|0)<0){pA[D>>2]=1;L=(G>>>31)+2147483647|0}U=(VA(U>>15,s)|0)>>16;G=VA(K,U)|0;if((G|0)==1073741824){pA[D>>2]=1;H=2147483647}else H=G<<1;y=(VA(x,U)|0)>>15;G=H+(y<<1)|0;if((H^y|0)>0&(G^H|0)<0){pA[D>>2]=1;G=(H>>>31)+2147483647|0}U=VA(S,s)|0;if((U|0)==1073741824){pA[D>>2]=1;H=2147483647}else H=U<<1;y=(VA(j,s)|0)>>15;U=H+(y<<1)|0;if((H^y|0)>0&(U^H|0)<0){pA[D>>2]=1;y=(H>>>31)+2147483647|0}else y=U;H=(VA(F,s)|0)>>16;U=VA(p,H)|0;if((U|0)==1073741824){pA[D>>2]=1;F=2147483647}else F=U<<1;R=(VA(W,H)|0)>>15;U=F+(R<<1)|0;if((F^R|0)>0&(U^F|0)<0){pA[D>>2]=1;U=(F>>>31)+2147483647|0}H=jA[b+4>>1]|0;F=jA[b+6>>1]|0;b=b+8|0;if((T-V&65535)<<16>>16<1?(Z=H<<16>>16,H<<16>>16<=g<<16>>16):0){I=(VA(F<<16>>16,o)|0)>>>15<<16;T=I>>16;d=Z<<1;F=(VA(d,Z)|0)>>16;H=VA(n,F)|0;if((H|0)==1073741824){pA[D>>2]=1;M=2147483647}else M=H<<1;R=(VA(P,F)|0)>>15;H=M+(R<<1)|0;if((M^R|0)>0&(H^M|0)<0){pA[D>>2]=1;H=(M>>>31)+2147483647|0}F=VA(h,Z)|0;if((F|0)==1073741824){pA[D>>2]=1;M=2147483647}else M=F<<1;R=(VA(r,Z)|0)>>15;F=M+(R<<1)|0;if((M^R|0)>0&(F^M|0)<0){pA[D>>2]=1;R=(M>>>31)+2147483647|0}else R=F;M=(VA(I>>15,T)|0)>>16;F=VA(f,M)|0;if((F|0)==1073741824){pA[D>>2]=1;I=2147483647}else I=F<<1;s=(VA(B,M)|0)>>15;F=I+(s<<1)|0;if((I^s|0)>0&(F^I|0)<0){pA[D>>2]=1;s=(I>>>31)+2147483647|0}else s=F;F=VA(i,T)|0;if((F|0)==1073741824){pA[D>>2]=1;M=2147483647}else M=F<<1;I=(VA(E,T)|0)>>15;F=M+(I<<1)|0;if((M^I|0)>0&(F^M|0)<0){pA[D>>2]=1;t=(M>>>31)+2147483647|0}else t=F;M=(VA(d,T)|0)>>16;F=VA(k,M)|0;if((F|0)==1073741824){pA[D>>2]=1;I=2147483647}else I=F<<1;T=(VA(z,M)|0)>>15;F=I+(T<<1)|0;if((I^T|0)>0&(F^I|0)<0){pA[D>>2]=1;F=(I>>>31)+2147483647|0}T=L+Q+G+y+U+H+R+s+t+F|0;y=(T|0)<(l|0);l=y?T:l;a=y?Y:a}Y=Y+1<<16>>16}while(Y<<16>>16<256);g=(a&65535)<<18>>16;pf(A,782+(g<<1)|0,_,e,v,u,D);Pi(A,0,w,$,AA,q,eA,D);w=(xi(14,jA[AA>>1]|0,D)|0)&65535;pf(A,782+((g|2)<<1)|0,w,jA[$>>1]|0,c,C,D);WA=fA;return a|0}function pf(A,e,f,i,r,n,t){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;var w=0,l=0,B=0,a=0;a=WA;WA=WA+16|0;l=a+2|0;B=a;jA[r>>1]=jA[e>>1]|0;w=jA[e+2>>1]|0;f=VA(f<<16>>16<<1,w)|0;r=10-(i&65535)|0;e=r&65535;r=r<<16>>16;if(e<<16>>16>0)e=e<<16>>16<31?f>>r:0;else{r=0-r<<16>>16;e=f<<r;e=(e>>r|0)==(f|0)?e:f>>31^2147483647}jA[n>>1]=e>>>16;Gi(w,l,B,t);jA[l>>1]=(DA[l>>1]|0)+65524;r=ir(jA[B>>1]|0,5,t)|0;i=jA[l>>1]|0;r=((i&65535)<<10)+(r&65535)&65535;f=jA[B>>1]|0;i=i<<16>>16;if((i*24660|0)==1073741824){pA[t>>2]=1;e=2147483647}else e=i*49320|0;B=(f<<16>>16)*24660>>15;i=e+(B<<1)|0;if(!((e^B|0)>0&(i^e|0)<0)){t=i;t=t<<13;t=t+32768|0;t=t>>>16;t=t&65535;ki(A,r,t);WA=a;return}pA[t>>2]=1;t=(e>>>31)+2147483647|0;t=t<<13;t=t+32768|0;t=t>>>16;t=t&65535;ki(A,r,t);WA=a;return}function Wf(A,e,f,i,r,n,t,w,l,B,a,s,o,Q,g,v,u,c,C,D,E){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;l=l|0;B=B|0;a=a|0;s=s|0;o=o|0;Q=Q|0;g=g|0;v=v|0;u=u|0;c=c|0;C=C|0;D=D|0;E=E|0;var h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0,T=0,y=0,Y=0,z=0,X=0,J=0,O=0,m=0,N=0,K=0,x=0,S=0,j=0,p=0,W=0,V=0,Z=0,_=0,q=0,$=0,AA=0,eA=0,fA=0,iA=0,rA=0,nA=0,tA=0;tA=WA;WA=WA+80|0;iA=tA+72|0;rA=tA+70|0;nA=tA+68|0;eA=tA+66|0;fA=tA+56|0;p=tA+24|0;j=tA+12|0;x=tA+48|0;S=tA+40|0;J=tA+34|0;m=tA+22|0;z=tA+6|0;X=tA;Zf(5,Q,g,z,X,pA[D+72>>2]|0,E)|0;k=xi(14,B,E)|0;O=D+68|0;Y=pA[O>>2]|0;K=l<<16>>16;N=K+65526|0;Q=(DA[n>>1]|0)+65523&65535;jA[fA>>1]=Q;D=(DA[n+2>>1]|0)+65522&65535;jA[fA+2>>1]=D;q=N<<16>>16;$=((N<<17>>17|0)==(q|0)?N<<1:q>>>15^32767)+15+(DA[n+4>>1]|0)&65535;jA[fA+4>>1]=$;AA=(DA[n+6>>1]|0)+q&65535;jA[fA+6>>1]=AA;n=q+1+(DA[n+8>>1]|0)&65535;jA[fA+8>>1]=n;D=D<<16>>16>Q<<16>>16?D:Q;D=$<<16>>16>D<<16>>16?$:D;D=AA<<16>>16>D<<16>>16?AA:D;D=(Ci(n<<16>>16>D<<16>>16?n:D,1,E)|0)&65535;n=Q;Q=0;while(1){B=D-(n&65535)|0;n=B&65535;P=DA[r+(Q<<1)>>1]<<16;B=B<<16>>16;if(n<<16>>16>0)B=n<<16>>16<31?P>>B:0;else{ZA()}Pf(B,p+(Q<<1)|0,j+(Q<<1)|0,E);B=Q+1|0;if((B|0)==5)break;n=jA[fA+(B<<1)>>1]|0;Q=B}T=p+2|0;y=j+2|0;AA=k<<16>>16;W=p+4|0;V=j+4|0;Z=p+6|0;_=j+6|0;q=p+8|0;$=j+8|0;I=0;n=2147483647;r=0;B=0;while(1){R=jA[z+(r<<1)>>1]|0;k=VA(R,R)|0;if(k>>>0>1073741823){pA[E>>2]=1;k=32767}else k=k>>>15;D=jA[j>>1]|0;P=k<<16>>16;k=VA(P,jA[p>>1]|0)|0;if((k|0)==1073741824){pA[E>>2]=1;Q=2147483647}else Q=k<<1;L=(VA(D<<16>>16,P)|0)>>15;k=Q+(L<<1)|0;if((Q^L|0)>0&(k^Q|0)<0){pA[E>>2]=1;k=(Q>>>31)+2147483647|0}D=jA[y>>1]|0;P=VA(jA[T>>1]|0,R)|0;if((P|0)!=1073741824){Q=(P<<1)+k|0;if((P^k|0)>0&(Q^k|0)<0){pA[E>>2]=1;Q=(k>>>31)+2147483647|0}}else{pA[E>>2]=1;Q=2147483647}k=(VA(D<<16>>16,R)|0)>>15;if((k|0)>32767){pA[E>>2]=1;k=32767}L=k<<16;k=(L>>15)+Q|0;if((L>>16^Q|0)>0&(k^Q|0)<0){pA[E>>2]=1;L=(Q>>>31)+2147483647|0}else L=k;U=(L>>>31)+2147483647|0;G=r&65535;k=I;F=0;H=Y;do{P=(VA(jA[H>>1]|0,AA)|0)>>15;H=H+6|0;if((P|0)>32767){pA[E>>2]=1;P=32767}M=P<<16>>16;P=VA(M,M)|0;if((P|0)==1073741824){pA[E>>2]=1;d=2147483647}else d=P<<1;Pf(d,iA,rA,E);P=VA(M,R)|0;if((P|0)==1073741824){pA[E>>2]=1;d=2147483647}else d=P<<1;Pf(d,nA,eA,E);Q=jA[V>>1]|0;b=jA[rA>>1]|0;P=jA[W>>1]|0;D=jA[iA>>1]|0;I=VA(D,P)|0;if((I|0)!=1073741824){d=(I<<1)+L|0;if((I^L|0)>0&(d^L|0)<0){pA[E>>2]=1;d=U}}else{pA[E>>2]=1;d=2147483647}I=(VA(b<<16>>16,P)|0)>>15;if((I|0)>32767){pA[E>>2]=1;I=32767}b=I<<16;I=(b>>15)+d|0;if((b>>16^d|0)>0&(I^d|0)<0){pA[E>>2]=1;I=(d>>>31)+2147483647|0}d=(VA(D,Q<<16>>16)|0)>>15;if((d|0)>32767){pA[E>>2]=1;d=32767}b=d<<16;d=(b>>15)+I|0;if((b>>16^I|0)>0&(d^I|0)<0){pA[E>>2]=1;d=(I>>>31)+2147483647|0}P=jA[_>>1]|0;I=VA(jA[Z>>1]|0,M)|0;if((I|0)!=1073741824){b=(I<<1)+d|0;if((I^d|0)>0&(b^d|0)<0){ZA()}}else{pA[E>>2]=1;b=2147483647}P=(VA(P<<16>>16,M)|0)>>15;if((P|0)>32767){pA[E>>2]=1;P=32767}M=P<<16;P=(M>>15)+b|0;if((M>>16^b|0)>0&(P^b|0)<0){pA[E>>2]=1;P=(b>>>31)+2147483647|0}D=jA[$>>1]|0;b=jA[eA>>1]|0;Q=jA[q>>1]|0;h=jA[nA>>1]|0;I=VA(h,Q)|0;do{if((I|0)==1073741824){pA[E>>2]=1;I=2147483647}else{d=(I<<1)+P|0;if(!((I^P|0)>0&(d^P|0)<0)){I=d;break}pA[E>>2]=1;I=(P>>>31)+2147483647|0}}while(0);d=(VA(b<<16>>16,Q)|0)>>15;if((d|0)>32767){pA[E>>2]=1;d=32767}M=d<<16;d=(M>>15)+I|0;if((M>>16^I|0)>0&(d^I|0)<0){pA[E>>2]=1;d=(I>>>31)+2147483647|0}P=(VA(h,D<<16>>16)|0)>>15;if((P|0)>32767){pA[E>>2]=1;P=32767}M=P<<16;P=(M>>15)+d|0;if((M>>16^d|0)>0&(P^d|0)<0){pA[E>>2]=1;P=(d>>>31)+2147483647|0}M=(P|0)<(n|0);k=M?F:k;B=M?G:B;n=M?P:n;F=F+1<<16>>16}while(F<<16>>16<32);r=r+1|0;if((r|0)==3){P=k;r=B;break}else I=k}y=(P<<16>>16)*3|0;n=jA[Y+(y<<1)>>1]|0;jA[u>>1]=jA[Y+(y+1<<1)>>1]|0;jA[c>>1]=jA[Y+(y+2<<1)>>1]|0;n=VA(n<<16>>16,AA)|0;if((n|0)==1073741824){pA[E>>2]=1;k=2147483647}else k=n<<1;y=9-K|0;Y=y&65535;y=y<<16>>16;T=Y<<16>>16>0;if(T)k=Y<<16>>16<31?k>>y:0;else{L=0-y<<16>>16;R=k<<L;k=(R>>L|0)==(k|0)?R:k>>31^2147483647}jA[v>>1]=k>>>16;R=r<<16>>16;z=jA[z+(R<<1)>>1]|0;jA[g>>1]=z;X=jA[X+(R<<1)>>1]|0;Re(e,f,i,z,a,x,S,J,E);wf(A,jA[J>>1]|0,jA[v>>1]|0,m,E);if(!((jA[x>>1]|0)!=0&(jA[m>>1]|0)>0)){E=P;u=pA[C>>2]|0;v=u+2|0;jA[u>>1]=X;u=u+4|0;pA[C>>2]=u;jA[v>>1]=E;WA=tA;return}M=x+6|0;jA[M>>1]=w;d=S+6|0;jA[d>>1]=t;l=((nr(o,l,E)|0)&65535)+10|0;D=l<<16>>16;if((l&65535)<<16>>16<0){B=0-D<<16;if((B|0)<983040)s=s<<16>>16>>(B>>16)&65535;else s=0}else{B=s<<16>>16;Q=B<<D;if((Q<<16>>16>>D|0)==(B|0))s=Q&65535;else s=(B>>>15^32767)&65535}n=jA[g>>1]|0;k=jA[m>>1]|0;O=pA[O>>2]|0;Q=jA[v>>1]|0;m=10-K|0;D=m<<16>>16;if((m&65535)<<16>>16<0){B=0-D<<16;if((B|0)<983040)w=Q<<16>>16>>(B>>16)&65535;else w=0}else{B=Q<<16>>16;Q=B<<D;if((Q<<16>>16>>D|0)==(B|0))w=Q&65535;else w=(B>>>15^32767)&65535}r=n<<16>>16;B=VA(r,r)|0;if(B>>>0>1073741823){pA[E>>2]=1;n=32767}else n=B>>>15;P=Ci(32767-(k&65535)&65535,1,E)|0;k=k<<16>>16;B=VA(jA[x+2>>1]|0,k)|0;if((B|0)==1073741824){pA[E>>2]=1;B=2147483647}else B=B<<1;m=B<<1;B=VA(((m>>1|0)==(B|0)?m:B>>31^2147418112)>>16,n<<16>>16)|0;if((B|0)==1073741824){pA[E>>2]=1;I=2147483647}else I=B<<1;b=(DA[S+2>>1]|0)+65521|0;D=b&65535;B=VA(jA[x+4>>1]|0,k)|0;if((B|0)==1073741824){pA[E>>2]=1;n=2147483647}else n=B<<1;B=n<<1;B=(VA(((B>>1|0)==(n|0)?B:n>>31^2147418112)>>16,r)|0)>>15;if((B|0)>32767){pA[E>>2]=1;B=32767}jA[W>>1]=B;n=N&65535;jA[iA>>1]=n;n=Ci(jA[S+4>>1]|0,n,E)|0;B=VA(jA[M>>1]|0,k)|0;if((B|0)==1073741824){pA[E>>2]=1;B=2147483647}else B=B<<1;h=B<<1;jA[Z>>1]=((h>>1|0)==(B|0)?h:B>>31^2147418112)>>>16;h=((K<<17>>17|0)==(K|0)?K<<1:K>>>15^32767)+65529&65535;jA[iA>>1]=h;h=Ci(jA[d>>1]|0,h,E)|0;B=(VA(jA[M>>1]|0,P<<16>>16)|0)>>15;if((B|0)>32767){pA[E>>2]=1;B=32767}jA[q>>1]=B;P=Ci(h,1,E)|0;Q=VA(jA[x>>1]|0,k)|0;if((Q|0)==1073741824){pA[E>>2]=1;B=2147483647}else B=Q<<1;d=rr(B,iA,E)|0;r=(DA[iA>>1]|0)+47|0;jA[iA>>1]=r;r=(DA[S>>1]|0)-(r&65535)|0;k=r+31&65535;k=D<<16>>16>k<<16>>16?D:k;k=n<<16>>16>k<<16>>16?n:k;k=h<<16>>16>k<<16>>16?h:k;k=(P<<16>>16>k<<16>>16?P:k)<<16>>16;Q=k-(b&65535)|0;B=Q&65535;Q=Q<<16>>16;if(B<<16>>16>0)L=B<<16>>16<31?I>>Q:0;else{S=0-Q<<16>>16;L=I<<S;L=(L>>S|0)==(I|0)?L:I>>31^2147483647}D=k-(n&65535)|0;B=D&65535;Q=DA[W>>1]<<16;D=D<<16>>16;if(B<<16>>16>0)Q=B<<16>>16<31?Q>>D:0;else{x=0-D<<16>>16;S=Q<<x;Q=(S>>x|0)==(Q|0)?S:Q>>31^2147483647}Pf(Q,W,V,E);h=k-(h&65535)|0;Q=h&65535;D=DA[Z>>1]<<16;h=h<<16>>16;if(Q<<16>>16>0)Q=Q<<16>>16<31?D>>h:0;else{ZA()}Pf(Q,Z,_,E);h=k-(P&65535)|0;Q=h&65535;D=DA[q>>1]<<16;h=h<<16>>16;if(Q<<16>>16>0)Q=Q<<16>>16<31?D>>h:0;else{S=0-h<<16>>16;Q=D<<S;Q=(Q>>S|0)==(D|0)?Q:D>>31^2147483647}Pf(Q,q,$,E);h=k+65505|0;jA[iA>>1]=h;h=h-(r&65535)|0;Q=fr(h&65535,1,E)|0;D=Q<<16>>16;if(Q<<16>>16>0)D=Q<<16>>16<31?d>>D:0;else{S=0-D<<16>>16;D=d<<S;D=(D>>S|0)==(d|0)?D:d>>31^2147483647}do{if(!(h&1))I=D;else{Pf(D,p,j,E);Q=jA[j>>1]|0;D=jA[p>>1]|0;if((D*23170|0)==1073741824){pA[E>>2]=1;h=2147483647}else h=D*46340|0;p=(Q<<16>>16)*23170>>15;D=h+(p<<1)|0;if(!((h^p|0)>0&(D^h|0)<0)){I=D;break}pA[E>>2]=1;I=(h>>>31)+2147483647|0}}while(0);M=(L>>>31)+2147483647|0;d=2147483647;b=0;D=0;F=O;while(1){Q=(VA(jA[F>>1]|0,AA)|0)>>15;F=F+6|0;if((Q|0)>32767){pA[E>>2]=1;Q=32767}h=Q&65535;if(h<<16>>16>=w<<16>>16)break;n=Q<<16>>16;Q=VA(n,n)|0;if((Q|0)==1073741824){pA[E>>2]=1;B=2147483647}else B=Q<<1;Pf(B,rA,nA,E);Q=(nr(h,s,E)|0)<<16>>16;Q=VA(Q,Q)|0;if((Q|0)==1073741824){pA[E>>2]=1;Q=2147483647}else Q=Q<<1;Pf(Q,eA,fA,E);h=jA[V>>1]|0;B=VA(jA[W>>1]|0,n)|0;do{if((B|0)==1073741824){pA[E>>2]=1;B=2147483647}else{Q=(B<<1)+L|0;if(!((B^L|0)>0&(Q^L|0)<0)){B=Q;break}pA[E>>2]=1;B=M}}while(0);Q=(VA(h<<16>>16,n)|0)>>15;if((Q|0)>32767){pA[E>>2]=1;Q=32767}p=Q<<16;Q=(p>>15)+B|0;if((p>>16^B|0)>0&(Q^B|0)<0){pA[E>>2]=1;Q=(B>>>31)+2147483647|0}r=jA[_>>1]|0;P=jA[nA>>1]|0;n=jA[Z>>1]|0;k=jA[rA>>1]|0;B=VA(k,n)|0;do{if((B|0)==1073741824){pA[E>>2]=1;h=2147483647}else{h=(B<<1)+Q|0;if(!((B^Q|0)>0&(h^Q|0)<0))break;pA[E>>2]=1;h=(Q>>>31)+2147483647|0}}while(0);B=(VA(P<<16>>16,n)|0)>>15;if((B|0)>32767){pA[E>>2]=1;B=32767}p=B<<16;B=(p>>15)+h|0;if((p>>16^h|0)>0&(B^h|0)<0){pA[E>>2]=1;B=(h>>>31)+2147483647|0}Q=(VA(k,r<<16>>16)|0)>>15;if((Q|0)>32767){pA[E>>2]=1;Q=32767}p=Q<<16;Q=(p>>15)+B|0;if((p>>16^B|0)>0&(Q^B|0)<0){pA[E>>2]=1;Q=(B>>>31)+2147483647|0}Q=rr(Q,iA,E)|0;h=fr(jA[iA>>1]|0,1,E)|0;B=h<<16>>16;if(h<<16>>16>0)h=h<<16>>16<31?Q>>B:0;else{p=0-B<<16>>16;h=Q<<p;h=(h>>p|0)==(Q|0)?h:Q>>31^2147483647}Q=h-I|0;if(((Q^h)&(h^I)|0)<0){pA[E>>2]=1;Q=(h>>>31)+2147483647|0}Q=(er(Q,E)|0)<<16>>16;Q=VA(Q,Q)|0;if((Q|0)==1073741824){pA[E>>2]=1;h=2147483647}else h=Q<<1;k=jA[$>>1]|0;n=jA[fA>>1]|0;P=jA[q>>1]|0;r=jA[eA>>1]|0;B=VA(r,P)|0;do{if((B|0)==1073741824){pA[E>>2]=1;Q=2147483647}else{Q=(B<<1)+h|0;if(!((B^h|0)>0&(Q^h|0)<0))break;pA[E>>2]=1;Q=(h>>>31)+2147483647|0}}while(0);B=(VA(n<<16>>16,P)|0)>>15;if((B|0)>32767){pA[E>>2]=1;B=32767}p=B<<16;B=(p>>15)+Q|0;if((p>>16^Q|0)>0&(B^Q|0)<0){pA[E>>2]=1;B=(Q>>>31)+2147483647|0}Q=(VA(r,k<<16>>16)|0)>>15;if((Q|0)>32767){pA[E>>2]=1;Q=32767}p=Q<<16;Q=(p>>15)+B|0;if((p>>16^B|0)>0&(Q^B|0)<0){pA[E>>2]=1;Q=(B>>>31)+2147483647|0}B=(Q|0)<(d|0);D=B?b:D;b=b+1<<16>>16;if(b<<16>>16>=32)break;else d=B?Q:d}nA=(D<<16>>16)*3|0;h=jA[O+(nA<<1)>>1]|0;jA[u>>1]=jA[O+(nA+1<<1)>>1]|0;jA[c>>1]=jA[O+(nA+2<<1)>>1]|0;h=VA(h<<16>>16,AA)|0;if((h|0)==1073741824){pA[E>>2]=1;h=2147483647}else h=h<<1;if(T)h=Y<<16>>16<31?h>>y:0;else{u=0-y<<16>>16;E=h<<u;h=(E>>u|0)==(h|0)?E:h>>31^2147483647}jA[v>>1]=h>>>16;E=D;u=pA[C>>2]|0;v=u+2|0;jA[u>>1]=X;u=u+4|0;pA[C>>2]=u;jA[v>>1]=E;WA=tA;return}function Vf(A,e,f,i,r,n,t,w){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;var l=0,B=0,a=0,s=0,o=0;o=(A|0)==7;l=jA[i>>1]|0;if(o){l=l<<16>>16>>>1&65535;s=xi(e,f,w)|0;e=s<<16;A=e>>16;if((s<<20>>20|0)==(A|0))A=e>>12;else A=A>>>15^32767}else{ZA()}s=A<<16>>16;w=l<<16>>16;e=w-((VA(s,jA[t>>1]|0)|0)>>>15&65535)|0;e=((e&32768|0)!=0?0-e|0:e)&65535;B=1;A=0;a=t;while(1){a=a+6|0;l=w-((VA(jA[a>>1]|0,s)|0)>>>15&65535)|0;f=l<<16;l=(f|0)<0?0-(f>>16)|0:l;f=(l<<16>>16|0)<(e<<16>>16|0);A=f?B:A;B=B+1<<16>>16;if(B<<16>>16>=32)break;else e=f?l&65535:e}a=(A<<16>>16)*196608>>16;jA[i>>1]=(VA(jA[t+(a<<1)>>1]|0,s)|0)>>>15<<(o&1);jA[r>>1]=jA[t+(a+1<<1)>>1]|0;jA[n>>1]=jA[t+(a+2<<1)>>1]|0;return A|0}function Zf(A,e,f,i,r,n,t){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;var w=0,l=0,B=0,a=0,s=0,o=0;w=nr(jA[f>>1]|0,jA[n>>1]|0,t)|0;w=(w&65535)-((w&65535)>>>15&65535)|0;w=(w<<16>>31^w)&65535;B=0;a=1;while(1){l=jA[n+(a<<1)>>1]|0;if(l<<16>>16>e<<16>>16)l=w;else{l=nr(jA[f>>1]|0,l,t)|0;l=(l&65535)-((l&65535)>>>15&65535)|0;l=(l<<16>>31^l)&65535;o=l<<16>>16<w<<16>>16;l=o?l:w;B=o?a&65535:B}a=a+1|0;if((a|0)==16)break;else w=l}if((A|0)!=5){w=jA[n+(B<<16>>16<<1)>>1]|0;if((A|0)==7){jA[f>>1]=w&65532;return B|0}else{jA[f>>1]=w;return B|0}}l=B<<16>>16;switch(B<<16>>16){case 0:{w=0;break}case 15:{s=8;break}default:if((jA[n+(l+1<<1)>>1]|0)>e<<16>>16)s=8;else w=l+65535&65535}if((s|0)==8)w=l+65534&65535;jA[r>>1]=w;o=w<<16>>16;jA[i>>1]=jA[n+(o<<1)>>1]|0;o=o+1|0;jA[r+2>>1]=o;o=o<<16>>16;jA[i+2>>1]=jA[n+(o<<1)>>1]|0;o=o+1|0;jA[r+4>>1]=o;jA[i+4>>1]=jA[n+(o<<16>>16<<1)>>1]|0;jA[f>>1]=jA[n+(l<<1)>>1]|0;return B|0}function _f(A,e,f,i,r,n,t,w,l,B,a,s){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;l=l|0;B=B|0;a=a|0;s=s|0;var o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0;L=WA;WA=WA+32|0;g=L+20|0;v=L+10|0;Q=L;switch(A|0){case 3:case 4:case 6:{a=a+84|0;G=128;break}default:{a=a+80|0;G=64}}U=pA[a>>2]|0;o=xi(14,f,s)|0;H=e<<16>>16;F=H+65525|0;A=(DA[r>>1]|0)+65523&65535;jA[Q>>1]=A;e=(DA[r+2>>1]|0)+65522&65535;jA[Q+2>>1]=e;M=F<<16>>16;M=Ci(jA[r+4>>1]|0,((F<<17>>17|0)==(M|0)?F<<1:M>>>15^32767)+15&65535,s)|0;jA[Q+4>>1]=M;F=Ci(jA[r+6>>1]|0,F&65535,s)|0;jA[Q+6>>1]=F;r=Ci(jA[r+8>>1]|0,H+65526&65535,s)|0;jA[Q+8>>1]=r;e=e<<16>>16>A<<16>>16?e:A;e=M<<16>>16>e<<16>>16?M:e;e=F<<16>>16>e<<16>>16?F:e;e=(r<<16>>16>e<<16>>16?r:e)+1&65535;r=0;while(1){f=e-(A&65535)|0;a=f&65535;A=DA[i+(r<<1)>>1]<<16;f=f<<16>>16;if(a<<16>>16>0)a=a<<16>>16<31?A>>f:0;else{ZA()}Pf(a,g+(r<<1)|0,v+(r<<1)|0,s);a=r+1|0;if((a|0)==5)break;A=jA[Q+(a<<1)>>1]|0;r=a}F=o<<16>>16;E=jA[g>>1]|0;h=jA[v>>1]|0;P=jA[g+2>>1]|0;k=jA[v+2>>1]|0;b=jA[g+4>>1]|0;d=jA[v+4>>1]|0;I=jA[g+6>>1]|0;M=jA[v+6>>1]|0;D=jA[g+8>>1]|0;u=jA[v+8>>1]|0;e=2147483647;c=0;a=0;C=U;while(1){r=jA[C>>1]|0;if(r<<16>>16>n<<16>>16)o=e;else{o=(VA(jA[C+2>>1]|0,F)|0)>>15;if((o|0)>32767){pA[s>>2]=1;o=32767}v=r<<16>>16;r=VA(v,v)|0;if(r>>>0>1073741823){pA[s>>2]=1;Q=32767}else Q=r>>>15;f=o<<16>>16;o=VA(f,f)|0;if(o>>>0>1073741823){pA[s>>2]=1;g=32767}else g=o>>>15;i=(VA(f,v)|0)>>15;if((i|0)>32767){pA[s>>2]=1;i=32767}o=Q<<16>>16;Q=VA(E,o)|0;if((Q|0)==1073741824){pA[s>>2]=1;r=2147483647}else r=Q<<1;o=(VA(h,o)|0)>>15;Q=r+(o<<1)|0;if((r^o|0)>0&(Q^r|0)<0){pA[s>>2]=1;Q=(r>>>31)+2147483647|0}o=VA(P,v)|0;if((o|0)==1073741824){pA[s>>2]=1;r=2147483647}else r=o<<1;v=(VA(k,v)|0)>>15;o=r+(v<<1)|0;if((r^v|0)>0&(o^r|0)<0){pA[s>>2]=1;o=(r>>>31)+2147483647|0}r=o+Q|0;if((o^Q|0)>-1&(r^Q|0)<0){pA[s>>2]=1;r=(Q>>>31)+2147483647|0}o=g<<16>>16;Q=VA(b,o)|0;if((Q|0)==1073741824){pA[s>>2]=1;A=2147483647}else A=Q<<1;v=(VA(d,o)|0)>>15;Q=A+(v<<1)|0;if((A^v|0)>0&(Q^A|0)<0){pA[s>>2]=1;Q=(A>>>31)+2147483647|0}o=Q+r|0;if((Q^r|0)>-1&(o^r|0)<0){pA[s>>2]=1;A=(r>>>31)+2147483647|0}else A=o;o=VA(I,f)|0;if((o|0)==1073741824){pA[s>>2]=1;Q=2147483647}else Q=o<<1;v=(VA(M,f)|0)>>15;o=Q+(v<<1)|0;if((Q^v|0)>0&(o^Q|0)<0){pA[s>>2]=1;o=(Q>>>31)+2147483647|0}r=o+A|0;if((o^A|0)>-1&(r^A|0)<0){pA[s>>2]=1;Q=(A>>>31)+2147483647|0}else Q=r;r=i<<16>>16;o=VA(D,r)|0;if((o|0)==1073741824){pA[s>>2]=1;A=2147483647}else A=o<<1;v=(VA(u,r)|0)>>15;o=A+(v<<1)|0;if((A^v|0)>0&(o^A|0)<0){pA[s>>2]=1;r=(A>>>31)+2147483647|0}else r=o;o=r+Q|0;if((r^Q|0)>-1&(o^Q|0)<0){pA[s>>2]=1;o=(Q>>>31)+2147483647|0}v=(o|0)<(e|0);o=v?o:e;a=v?c:a}C=C+8|0;c=c+1<<16>>16;if((c<<16>>16|0)>=(G|0))break;else e=o}n=a<<16>>16;n=((n<<18>>18|0)==(n|0)?n<<2:n>>>15^32767)<<16>>16;jA[t>>1]=jA[U+(n<<1)>>1]|0;e=jA[U+(n+1<<1)>>1]|0;jA[l>>1]=jA[U+(n+2<<1)>>1]|0;jA[B>>1]=jA[U+(n+3<<1)>>1]|0;e=VA(e<<16>>16,F)|0;if((e|0)==1073741824){pA[s>>2]=1;A=2147483647}else A=e<<1;f=10-H|0;e=f&65535;f=f<<16>>16;if(e<<16>>16>0){s=e<<16>>16<31?A>>f:0;s=s>>>16;s=s&65535;jA[w>>1]=s;WA=L;return a|0}else{l=0-f<<16>>16;s=A<<l;s=(s>>l|0)==(A|0)?s:A>>31^2147483647;s=s>>>16;s=s&65535;jA[w>>1]=s;WA=L;return a|0}return 0}function qf(A,e,f,i,r,n,t,w,l){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;l=l|0;var B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0,T=0,y=0,Y=0,z=0,X=0,J=0,O=0,m=0,N=0,K=0,x=0,S=0,j=0,p=0,W=0,V=0,Z=0,_=0,q=0,$=0,AA=0,eA=0,fA=0,iA=0,rA=0,nA=0,tA=0,wA=0,lA=0,BA=0,aA=0,sA=0,oA=0,QA=0,gA=0,vA=0,uA=0,cA=0,CA=0;CA=WA;WA=WA+160|0;cA=CA;a=A<<16>>16;vA=A<<16>>16==10;uA=jA[t+(jA[n>>1]<<1)>>1]|0;if(A<<16>>16>0){l=0;B=w;while(1){jA[B>>1]=l;l=l+1<<16>>16;if(l<<16>>16>=A<<16>>16)break;else B=B+2|0}}if(f<<16>>16<=1){WA=CA;return}QA=n+2|0;gA=uA<<16>>16;aA=i+(gA<<1)|0;sA=r+(gA*80|0)+(gA<<1)|0;oA=n+6|0;j=e&65535;p=n+4|0;W=n+10|0;V=n+8|0;Z=n+14|0;_=n+12|0;q=n+18|0;$=n+16|0;AA=w+2|0;eA=w+4|0;fA=w+6|0;iA=w+8|0;rA=w+10|0;nA=w+12|0;tA=w+14|0;wA=w+16|0;lA=w+18|0;BA=A<<16>>16>2;x=n+(a+-1<<1)|0;N=1;S=1;Y=0;z=0;K=-1;while(1){m=jA[t+(jA[QA>>1]<<1)>>1]|0;O=m<<16>>16;e=(DA[i+(O<<1)>>1]|0)+(DA[aA>>1]|0)|0;B=(jA[r+(gA*80|0)+(O<<1)>>1]<<13)+32768+((jA[r+(O*80|0)+(O<<1)>>1]|0)+(jA[sA>>1]|0)<<12)|0;a=jA[oA>>1]|0;if(a<<16>>16<40){a=a<<16>>16;s=cA;while(1){X=(jA[r+(a*80|0)+(a<<1)>>1]|0)>>>1;y=jA[r+(a*80|0)+(gA<<1)>>1]|0;J=jA[r+(a*80|0)+(O<<1)>>1]|0;jA[s>>1]=e+(DA[i+(a<<1)>>1]|0);jA[s+2>>1]=(y+2+X+J|0)>>>2;a=a+j|0;if((a&65535)<<16>>16<40){a=a<<16>>16;s=s+4|0}else break}b=jA[oA>>1]|0}else b=a;e=jA[p>>1]|0;k=B>>12;a=e<<16>>16;A:do{if(e<<16>>16<40){P=b<<16>>16;if(b<<16>>16<40){s=1;Q=e;v=b;g=0;o=-1}else while(1){ZA()}while(1){h=((jA[r+(a*80|0)+(a<<1)>>1]|0)+k>>1)+(jA[r+(a*80|0)+(gA<<1)>>1]|0)+(jA[r+(a*80|0)+(O<<1)>>1]|0)|0;E=DA[i+(a<<1)>>1]|0;C=P;D=b;c=cA;u=g;while(1){B=(DA[c>>1]|0)+E|0;l=B<<16>>16;l=(VA(l,l)|0)>>>15;g=(h+(jA[r+(a*80|0)+(C<<1)>>1]|0)>>2)+(jA[c+2>>1]|0)>>1;if((VA(l<<16>>16,s<<16>>16)|0)>(VA(g,o<<16>>16)|0)){s=g&65535;Q=e;v=D;g=B&65535;o=l&65535}else g=u;B=C+j|0;D=B&65535;if(D<<16>>16>=40)break;else{C=B<<16>>16;c=c+4|0;u=g}}a=a+j|0;e=a&65535;if(e<<16>>16<40)a=a<<16>>16;else{J=Q;X=v;a=g;break}}}else{s=1;J=e;X=b;a=0}}while(0);Q=s<<16>>16<<15;s=jA[W>>1]|0;if(s<<16>>16<40){B=J<<16>>16;l=X<<16>>16;e=a&65535;s=s<<16>>16;a=cA;while(1){L=jA[r+(s*80|0)+(s<<1)>>1]>>1;G=jA[r+(s*80|0)+(gA<<1)>>1]|0;R=jA[r+(s*80|0)+(O<<1)>>1]|0;T=jA[r+(s*80|0)+(B<<1)>>1]|0;y=jA[r+(s*80|0)+(l<<1)>>1]|0;jA[a>>1]=(DA[i+(s<<1)>>1]|0)+e;jA[a+2>>1]=(G+2+L+R+T+y|0)>>>2;s=s+j|0;if((s&65535)<<16>>16<40){s=s<<16>>16;a=a+4|0}else break}L=jA[W>>1]|0}else L=s;o=jA[V>>1]|0;s=o<<16>>16;A:do{if(o<<16>>16<40){d=J<<16>>16;I=X<<16>>16;M=L<<16>>16;b=Q+32768|0;if(L<<16>>16<40){g=1;Q=o;e=L;v=o;a=0;o=-1}else while(1){ZA()}while(1){l=DA[i+(s<<1)>>1]|0;k=(jA[r+(s*80|0)+(O<<1)>>1]|0)+(jA[r+(s*80|0)+(gA<<1)>>1]|0)+(jA[r+(s*80|0)+(d<<1)>>1]|0)+(jA[r+(s*80|0)+(I<<1)>>1]|0)|0;P=b+(jA[r+(s*80|0)+(s<<1)>>1]<<11)|0;E=M;C=L;h=cA;while(1){u=(DA[h>>1]|0)+l|0;B=P+(jA[h+2>>1]<<14)+(k+(jA[r+(s*80|0)+(E<<1)>>1]|0)<<12)|0;c=u<<16>>16;c=(VA(c,c)|0)>>>15;if((VA(c<<16>>16,g<<16>>16)|0)>(VA(B>>16,o<<16>>16)|0)){g=B>>>16&65535;D=v;e=C;a=u&65535;o=c&65535}else D=Q;Q=E+j|0;C=Q&65535;if(C<<16>>16>=40){Q=D;break}else{E=Q<<16>>16;Q=D;h=h+4|0}}s=s+j|0;v=s&65535;if(v<<16>>16<40)s=s<<16>>16;else{s=g;y=Q;T=e;break}}}else{s=1;y=o;T=L;a=0}}while(0);g=s<<16>>16<<15;s=jA[Z>>1]|0;if(s<<16>>16<40){B=J<<16>>16;l=X<<16>>16;o=y<<16>>16;Q=T<<16>>16;e=a&65535;s=s<<16>>16;a=cA;while(1){F=jA[r+(s*80|0)+(s<<1)>>1]>>1;M=jA[r+(gA*80|0)+(s<<1)>>1]|0;H=jA[r+(O*80|0)+(s<<1)>>1]|0;U=jA[r+(B*80|0)+(s<<1)>>1]|0;G=jA[r+(l*80|0)+(s<<1)>>1]|0;L=jA[r+(o*80|0)+(s<<1)>>1]|0;R=jA[r+(Q*80|0)+(s<<1)>>1]|0;jA[a>>1]=(DA[i+(s<<1)>>1]|0)+e;jA[a+2>>1]=(M+4+F+H+U+G+L+R|0)>>>3;s=s+j|0;if((s&65535)<<16>>16<40){s=s<<16>>16;a=a+4|0}else break}e=jA[Z>>1]|0}else e=s;v=jA[_>>1]|0;if(v<<16>>16<40){L=J<<16>>16;F=X<<16>>16;M=y<<16>>16;I=T<<16>>16;d=e<<16>>16;b=e<<16>>16<40;H=g+32768|0;G=v<<16>>16;l=1;D=v;C=e;U=v;Q=0;s=-1;while(1){if(b){g=DA[i+(G<<1)>>1]|0;a=(jA[r+(G*80|0)+(O<<1)>>1]|0)+(jA[r+(G*80|0)+(gA<<1)>>1]|0)+(jA[r+(G*80|0)+(L<<1)>>1]|0)+(jA[r+(G*80|0)+(F<<1)>>1]|0)+(jA[r+(G*80|0)+(M<<1)>>1]|0)+(jA[r+(G*80|0)+(I<<1)>>1]|0)|0;o=H+(jA[r+(G*80|0)+(G<<1)>>1]<<10)|0;c=d;v=e;P=C;k=cA;while(1){h=(DA[k>>1]|0)+g|0;C=o+(jA[k+2>>1]<<14)+(a+(jA[r+(G*80|0)+(c<<1)>>1]|0)<<11)|0;E=h<<16>>16;E=(VA(E,E)|0)>>>15;if((VA(E<<16>>16,l<<16>>16)|0)>(VA(C>>16,s<<16>>16)|0)){l=C>>>16&65535;D=U;C=v;Q=h&65535;s=E&65535}else C=P;u=c+j|0;v=u&65535;if(v<<16>>16>=40)break;else{c=u<<16>>16;P=C;k=k+4|0}}}v=G+j|0;U=v&65535;if(U<<16>>16>=40){R=C;break}else G=v<<16>>16}}else{l=1;D=v;R=e;Q=0;s=-1}if(vA){c=l<<16>>16<<15;s=jA[q>>1]|0;if(s<<16>>16<40){a=J<<16>>16;e=X<<16>>16;B=y<<16>>16;l=T<<16>>16;g=D<<16>>16;v=R<<16>>16;o=Q&65535;s=s<<16>>16;Q=cA;while(1){M=jA[r+(s*80|0)+(s<<1)>>1]>>1;I=jA[r+(gA*80|0)+(s<<1)>>1]|0;F=jA[r+(O*80|0)+(s<<1)>>1]|0;H=jA[r+(a*80|0)+(s<<1)>>1]|0;U=jA[r+(e*80|0)+(s<<1)>>1]|0;G=jA[r+(B*80|0)+(s<<1)>>1]|0;L=jA[r+(l*80|0)+(s<<1)>>1]|0;Y=jA[r+(g*80|0)+(s<<1)>>1]|0;z=jA[r+(v*80|0)+(s<<1)>>1]|0;jA[Q>>1]=(DA[i+(s<<1)>>1]|0)+o;jA[Q+2>>1]=(I+4+M+F+H+U+G+L+Y+z|0)>>>3;s=s+j|0;if((s&65535)<<16>>16<40){s=s<<16>>16;Q=Q+4|0}else break}L=jA[q>>1]|0}else L=s;g=jA[$>>1]|0;if(g<<16>>16<40){M=J<<16>>16;I=X<<16>>16;d=y<<16>>16;B=T<<16>>16;F=D<<16>>16;H=R<<16>>16;U=L<<16>>16;G=L<<16>>16<40;b=c+32768|0;a=g<<16>>16;l=1;v=g;Q=L;e=g;s=-1;while(1){if(G){c=DA[i+(a<<1)>>1]|0;o=(jA[r+(O*80|0)+(a<<1)>>1]|0)+(jA[r+(gA*80|0)+(a<<1)>>1]|0)+(jA[r+(M*80|0)+(a<<1)>>1]|0)+(jA[r+(I*80|0)+(a<<1)>>1]|0)+(jA[r+(d*80|0)+(a<<1)>>1]|0)+(jA[r+(B*80|0)+(a<<1)>>1]|0)+(jA[r+(F*80|0)+(a<<1)>>1]|0)+(jA[r+(H*80|0)+(a<<1)>>1]|0)|0;g=b+(jA[r+(a*80|0)+(a<<1)>>1]<<9)|0;k=U;E=L;P=cA;while(1){h=(DA[P>>1]|0)+c<<16>>16;h=(VA(h,h)|0)>>>15;C=g+(jA[P+2>>1]<<13)+(o+(jA[r+(a*80|0)+(k<<1)>>1]|0)<<10)|0;if((VA(h<<16>>16,l<<16>>16)|0)>(VA(C>>16,s<<16>>16)|0)){l=C>>>16&65535;v=e;Q=E;s=h&65535}u=k+j|0;E=u&65535;if(E<<16>>16>=40)break;else{k=u<<16>>16;P=P+4|0}}}g=a+j|0;e=g&65535;if(e<<16>>16>=40)break;else a=g<<16>>16}}else{l=1;v=g;Q=L;s=-1}}else{v=Y;Q=z}if((VA(s<<16>>16,N<<16>>16)|0)>(VA(l<<16>>16,K<<16>>16)|0)){jA[w>>1]=uA;jA[AA>>1]=m;jA[eA>>1]=J;jA[fA>>1]=X;jA[iA>>1]=y;jA[rA>>1]=T;jA[nA>>1]=D;jA[tA>>1]=R;if(vA){jA[wA>>1]=v;jA[lA>>1]=Q}}else{l=N;s=K}a=jA[QA>>1]|0;if(BA){e=1;B=2;while(1){jA[n+(e<<1)>>1]=jA[n+(B<<1)>>1]|0;B=B+1|0;if((B&65535)<<16>>16==A<<16>>16)break;else e=e+1|0}}jA[x>>1]=a;S=S+1<<16>>16;if(S<<16>>16>=f<<16>>16)break;else{N=l;Y=v;z=Q;K=s}}WA=CA;return}function $f(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var r=0,n=0,t=0,w=0,l=0,B=0;w=39;while(1){t=A+(w<<1)|0;n=jA[t>>1]|0;r=e+(w<<1)|0;if(n<<16>>16>-1)jA[r>>1]=32767;else{jA[r>>1]=-32767;if(n<<16>>16==-32768)n=32767;else n=0-(n&65535)&65535;jA[t>>1]=n}jA[f+(w<<1)>>1]=n;if((w|0)>0)w=w+-1|0;else break}B=8-(i<<16>>16)|0;if((B|0)>0){l=0;r=0}else return;do{i=0;A=0;t=32767;while(1){e=jA[f+(i<<1)>>1]|0;w=e<<16>>16>-1?e<<16>>16<t<<16>>16:0;r=w?A:r;n=i+5|0;A=n&65535;if(A<<16>>16>=40)break;else{i=n<<16>>16;t=w?e:t}}jA[f+(r<<16>>16<<1)>>1]=-1;l=l+1<<16>>16}while((l<<16>>16|0)<(B|0));l=0;do{e=1;A=1;n=32767;while(1){i=jA[f+(e<<1)>>1]|0;w=i<<16>>16>-1?i<<16>>16<n<<16>>16:0;r=w?A:r;t=e+5|0;A=t&65535;if(A<<16>>16>=40)break;else{e=t<<16>>16;n=w?i:n}}jA[f+(r<<16>>16<<1)>>1]=-1;l=l+1<<16>>16}while((l<<16>>16|0)<(B|0));l=0;do{e=2;A=2;n=32767;while(1){i=jA[f+(e<<1)>>1]|0;w=i<<16>>16>-1?i<<16>>16<n<<16>>16:0;r=w?A:r;t=e+5|0;A=t&65535;if(A<<16>>16>=40)break;else{e=t<<16>>16;n=w?i:n}}jA[f+(r<<16>>16<<1)>>1]=-1;l=l+1<<16>>16}while((l<<16>>16|0)<(B|0));l=0;while(1){e=3;A=3;n=32767;while(1){i=jA[f+(e<<1)>>1]|0;w=i<<16>>16>-1?i<<16>>16<n<<16>>16:0;r=w?A:r;t=e+5|0;A=t&65535;if(A<<16>>16>=40){n=r;break}else{e=t<<16>>16;n=w?i:n}}jA[f+(n<<16>>16<<1)>>1]=-1;l=l+1<<16>>16;if((l<<16>>16|0)>=(B|0)){r=0;break}else r=n}do{e=4;A=4;l=32767;while(1){i=jA[f+(e<<1)>>1]|0;w=i<<16>>16>-1?i<<16>>16<l<<16>>16:0;n=w?A:n;t=e+5|0;A=t&65535;if(A<<16>>16>=40)break;else{e=t<<16>>16;l=w?i:l}}jA[f+(n<<16>>16<<1)>>1]=-1;r=r+1<<16>>16}while((r<<16>>16|0)<(B|0));return}function Ai(A,e,f,i,r,n,t,w){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;var l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0;h=WA;WA=WA+80|0;E=h;o=40;Q=e;g=A;B=256;a=256;while(1){l=jA[Q>>1]|0;Q=Q+2|0;l=VA(l,l)|0;if((l|0)!=1073741824){s=(l<<1)+B|0;if((l^B|0)>0&(s^B|0)<0){pA[w>>2]=1;B=(B>>>31)+2147483647|0}else B=s}else{pA[w>>2]=1;B=2147483647}D=jA[g>>1]|0;a=(VA(D<<1,D)|0)+a|0;o=o+-1<<16>>16;if(!(o<<16>>16))break;else g=g+2|0}D=Ui(B,w)|0;c=D<<5;D=((c>>5|0)==(D|0)?c:D>>31^2147418112)>>16;c=(Ui(a,w)|0)<<5>>16;C=39;v=e+78|0;u=E+78|0;l=f+78|0;while(1){g=VA(jA[v>>1]|0,D)|0;v=v+-2|0;Q=g<<1;e=A+(C<<1)|0;B=jA[e>>1]|0;o=VA(B<<16>>16,c)|0;if((o|0)!=1073741824){s=(o<<1)+Q|0;if((o^Q|0)>0&(s^Q|0)<0){pA[w>>2]=1;s=(g>>>30&1)+2147483647|0}}else{pA[w>>2]=1;s=2147483647}a=s<<10;a=er((a>>10|0)==(s|0)?a:s>>31^2147483647,w)|0;if(a<<16>>16>-1)jA[l>>1]=32767;else{jA[l>>1]=-32767;if(a<<16>>16==-32768)a=32767;else a=0-(a&65535)&65535;if(B<<16>>16==-32768)s=32767;else s=0-(B&65535)&65535;jA[e>>1]=s}l=l+-2|0;jA[u>>1]=a;if((C|0)<=0)break;else{C=C+-1|0;u=u+-2|0}}e=r<<16>>16;if(r<<16>>16<=0){jA[n+(e<<1)>>1]=jA[n>>1]|0;WA=h;return}g=t&65535;Q=0;o=-1;l=0;while(1){if((Q|0)<40){a=Q;s=Q&65535;B=-1;while(1){w=jA[E+(a<<1)>>1]|0;t=w<<16>>16>B<<16>>16;B=t?w:B;l=t?s:l;a=a+g|0;s=a&65535;if(s<<16>>16>=40)break;else a=a<<16>>16}}else B=-1;jA[i+(Q<<1)>>1]=l;if(B<<16>>16>o<<16>>16)jA[n>>1]=Q;else B=o;Q=Q+1|0;if((Q&65535)<<16>>16==r<<16>>16)break;else o=B}l=jA[n>>1]|0;jA[n+(e<<1)>>1]=l;if(r<<16>>16>1)B=1;else{WA=h;return}do{i=l+1<<16>>16;l=i<<16>>16>=r<<16>>16?0:i;jA[n+(B<<1)>>1]=l;jA[n+(B+e<<1)>>1]=l;B=B+1|0}while((B&65535)<<16>>16!=r<<16>>16);WA=h;return}function ei(A){A=A|0;var e=0;if(!A){A=-1;return A|0}pA[A>>2]=0;e=lr(12)|0;if(!e){A=-1;return A|0}jA[e>>1]=8;pA[A>>2]=e;jA[e+2>>1]=3;jA[e+4>>1]=0;pA[e+8>>2]=0;A=0;return A|0}function fi(A){A=A|0;var e=0;if(!A)return;e=pA[A>>2]|0;if(!e)return;Br(e);pA[A>>2]=0;return}function ii(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,r=0,n=0;do{if((e|0)==8){ZA()}else{jA[A+2>>1]=jA[A>>1]|0;pA[f>>2]=0;e=A+8|0}}while(0);pA[e>>2]=pA[f>>2];return}function ri(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,r=0,n=0;if(!A){A=-1;return A|0}pA[A>>2]=0;f=lr(12)|0;i=f;if(!f){A=-1;return A|0}pA[f>>2]=0;r=f+4|0;pA[r>>2]=0;n=f+8|0;pA[n>>2]=e;if((Of(f)|0)<<16>>16==0?(me(r,pA[n>>2]|0)|0)<<16>>16==0:0){mf(pA[f>>2]|0)|0;Ke(pA[r>>2]|0)|0;pA[A>>2]=i;A=0;return A|0}Nf(f);Ne(r);Br(f);A=-1;return A|0}function ni(A){A=A|0;var e=0;if(!A)return;e=pA[A>>2]|0;if(!e)return;Nf(e);Ne((pA[A>>2]|0)+4|0);Br(pA[A>>2]|0);pA[A>>2]=0;return}function ti(A,e,f,i,r){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;var n=0,t=0,w=0,l=0,B=0;l=WA;WA=WA+448|0;t=l+320|0;w=l;gr(i|0,0,488)|0;n=0;do{B=f+(n<<1)|0;jA[B>>1]=(DA[B>>1]|0)&65528;n=n+1|0}while((n|0)!=160);Kf(pA[A>>2]|0,f,160);B=A+4|0;xe(pA[B>>2]|0,e,f,t,r,w)|0;xf(pA[r>>2]|0,t,i,(pA[B>>2]|0)+2392|0);WA=l;return}function wi(A,e,f,i,r,n,t,w,l,B,a,s,o,Q,g,v){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;l=l|0;B=B|0;a=a|0;s=s|0;o=o|0;Q=Q|0;g=g|0;v=v|0;var u=0,c=0,C=0;C=WA;WA=WA+48|0;u=C+22|0;c=C;wr(r,(A&-2|0)==6?f:e,u);wr(r,i,c);f=a;e=u;r=f+22|0;do{jA[f>>1]=jA[e>>1]|0;f=f+2|0;e=e+2|0}while((f|0)<(r|0));tr(n,a,o,40,B,0);tr(c,o,o,40,B,0);Ar(n,t,g,40);f=s;e=g;r=f+80|0;do{jA[f>>1]=jA[e>>1]|0;f=f+2|0;e=e+2|0}while((f|0)<(r|0));tr(n,s,v,40,w,0);Ar(u,v,Q,40);tr(c,Q,Q,40,l,0);WA=C;return}function li(A,e,f,i,r,n,t,w,l,B,a,s,o,Q,g,v,u){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;l=l|0;B=B|0;a=a|0;s=s|0;o=o|0;Q=Q|0;g=g|0;v=v|0;u=u|0;var c=0,C=0,D=0,E=0,h=0;if((e|0)==7){D=11;e=i<<16>>16>>>1&65535;c=2}else{D=13;e=i;c=1}jA[v>>1]=i<<16>>16<13017?i:13017;C=f<<16>>16;g=g+(C<<1)|0;v=e<<16>>16;r=r<<16>>16;f=20;e=l;u=g;while(1){l=u+2|0;h=VA(jA[u>>1]|0,v)|0;E=VA(jA[l>>1]|0,v)|0;h=(VA(jA[e>>1]|0,r)|0)+h<<1;E=(VA(jA[e+2>>1]|0,r)|0)+E<<1<<c;jA[u>>1]=((h<<c)+32768|0)>>>16;jA[l>>1]=(E+32768|0)>>>16;f=f+-1<<16>>16;if(!(f<<16>>16))break;else{e=e+4|0;u=u+4|0}}e=i<<16>>16;tr(n,g,t+(C<<1)|0,40,s,1);f=30;u=0;while(1){E=f+C|0;jA[o+(u<<1)>>1]=(DA[A+(E<<1)>>1]|0)-(DA[t+(E<<1)>>1]|0);E=VA(jA[B+(f<<1)>>1]|0,e)|0;h=(VA(jA[a+(f<<1)>>1]|0,r)|0)>>D;jA[Q+(u<<1)>>1]=(DA[w+(f<<1)>>1]|0)-(E>>>14)-h;u=u+1|0;if((u|0)==10)break;else f=f+1|0}return}function Bi(A){A=A|0;var e=0;if(!A){A=-1;return A|0}pA[A>>2]=0;e=lr(16)|0;if(!e){A=-1;return A|0}jA[e>>1]=0;jA[e+2>>1]=0;jA[e+4>>1]=0;jA[e+6>>1]=0;jA[e+8>>1]=0;jA[e+10>>1]=0;jA[e+12>>1]=0;jA[e+14>>1]=0;pA[A>>2]=e;A=0;return A|0}function ai(A){A=A|0;if(!A){A=-1;return A|0}jA[A>>1]=0;jA[A+2>>1]=0;jA[A+4>>1]=0;jA[A+6>>1]=0;jA[A+8>>1]=0;jA[A+10>>1]=0;jA[A+12>>1]=0;jA[A+14>>1]=0;A=0;return A|0}function si(A){A=A|0;var e=0;if(!A)return;e=pA[A>>2]|0;if(!e)return;Br(e);pA[A>>2]=0;return}function oi(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,r=0,n=0,t=0;i=DA[e+6>>1]|0;f=DA[e+8>>1]|0;r=i-f|0;r=(r&65535|0)!=32767?r&65535:32767;n=DA[e+10>>1]|0;f=f-n|0;r=(f<<16>>16|0)<(r<<16>>16|0)?f&65535:r;f=DA[e+12>>1]|0;n=n-f|0;r=(n<<16>>16|0)<(r<<16>>16|0)?n&65535:r;n=DA[e+14>>1]|0;f=f-n|0;r=(f<<16>>16|0)<(r<<16>>16|0)?f&65535:r;n=n-(DA[e+16>>1]|0)|0;f=jA[e+2>>1]|0;t=DA[e+4>>1]|0;e=(f&65535)-t|0;e=(e&65535|0)!=32767?e&65535:32767;i=t-i|0;if(((n<<16>>16|0)<(r<<16>>16|0)?n&65535:r)<<16>>16<1500?1:(((i<<16>>16|0)<(e<<16>>16|0)?i&65535:e)<<16>>16|0)<((f<<16>>16>32e3?600:f<<16>>16>30500?800:1100)|0)){n=(jA[A>>1]|0)+1<<16>>16;t=n<<16>>16>11;jA[A>>1]=t?12:n;return t&1|0}else{jA[A>>1]=0;return 0}return 0}function Qi(A,e,f){A=A|0;e=e|0;f=f|0;e=fr(e,3,f)|0;e=Ci(e,jA[A+2>>1]|0,f)|0;e=Ci(e,jA[A+4>>1]|0,f)|0;e=Ci(e,jA[A+6>>1]|0,f)|0;e=Ci(e,jA[A+8>>1]|0,f)|0;e=Ci(e,jA[A+10>>1]|0,f)|0;e=Ci(e,jA[A+12>>1]|0,f)|0;return(Ci(e,jA[A+14>>1]|0,f)|0)<<16>>16>15565|0}function gi(A,e,f){A=A|0;e=e|0;f=f|0;var i=0;f=A+4|0;jA[A+2>>1]=jA[f>>1]|0;i=A+6|0;jA[f>>1]=jA[i>>1]|0;f=A+8|0;jA[i>>1]=jA[f>>1]|0;i=A+10|0;jA[f>>1]=jA[i>>1]|0;f=A+12|0;jA[i>>1]=jA[f>>1]|0;A=A+14|0;jA[f>>1]=jA[A>>1]|0;jA[A>>1]=e<<16>>16>>>3;return}function vi(A){A=A|0;var e=0,f=0,i=0;if(!A){i=-1;return i|0}pA[A>>2]=0;e=lr(128)|0;if(!e){i=-1;return i|0}f=e+72|0;i=f+46|0;do{jA[f>>1]=0;f=f+2|0}while((f|0)<(i|0));jA[e>>1]=150;jA[e+36>>1]=150;jA[e+18>>1]=150;jA[e+54>>1]=0;jA[e+2>>1]=150;jA[e+38>>1]=150;jA[e+20>>1]=150;jA[e+56>>1]=0;jA[e+4>>1]=150;jA[e+40>>1]=150;jA[e+22>>1]=150;jA[e+58>>1]=0;jA[e+6>>1]=150;jA[e+42>>1]=150;jA[e+24>>1]=150;jA[e+60>>1]=0;jA[e+8>>1]=150;jA[e+44>>1]=150;jA[e+26>>1]=150;jA[e+62>>1]=0;jA[e+10>>1]=150;jA[e+46>>1]=150;jA[e+28>>1]=150;jA[e+64>>1]=0;jA[e+12>>1]=150;jA[e+48>>1]=150;jA[e+30>>1]=150;jA[e+66>>1]=0;jA[e+14>>1]=150;jA[e+50>>1]=150;jA[e+32>>1]=150;jA[e+68>>1]=0;jA[e+16>>1]=150;jA[e+52>>1]=150;jA[e+34>>1]=150;jA[e+70>>1]=0;jA[e+118>>1]=13106;jA[e+120>>1]=0;jA[e+122>>1]=0;jA[e+124>>1]=0;jA[e+126>>1]=13106;pA[A>>2]=e;i=0;return i|0}function ui(A){A=A|0;var e=0,f=0;if(!A){f=-1;return f|0}e=A+72|0;f=e+46|0;do{jA[e>>1]=0;e=e+2|0}while((e|0)<(f|0));jA[A>>1]=150;jA[A+36>>1]=150;jA[A+18>>1]=150;jA[A+54>>1]=0;jA[A+2>>1]=150;jA[A+38>>1]=150;jA[A+20>>1]=150;jA[A+56>>1]=0;jA[A+4>>1]=150;jA[A+40>>1]=150;jA[A+22>>1]=150;jA[A+58>>1]=0;jA[A+6>>1]=150;jA[A+42>>1]=150;jA[A+24>>1]=150;jA[A+60>>1]=0;jA[A+8>>1]=150;jA[A+44>>1]=150;jA[A+26>>1]=150;jA[A+62>>1]=0;jA[A+10>>1]=150;jA[A+46>>1]=150;jA[A+28>>1]=150;jA[A+64>>1]=0;jA[A+12>>1]=150;jA[A+48>>1]=150;jA[A+30>>1]=150;jA[A+66>>1]=0;jA[A+14>>1]=150;jA[A+50>>1]=150;jA[A+32>>1]=150;jA[A+68>>1]=0;jA[A+16>>1]=150;jA[A+52>>1]=150;jA[A+34>>1]=150;jA[A+70>>1]=0;jA[A+118>>1]=13106;jA[A+120>>1]=0;jA[A+122>>1]=0;jA[A+124>>1]=0;jA[A+126>>1]=13106;f=0;return f|0}function ci(A){A=A|0;var e=0;if(!A)return;e=pA[A>>2]|0;if(!e)return;Br(e);pA[A>>2]=0;return}function Ci(A,e,f){A=A|0;e=e|0;f=f|0;A=(e<<16>>16)+(A<<16>>16)|0;if((A|0)<=32767){if((A|0)<-32768){pA[f>>2]=1;A=-32768}}else{pA[f>>2]=1;A=32767}return A&65535|0}function Di(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var r=0,n=0,t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0;P=WA;WA=WA+32|0;E=P+12|0;h=P;jA[E>>1]=1024;jA[h>>1]=1024;l=jA[A+2>>1]|0;t=jA[A+20>>1]|0;i=((t+l|0)>>>2)+64512|0;jA[E+2>>1]=i;t=((l-t|0)>>>2)+1024|0;jA[h+2>>1]=t;l=jA[A+4>>1]|0;r=jA[A+18>>1]|0;i=((r+l|0)>>>2)-i|0;jA[E+4>>1]=i;t=((l-r|0)>>>2)+t|0;jA[h+4>>1]=t;r=jA[A+6>>1]|0;l=jA[A+16>>1]|0;i=((l+r|0)>>>2)-i|0;jA[E+6>>1]=i;t=((r-l|0)>>>2)+t|0;jA[h+6>>1]=t;l=jA[A+8>>1]|0;r=jA[A+14>>1]|0;i=((r+l|0)>>>2)-i|0;jA[E+8>>1]=i;t=((l-r|0)>>>2)+t|0;jA[h+8>>1]=t;r=jA[A+10>>1]|0;l=jA[A+12>>1]|0;i=((l+r|0)>>>2)-i|0;jA[E+10>>1]=i;jA[h+10>>1]=((r-l|0)>>>2)+t;t=jA[3454]|0;l=t<<16>>16;A=jA[E+2>>1]|0;r=(A<<16>>16<<14)+(l<<10)|0;u=r&-65536;r=(r>>>1)-(r>>16<<15)<<16;D=(((VA(r>>16,l)|0)>>15)+(VA(u>>16,l)|0)<<2)+-16777216|0;D=(jA[E+4>>1]<<14)+D|0;w=D>>16;D=(D>>>1)-(w<<15)<<16;u=(((VA(D>>16,l)|0)>>15)+(VA(w,l)|0)<<2)-((r>>15)+u)|0;u=(jA[E+6>>1]<<14)+u|0;r=u>>16;u=(u>>>1)-(r<<15)<<16;w=(((VA(u>>16,l)|0)>>15)+(VA(r,l)|0)<<2)-((D>>15)+(w<<16))|0;w=(jA[E+8>>1]<<14)+w|0;D=w>>16;r=(i<<16>>3)+((((VA((w>>>1)-(D<<15)<<16>>16,l)|0)>>15)+(VA(D,l)|0)<<1)-((u>>15)+(r<<16)))|0;u=E+4|0;l=E;D=0;w=0;i=0;v=E+10|0;r=(r+33554432|0)>>>0<67108863?r>>>10&65535:(r|0)>33554431?32767:-32768;A:while(1){c=A<<16>>16<<14;g=l+6|0;Q=l+8|0;o=w<<16>>16;while(1){if((o|0)>=60)break A;l=(o&65535)+1<<16>>16;B=jA[6908+(l<<16>>16<<1)>>1]|0;C=B<<16>>16;w=c+(C<<10)|0;n=w&-65536;w=(w>>>1)-(w>>16<<15)<<16;a=(((VA(w>>16,C)|0)>>15)+(VA(n>>16,C)|0)<<2)+-16777216|0;s=jA[u>>1]|0;a=(s<<16>>16<<14)+a|0;d=a>>16;a=(a>>>1)-(d<<15)<<16;n=(((VA(a>>16,C)|0)>>15)+(VA(d,C)|0)<<2)-((w>>15)+n)|0;w=jA[g>>1]|0;n=(w<<16>>16<<14)+n|0;A=n>>16;n=(n>>>1)-(A<<15)<<16;d=(((VA(n>>16,C)|0)>>15)+(VA(A,C)|0)<<2)-((a>>15)+(d<<16))|0;a=jA[Q>>1]|0;d=(a<<16>>16<<14)+d|0;b=d>>16;A=(((VA((d>>>1)-(b<<15)<<16>>16,C)|0)>>15)+(VA(b,C)|0)<<1)-((n>>15)+(A<<16))|0;n=jA[v>>1]|0;A=(n<<16>>16<<13)+A|0;A=(A+33554432|0)>>>0<67108863?A>>>10&65535:(A|0)>33554431?32767:-32768;if((VA(A<<16>>16,r<<16>>16)|0)<1){C=l;l=s;break}else{o=o+1|0;t=B;r=A}}u=n<<16>>16<<13;v=l<<16>>16<<14;s=w<<16>>16<<14;Q=a<<16>>16<<14;n=B<<16>>16;o=4;while(1){b=(t<<16>>16>>>1)+(n>>>1)|0;n=b<<16;g=n>>16;n=c+(n>>6)|0;d=n&-65536;n=(n>>>1)-(n>>16<<15)<<16;a=v+((((VA(n>>16,g)|0)>>15)+(VA(d>>16,g)|0)<<2)+-16777216)|0;l=a>>16;a=(a>>>1)-(l<<15)<<16;d=s+((((VA(a>>16,g)|0)>>15)+(VA(l,g)|0)<<2)-((n>>15)+d))|0;n=d>>16;d=(d>>>1)-(n<<15)<<16;l=Q+((((VA(d>>16,g)|0)>>15)+(VA(n,g)|0)<<2)-((a>>15)+(l<<16)))|0;a=l>>16;b=b&65535;n=u+((((VA((l>>>1)-(a<<15)<<16>>16,g)|0)>>15)+(VA(a,g)|0)<<1)-((d>>15)+(n<<16)))|0;n=(n+33554432|0)>>>0<67108863?n>>>10&65535:(n|0)>33554431?32767:-32768;d=(VA(n<<16>>16,A<<16>>16)|0)<1;g=d?B:b;A=d?A:n;t=d?b:t;r=d?n:r;o=o+-1<<16>>16;n=g<<16>>16;if(!(o<<16>>16)){B=n;w=t;t=g;break}else B=g}l=i<<16>>16;n=A<<16>>16;A=(r&65535)-n|0;r=A<<16;if(r){d=(A&65535)-(A>>>15&1)|0;d=d<<16>>31^d;A=(Ki(d&65535)|0)<<16>>16;A=(VA((Ei(16383,d<<16>>16<<A&65535)|0)<<16>>16,(w&65535)-B<<16>>16)|0)>>19-A;if((r|0)<0)A=0-(A<<16>>16)|0;t=B-((VA(A<<16>>16,n)|0)>>>10)&65535}jA[e+(l<<1)>>1]=t;r=D<<16>>16==0?h:E;b=t<<16>>16;A=jA[r+2>>1]|0;n=(A<<16>>16<<14)+(b<<10)|0;d=n&-65536;n=(n>>>1)-(n>>16<<15)<<16;c=(((VA(n>>16,b)|0)>>15)+(VA(d>>16,b)|0)<<2)+-16777216|0;c=(jA[r+4>>1]<<14)+c|0;u=c>>16;c=(c>>>1)-(u<<15)<<16;d=(((VA(c>>16,b)|0)>>15)+(VA(u,b)|0)<<2)-((n>>15)+d)|0;d=(jA[r+6>>1]<<14)+d|0;n=d>>16;d=(d>>>1)-(n<<15)<<16;u=(((VA(d>>16,b)|0)>>15)+(VA(n,b)|0)<<2)-((c>>15)+(u<<16))|0;u=(jA[r+8>>1]<<14)+u|0;c=u>>16;i=i+1<<16>>16;n=(((VA((u>>>1)-(c<<15)<<16>>16,b)|0)>>15)+(VA(c,b)|0)<<1)-((d>>15)+(n<<16))|0;n=(jA[r+10>>1]<<13)+n|0;if(i<<16>>16<10){u=r+4|0;l=r;D=D^1;w=C;v=r+10|0;r=(n+33554432|0)>>>0<67108863?n>>>10&65535:(n|0)>33554431?32767:-32768}else{k=13;break}}if((k|0)==13){WA=P;return}jA[e>>1]=jA[f>>1]|0;jA[e+2>>1]=jA[f+2>>1]|0;jA[e+4>>1]=jA[f+4>>1]|0;jA[e+6>>1]=jA[f+6>>1]|0;jA[e+8>>1]=jA[f+8>>1]|0;jA[e+10>>1]=jA[f+10>>1]|0;jA[e+12>>1]=jA[f+12>>1]|0;jA[e+14>>1]=jA[f+14>>1]|0;jA[e+16>>1]=jA[f+16>>1]|0;jA[e+18>>1]=jA[f+18>>1]|0;WA=P;return}function Ei(A,e){A=A|0;e=e|0;var f=0,i=0,r=0,n=0,t=0,w=0;r=e<<16>>16;if(A<<16>>16<1?1:A<<16>>16>e<<16>>16){r=0;return r|0}if(A<<16>>16==e<<16>>16){r=32767;return r|0}i=r<<1;f=r<<2;n=A<<16>>16<<3;A=(n|0)<(f|0);n=n-(A?0:f)|0;A=A?0:4;t=(n|0)<(i|0);n=n-(t?0:i)|0;e=(n|0)<(r|0);A=(e&1|(t?A:A|2))<<3^8;e=n-(e?0:r)<<3;if((e|0)>=(f|0)){e=e-f|0;A=A&65528|4}n=(e|0)<(i|0);t=e-(n?0:i)|0;e=(t|0)<(r|0);A=(e&1^1|(n?A:A|2))<<16>>13;e=t-(e?0:r)<<3;if((e|0)>=(f|0)){e=e-f|0;A=A&65528|4}n=(e|0)<(i|0);t=e-(n?0:i)|0;e=(t|0)<(r|0);A=(e&1^1|(n?A:A|2))<<16>>13;e=t-(e?0:r)<<3;if((e|0)>=(f|0)){e=e-f|0;A=A&65528|4}w=(e|0)<(i|0);n=e-(w?0:i)|0;t=(n|0)<(r|0);e=(t&1^1|(w?A:A|2))<<16>>13;A=n-(t?0:r)<<3;if((A|0)>=(f|0)){A=A-f|0;e=e&65528|4}w=(A|0)<(i|0);w=((A-(w?0:i)|0)>=(r|0)|(w?e:e|2))&65535;return w|0}function hi(A){A=A|0;if(!A){A=-1;return A|0}jA[A>>1]=-14336;jA[A+8>>1]=-2381;jA[A+2>>1]=-14336;jA[A+10>>1]=-2381;jA[A+4>>1]=-14336;jA[A+12>>1]=-2381;jA[A+6>>1]=-14336;jA[A+14>>1]=-2381;A=0;return A|0}function Pi(A,e,f,i,r,n,t,w){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;var l=0,B=0,a=0,s=0,o=0,Q=0;Q=WA;WA=WA+16|0;s=Q+2|0;o=Q;l=0;B=10;while(1){a=jA[f>>1]|0;a=((VA(a,a)|0)>>>3)+l|0;l=jA[f+2>>1]|0;l=a+((VA(l,l)|0)>>>3)|0;a=jA[f+4>>1]|0;a=l+((VA(a,a)|0)>>>3)|0;l=jA[f+6>>1]|0;l=a+((VA(l,l)|0)>>>3)|0;B=B+-1<<16>>16;if(!(B<<16>>16))break;else f=f+8|0}B=l<<4;B=(B|0)<0?2147483647:B;if((e|0)==7){Gi(((er(B,w)|0)<<16>>16)*52428|0,s,o,w);a=DA[s>>1]<<16;B=jA[o>>1]<<1;e=jA[A+8>>1]|0;l=(e<<16>>16)*88|0;if(e<<16>>16>-1&(l|0)<-783741){pA[w>>2]=1;f=2147483647}else f=l+783741|0;e=(jA[A+10>>1]|0)*74|0;l=e+f|0;if((e^f|0)>-1&(l^f|0)<0){pA[w>>2]=1;f=(f>>>31)+2147483647|0}else f=l;e=(jA[A+12>>1]|0)*44|0;l=e+f|0;if((e^f|0)>-1&(l^f|0)<0){pA[w>>2]=1;f=(f>>>31)+2147483647|0}else f=l;A=(jA[A+14>>1]|0)*24|0;l=A+f|0;if((A^f|0)>-1&(l^f|0)<0){pA[w>>2]=1;l=(f>>>31)+2147483647|0}A=a+-1966080+B|0;f=l-A|0;if(((f^l)&(l^A)|0)<0){pA[w>>2]=1;f=(l>>>31)+2147483647|0}w=f>>17;jA[i>>1]=w;w=(f>>2)-(w<<15)|0;w=w&65535;jA[r>>1]=w;WA=Q;return}a=Ni(B)|0;l=a<<16>>16;if(a<<16>>16>0){f=B<<l;if((f>>l|0)==(B|0))B=f;else B=B>>31^2147483647}else{l=0-l<<16;if((l|0)<2031616)B=B>>(l>>16);else B=0}Li(B,a,s,o);s=VA(jA[s>>1]|0,-49320)|0;l=(VA(jA[o>>1]|0,-24660)|0)>>15;l=(l&65536|0)==0?l:l|-65536;o=l<<1;f=o+s|0;if((o^s|0)>-1&(f^o|0)<0){pA[w>>2]=1;f=(l>>>30&1)+2147483647|0}switch(e|0){case 6:{l=f+2134784|0;if((f|0)>-1&(l^f|0)<0){pA[w>>2]=1;l=(f>>>31)+2147483647|0}break}case 5:{jA[t>>1]=B>>>16;jA[n>>1]=-11-(a&65535);l=f+2183936|0;if((f|0)>-1&(l^f|0)<0){pA[w>>2]=1;l=(f>>>31)+2147483647|0}break}case 4:{l=f+2085632|0;if((f|0)>-1&(l^f|0)<0){pA[w>>2]=1;l=(f>>>31)+2147483647|0}break}case 3:{l=f+2065152|0;if((f|0)>-1&(l^f|0)<0){pA[w>>2]=1;l=(f>>>31)+2147483647|0}break}default:{l=f+2134784|0;if((f|0)>-1&(l^f|0)<0){pA[w>>2]=1;l=(f>>>31)+2147483647|0}}}do{if((l|0)<=2097151)if((l|0)<-2097152){pA[w>>2]=1;f=-2147483648;break}else{f=l<<10;break}else{pA[w>>2]=1;f=2147483647}}while(0);t=(jA[A>>1]|0)*11142|0;l=t+f|0;if((t^f|0)>-1&(l^f|0)<0){pA[w>>2]=1;l=(f>>>31)+2147483647|0}t=(jA[A+2>>1]|0)*9502|0;f=t+l|0;if((t^l|0)>-1&(f^l|0)<0){pA[w>>2]=1;f=(l>>>31)+2147483647|0}t=(jA[A+4>>1]|0)*5570|0;l=t+f|0;if((t^f|0)>-1&(l^f|0)<0){pA[w>>2]=1;l=(f>>>31)+2147483647|0}A=(jA[A+6>>1]|0)*3112|0;f=A+l|0;if((A^l|0)>-1&(f^l|0)<0){pA[w>>2]=1;f=(l>>>31)+2147483647|0}f=VA(f>>16,(e|0)==4?10878:10886)|0;if((f|0)<0)f=~((f^-256)>>8);else f=f>>8;jA[i>>1]=f>>>16;if((f|0)<0)l=~((f^-2)>>1);else l=f>>1;i=f>>16<<15;f=l-i|0;if(((f^l)&(i^l)|0)>=0){w=f;w=w&65535;jA[r>>1]=w;WA=Q;return}pA[w>>2]=1;w=(l>>>31)+2147483647|0;w=w&65535;jA[r>>1]=w;WA=Q;return}function ki(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,r=0,n=0;r=A+4|0;jA[A+6>>1]=jA[r>>1]|0;n=A+12|0;jA[A+14>>1]=jA[n>>1]|0;i=A+2|0;jA[r>>1]=jA[i>>1]|0;r=A+10|0;jA[n>>1]=jA[r>>1]|0;jA[i>>1]=jA[A>>1]|0;i=A+8|0;jA[r>>1]=jA[i>>1]|0;jA[i>>1]=e;jA[A>>1]=f;return}function bi(A){A=A|0;pA[A>>2]=6892;pA[A+4>>2]=8180;pA[A+8>>2]=21e3;pA[A+12>>2]=9716;pA[A+16>>2]=22024;pA[A+20>>2]=12788;pA[A+24>>2]=24072;pA[A+28>>2]=26120;pA[A+32>>2]=28168;pA[A+36>>2]=6876;pA[A+40>>2]=7452;pA[A+44>>2]=8140;pA[A+48>>2]=20980;pA[A+52>>2]=16884;pA[A+56>>2]=17908;pA[A+60>>2]=7980;pA[A+64>>2]=8160;pA[A+68>>2]=6678;pA[A+72>>2]=6646;pA[A+76>>2]=6614;pA[A+80>>2]=29704;pA[A+84>>2]=28680;pA[A+88>>2]=3720;pA[A+92>>2]=8;pA[A+96>>2]=4172;pA[A+100>>2]=44;pA[A+104>>2]=3436;pA[A+108>>2]=30316;pA[A+112>>2]=30796;pA[A+116>>2]=31276;pA[A+120>>2]=7472;pA[A+124>>2]=7552;pA[A+128>>2]=7632;pA[A+132>>2]=7712;return}function di(A,e){A=A|0;e=e|0;var f=0,i=0,r=0,n=0,t=0,w=0,l=0,B=0,a=0,s=0;s=WA;WA=WA+48|0;B=s+18|0;a=s;l=e<<16>>16;or(a|0,A|0,l<<1|0)|0;if(e<<16>>16>0){f=0;i=0}else{ZA()}do{w=0;t=-32767;while(1){r=jA[a+(w<<1)>>1]|0;n=r<<16>>16<t<<16>>16;i=n?i:w&65535;w=w+1|0;if((w&65535)<<16>>16==e<<16>>16)break;else t=n?t:r}jA[a+(i<<16>>16<<1)>>1]=-32768;jA[B+(f<<1)>>1]=i;f=f+1|0}while((f&65535)<<16>>16!=e<<16>>16);a=l>>1;a=B+(a<<1)|0;a=jA[a>>1]|0;a=a<<16>>16;a=A+(a<<1)|0;a=jA[a>>1]|0;WA=s;return a|0}function Ii(A,e,f,i,r){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;var n=0,t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0;n=WA;WA=WA+32|0;t=n;b=e+2|0;k=t+2|0;jA[t>>1]=((jA[e>>1]|0)>>>1)+((jA[A>>1]|0)>>>1);P=e+4|0;h=t+4|0;jA[k>>1]=((jA[b>>1]|0)>>>1)+((jA[A+2>>1]|0)>>>1);E=e+6|0;D=t+6|0;jA[h>>1]=((jA[P>>1]|0)>>>1)+((jA[A+4>>1]|0)>>>1);C=e+8|0;c=t+8|0;jA[D>>1]=((jA[E>>1]|0)>>>1)+((jA[A+6>>1]|0)>>>1);u=e+10|0;v=t+10|0;jA[c>>1]=((jA[C>>1]|0)>>>1)+((jA[A+8>>1]|0)>>>1);g=e+12|0;Q=t+12|0;jA[v>>1]=((jA[u>>1]|0)>>>1)+((jA[A+10>>1]|0)>>>1);o=e+14|0;s=t+14|0;jA[Q>>1]=((jA[g>>1]|0)>>>1)+((jA[A+12>>1]|0)>>>1);a=e+16|0;B=t+16|0;jA[s>>1]=((jA[o>>1]|0)>>>1)+((jA[A+14>>1]|0)>>>1);l=e+18|0;w=t+18|0;jA[B>>1]=((jA[a>>1]|0)>>>1)+((jA[A+16>>1]|0)>>>1);jA[w>>1]=((jA[l>>1]|0)>>>1)+((jA[A+18>>1]|0)>>>1);yi(t,i,r);yi(e,i+22|0,r);jA[t>>1]=((jA[f>>1]|0)>>>1)+((jA[e>>1]|0)>>>1);jA[k>>1]=((jA[f+2>>1]|0)>>>1)+((jA[b>>1]|0)>>>1);jA[h>>1]=((jA[f+4>>1]|0)>>>1)+((jA[P>>1]|0)>>>1);jA[D>>1]=((jA[f+6>>1]|0)>>>1)+((jA[E>>1]|0)>>>1);jA[c>>1]=((jA[f+8>>1]|0)>>>1)+((jA[C>>1]|0)>>>1);jA[v>>1]=((jA[f+10>>1]|0)>>>1)+((jA[u>>1]|0)>>>1);jA[Q>>1]=((jA[f+12>>1]|0)>>>1)+((jA[g>>1]|0)>>>1);jA[s>>1]=((jA[f+14>>1]|0)>>>1)+((jA[o>>1]|0)>>>1);jA[B>>1]=((jA[f+16>>1]|0)>>>1)+((jA[a>>1]|0)>>>1);jA[w>>1]=((jA[f+18>>1]|0)>>>1)+((jA[l>>1]|0)>>>1);yi(t,i+44|0,r);yi(f,i+66|0,r);WA=n;return}function Mi(A,e,f,i,r){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;var n=0,t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0;n=WA;WA=WA+32|0;t=n;b=e+2|0;k=t+2|0;jA[t>>1]=((jA[e>>1]|0)>>>1)+((jA[A>>1]|0)>>>1);P=e+4|0;h=t+4|0;jA[k>>1]=((jA[b>>1]|0)>>>1)+((jA[A+2>>1]|0)>>>1);E=e+6|0;D=t+6|0;jA[h>>1]=((jA[P>>1]|0)>>>1)+((jA[A+4>>1]|0)>>>1);C=e+8|0;c=t+8|0;jA[D>>1]=((jA[E>>1]|0)>>>1)+((jA[A+6>>1]|0)>>>1);u=e+10|0;v=t+10|0;jA[c>>1]=((jA[C>>1]|0)>>>1)+((jA[A+8>>1]|0)>>>1);g=e+12|0;Q=t+12|0;jA[v>>1]=((jA[u>>1]|0)>>>1)+((jA[A+10>>1]|0)>>>1);o=e+14|0;s=t+14|0;jA[Q>>1]=((jA[g>>1]|0)>>>1)+((jA[A+12>>1]|0)>>>1);a=e+16|0;B=t+16|0;jA[s>>1]=((jA[o>>1]|0)>>>1)+((jA[A+14>>1]|0)>>>1);l=e+18|0;w=t+18|0;jA[B>>1]=((jA[a>>1]|0)>>>1)+((jA[A+16>>1]|0)>>>1);jA[w>>1]=((jA[l>>1]|0)>>>1)+((jA[A+18>>1]|0)>>>1);yi(t,i,r);jA[t>>1]=((jA[f>>1]|0)>>>1)+((jA[e>>1]|0)>>>1);jA[k>>1]=((jA[f+2>>1]|0)>>>1)+((jA[b>>1]|0)>>>1);jA[h>>1]=((jA[f+4>>1]|0)>>>1)+((jA[P>>1]|0)>>>1);jA[D>>1]=((jA[f+6>>1]|0)>>>1)+((jA[E>>1]|0)>>>1);jA[c>>1]=((jA[f+8>>1]|0)>>>1)+((jA[C>>1]|0)>>>1);jA[v>>1]=((jA[f+10>>1]|0)>>>1)+((jA[u>>1]|0)>>>1);jA[Q>>1]=((jA[f+12>>1]|0)>>>1)+((jA[g>>1]|0)>>>1);jA[s>>1]=((jA[f+14>>1]|0)>>>1)+((jA[o>>1]|0)>>>1);jA[B>>1]=((jA[f+16>>1]|0)>>>1)+((jA[a>>1]|0)>>>1);jA[w>>1]=((jA[f+18>>1]|0)>>>1)+((jA[l>>1]|0)>>>1);yi(t,i+44|0,r);WA=n;return}function Fi(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var r=0,n=0,t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0;r=WA;WA=WA+32|0;n=r;G=jA[A>>1]|0;jA[n>>1]=G-(G>>>2)+((jA[e>>1]|0)>>>2);G=A+2|0;F=jA[G>>1]|0;L=e+2|0;U=n+2|0;jA[U>>1]=F-(F>>>2)+((jA[L>>1]|0)>>>2);F=A+4|0;d=jA[F>>1]|0;H=e+4|0;M=n+4|0;jA[M>>1]=d-(d>>>2)+((jA[H>>1]|0)>>>2);d=A+6|0;P=jA[d>>1]|0;I=e+6|0;b=n+6|0;jA[b>>1]=P-(P>>>2)+((jA[I>>1]|0)>>>2);P=A+8|0;D=jA[P>>1]|0;k=e+8|0;h=n+8|0;jA[h>>1]=D-(D>>>2)+((jA[k>>1]|0)>>>2);D=A+10|0;u=jA[D>>1]|0;E=e+10|0;C=n+10|0;jA[C>>1]=u-(u>>>2)+((jA[E>>1]|0)>>>2);u=A+12|0;Q=jA[u>>1]|0;c=e+12|0;v=n+12|0;jA[v>>1]=Q-(Q>>>2)+((jA[c>>1]|0)>>>2);Q=A+14|0;a=jA[Q>>1]|0;g=e+14|0;o=n+14|0;jA[o>>1]=a-(a>>>2)+((jA[g>>1]|0)>>>2);a=A+16|0;w=jA[a>>1]|0;s=e+16|0;B=n+16|0;jA[B>>1]=w-(w>>>2)+((jA[s>>1]|0)>>>2);w=A+18|0;R=jA[w>>1]|0;l=e+18|0;t=n+18|0;jA[t>>1]=R-(R>>>2)+((jA[l>>1]|0)>>>2);yi(n,f,i);jA[n>>1]=((jA[A>>1]|0)>>>1)+((jA[e>>1]|0)>>>1);jA[U>>1]=((jA[G>>1]|0)>>>1)+((jA[L>>1]|0)>>>1);jA[M>>1]=((jA[F>>1]|0)>>>1)+((jA[H>>1]|0)>>>1);jA[b>>1]=((jA[d>>1]|0)>>>1)+((jA[I>>1]|0)>>>1);jA[h>>1]=((jA[P>>1]|0)>>>1)+((jA[k>>1]|0)>>>1);jA[C>>1]=((jA[D>>1]|0)>>>1)+((jA[E>>1]|0)>>>1);jA[v>>1]=((jA[u>>1]|0)>>>1)+((jA[c>>1]|0)>>>1);jA[o>>1]=((jA[Q>>1]|0)>>>1)+((jA[g>>1]|0)>>>1);jA[B>>1]=((jA[a>>1]|0)>>>1)+((jA[s>>1]|0)>>>1);jA[t>>1]=((jA[w>>1]|0)>>>1)+((jA[l>>1]|0)>>>1);yi(n,f+22|0,i);R=jA[e>>1]|0;jA[n>>1]=R-(R>>>2)+((jA[A>>1]|0)>>>2);A=jA[L>>1]|0;jA[U>>1]=A-(A>>>2)+((jA[G>>1]|0)>>>2);A=jA[H>>1]|0;jA[M>>1]=A-(A>>>2)+((jA[F>>1]|0)>>>2);A=jA[I>>1]|0;jA[b>>1]=A-(A>>>2)+((jA[d>>1]|0)>>>2);A=jA[k>>1]|0;jA[h>>1]=A-(A>>>2)+((jA[P>>1]|0)>>>2);A=jA[E>>1]|0;jA[C>>1]=A-(A>>>2)+((jA[D>>1]|0)>>>2);A=jA[c>>1]|0;jA[v>>1]=A-(A>>>2)+((jA[u>>1]|0)>>>2);A=jA[g>>1]|0;jA[o>>1]=A-(A>>>2)+((jA[Q>>1]|0)>>>2);A=jA[s>>1]|0;jA[B>>1]=A-(A>>>2)+((jA[a>>1]|0)>>>2);A=jA[l>>1]|0;jA[t>>1]=A-(A>>>2)+((jA[w>>1]|0)>>>2);yi(n,f+44|0,i);yi(e,f+66|0,i);WA=r;return}function Hi(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var r=0,n=0,t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0;r=WA;WA=WA+32|0;n=r;G=jA[A>>1]|0;jA[n>>1]=G-(G>>>2)+((jA[e>>1]|0)>>>2);G=A+2|0;F=jA[G>>1]|0;L=e+2|0;U=n+2|0;jA[U>>1]=F-(F>>>2)+((jA[L>>1]|0)>>>2);F=A+4|0;d=jA[F>>1]|0;H=e+4|0;M=n+4|0;jA[M>>1]=d-(d>>>2)+((jA[H>>1]|0)>>>2);d=A+6|0;P=jA[d>>1]|0;I=e+6|0;b=n+6|0;jA[b>>1]=P-(P>>>2)+((jA[I>>1]|0)>>>2);P=A+8|0;D=jA[P>>1]|0;k=e+8|0;h=n+8|0;jA[h>>1]=D-(D>>>2)+((jA[k>>1]|0)>>>2);D=A+10|0;u=jA[D>>1]|0;E=e+10|0;C=n+10|0;jA[C>>1]=u-(u>>>2)+((jA[E>>1]|0)>>>2);u=A+12|0;Q=jA[u>>1]|0;c=e+12|0;v=n+12|0;jA[v>>1]=Q-(Q>>>2)+((jA[c>>1]|0)>>>2);Q=A+14|0;a=jA[Q>>1]|0;g=e+14|0;o=n+14|0;jA[o>>1]=a-(a>>>2)+((jA[g>>1]|0)>>>2);a=A+16|0;w=jA[a>>1]|0;s=e+16|0;B=n+16|0;jA[B>>1]=w-(w>>>2)+((jA[s>>1]|0)>>>2);w=A+18|0;R=jA[w>>1]|0;l=e+18|0;t=n+18|0;jA[t>>1]=R-(R>>>2)+((jA[l>>1]|0)>>>2);yi(n,f,i);jA[n>>1]=((jA[A>>1]|0)>>>1)+((jA[e>>1]|0)>>>1);jA[U>>1]=((jA[G>>1]|0)>>>1)+((jA[L>>1]|0)>>>1);jA[M>>1]=((jA[F>>1]|0)>>>1)+((jA[H>>1]|0)>>>1);jA[b>>1]=((jA[d>>1]|0)>>>1)+((jA[I>>1]|0)>>>1);jA[h>>1]=((jA[P>>1]|0)>>>1)+((jA[k>>1]|0)>>>1);jA[C>>1]=((jA[D>>1]|0)>>>1)+((jA[E>>1]|0)>>>1);jA[v>>1]=((jA[u>>1]|0)>>>1)+((jA[c>>1]|0)>>>1);jA[o>>1]=((jA[Q>>1]|0)>>>1)+((jA[g>>1]|0)>>>1);jA[B>>1]=((jA[a>>1]|0)>>>1)+((jA[s>>1]|0)>>>1);jA[t>>1]=((jA[w>>1]|0)>>>1)+((jA[l>>1]|0)>>>1);yi(n,f+22|0,i);e=jA[e>>1]|0;jA[n>>1]=e-(e>>>2)+((jA[A>>1]|0)>>>2);A=jA[L>>1]|0;jA[U>>1]=A-(A>>>2)+((jA[G>>1]|0)>>>2);A=jA[H>>1]|0;jA[M>>1]=A-(A>>>2)+((jA[F>>1]|0)>>>2);A=jA[I>>1]|0;jA[b>>1]=A-(A>>>2)+((jA[d>>1]|0)>>>2);A=jA[k>>1]|0;jA[h>>1]=A-(A>>>2)+((jA[P>>1]|0)>>>2);A=jA[E>>1]|0;jA[C>>1]=A-(A>>>2)+((jA[D>>1]|0)>>>2);A=jA[c>>1]|0;jA[v>>1]=A-(A>>>2)+((jA[u>>1]|0)>>>2);A=jA[g>>1]|0;jA[o>>1]=A-(A>>>2)+((jA[Q>>1]|0)>>>2);A=jA[s>>1]|0;jA[B>>1]=A-(A>>>2)+((jA[a>>1]|0)>>>2);A=jA[l>>1]|0;jA[t>>1]=A-(A>>>2)+((jA[w>>1]|0)>>>2);yi(n,f+44|0,i);WA=r;return}function Ui(A,e){A=A|0;e=e|0;var f=0,i=0;if((A|0)<1){e=1073741823;return e|0}f=(Ni(A)|0)<<16>>16;e=30-f|0;A=A<<f>>(e&1^1);f=(A>>25<<16)+-1048576>>16;i=jA[7030+(f<<1)>>1]|0;e=(i<<16)-(VA(i-(DA[7030+(f+1<<1)>>1]|0)<<16>>15,A>>>10&32767)|0)>>(e<<16>>17)+1;return e|0}function Gi(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;i=Ni(A)|0;Li(A<<(i<<16>>16),i,e,f);return}function Li(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;if((A|0)<1){jA[f>>1]=0;f=0;jA[i>>1]=f;return}else{jA[f>>1]=30-(e&65535);f=(A>>25<<16)+-2097152>>16;e=jA[7128+(f<<1)>>1]|0;f=((e<<16)-(VA(A>>>9&65534,e-(DA[7128+(f+1<<1)>>1]|0)<<16>>16)|0)|0)>>>16&65535;jA[i>>1]=f;return}}function Ri(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,r=0;i=A+2|0;f=jA[i>>1]|0;jA[e>>1]=f;r=A+4|0;jA[e+2>>1]=(DA[r>>1]|0)-(DA[A>>1]|0);jA[e+4>>1]=(DA[A+6>>1]|0)-(DA[i>>1]|0);i=A+8|0;jA[e+6>>1]=(DA[i>>1]|0)-(DA[r>>1]|0);jA[e+8>>1]=(DA[A+10>>1]|0)-(DA[A+6>>1]|0);r=A+12|0;jA[e+10>>1]=(DA[r>>1]|0)-(DA[i>>1]|0);jA[e+12>>1]=(DA[A+14>>1]|0)-(DA[A+10>>1]|0);jA[e+14>>1]=(DA[A+16>>1]|0)-(DA[r>>1]|0);jA[e+16>>1]=(DA[A+18>>1]|0)-(DA[A+14>>1]|0);jA[e+18>>1]=16384-(DA[A+16>>1]|0);A=10;r=e;while(1){f=f<<16>>16;e=(f<<16)+-120782848|0;if((e|0)>0)e=1843-((e>>16)*12484>>16)|0;else e=3427-((f*56320|0)>>>16)|0;i=r+2|0;jA[r>>1]=e<<3;A=A+-1<<16>>16;if(!(A<<16>>16))break;f=jA[i>>1]|0;r=i}return}function Ti(A,e,f){A=A|0;e=e|0;f=f|0;f=e<<16>>16;if(e<<16>>16>31){e=0;return e|0}if(e<<16>>16>0)return((1<<f+-1&A|0)!=0&1)+(e<<16>>16<31?A>>f:0)|0;f=0-f<<16>>16;e=A<<f;e=(e>>f|0)==(A|0)?e:A>>31^2147483647;return e|0}function yi(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,r=0,n=0,t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0;v=WA;WA=WA+48|0;Q=v+24|0;g=v;s=Q+4|0;pA[Q>>2]=16777216;i=0-(jA[A>>1]|0)|0;o=Q+8|0;pA[s>>2]=i<<10;r=jA[A+4>>1]|0;l=i>>6;pA[o>>2]=33554432-(((VA((i<<9)-(l<<15)<<16>>16,r)|0)>>15)+(VA(l,r)|0)<<2);l=Q+4|0;r=(pA[l>>2]|0)-(r<<10)|0;pA[l>>2]=r;l=Q+12|0;i=Q+4|0;pA[l>>2]=r;f=jA[A+8>>1]|0;n=r;B=1;while(1){w=l+-4|0;t=pA[w>>2]|0;a=t>>16;pA[l>>2]=n+r-(((VA((t>>>1)-(a<<15)<<16>>16,f)|0)>>15)+(VA(a,f)|0)<<2);if((B|0)==2)break;n=pA[l+-12>>2]|0;l=w;r=t;B=B+1|0}pA[i>>2]=(pA[i>>2]|0)-(f<<10);f=Q+16|0;i=pA[Q+8>>2]|0;pA[f>>2]=i;w=jA[A+12>>1]|0;r=i;l=1;while(1){t=f+-4|0;n=pA[t>>2]|0;a=n>>16;pA[f>>2]=r+i-(((VA((n>>>1)-(a<<15)<<16>>16,w)|0)>>15)+(VA(a,w)|0)<<2);if((l|0)==3)break;r=pA[f+-12>>2]|0;f=t;i=n;l=l+1|0}f=Q+4|0;pA[f>>2]=(pA[f>>2]|0)-(w<<10);f=Q+20|0;r=pA[Q+12>>2]|0;pA[f>>2]=r;i=jA[A+16>>1]|0;n=r;l=1;while(1){w=f+-4|0;t=pA[w>>2]|0;a=t>>16;pA[f>>2]=n+r-(((VA((t>>>1)-(a<<15)<<16>>16,i)|0)>>15)+(VA(a,i)|0)<<2);if((l|0)==4)break;n=pA[f+-12>>2]|0;f=w;r=t;l=l+1|0}l=Q+4|0;pA[l>>2]=(pA[l>>2]|0)-(i<<10);pA[g>>2]=16777216;l=0-(jA[A+2>>1]|0)|0;a=g+8|0;pA[g+4>>2]=l<<10;i=jA[A+6>>1]|0;B=l>>6;pA[a>>2]=33554432-(((VA((l<<9)-(B<<15)<<16>>16,i)|0)>>15)+(VA(B,i)|0)<<2);B=g+4|0;i=(pA[B>>2]|0)-(i<<10)|0;pA[B>>2]=i;B=g+12|0;l=g+4|0;pA[B>>2]=i;w=jA[A+10>>1]|0;r=i;f=1;while(1){t=B+-4|0;n=pA[t>>2]|0;u=n>>16;pA[B>>2]=r+i-(((VA((n>>>1)-(u<<15)<<16>>16,w)|0)>>15)+(VA(u,w)|0)<<2);if((f|0)==2)break;r=pA[B+-12>>2]|0;B=t;i=n;f=f+1|0}pA[l>>2]=(pA[l>>2]|0)-(w<<10);l=g+16|0;i=pA[g+8>>2]|0;pA[l>>2]=i;w=jA[A+14>>1]|0;r=i;f=1;while(1){t=l+-4|0;n=pA[t>>2]|0;u=n>>16;pA[l>>2]=r+i-(((VA((n>>>1)-(u<<15)<<16>>16,w)|0)>>15)+(VA(u,w)|0)<<2);if((f|0)==3)break;r=pA[l+-12>>2]|0;l=t;i=n;f=f+1|0}f=g+4|0;pA[f>>2]=(pA[f>>2]|0)-(w<<10);f=g+20|0;w=pA[g+12>>2]|0;pA[f>>2]=w;i=jA[A+18>>1]|0;t=w;l=1;while(1){r=f+-4|0;n=pA[r>>2]|0;u=n>>16;pA[f>>2]=t+w-(((VA((n>>>1)-(u<<15)<<16>>16,i)|0)>>15)+(VA(u,i)|0)<<2);if((l|0)==4)break;t=pA[f+-12>>2]|0;f=r;w=n;l=l+1|0}t=(pA[g+4>>2]|0)-(i<<10)|0;B=Q+20|0;w=g+20|0;l=pA[Q+16>>2]|0;A=(pA[B>>2]|0)+l|0;pA[B>>2]=A;B=pA[g+16>>2]|0;u=(pA[w>>2]|0)-B|0;pA[w>>2]=u;w=pA[Q+12>>2]|0;l=l+w|0;pA[Q+16>>2]=l;n=pA[g+12>>2]|0;B=B-n|0;pA[g+16>>2]=B;i=pA[o>>2]|0;w=w+i|0;pA[Q+12>>2]=w;r=pA[a>>2]|0;o=n-r|0;pA[g+12>>2]=o;n=pA[s>>2]|0;a=i+n|0;pA[Q+8>>2]=a;s=r-t|0;pA[g+8>>2]=s;Q=n+(pA[Q>>2]|0)|0;g=t-(pA[g>>2]|0)|0;jA[e>>1]=4096;Q=Q+4096|0;jA[e+2>>1]=(Q+g|0)>>>13;jA[e+20>>1]=(Q-g|0)>>>13;g=a+4096|0;jA[e+4>>1]=(g+s|0)>>>13;jA[e+18>>1]=(g-s|0)>>>13;g=w+4096|0;jA[e+6>>1]=(g+o|0)>>>13;jA[e+16>>1]=(g-o|0)>>>13;g=l+4096|0;jA[e+8>>1]=(g+B|0)>>>13;jA[e+14>>1]=(g-B|0)>>>13;g=A+4096|0;jA[e+10>>1]=(g+u|0)>>>13;jA[e+12>>1]=(g-u|0)>>>13;WA=v;return}function Yi(A){A=A|0;var e=0,f=0,i=0,r=0,n=0;if(!A){n=-1;return n|0}pA[A>>2]=0;e=lr(44)|0;if(!e){n=-1;return n|0}f=e+40|0;if((Zi(f)|0)<<16>>16){n=-1;return n|0}i=e;r=7452;n=i+20|0;do{jA[i>>1]=jA[r>>1]|0;i=i+2|0;r=r+2|0}while((i|0)<(n|0));i=e+20|0;r=7452;n=i+20|0;do{jA[i>>1]=jA[r>>1]|0;i=i+2|0;r=r+2|0}while((i|0)<(n|0));_i(pA[f>>2]|0)|0;pA[A>>2]=e;n=0;return n|0}function zi(A){A=A|0;var e=0,f=0,i=0;if(!A){i=-1;return i|0}e=A;f=7452;i=e+20|0;do{jA[e>>1]=jA[f>>1]|0;e=e+2|0;f=f+2|0}while((e|0)<(i|0));e=A+20|0;f=7452;i=e+20|0;do{jA[e>>1]=jA[f>>1]|0;e=e+2|0;f=f+2|0}while((e|0)<(i|0));_i(pA[A+40>>2]|0)|0;i=0;return i|0}function Xi(A){A=A|0;var e=0;if(!A)return;e=pA[A>>2]|0;if(!e)return;qi(e+40|0);Br(pA[A>>2]|0);pA[A>>2]=0;return}function Ji(A,e,f,i,r,n,t,w){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;var l=0,B=0,a=0,s=0,o=0;o=WA;WA=WA+64|0;s=o+44|0;l=o+24|0;B=o+4|0;a=o;if((e|0)==7){Di(i+22|0,l,A,w);Di(i+66|0,n,l,w);Mi(A,l,n,i,w);if((f|0)==8)i=6;else{Wi(pA[A+40>>2]|0,l,n,B,s,pA[t>>2]|0,w);Ii(A+20|0,B,s,r,w);r=(pA[t>>2]|0)+10|0;i=7}}else{Di(i+66|0,n,A,w);Hi(A,n,i,w);if((f|0)==8)i=6;else{ji(pA[A+40>>2]|0,e,n,s,pA[t>>2]|0,a,w);Fi(A+20|0,s,r,w);r=(pA[t>>2]|0)+6|0;i=7}}if((i|0)==6){ZA()}else if((i|0)==7){pA[t>>2]=r;i=A;r=i+20|0;do{jA[i>>1]=jA[n>>1]|0;i=i+2|0;n=n+2|0}while((i|0)<(r|0));i=A+20|0;n=s;r=i+20|0;do{jA[i>>1]=jA[n>>1]|0;i=i+2|0;n=n+2|0}while((i|0)<(r|0));WA=o;return}}function Oi(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var r=0,n=0,t=0;if(f<<16>>16>0)i=0;else return;do{n=jA[A+(i<<1)>>1]|0;t=n>>8;r=jA[7194+(t<<1)>>1]|0;jA[e+(i<<1)>>1]=((VA((jA[7194+(t+1<<1)>>1]|0)-r|0,n&255)|0)>>>8)+r;i=i+1|0}while((i&65535)<<16>>16!=f<<16>>16);return}function mi(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var r=0,n=0,t=0;i=(f<<16>>16)+-1|0;f=i&65535;if(f<<16>>16<=-1)return;r=63;t=e+(i<<1)|0;n=A+(i<<1)|0;while(1){A=jA[n>>1]|0;e=r;while(1){i=e<<16>>16;r=jA[7194+(i<<1)>>1]|0;if(A<<16>>16>r<<16>>16)e=e+-1<<16>>16;else break}jA[t>>1]=(((VA(jA[7324+(i<<1)>>1]|0,(A<<16>>16)-(r<<16>>16)|0)|0)+2048|0)>>>12)+(i<<8);f=f+-1<<16>>16;if(f<<16>>16>-1){r=e;t=t+-2|0;n=n+-2|0}else break}return}function Ni(A){A=A|0;var e=0;A:do{if((A|0)!=0?(e=A-(A>>>31)|0,e=e>>31^e,(e&1073741824|0)==0):0){A=e;e=0;while(1){if(A&536870912){A=7;break}if(A&268435456){A=8;break}if(A&134217728){A=9;break}e=e+4<<16>>16;A=A<<4;if(A&1073741824)break A}if((A|0)==7){e=e|1;break}else if((A|0)==8){e=e|2;break}else if((A|0)==9){e=e|3;break}}else e=0}while(0);return e|0}function Ki(A){A=A|0;var e=0,f=0;if(!(A<<16>>16)){f=0;return f|0}e=(A&65535)-((A&65535)>>>15&65535)|0;e=(e<<16>>31^e)<<16;A=e>>16;if(!(A&16384)){f=e;e=0}else{f=0;return f|0}while(1){if(A&8192){A=e;f=7;break}if(A&4096){A=e;f=8;break}if(A&2048){A=e;f=9;break}e=e+4<<16>>16;f=f<<4;A=f>>16;if(A&16384){A=e;f=10;break}}if((f|0)==7){f=A|1;return f|0}else if((f|0)==8){f=A|2;return f|0}else if((f|0)==9){f=A|3;return f|0}else if((f|0)==10)return A|0;return 0}function xi(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,r=0,n=0;e=e<<16>>16;if((e&134217727|0)==33554432){pA[f>>2]=1;e=2147483647}else e=e<<6;i=e>>>16&31;n=jA[7792+(i<<1)>>1]|0;r=n<<16;e=VA(n-(DA[7792+(i+1<<1)>>1]|0)<<16>>16,e>>>1&32767)|0;if((e|0)==1073741824){pA[f>>2]=1;i=2147483647}else i=e<<1;e=r-i|0;if(((e^r)&(i^r)|0)>=0){n=e;A=A&65535;A=30-A|0;A=A&65535;f=Ti(n,A,f)|0;return f|0}pA[f>>2]=1;n=(n>>>15&1)+2147483647|0;A=A&65535;A=30-A|0;A=A&65535;f=Ti(n,A,f)|0;return f|0}function Si(A,e,f,i,r,n){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;var t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0;o=WA;WA=WA+48|0;s=o;a=0-(f&65535)|0;a=r<<16>>16==0?a:a<<1&131070;f=a&65535;a=(f<<16>>16<0?a+6|0:a)<<16>>16;n=6-a|0;jA[s>>1]=jA[7858+(a<<1)>>1]|0;jA[s+2>>1]=jA[7858+(n<<1)>>1]|0;jA[s+4>>1]=jA[7858+(a+6<<1)>>1]|0;jA[s+6>>1]=jA[7858+(n+6<<1)>>1]|0;jA[s+8>>1]=jA[7858+(a+12<<1)>>1]|0;jA[s+10>>1]=jA[7858+(n+12<<1)>>1]|0;jA[s+12>>1]=jA[7858+(a+18<<1)>>1]|0;jA[s+14>>1]=jA[7858+(n+18<<1)>>1]|0;jA[s+16>>1]=jA[7858+(a+24<<1)>>1]|0;jA[s+18>>1]=jA[7858+(n+24<<1)>>1]|0;jA[s+20>>1]=jA[7858+(a+30<<1)>>1]|0;jA[s+22>>1]=jA[7858+(n+30<<1)>>1]|0;jA[s+24>>1]=jA[7858+(a+36<<1)>>1]|0;jA[s+26>>1]=jA[7858+(n+36<<1)>>1]|0;jA[s+28>>1]=jA[7858+(a+42<<1)>>1]|0;jA[s+30>>1]=jA[7858+(n+42<<1)>>1]|0;jA[s+32>>1]=jA[7858+(a+48<<1)>>1]|0;jA[s+34>>1]=jA[7858+(n+48<<1)>>1]|0;jA[s+36>>1]=jA[7858+(a+54<<1)>>1]|0;jA[s+38>>1]=jA[7858+(n+54<<1)>>1]|0;n=i<<16>>16>>>1&65535;if(!(n<<16>>16)){WA=o;return}a=A+((f<<16>>16>>15<<16>>16)-(e<<16>>16)<<1)|0;while(1){B=a+2|0;t=jA[B>>1]|0;e=t;i=a;w=5;l=s;r=16384;f=16384;while(1){g=jA[l>>1]|0;v=(VA(g,e<<16>>16)|0)+f|0;Q=jA[B+-2>>1]|0;f=(VA(Q,g)|0)+r|0;g=i;i=i+4|0;u=jA[l+2>>1]|0;f=f+(VA(u,t<<16>>16)|0)|0;r=jA[i>>1]|0;u=v+(VA(r,u)|0)|0;B=B+-4|0;v=jA[l+4>>1]|0;Q=u+(VA(v,Q)|0)|0;e=jA[B>>1]|0;v=f+(VA(e<<16>>16,v)|0)|0;f=jA[l+6>>1]|0;r=v+(VA(f,r)|0)|0;t=jA[g+6>>1]|0;f=Q+(VA(t<<16>>16,f)|0)|0;if(w<<16>>16<=1)break;else{w=w+-1<<16>>16;l=l+8|0}}jA[A>>1]=r>>>15;jA[A+2>>1]=f>>>15;n=n+-1<<16>>16;if(!(n<<16>>16))break;else{a=a+4|0;A=A+4|0}}WA=o;return}function ji(A,e,f,i,r,n,t){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;var w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0;d=WA;WA=WA+144|0;u=d+120|0;h=d+100|0;k=d+80|0;b=d+60|0;P=d+40|0;Q=d+20|0;g=d;mi(f,u,10,t);Ri(u,h,t);if((e|0)==8){ZA()}else{f=0;do{E=VA(jA[8160+(f<<1)>>1]|0,jA[A+(f<<1)>>1]|0)|0;E=(E>>>15)+(DA[8140+(f<<1)>>1]|0)|0;jA[k+(f<<1)>>1]=E;jA[b+(f<<1)>>1]=(DA[u+(f<<1)>>1]|0)-E;f=f+1|0}while((f|0)!=10)}do{if(e>>>0>=2){E=b+2|0;D=b+4|0;C=DA[b>>1]|0;c=jA[h>>1]<<1;u=DA[E>>1]|0;Q=jA[h+2>>1]<<1;o=DA[D>>1]|0;s=jA[h+4>>1]<<1;if((e|0)==5){g=2147483647;n=0;f=0;v=17908;while(1){B=(VA(C-(DA[v>>1]|0)<<16>>16,c)|0)>>16;B=VA(B,B)|0;a=(VA(u-(DA[v+2>>1]|0)<<16>>16,Q)|0)>>16;B=(VA(a,a)|0)+B|0;a=(VA(o-(DA[v+4>>1]|0)<<16>>16,s)|0)>>16;a=B+(VA(a,a)|0)|0;B=(a|0)<(g|0);f=B?n:f;n=n+1<<16>>16;if(n<<16>>16>=512)break;else{g=B?a:g;v=v+6|0}}a=(f<<16>>16)*3|0;jA[b>>1]=jA[17908+(a<<1)>>1]|0;jA[E>>1]=jA[17908+(a+1<<1)>>1]|0;jA[D>>1]=jA[17908+(a+2<<1)>>1]|0;jA[r>>1]=f;a=b+6|0;B=b+8|0;C=b+10|0;v=DA[a>>1]|0;n=jA[h+6>>1]<<1;g=DA[B>>1]|0;Q=jA[h+8>>1]<<1;o=DA[C>>1]|0;s=jA[h+10>>1]<<1;w=2147483647;u=0;f=0;c=9716;while(1){l=(VA(n,v-(DA[c>>1]|0)<<16>>16)|0)>>16;l=VA(l,l)|0;e=(VA(Q,g-(DA[c+2>>1]|0)<<16>>16)|0)>>16;l=(VA(e,e)|0)+l|0;e=(VA(s,o-(DA[c+4>>1]|0)<<16>>16)|0)>>16;e=l+(VA(e,e)|0)|0;l=(e|0)<(w|0);f=l?u:f;u=u+1<<16>>16;if(u<<16>>16>=512)break;else{w=l?e:w;c=c+6|0}}w=(f<<16>>16)*3|0;jA[a>>1]=jA[9716+(w<<1)>>1]|0;jA[B>>1]=jA[9716+(w+1<<1)>>1]|0;jA[C>>1]=jA[9716+(w+2<<1)>>1]|0;jA[r+2>>1]=f;w=b+12|0;jA[r+4>>1]=pi(w,12788,h+12|0,512)|0;u=E;v=D;f=C;l=b;break}else{g=2147483647;n=0;f=0;v=8180;while(1){B=(VA(C-(DA[v>>1]|0)<<16>>16,c)|0)>>16;B=VA(B,B)|0;a=(VA(u-(DA[v+2>>1]|0)<<16>>16,Q)|0)>>16;B=(VA(a,a)|0)+B|0;a=(VA(o-(DA[v+4>>1]|0)<<16>>16,s)|0)>>16;a=B+(VA(a,a)|0)|0;B=(a|0)<(g|0);f=B?n:f;n=n+1<<16>>16;if(n<<16>>16>=256)break;else{g=B?a:g;v=v+6|0}}a=(f<<16>>16)*3|0;jA[b>>1]=jA[8180+(a<<1)>>1]|0;jA[E>>1]=jA[8180+(a+1<<1)>>1]|0;jA[D>>1]=jA[8180+(a+2<<1)>>1]|0;jA[r>>1]=f;a=b+6|0;B=b+8|0;C=b+10|0;v=DA[a>>1]|0;n=jA[h+6>>1]<<1;g=DA[B>>1]|0;Q=jA[h+8>>1]<<1;o=DA[C>>1]|0;s=jA[h+10>>1]<<1;w=2147483647;u=0;f=0;c=9716;while(1){l=(VA(n,v-(DA[c>>1]|0)<<16>>16)|0)>>16;l=VA(l,l)|0;e=(VA(Q,g-(DA[c+2>>1]|0)<<16>>16)|0)>>16;l=(VA(e,e)|0)+l|0;e=(VA(s,o-(DA[c+4>>1]|0)<<16>>16)|0)>>16;e=l+(VA(e,e)|0)|0;l=(e|0)<(w|0);f=l?u:f;u=u+1<<16>>16;if(u<<16>>16>=512)break;else{w=l?e:w;c=c+6|0}}w=(f<<16>>16)*3|0;jA[a>>1]=jA[9716+(w<<1)>>1]|0;jA[B>>1]=jA[9716+(w+1<<1)>>1]|0;jA[C>>1]=jA[9716+(w+2<<1)>>1]|0;jA[r+2>>1]=f;w=b+12|0;jA[r+4>>1]=pi(w,12788,h+12|0,512)|0;u=E;v=D;f=C;l=b;break}}else{D=b+2|0;E=b+4|0;a=DA[b>>1]|0;B=jA[h>>1]<<1;l=DA[D>>1]|0;w=jA[h+2>>1]<<1;e=DA[E>>1]|0;s=jA[h+4>>1]<<1;g=2147483647;n=0;f=0;v=8180;while(1){Q=(VA(B,a-(DA[v>>1]|0)<<16>>16)|0)>>16;Q=VA(Q,Q)|0;o=(VA(w,l-(DA[v+2>>1]|0)<<16>>16)|0)>>16;Q=(VA(o,o)|0)+Q|0;o=(VA(s,e-(DA[v+4>>1]|0)<<16>>16)|0)>>16;o=Q+(VA(o,o)|0)|0;Q=(o|0)<(g|0);f=Q?n:f;n=n+1<<16>>16;if(n<<16>>16>=256)break;else{g=Q?o:g;v=v+6|0}}a=(f<<16>>16)*3|0;jA[b>>1]=jA[8180+(a<<1)>>1]|0;jA[D>>1]=jA[8180+(a+1<<1)>>1]|0;jA[E>>1]=jA[8180+(a+2<<1)>>1]|0;jA[r>>1]=f;a=b+6|0;B=b+8|0;C=b+10|0;v=DA[a>>1]|0;n=jA[h+6>>1]<<1;g=DA[B>>1]|0;Q=jA[h+8>>1]<<1;o=DA[C>>1]|0;s=jA[h+10>>1]<<1;w=2147483647;u=0;f=0;c=9716;while(1){l=(VA(n,v-(DA[c>>1]|0)<<16>>16)|0)>>16;l=VA(l,l)|0;e=(VA(Q,g-(DA[c+2>>1]|0)<<16>>16)|0)>>16;l=(VA(e,e)|0)+l|0;e=(VA(s,o-(DA[c+4>>1]|0)<<16>>16)|0)>>16;e=l+(VA(e,e)|0)|0;l=(e|0)<(w|0);f=l?u:f;u=u+1<<16>>16;if(u<<16>>16>=256)break;else{w=l?e:w;c=c+12|0}}w=(f<<16>>16)*6|0;jA[a>>1]=jA[9716+(w<<1)>>1]|0;jA[B>>1]=jA[9716+((w|1)<<1)>>1]|0;jA[C>>1]=jA[9716+(w+2<<1)>>1]|0;jA[r+2>>1]=f;w=b+12|0;jA[r+4>>1]=pi(w,16884,h+12|0,128)|0;u=D;v=E;f=C;l=b}}while(0);c=A;o=b;s=c+20|0;do{jA[c>>1]=jA[o>>1]|0;c=c+2|0;o=o+2|0}while((c|0)<(s|0));jA[P>>1]=(DA[k>>1]|0)+(DA[l>>1]|0);jA[P+2>>1]=(DA[k+2>>1]|0)+(DA[u>>1]|0);jA[P+4>>1]=(DA[k+4>>1]|0)+(DA[v>>1]|0);jA[P+6>>1]=(DA[k+6>>1]|0)+(DA[a>>1]|0);jA[P+8>>1]=(DA[k+8>>1]|0)+(DA[B>>1]|0);jA[P+10>>1]=(DA[k+10>>1]|0)+(DA[f>>1]|0);jA[P+12>>1]=(DA[k+12>>1]|0)+(DA[w>>1]|0);jA[P+14>>1]=(DA[k+14>>1]|0)+(DA[b+14>>1]|0);jA[P+16>>1]=(DA[k+16>>1]|0)+(DA[b+16>>1]|0);jA[P+18>>1]=(DA[k+18>>1]|0)+(DA[b+18>>1]|0);$i(P,205,10,t);Oi(P,i,10,t);WA=d;return}function pi(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var r=0,n=0,t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0;c=A+2|0;C=A+4|0;D=A+6|0;if(i<<16>>16>0){a=DA[A>>1]|0;s=jA[f>>1]<<1;o=DA[c>>1]|0;Q=jA[f+2>>1]<<1;g=DA[C>>1]|0;v=jA[f+4>>1]<<1;u=DA[D>>1]|0;r=jA[f+6>>1]<<1;w=2147483647;l=0;f=0;B=e;while(1){n=(VA(s,a-(DA[B>>1]|0)<<16>>16)|0)>>16;n=VA(n,n)|0;t=(VA(Q,o-(DA[B+2>>1]|0)<<16>>16)|0)>>16;n=(VA(t,t)|0)+n|0;t=(VA(v,g-(DA[B+4>>1]|0)<<16>>16)|0)>>16;t=n+(VA(t,t)|0)|0;n=(VA(r,u-(DA[B+6>>1]|0)<<16>>16)|0)>>16;n=t+(VA(n,n)|0)|0;t=(n|0)<(w|0);f=t?l:f;l=l+1<<16>>16;if(l<<16>>16>=i<<16>>16)break;else{w=t?n:w;B=B+8|0}}}else f=0;i=f<<16>>16<<2;u=i|1;jA[A>>1]=jA[e+(i<<1)>>1]|0;jA[c>>1]=jA[e+(u<<1)>>1]|0;jA[C>>1]=jA[e+(u+1<<1)>>1]|0;jA[D>>1]=jA[e+((i|3)<<1)>>1]|0;return f|0}function Wi(A,e,f,i,r,n,t){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;var w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0;G=WA;WA=WA+192|0;B=G+160|0;l=G+140|0;d=G+120|0;I=G+100|0;M=G+80|0;F=G+60|0;w=G+40|0;H=G+20|0;U=G;mi(e,B,10,t);mi(f,l,10,t);Ri(B,d,t);Ri(l,I,t);a=0;f=M;e=F;s=w;while(1){b=(((jA[A+(a<<1)>>1]|0)*21299|0)>>>15)+(DA[20980+(a<<1)>>1]|0)|0;jA[f>>1]=b;jA[e>>1]=(DA[B>>1]|0)-b;jA[s>>1]=(DA[l>>1]|0)-b;a=a+1|0;if((a|0)==10)break;else{B=B+2|0;l=l+2|0;f=f+2|0;e=e+2|0;s=s+2|0}}jA[n>>1]=Vi(F,w,21e3,jA[d>>1]|0,jA[d+2>>1]|0,jA[I>>1]|0,jA[I+2>>1]|0,128)|0;jA[n+2>>1]=Vi(F+4|0,w+4|0,22024,jA[d+4>>1]|0,jA[d+6>>1]|0,jA[I+4>>1]|0,jA[I+6>>1]|0,256)|0;h=F+8|0;P=w+8|0;k=F+10|0;b=w+10|0;f=jA[h>>1]|0;o=jA[d+8>>1]<<1;Q=jA[k>>1]|0;g=jA[d+10>>1]<<1;v=jA[P>>1]|0;u=jA[I+8>>1]<<1;c=jA[b>>1]|0;C=jA[I+10>>1]<<1;l=2147483647;D=0;s=0;E=24072;e=0;while(1){B=jA[E>>1]|0;a=(VA(f-B<<16>>16,o)|0)>>16;a=VA(a,a)|0;B=(VA(B+f<<16>>16,o)|0)>>16;B=VA(B,B)|0;L=jA[E+2>>1]|0;R=(VA(Q-L<<16>>16,g)|0)>>16;a=(VA(R,R)|0)+a|0;L=(VA(L+Q<<16>>16,g)|0)>>16;B=(VA(L,L)|0)+B|0;if((a|0)<(l|0)|(B|0)<(l|0)){R=jA[E+4>>1]|0;L=(VA(v-R<<16>>16,u)|0)>>16;L=(VA(L,L)|0)+a|0;R=(VA(R+v<<16>>16,u)|0)>>16;R=(VA(R,R)|0)+B|0;B=jA[E+6>>1]|0;a=(VA(c-B<<16>>16,C)|0)>>16;a=L+(VA(a,a)|0)|0;B=(VA(B+c<<16>>16,C)|0)>>16;B=R+(VA(B,B)|0)|0;R=(a|0)<(l|0);a=R?a:l;L=(B|0)<(a|0);a=L?B:a;s=R|L?D:s;e=L?1:R?0:e}else a=l;D=D+1<<16>>16;if(D<<16>>16>=256)break;else{l=a;E=E+8|0}}a=s<<16>>16;B=a<<2;s=B|1;l=24072+(s<<1)|0;f=jA[24072+(B<<1)>>1]|0;if(!(e<<16>>16)){jA[h>>1]=f;jA[k>>1]=jA[l>>1]|0;jA[P>>1]=jA[24072+(s+1<<1)>>1]|0;jA[b>>1]=jA[24072+((B|3)<<1)>>1]|0;e=a<<1}else{jA[h>>1]=0-(f&65535);jA[k>>1]=0-(DA[l>>1]|0);jA[P>>1]=0-(DA[24072+(s+1<<1)>>1]|0);jA[b>>1]=0-(DA[24072+((B|3)<<1)>>1]|0);e=a<<1&65534|1}jA[n+4>>1]=e;jA[n+6>>1]=Vi(F+12|0,w+12|0,26120,jA[d+12>>1]|0,jA[d+14>>1]|0,jA[I+12>>1]|0,jA[I+14>>1]|0,256)|0;jA[n+8>>1]=Vi(F+16|0,w+16|0,28168,jA[d+16>>1]|0,jA[d+18>>1]|0,jA[I+16>>1]|0,jA[I+18>>1]|0,64)|0;l=0;B=H;a=U;f=M;e=F;while(1){L=DA[f>>1]|0;jA[B>>1]=L+(DA[e>>1]|0);R=jA[w>>1]|0;jA[a>>1]=L+(R&65535);jA[A+(l<<1)>>1]=R;l=l+1|0;if((l|0)==10)break;else{B=B+2|0;a=a+2|0;f=f+2|0;e=e+2|0;w=w+2|0}}$i(H,205,10,t);$i(U,205,10,t);Oi(H,i,10,t);Oi(U,r,10,t);WA=G;return}function Vi(A,e,f,i,r,n,t,w){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;var l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0;Q=jA[A>>1]|0;D=A+2|0;v=jA[D>>1]|0;c=jA[e>>1]|0;E=e+2|0;C=jA[E>>1]|0;if(w<<16>>16>0){o=i<<16>>16<<1;s=r<<16>>16<<1;a=n<<16>>16<<1;r=t<<16>>16<<1;n=2147483647;l=0;i=0;B=f;while(1){t=(VA(o,Q-(jA[B>>1]|0)|0)|0)>>16;t=VA(t,t)|0;if(((t|0)<(n|0)?(g=(VA(s,v-(jA[B+2>>1]|0)|0)|0)>>16,g=(VA(g,g)|0)+t|0,(g|0)<(n|0)):0)?(u=(VA(a,c-(jA[B+4>>1]|0)|0)|0)>>16,u=(VA(u,u)|0)+g|0,(u|0)<(n|0)):0){t=(VA(r,C-(jA[B+6>>1]|0)|0)|0)>>16;t=(VA(t,t)|0)+u|0;h=(t|0)<(n|0);t=h?t:n;i=h?l:i}else t=n;l=l+1<<16>>16;if(l<<16>>16>=w<<16>>16)break;else{n=t;B=B+8|0}}}else i=0;h=i<<16>>16<<2;w=h|1;jA[A>>1]=jA[f+(h<<1)>>1]|0;jA[D>>1]=jA[f+(w<<1)>>1]|0;jA[e>>1]=jA[f+(w+1<<1)>>1]|0;jA[E>>1]=jA[f+((h|3)<<1)>>1]|0;return i|0}function Zi(A){A=A|0;var e=0,f=0,i=0;if(!A){i=-1;return i|0}pA[A>>2]=0;e=lr(20)|0;if(!e){i=-1;return i|0}f=e;i=f+20|0;do{jA[f>>1]=0;f=f+2|0}while((f|0)<(i|0));pA[A>>2]=e;i=0;return i|0}function _i(A){A=A|0;var e=0;if(!A){e=-1;return e|0}e=A+20|0;do{jA[A>>1]=0;A=A+2|0}while((A|0)<(e|0));e=0;return e|0}function qi(A){A=A|0;var e=0;if(!A)return;e=pA[A>>2]|0;if(!e)return;Br(e);pA[A>>2]=0;return}function $i(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var r=0,n=0,t=0;if(f<<16>>16<=0)return;r=e<<16>>16;n=e&65535;t=0;while(1){i=jA[A>>1]|0;if(i<<16>>16<e<<16>>16){jA[A>>1]=e;i=(e<<16>>16)+r|0}else i=(i&65535)+n|0;t=t+1<<16>>16;if(t<<16>>16>=f<<16>>16)break;else{e=i&65535;A=A+2|0}}return}function Ar(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var r=0,n=0,t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0;r=i<<16>>16;i=r>>>2&65535;if(!(i<<16>>16))return;o=r+-1|0;C=A+20|0;g=e+(r+-4<<1)|0;v=e+(r+-3<<1)|0;u=e+(r+-2<<1)|0;c=e+(o<<1)|0;Q=e+(r+-11<<1)|0;o=f+(o<<1)|0;while(1){e=jA[C>>1]|0;t=5;w=C;l=Q;B=Q+-2|0;a=Q+-4|0;s=Q+-6|0;n=2048;A=2048;r=2048;f=2048;while(1){n=(VA(jA[l>>1]|0,e)|0)+n|0;A=(VA(jA[B>>1]|0,e)|0)+A|0;r=(VA(jA[a>>1]|0,e)|0)+r|0;e=(VA(jA[s>>1]|0,e)|0)+f|0;f=jA[w+-2>>1]|0;n=n+(VA(jA[l+2>>1]|0,f)|0)|0;A=A+(VA(jA[B+2>>1]|0,f)|0)|0;r=r+(VA(jA[a+2>>1]|0,f)|0)|0;w=w+-4|0;f=e+(VA(jA[s+2>>1]|0,f)|0)|0;t=t+-1<<16>>16;e=jA[w>>1]|0;if(!(t<<16>>16))break;else{l=l+4|0;B=B+4|0;a=a+4|0;s=s+4|0}}l=(VA(jA[c>>1]|0,e)|0)+n|0;B=(VA(jA[u>>1]|0,e)|0)+A|0;a=(VA(jA[v>>1]|0,e)|0)+r|0;s=(VA(jA[g>>1]|0,e)|0)+f|0;jA[o>>1]=l>>>12;jA[o+-2>>1]=B>>>12;jA[o+-4>>1]=a>>>12;jA[o+-6>>1]=s>>>12;i=i+-1<<16>>16;if(!(i<<16>>16))break;else{g=g+-8|0;v=v+-8|0;u=u+-8|0;c=c+-8|0;Q=Q+-8|0;o=o+-8|0}}return}function er(A,e){A=A|0;e=e|0;var f=0;f=A+32768|0;if((A|0)>-1&(f^A|0)<0){pA[e>>2]=1;f=(A>>>31)+2147483647|0}return f>>>16&65535|0}function fr(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,r=0;i=e<<16>>16;if(!(e<<16>>16))return A|0;if(e<<16>>16>0){A=A<<16>>16>>(e<<16>>16>15?15:i)&65535;return A|0}r=0-i|0;e=A<<16>>16;r=(r&65535)<<16>>16>15?15:r<<16>>16;i=e<<r;if((i<<16>>16>>r|0)==(e|0)){r=i&65535;return r|0}pA[f>>2]=1;r=A<<16>>16>0?32767:-32768;return r|0}function ir(A,e,f){A=A|0;e=e|0;f=f|0;if(e<<16>>16>15){e=0;return e|0}f=fr(A,e,f)|0;if(e<<16>>16>0)return f+((1<<(e<<16>>16)+-1&A<<16>>16|0)!=0&1)<<16>>16|0;else{e=f;return e|0}return 0}function rr(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,r=0,n=0;if((A|0)<1){jA[e>>1]=0;f=0;return f|0}r=(Ni(A)|0)&65534;n=r&65535;r=r<<16>>16;if(n<<16>>16>0){i=A<<r;if((i>>r|0)!=(A|0))i=A>>31^2147483647}else{r=0-r<<16;if((r|0)<2031616)i=A>>(r>>16);else i=0}jA[e>>1]=n;e=i>>>25&63;e=e>>>0>15?e+-16|0:e;n=jA[30216+(e<<1)>>1]|0;A=n<<16;i=VA(n-(DA[30216+(e+1<<1)>>1]|0)<<16>>16,i>>>10&32767)|0;if((i|0)==1073741824){pA[f>>2]=1;r=2147483647}else r=i<<1;i=A-r|0;if(((i^A)&(r^A)|0)>=0){f=i;return f|0}pA[f>>2]=1;f=(n>>>15&1)+2147483647|0;return f|0}function nr(A,e,f){A=A|0;e=e|0;f=f|0;A=(A<<16>>16)-(e<<16>>16)|0;if((A+32768|0)>>>0<=65535){f=A;f=f&65535;return f|0}pA[f>>2]=1;f=(A|0)>32767?32767:-32768;f=f&65535;return f|0}function tr(A,e,f,i,r,n){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;var t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0;b=WA;WA=WA+48|0;o=b;l=o;t=r;w=l+20|0;do{jA[l>>1]=jA[t>>1]|0;l=l+2|0;t=t+2|0}while((l|0)<(w|0));s=o+18|0;u=A+2|0;c=A+4|0;Q=e+20|0;C=A+6|0;D=A+8|0;E=A+10|0;h=A+12|0;P=A+14|0;k=A+16|0;g=A+18|0;v=A+20|0;w=jA[s>>1]|0;t=5;B=e;a=f;l=o+20|0;while(1){M=jA[A>>1]|0;I=(VA(M,jA[B>>1]|0)|0)+2048|0;M=(VA(jA[B+2>>1]|0,M)|0)+2048|0;o=w<<16>>16;I=I-(VA(o,jA[u>>1]|0)|0)|0;d=jA[c>>1]|0;o=M-(VA(o,d)|0)|0;M=jA[s+-2>>1]|0;d=I-(VA(M,d)|0)|0;I=jA[C>>1]|0;M=o-(VA(I,M)|0)|0;o=jA[s+-4>>1]|0;I=d-(VA(o,I)|0)|0;d=jA[D>>1]|0;o=M-(VA(d,o)|0)|0;M=jA[s+-6>>1]|0;d=I-(VA(M,d)|0)|0;I=jA[E>>1]|0;M=o-(VA(M,I)|0)|0;o=jA[s+-8>>1]|0;I=d-(VA(o,I)|0)|0;d=jA[h>>1]|0;o=M-(VA(d,o)|0)|0;M=jA[s+-10>>1]|0;d=I-(VA(M,d)|0)|0;I=jA[P>>1]|0;M=o-(VA(I,M)|0)|0;o=jA[s+-12>>1]|0;I=d-(VA(o,I)|0)|0;d=jA[k>>1]|0;o=M-(VA(o,d)|0)|0;M=jA[s+-14>>1]|0;d=I-(VA(M,d)|0)|0;I=jA[g>>1]|0;M=o-(VA(I,M)|0)|0;o=jA[s+-16>>1]|0;I=d-(VA(o,I)|0)|0;d=jA[v>>1]|0;o=M-(VA(d,o)|0)|0;d=I-(VA(jA[s+-18>>1]|0,d)|0)|0;d=(d+134217728|0)>>>0<268435455?d>>>12&65535:(d|0)>134217727?32767:-32768;o=o-(VA(jA[u>>1]|0,d<<16>>16)|0)|0;s=l+2|0;jA[l>>1]=d;jA[a>>1]=d;w=(o+134217728|0)>>>0<268435455?o>>>12&65535:(o|0)>134217727?32767:-32768;jA[s>>1]=w;jA[a+2>>1]=w;t=t+-1<<16>>16;if(!(t<<16>>16))break;else{B=B+4|0;a=a+4|0;l=l+4|0}}i=(i<<16>>16)+-10|0;l=i>>>1&65535;if(l<<16>>16){o=f+18|0;w=e+16|0;s=jA[o>>1]|0;B=Q;t=f+20|0;while(1){d=jA[A>>1]|0;a=(VA(d,jA[B>>1]|0)|0)+2048|0;d=(VA(jA[w+6>>1]|0,d)|0)+2048|0;w=jA[u>>1]|0;I=s<<16>>16;a=a-(VA(I,w)|0)|0;M=jA[c>>1]|0;I=d-(VA(I,M)|0)|0;d=jA[o+-2>>1]|0;M=a-(VA(d,M)|0)|0;a=jA[C>>1]|0;d=I-(VA(a,d)|0)|0;I=jA[o+-4>>1]|0;a=M-(VA(I,a)|0)|0;M=jA[D>>1]|0;I=d-(VA(M,I)|0)|0;d=jA[o+-6>>1]|0;M=a-(VA(d,M)|0)|0;a=jA[E>>1]|0;d=I-(VA(d,a)|0)|0;I=jA[o+-8>>1]|0;a=M-(VA(I,a)|0)|0;M=jA[h>>1]|0;I=d-(VA(M,I)|0)|0;d=jA[o+-10>>1]|0;M=a-(VA(d,M)|0)|0;a=jA[P>>1]|0;d=I-(VA(a,d)|0)|0;I=jA[o+-12>>1]|0;a=M-(VA(I,a)|0)|0;M=jA[k>>1]|0;I=d-(VA(I,M)|0)|0;d=jA[o+-14>>1]|0;M=a-(VA(d,M)|0)|0;a=jA[g>>1]|0;d=I-(VA(a,d)|0)|0;I=jA[o+-16>>1]|0;a=M-(VA(I,a)|0)|0;M=jA[v>>1]|0;I=d-(VA(M,I)|0)|0;M=a-(VA(jA[o+-18>>1]|0,M)|0)|0;a=B+4|0;M=(M+134217728|0)>>>0<268435455?M>>>12&65535:(M|0)>134217727?32767:-32768;w=I-(VA(w,M<<16>>16)|0)|0;o=t+2|0;jA[t>>1]=M;do{if((w+134217728|0)>>>0>=268435455){t=t+4|0;if((w|0)>134217727){jA[o>>1]=32767;w=32767;break}else{jA[o>>1]=-32768;w=-32768;break}}else{w=w>>>12&65535;jA[o>>1]=w;t=t+4|0}}while(0);l=l+-1<<16>>16;if(!(l<<16>>16))break;else{M=B;s=w;B=a;w=M}}}if(!(n<<16>>16)){WA=b;return}l=r;t=f+(i<<1)|0;w=l+20|0;do{jA[l>>1]=jA[t>>1]|0;l=l+2|0;t=t+2|0}while((l|0)<(w|0));WA=b;return}function wr(A,e,f){A=A|0;e=e|0;f=f|0;jA[f>>1]=jA[A>>1]|0;jA[f+2>>1]=((VA(jA[e>>1]|0,jA[A+2>>1]|0)|0)+16384|0)>>>15;jA[f+4>>1]=((VA(jA[e+2>>1]|0,jA[A+4>>1]|0)|0)+16384|0)>>>15;jA[f+6>>1]=((VA(jA[e+4>>1]|0,jA[A+6>>1]|0)|0)+16384|0)>>>15;jA[f+8>>1]=((VA(jA[e+6>>1]|0,jA[A+8>>1]|0)|0)+16384|0)>>>15;jA[f+10>>1]=((VA(jA[e+8>>1]|0,jA[A+10>>1]|0)|0)+16384|0)>>>15;jA[f+12>>1]=((VA(jA[e+10>>1]|0,jA[A+12>>1]|0)|0)+16384|0)>>>15;jA[f+14>>1]=((VA(jA[e+12>>1]|0,jA[A+14>>1]|0)|0)+16384|0)>>>15;jA[f+16>>1]=((VA(jA[e+14>>1]|0,jA[A+16>>1]|0)|0)+16384|0)>>>15;jA[f+18>>1]=((VA(jA[e+16>>1]|0,jA[A+18>>1]|0)|0)+16384|0)>>>15;jA[f+20>>1]=((VA(jA[e+18>>1]|0,jA[A+20>>1]|0)|0)+16384|0)>>>15;return}function lr(A){A=A|0;var e=0,f=0,i=0,r=0,n=0,t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0,T=0,y=0,Y=0,z=0,X=0,J=0,O=0,m=0,N=0,K=0,x=0,S=0,j=0;do{if(A>>>0<245){c=A>>>0<11?16:A+11&-8;A=c>>>3;s=pA[26]|0;l=s>>>A;if(l&3){ZA()}e=pA[28]|0;if(c>>>0>e>>>0){if(l){ZA()}A=pA[27]|0;if(A){ZA()}else j=154}else j=154}else if(A>>>0<=4294967231){A=A+11|0;h=A&-8;s=pA[27]|0;if(s){ZA()}else{c=h;j=154}}else{c=-1;j=154}}while(0);A:do{if((j|0)==154){A=pA[28]|0;if(A>>>0>=c>>>0){ZA()}A=pA[29]|0;if(A>>>0>c>>>0){j=A-c|0;pA[29]=j;n=pA[32]|0;pA[32]=n+c;pA[n+(c+4)>>2]=j|1;pA[n+4>>2]=c|3;n=n+8|0;break}if(!(pA[144]|0))sr();s=c+48|0;f=pA[146]|0;a=c+47|0;i=f+a|0;f=0-f|0;B=i&f;if(B>>>0>c>>>0){A=pA[136]|0;if((A|0)!=0?(M=pA[134]|0,L=M+B|0,L>>>0<=M>>>0|L>>>0>A>>>0):0){n=0;break}e:do{if(!(pA[137]&4)){A=pA[32]|0;f:do{if(A){w=552;while(1){l=pA[w>>2]|0;if(l>>>0<=A>>>0?(P=w+4|0,(l+(pA[P>>2]|0)|0)>>>0>A>>>0):0){n=w;A=P;break}w=pA[w+8>>2]|0;if(!w){j=172;break f}}l=i-(pA[29]|0)&f;if(l>>>0<2147483647){w=iA(l|0)|0;L=(w|0)==((pA[n>>2]|0)+(pA[A>>2]|0)|0);A=L?l:0;if(L){if((w|0)!=(-1|0)){d=w;C=A;j=192;break e}}else j=182}else A=0}else j=172}while(0);do{if((j|0)==172){n=iA(0)|0;if((n|0)!=(-1|0)){A=n;l=pA[145]|0;w=l+-1|0;if(!(w&A))l=B;else l=B-A+(w+A&0-l)|0;A=pA[134]|0;w=A+l|0;if(l>>>0>c>>>0&l>>>0<2147483647){L=pA[136]|0;if((L|0)!=0?w>>>0<=A>>>0|w>>>0>L>>>0:0){A=0;break}w=iA(l|0)|0;j=(w|0)==(n|0);A=j?l:0;if(j){d=n;C=A;j=192;break e}else j=182}else A=0}else A=0}}while(0);f:do{if((j|0)==182){ZA()}}while(0);pA[137]=pA[137]|4;j=189}else{A=0;j=189}}while(0);if((((j|0)==189?B>>>0<2147483647:0)?(F=iA(B|0)|0,H=iA(0)|0,F>>>0<H>>>0&((F|0)!=(-1|0)&(H|0)!=(-1|0))):0)?(U=H-F|0,G=U>>>0>(c+40|0)>>>0,G):0){d=F;C=G?U:A;j=192}if((j|0)==192){l=(pA[134]|0)+C|0;pA[134]=l;if(l>>>0>(pA[135]|0)>>>0)pA[135]=l;v=pA[32]|0;e:do{if(v){n=552;do{A=pA[n>>2]|0;l=n+4|0;w=pA[l>>2]|0;if((d|0)==(A+w|0)){R=A;T=l;y=w;Y=n;j=202;break}n=pA[n+8>>2]|0}while((n|0)!=0);if(((j|0)==202?(pA[Y+12>>2]&8|0)==0:0)?v>>>0<d>>>0&v>>>0>=R>>>0:0){pA[T>>2]=y+C;j=(pA[29]|0)+C|0;S=v+8|0;S=(S&7|0)==0?0:0-S&7;x=j-S|0;pA[32]=v+S;pA[29]=x;pA[v+(S+4)>>2]=x|1;pA[v+(j+4)>>2]=40;pA[33]=pA[148];break}l=pA[30]|0;if(d>>>0<l>>>0){pA[30]=d;l=d}w=d+C|0;A=552;while(1){if((pA[A>>2]|0)==(w|0)){ZA()}A=pA[A+8>>2]|0;if(!A){w=552;break}}if((j|0)==210)if(!(pA[w+12>>2]&8)){ZA()}else w=552;while(1){n=pA[w>>2]|0;if(n>>>0<=v>>>0?(t=pA[w+4>>2]|0,r=n+t|0,r>>>0>v>>>0):0)break;w=pA[w+8>>2]|0}w=n+(t+-39)|0;w=n+(t+-47+((w&7|0)==0?0:0-w&7))|0;l=v+16|0;w=w>>>0<l>>>0?v:w;t=w+8|0;n=d+8|0;n=(n&7|0)==0?0:0-n&7;j=C+-40-n|0;pA[32]=d+n;pA[29]=j;pA[d+(n+4)>>2]=j|1;pA[d+(C+-36)>>2]=40;pA[33]=pA[148];n=w+4|0;pA[n>>2]=27;pA[t>>2]=pA[138];pA[t+4>>2]=pA[139];pA[t+8>>2]=pA[140];pA[t+12>>2]=pA[141];pA[138]=d;pA[139]=C;pA[141]=0;pA[140]=t;t=w+28|0;pA[t>>2]=7;if((w+32|0)>>>0<r>>>0)do{ZA()}while((j+8|0)>>>0<r>>>0);if((w|0)!=(v|0)){ZA()}}else{j=pA[30]|0;if((j|0)==0|d>>>0<j>>>0)pA[30]=d;pA[138]=d;pA[139]=C;pA[141]=0;pA[35]=pA[144];pA[34]=-1;f=0;do{j=f<<1;S=144+(j<<2)|0;pA[144+(j+3<<2)>>2]=S;pA[144+(j+2<<2)>>2]=S;f=f+1|0}while((f|0)!=32);j=d+8|0;j=(j&7|0)==0?0:0-j&7;S=C+-40-j|0;pA[32]=d+j;pA[29]=S;pA[d+(j+4)>>2]=S|1;pA[d+(C+-36)>>2]=40;pA[33]=pA[148]}}while(0);e=pA[29]|0;if(e>>>0>c>>>0){j=e-c|0;pA[29]=j;n=pA[32]|0;pA[32]=n+c;pA[n+(c+4)>>2]=j|1;pA[n+4>>2]=c|3;n=n+8|0;break}}pA[(ar()|0)>>2]=12;n=0}else n=0}}while(0);return n|0}function Br(A){A=A|0;var e=0,f=0,i=0,r=0,n=0,t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0;A:do{if(A){r=A+-8|0;B=pA[30]|0;e:do{if(r>>>0>=B>>>0?(i=pA[A+-4>>2]|0,f=i&3,(f|0)!=1):0){E=i&-8;h=A+(E+-8)|0;do{if(!(i&1)){r=pA[r>>2]|0;if(!f)break A;a=-8-r|0;o=A+a|0;Q=r+E|0;if(o>>>0<B>>>0)break e;if((o|0)==(pA[31]|0)){ZA()}f=r>>>3;if(r>>>0<256){i=pA[A+(a+8)>>2]|0;n=pA[A+(a+12)>>2]|0;r=144+(f<<1<<2)|0;do{if((i|0)!=(r|0)){ZA()}}while(0);if((n|0)==(i|0)){pA[26]=pA[26]&~(1<<f);I=o;n=Q;break}do{if((n|0)==(r|0))e=n+8|0;else{ZA()}}while(0);pA[i+12>>2]=n;pA[e>>2]=i;I=o;n=Q;break}t=pA[A+(a+24)>>2]|0;r=pA[A+(a+12)>>2]|0;do{if((r|0)==(o|0)){i=A+(a+20)|0;r=pA[i>>2]|0;if(!r){i=A+(a+16)|0;r=pA[i>>2]|0;if(!r){s=0;break}}while(1){f=r+20|0;e=pA[f>>2]|0;if(e){r=e;i=f;continue}f=r+16|0;e=pA[f>>2]|0;if(!e)break;else{r=e;i=f}}if(i>>>0<B>>>0)eA();else{pA[i>>2]=0;s=r;break}}else{ZA()}}while(0);if(t){r=pA[A+(a+28)>>2]|0;i=408+(r<<2)|0;if((o|0)==(pA[i>>2]|0)){pA[i>>2]=s;if(!s){pA[27]=pA[27]&~(1<<r);I=o;n=Q;break}}else{if(t>>>0<(pA[30]|0)>>>0)eA();r=t+16|0;if((pA[r>>2]|0)==(o|0))pA[r>>2]=s;else pA[t+20>>2]=s;if(!s){I=o;n=Q;break}}i=pA[30]|0;if(s>>>0<i>>>0)eA();pA[s+24>>2]=t;r=pA[A+(a+16)>>2]|0;do{if(r)if(r>>>0<i>>>0)eA();else{pA[s+16>>2]=r;pA[r+24>>2]=s;break}}while(0);r=pA[A+(a+20)>>2]|0;if(r)if(r>>>0<(pA[30]|0)>>>0)eA();else{ZA()}else{I=o;n=Q}}else{I=o;n=Q}}else{I=r;n=E}}while(0);if(I>>>0<h>>>0?(g=A+(E+-4)|0,v=pA[g>>2]|0,(v&1|0)!=0):0){if(!(v&2)){if((h|0)==(pA[32]|0)){d=(pA[29]|0)+n|0;pA[29]=d;pA[32]=I;pA[I+4>>2]=d|1;if((I|0)!=(pA[31]|0))break A;pA[31]=0;pA[28]=0;break A}if((h|0)==(pA[31]|0)){ZA()}l=(v&-8)+n|0;f=v>>>3;do{if(v>>>0>=256){e=pA[A+(E+16)>>2]|0;n=pA[A+(E|4)>>2]|0;do{if((n|0)==(h|0)){r=A+(E+12)|0;n=pA[r>>2]|0;if(!n){r=A+(E+8)|0;n=pA[r>>2]|0;if(!n){P=0;break}}while(1){i=n+20|0;f=pA[i>>2]|0;if(f){n=f;r=i;continue}i=n+16|0;f=pA[i>>2]|0;if(!f)break;else{n=f;r=i}}if(r>>>0<(pA[30]|0)>>>0)eA();else{pA[r>>2]=0;P=n;break}}else{ZA()}}while(0);if(e){n=pA[A+(E+20)>>2]|0;r=408+(n<<2)|0;if((h|0)==(pA[r>>2]|0)){pA[r>>2]=P;if(!P){pA[27]=pA[27]&~(1<<n);break}}else{if(e>>>0<(pA[30]|0)>>>0)eA();n=e+16|0;if((pA[n>>2]|0)==(h|0))pA[n>>2]=P;else pA[e+20>>2]=P;if(!P)break}n=pA[30]|0;if(P>>>0<n>>>0)eA();pA[P+24>>2]=e;r=pA[A+(E+8)>>2]|0;do{if(r)if(r>>>0<n>>>0)eA();else{ZA()}}while(0);f=pA[A+(E+12)>>2]|0;if(f)if(f>>>0<(pA[30]|0)>>>0)eA();else{ZA()}}}else{i=pA[A+E>>2]|0;n=pA[A+(E|4)>>2]|0;r=144+(f<<1<<2)|0;do{if((i|0)!=(r|0)){ZA()}}while(0);if((n|0)==(i|0)){pA[26]=pA[26]&~(1<<f);break}do{if((n|0)==(r|0))u=n+8|0;else{ZA()}}while(0);pA[i+12>>2]=n;pA[u>>2]=i}}while(0);pA[I+4>>2]=l|1;pA[I+l>>2]=l;if((I|0)==(pA[31]|0)){pA[28]=l;break A}else n=l}else{pA[g>>2]=v&-2;pA[I+4>>2]=n|1;pA[I+n>>2]=n}r=n>>>3;if(n>>>0<256){i=r<<1;n=144+(i<<2)|0;e=pA[26]|0;f=1<<r;if(e&f){ZA()}else{pA[26]=e|f;k=144+(i+2<<2)|0;b=n}pA[k>>2]=I;pA[b+12>>2]=I;pA[I+8>>2]=b;pA[I+12>>2]=n;break A}e=n>>>8;if(e)if(n>>>0>16777215)r=31;else{k=(e+1048320|0)>>>16&8;b=e<<k;A=(b+520192|0)>>>16&4;b=b<<A;r=(b+245760|0)>>>16&2;r=14-(A|k|r)+(b<<r>>>15)|0;r=n>>>(r+7|0)&1|r<<1}else r=0;f=408+(r<<2)|0;pA[I+28>>2]=r;pA[I+20>>2]=0;pA[I+16>>2]=0;e=pA[27]|0;i=1<<r;f:do{if(e&i){f=pA[f>>2]|0;i:do{if((pA[f+4>>2]&-8|0)!=(n|0)){r=n<<((r|0)==31?0:25-(r>>>1)|0);while(1){e=f+16+(r>>>31<<2)|0;i=pA[e>>2]|0;if(!i)break;if((pA[i+4>>2]&-8|0)==(n|0)){d=i;break i}else{r=r<<1;f=i}}if(e>>>0<(pA[30]|0)>>>0)eA();else{pA[e>>2]=I;pA[I+24>>2]=f;pA[I+12>>2]=I;pA[I+8>>2]=I;break f}}else d=f}while(0);e=d+8|0;f=pA[e>>2]|0;b=pA[30]|0;if(f>>>0>=b>>>0&d>>>0>=b>>>0){ZA()}else eA()}else{pA[27]=e|i;pA[f>>2]=I;pA[I+24>>2]=f;pA[I+12>>2]=I;pA[I+8>>2]=I}}while(0);I=(pA[34]|0)+-1|0;pA[34]=I;if(!I)e=560;else break A;while(1){e=pA[e>>2]|0;if(!e)break;else e=e+8|0}pA[34]=-1;break A}}}while(0);eA()}}while(0);return}function ar(){var A=0;if(!0)A=600;else A=pA[(AA()|0)+60>>2]|0;return A|0}function sr(){var A=0;do{if(!(pA[144]|0)){A=$(30)|0;if(!(A+-1&A)){pA[146]=A;pA[145]=A;pA[147]=-1;pA[148]=-1;pA[149]=0;pA[137]=0;pA[144]=(rA(0)|0)&-16^1431655768;break}else eA()}}while(0);return}function or(A,e,f){A=A|0;e=e|0;f=f|0;var i=0;if((f|0)>=4096)return tA(A|0,e|0,f|0)|0;i=A|0;if((A&3)==(e&3)){while(A&3){if(!f)return i|0;SA[A>>0]=SA[e>>0]|0;A=A+1|0;e=e+1|0;f=f-1|0}while((f|0)>=4){pA[A>>2]=pA[e>>2];A=A+4|0;e=e+4|0;f=f-4|0}}while((f|0)>0){SA[A>>0]=SA[e>>0]|0;A=A+1|0;e=e+1|0;f=f-1|0}return i|0}function Qr(A,e,f){A=A|0;e=e|0;f=f|0;var i=0;if((e|0)<(A|0)&(A|0)<(e+f|0)){ZA()}else or(A,e,f)|0;return A|0}function gr(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,r=0,n=0,t=0;i=A+f|0;if((f|0)>=20){e=e&255;n=A&3;t=e|e<<8|e<<16|e<<24;r=i&~3;if(n){n=A+4-n|0;while((A|0)<(n|0)){SA[A>>0]=e;A=A+1|0}}while((A|0)<(r|0)){pA[A>>2]=t;A=A+4|0}}while((A|0)<(i|0)){SA[A>>0]=e;A=A+1|0}return A-f|0}return{_free:Br,___errno_location:ar,_memmove:Qr,_Decoder_Interface_Decode:sA,_Decoder_Interface_exit:aA,_Encoder_Interface_init:oA,_memset:gr,_malloc:lr,_memcpy:or,_Encoder_Interface_exit:QA,_Decoder_Interface_init:BA,_Encoder_Interface_Encode:gA}}(n.asmGlobalArg,n.asmLibraryArg,C);n._Encoder_Interface_Encode=U._Encoder_Interface_Encode,n._free=U._free;var G=n._memmove=U._memmove;n._Decoder_Interface_exit=U._Decoder_Interface_exit,n._Encoder_Interface_init=U._Encoder_Interface_init;var L=n._memset=U._memset;n._malloc=U._malloc;var R=n._memcpy=U._memcpy;return n._Decoder_Interface_Decode=U._Decoder_Interface_Decode,n._Decoder_Interface_init=U._Decoder_Interface_init,n._Encoder_Interface_exit=U._Encoder_Interface_exit,n.___errno_location=U.___errno_location,n._main(),u.Create=r,u}()}(("object"==typeof window&&window.document?window:Object).Recorder),function(A){var e="object"==typeof window&&!!window.document,f=(e?window:Object).Recorder,i=f.i18n;!function(Q,A,g,e){"use strict";Q.prototype.enc_wav={stable:!0,fast:!0,getTestMsg:function(){return g("gPSE::支持位数8位、16位（填在比特率里面），采样率取值无限制；此编码器仅在pcm数据前加了一个44字节的wav头，编码出来的16位wav文件去掉开头的44字节即可得到pcm（注：其他wav编码器可能不是44字节）")}};Q.prototype.wav=function(A,e,f){var i=this.set;!function(A){var e=A.bitRate,f=8==e?8:16;e!=f&&Q.CLog(g("wyw9::WAV Info: 不支持{1}位，已更新成{2}位",0,e,f),3);A.bitRate=f}(i);var r=A.length,n=i.sampleRate,t=i.bitRate,w=r*(t/8),l=Q.wav_header(1,1,n,t,w),B=l.length,a=new Uint8Array(B+w);if(a.set(l),8==t)for(var s=0;s<r;s++){var o=128+(A[s]>>8);a[B++]=o}else(a=new Int16Array(a.buffer)).set(A,B/2);e(a.buffer,"audio/wav")},Q.wav_header=function(A,e,f,i,r){var n=1==A?0:2,t=new ArrayBuffer(44+n),w=new DataView(t),l=0,B=function(A){for(var e=0;e<A.length;e++,l++)w.setUint8(l,A.charCodeAt(e))},a=function(A){w.setUint16(l,A,!0),l+=2},s=function(A){w.setUint32(l,A,!0),l+=4};return B("RIFF"),s(36+n+r),B("WAVE"),B("fmt "),s(16+n),a(A),a(e),s(f),s(f*(e*i/8)),a(e*i/8),a(i),1!=A&&a(0),B("data"),s(r),new Uint8Array(t)}}(f,0,i.$T)}();