"use strict";
/*RSA、AES、SHA1*/



//rsa 源码CryptoJS v3.1.2
"use strict";!function(){var i="0000000000000000";window.AES=function(t,e,r){return W.AES.encrypt(t||"",W.enc.Utf8.parse(((e||"")+i).substr(0,16)),{iv:W.enc.Utf8.parse(((r||"")+i).substr(0,16))})+""},AES.Decode=function(t,e,r){return W.AES.decrypt(t||"",W.enc.Utf8.parse(((e||"")+i).substr(0,16)),{iv:W.enc.Utf8.parse(((r||"")+i).substr(0,16))}).toString(W.enc.Utf8)},window.SHA1=function(t){return(W.SHA1(t)+"").toUpperCase()},window.SHA256=function(t){return(W.SHA256(t)+"").toUpperCase()};var h,t,e,r,u,n,s,o,c,a,f,p,l,d,_,v,y,g,B,S,m,k,x,w,z,A,C,H,E,b,M,D,O,R,F,U,P,I,X,W=W||(h=Math,e=(t={}).lib={},r=e.Base=function(){function r(){}return{extend:function(t){r.prototype=this;var e=new r;return t&&e.mixIn(t),e.hasOwnProperty("init")||(e.init=function(){e.$super.init.apply(this,arguments)}),(e.init.prototype=e).$super=this,e},create:function(){var t=this.extend();return t.init.apply(t,arguments),t},init:function(){},mixIn:function(t){for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);t.hasOwnProperty("toString")&&(this.toString=t.toString)},clone:function(){return this.init.prototype.extend(this)}}}(),u=e.WordArray=r.extend({init:function(t,e){t=this.words=t||[],this.sigBytes=null!=e?e:4*t.length},toString:function(t){return(t||s).stringify(this)},concat:function(t){var e=this.words,r=t.words,i=this.sigBytes,n=t.sigBytes;if(this.clamp(),i%4)for(var s=0;s<n;s++){var o=r[s>>>2]>>>24-s%4*8&255;e[i+s>>>2]|=o<<24-(i+s)%4*8}else if(65535<r.length)for(s=0;s<n;s+=4)e[i+s>>>2]=r[s>>>2];else e.push.apply(e,r);return this.sigBytes+=n,this},clamp:function(){var t=this.words,e=this.sigBytes;t[e>>>2]&=4294967295<<32-e%4*8,t.length=h.ceil(e/4)},clone:function(){var t=r.clone.call(this);return t.words=this.words.slice(0),t},random:function(t){for(var e=[],r=0;r<t;r+=4)e.push(4294967296*h.random()|0);return new u.init(e,t)}}),n=t.enc={},s=n.Hex={stringify:function(t){for(var e=t.words,r=t.sigBytes,i=[],n=0;n<r;n++){var s=e[n>>>2]>>>24-n%4*8&255;i.push((s>>>4).toString(16)),i.push((15&s).toString(16))}return i.join("")},parse:function(t){for(var e=t.length,r=[],i=0;i<e;i+=2)r[i>>>3]|=parseInt(t.substr(i,2),16)<<24-i%8*4;return new u.init(r,e/2)}},o=n.Latin1={stringify:function(t){for(var e=t.words,r=t.sigBytes,i=[],n=0;n<r;n++){var s=e[n>>>2]>>>24-n%4*8&255;i.push(String.fromCharCode(s))}return i.join("")},parse:function(t){for(var e=t.length,r=[],i=0;i<e;i++)r[i>>>2]|=(255&t.charCodeAt(i))<<24-i%4*8;return new u.init(r,e)}},c=n.Utf8={stringify:function(t){try{return decodeURIComponent(escape(o.stringify(t)))}catch(t){throw new Error("Malformed UTF-8 data")}},parse:function(t){return o.parse(unescape(encodeURIComponent(t)))}},a=e.BufferedBlockAlgorithm=r.extend({reset:function(){this._data=new u.init,this._nDataBytes=0},_append:function(t){"string"==typeof t&&(t=c.parse(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes},_process:function(t){var e=this._data,r=e.words,i=e.sigBytes,n=this.blockSize,s=i/(4*n),o=(s=t?h.ceil(s):h.max((0|s)-this._minBufferSize,0))*n,c=h.min(4*o,i);if(o){for(var a=0;a<o;a+=n)this._doProcessBlock(r,a);var f=r.splice(0,o);e.sigBytes-=c}return new u.init(f,c)},clone:function(){var t=r.clone.call(this);return t._data=this._data.clone(),t},_minBufferSize:0}),e.Hasher=a.extend({cfg:r.extend(),init:function(t){this.cfg=this.cfg.extend(t),this.reset()},reset:function(){a.reset.call(this),this._doReset()},update:function(t){return this._append(t),this._process(),this},finalize:function(t){return t&&this._append(t),this._doFinalize()},blockSize:16,_createHelper:function(r){return function(t,e){return new r.init(e).finalize(t)}},_createHmacHelper:function(r){return function(t,e){return new f.HMAC.init(r,e).finalize(t)}}}),f=t.algo={},t);l=(p=W).lib.WordArray,p.enc.Base64={stringify:function(t){var e=t.words,r=t.sigBytes,i=this._map;t.clamp();for(var n=[],s=0;s<r;s+=3)for(var o=(e[s>>>2]>>>24-s%4*8&255)<<16|(e[s+1>>>2]>>>24-(s+1)%4*8&255)<<8|e[s+2>>>2]>>>24-(s+2)%4*8&255,c=0;c<4&&s+.75*c<r;c++)n.push(i.charAt(o>>>6*(3-c)&63));var a=i.charAt(64);if(a)for(;n.length%4;)n.push(a);return n.join("")},parse:function(t){var e=t.length,r=this._map,i=r.charAt(64);if(i){var n=t.indexOf(i);-1!=n&&(e=n)}for(var s=[],o=0,c=0;c<e;c++)if(c%4){var a=r.indexOf(t.charAt(c-1))<<c%4*2,f=r.indexOf(t.charAt(c))>>>6-c%4*2;s[o>>>2]|=(a|f)<<24-o%4*8,o++}return l.create(s,o)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},W.lib.Cipher||(v=(_=W).lib,y=v.Base,g=v.WordArray,B=v.BufferedBlockAlgorithm,(S=_.enc).Utf8,m=S.Base64,k=_.algo.EvpKDF,x=v.Cipher=B.extend({cfg:y.extend(),createEncryptor:function(t,e){return this.create(this._ENC_XFORM_MODE,t,e)},createDecryptor:function(t,e){return this.create(this._DEC_XFORM_MODE,t,e)},init:function(t,e,r){this.cfg=this.cfg.extend(r),this._xformMode=t,this._key=e,this.reset()},reset:function(){B.reset.call(this),this._doReset()},process:function(t){return this._append(t),this._process()},finalize:function(t){return t&&this._append(t),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function n(t){return"string"==typeof t?D:b}return function(i){return{encrypt:function(t,e,r){return n(e).encrypt(i,t,e,r)},decrypt:function(t,e,r){return n(e).decrypt(i,t,e,r)}}}}()}),v.StreamCipher=x.extend({_doFinalize:function(){return this._process(!0)},blockSize:1}),w=_.mode={},z=v.BlockCipherMode=y.extend({createEncryptor:function(t,e){return this.Encryptor.create(t,e)},createDecryptor:function(t,e){return this.Decryptor.create(t,e)},init:function(t,e){this._cipher=t,this._iv=e}}),A=w.CBC=function(){var t=z.extend();function s(t,e,r){var i=this._iv;if(i){var n=i;this._iv=d}else n=this._prevBlock;for(var s=0;s<r;s++)t[e+s]^=n[s]}return t.Encryptor=t.extend({processBlock:function(t,e){var r=this._cipher,i=r.blockSize;s.call(this,t,e,i),r.encryptBlock(t,e),this._prevBlock=t.slice(e,e+i)}}),t.Decryptor=t.extend({processBlock:function(t,e){var r=this._cipher,i=r.blockSize,n=t.slice(e,e+i);r.decryptBlock(t,e),s.call(this,t,e,i),this._prevBlock=n}}),t}(),C=(_.pad={}).Pkcs7={pad:function(t,e){for(var r=4*e,i=r-t.sigBytes%r,n=i<<24|i<<16|i<<8|i,s=[],o=0;o<i;o+=4)s.push(n);var c=g.create(s,i);t.concat(c)},unpad:function(t){var e=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=e}},v.BlockCipher=x.extend({cfg:x.cfg.extend({mode:A,padding:C}),reset:function(){x.reset.call(this);var t=this.cfg,e=t.iv,r=t.mode;if(this._xformMode==this._ENC_XFORM_MODE)var i=r.createEncryptor;else{i=r.createDecryptor;this._minBufferSize=1}this._mode=i.call(r,this,e&&e.words)},_doProcessBlock:function(t,e){this._mode.processBlock(t,e)},_doFinalize:function(){var t=this.cfg.padding;if(this._xformMode==this._ENC_XFORM_MODE){t.pad(this._data,this.blockSize);var e=this._process(!0)}else{e=this._process(!0);t.unpad(e)}return e},blockSize:4}),H=v.CipherParams=y.extend({init:function(t){this.mixIn(t)},toString:function(t){return(t||this.formatter).stringify(this)}}),E=(_.format={}).OpenSSL={stringify:function(t){var e=t.ciphertext,r=t.salt;if(r)var i=g.create([1398893684,1701076831]).concat(r).concat(e);else i=e;return i.toString(m)},parse:function(t){var e=m.parse(t),r=e.words;if(1398893684==r[0]&&1701076831==r[1]){var i=g.create(r.slice(2,4));r.splice(0,4),e.sigBytes-=16}return H.create({ciphertext:e,salt:i})}},b=v.SerializableCipher=y.extend({cfg:y.extend({format:E}),encrypt:function(t,e,r,i){i=this.cfg.extend(i);var n=t.createEncryptor(r,i),s=n.finalize(e),o=n.cfg;return H.create({ciphertext:s,key:r,iv:o.iv,algorithm:t,mode:o.mode,padding:o.padding,blockSize:t.blockSize,formatter:i.format})},decrypt:function(t,e,r,i){return i=this.cfg.extend(i),e=this._parse(e,i.format),t.createDecryptor(r,i).finalize(e.ciphertext)},_parse:function(t,e){return"string"==typeof t?e.parse(t,this):t}}),M=(_.kdf={}).OpenSSL={execute:function(t,e,r,i){i||(i=g.random(8));var n=k.create({keySize:e+r}).compute(t,i),s=g.create(n.words.slice(e),4*r);return n.sigBytes=4*e,H.create({key:n,iv:s,salt:i})}},D=v.PasswordBasedCipher=b.extend({cfg:b.cfg.extend({kdf:M}),encrypt:function(t,e,r,i){var n=(i=this.cfg.extend(i)).kdf.execute(r,t.keySize,t.ivSize);i.iv=n.iv;var s=b.encrypt.call(this,t,e,n.key,i);return s.mixIn(n),s},decrypt:function(t,e,r,i){i=this.cfg.extend(i),e=this._parse(e,i.format);var n=i.kdf.execute(r,t.keySize,t.ivSize,e.salt);return i.iv=n.iv,b.decrypt.call(this,t,e,n.key,i)}})),function(){var t=W,e=t.lib.BlockCipher,r=t.algo,f=[],h=[],u=[],p=[],l=[],d=[],_=[],v=[],y=[],g=[];!function(){for(var t=[],e=0;e<256;e++)t[e]=e<128?e<<1:e<<1^283;var r=0,i=0;for(e=0;e<256;e++){var n=i^i<<1^i<<2^i<<3^i<<4;n=n>>>8^255&n^99,f[r]=n;var s=t[h[n]=r],o=t[s],c=t[o],a=257*t[n]^16843008*n;u[r]=a<<24|a>>>8,p[r]=a<<16|a>>>16,l[r]=a<<8|a>>>24,d[r]=a;a=16843009*c^65537*o^257*s^16843008*r;_[n]=a<<24|a>>>8,v[n]=a<<16|a>>>16,y[n]=a<<8|a>>>24,g[n]=a,r?(r=s^t[t[t[c^s]]],i^=t[t[i]]):r=i=1}}();var B=[0,1,2,4,8,16,32,64,128,27,54],i=r.AES=e.extend({_doReset:function(){for(var t=this._key,e=t.words,r=t.sigBytes/4,i=4*((this._nRounds=r+6)+1),n=this._keySchedule=[],s=0;s<i;s++)if(s<r)n[s]=e[s];else{var o=n[s-1];s%r?6<r&&s%r==4&&(o=f[o>>>24]<<24|f[o>>>16&255]<<16|f[o>>>8&255]<<8|f[255&o]):(o=f[(o=o<<8|o>>>24)>>>24]<<24|f[o>>>16&255]<<16|f[o>>>8&255]<<8|f[255&o],o^=B[s/r|0]<<24),n[s]=n[s-r]^o}for(var c=this._invKeySchedule=[],a=0;a<i;a++){s=i-a;if(a%4)o=n[s];else o=n[s-4];c[a]=a<4||s<=4?o:_[f[o>>>24]]^v[f[o>>>16&255]]^y[f[o>>>8&255]]^g[f[255&o]]}},encryptBlock:function(t,e){this._doCryptBlock(t,e,this._keySchedule,u,p,l,d,f)},decryptBlock:function(t,e){var r=t[e+1];t[e+1]=t[e+3],t[e+3]=r,this._doCryptBlock(t,e,this._invKeySchedule,_,v,y,g,h);r=t[e+1];t[e+1]=t[e+3],t[e+3]=r},_doCryptBlock:function(t,e,r,i,n,s,o,c){for(var a=this._nRounds,f=t[e]^r[0],h=t[e+1]^r[1],u=t[e+2]^r[2],p=t[e+3]^r[3],l=4,d=1;d<a;d++){var _=i[f>>>24]^n[h>>>16&255]^s[u>>>8&255]^o[255&p]^r[l++],v=i[h>>>24]^n[u>>>16&255]^s[p>>>8&255]^o[255&f]^r[l++],y=i[u>>>24]^n[p>>>16&255]^s[f>>>8&255]^o[255&h]^r[l++],g=i[p>>>24]^n[f>>>16&255]^s[h>>>8&255]^o[255&u]^r[l++];f=_,h=v,u=y,p=g}_=(c[f>>>24]<<24|c[h>>>16&255]<<16|c[u>>>8&255]<<8|c[255&p])^r[l++],v=(c[h>>>24]<<24|c[u>>>16&255]<<16|c[p>>>8&255]<<8|c[255&f])^r[l++],y=(c[u>>>24]<<24|c[p>>>16&255]<<16|c[f>>>8&255]<<8|c[255&h])^r[l++],g=(c[p>>>24]<<24|c[f>>>16&255]<<16|c[h>>>8&255]<<8|c[255&u])^r[l++];t[e]=_,t[e+1]=v,t[e+2]=y,t[e+3]=g},keySize:8});t.AES=e._createHelper(i)}(),R=(O=W).lib,F=R.WordArray,U=R.Hasher,P=O.algo,I=[],X=P.SHA1=U.extend({_doReset:function(){this._hash=new F.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(t,e){for(var r=this._hash.words,i=r[0],n=r[1],s=r[2],o=r[3],c=r[4],a=0;a<80;a++){if(a<16)I[a]=0|t[e+a];else{var f=I[a-3]^I[a-8]^I[a-14]^I[a-16];I[a]=f<<1|f>>>31}var h=(i<<5|i>>>27)+c+I[a];h+=a<20?1518500249+(n&s|~n&o):a<40?1859775393+(n^s^o):a<60?(n&s|n&o|s&o)-1894007588:(n^s^o)-899497514,c=o,o=s,s=n<<30|n>>>2,n=i,i=h}r[0]=r[0]+i|0,r[1]=r[1]+n|0,r[2]=r[2]+s|0,r[3]=r[3]+o|0,r[4]=r[4]+c|0},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,i=8*t.sigBytes;return e[i>>>5]|=128<<24-i%32,e[14+(i+64>>>9<<4)]=Math.floor(r/4294967296),e[15+(i+64>>>9<<4)]=r,t.sigBytes=4*e.length,this._process(),this._hash},clone:function(){var t=U.clone.call(this);return t._hash=this._hash.clone(),t}}),O.SHA1=U._createHelper(X),O.HmacSHA1=U._createHmacHelper(X),function(n){var t=W,e=t.lib,r=e.WordArray,i=e.Hasher,s=t.algo,o=[],B=[];!function(){function t(t){for(var e=n.sqrt(t),r=2;r<=e;r++)if(!(t%r))return!1;return!0}function e(t){return 4294967296*(t-(0|t))|0}for(var r=2,i=0;i<64;)t(r)&&(i<8&&(o[i]=e(n.pow(r,.5))),B[i]=e(n.pow(r,1/3)),i++),r++}();var S=[],c=s.SHA256=i.extend({_doReset:function(){this._hash=new r.init(o.slice(0))},_doProcessBlock:function(t,e){for(var r=this._hash.words,i=r[0],n=r[1],s=r[2],o=r[3],c=r[4],a=r[5],f=r[6],h=r[7],u=0;u<64;u++){if(u<16)S[u]=0|t[e+u];else{var p=S[u-15],l=(p<<25|p>>>7)^(p<<14|p>>>18)^p>>>3,d=S[u-2],_=(d<<15|d>>>17)^(d<<13|d>>>19)^d>>>10;S[u]=l+S[u-7]+_+S[u-16]}var v=i&n^i&s^n&s,y=(i<<30|i>>>2)^(i<<19|i>>>13)^(i<<10|i>>>22),g=h+((c<<26|c>>>6)^(c<<21|c>>>11)^(c<<7|c>>>25))+(c&a^~c&f)+B[u]+S[u];h=f,f=a,a=c,c=o+g|0,o=s,s=n,n=i,i=g+(y+v)|0}r[0]=r[0]+i|0,r[1]=r[1]+n|0,r[2]=r[2]+s|0,r[3]=r[3]+o|0,r[4]=r[4]+c|0,r[5]=r[5]+a|0,r[6]=r[6]+f|0,r[7]=r[7]+h|0},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,i=8*t.sigBytes;return e[i>>>5]|=128<<24-i%32,e[14+(i+64>>>9<<4)]=n.floor(r/4294967296),e[15+(i+64>>>9<<4)]=r,t.sigBytes=4*e.length,this._process(),this._hash},clone:function(){var t=i.clone.call(this);return t._hash=this._hash.clone(),t}});t.SHA256=i._createHelper(c),t.HmacSHA256=i._createHmacHelper(c)}(Math),AES.CryptoJS=W}();












//rsa 源码http://www-cs-students.stanford.edu/~tjw/jsbn/


"use strict";!function(){var t,e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",h="=";function c(t){var i,r,o="";for(t.length%2&&(t="0"+t),i=0;i+3<=t.length;i+=3)r=parseInt(t.substring(i,i+3),16),o+=e.charAt(r>>6)+e.charAt(63&r);for(i+1==t.length?(r=parseInt(t.substring(i,i+1),16),o+=e.charAt(r<<2)):i+2==t.length&&(r=parseInt(t.substring(i,i+2),16),o+=e.charAt(r>>2)+e.charAt((3&r)<<4));0<(3&o.length);)o+=h;return o}function a(t){var i,r,o,s="",n=0;for(i=0;i<t.length&&t.charAt(i)!=h;++i)(o=e.indexOf(t.charAt(i)))<0||(0==n?(s+=u(o>>2),r=3&o,n=1):1==n?(s+=u(r<<2|o>>4),r=15&o,n=2):2==n?(s+=u(r),s+=u(o>>2),r=3&o,n=3):(s+=u(r<<2|o>>4),s+=u(15&o),n=0));return 1==n&&(s+=u(r<<2)),s}function g(t,i,r){null!=t&&("number"==typeof t?this.fromNumber(t,i,r):null==i&&"string"!=typeof t?this.fromString(t,256):this.fromString(t,i))}function b(){return new g(null)}"Microsoft Internet Explorer"==navigator.appName?(g.prototype.am=function(t,i,r,o,s,n){for(var e=32767&i,h=i>>15;0<=--n;){var u=32767&this[t],f=this[t++]>>15,p=h*u+f*e;s=((u=e*u+((32767&p)<<15)+r[o]+(1073741823&s))>>>30)+(p>>>15)+h*f+(s>>>30),r[o++]=1073741823&u}return s},t=30):"Netscape"!=navigator.appName?(g.prototype.am=function(t,i,r,o,s,n){for(;0<=--n;){var e=i*this[t++]+r[o]+s;s=Math.floor(e/67108864),r[o++]=67108863&e}return s},t=26):(g.prototype.am=function(t,i,r,o,s,n){for(var e=16383&i,h=i>>14;0<=--n;){var u=16383&this[t],f=this[t++]>>14,p=h*u+f*e;s=((u=e*u+((16383&p)<<14)+r[o]+s)>>28)+(p>>14)+h*f,r[o++]=268435455&u}return s},t=28),g.prototype.DB=t,g.prototype.DM=(1<<t)-1,g.prototype.DV=1<<t;g.prototype.FV=Math.pow(2,52),g.prototype.F1=52-t,g.prototype.F2=2*t-52;var i,r,o="0123456789abcdefghijklmnopqrstuvwxyz",s=new Array;for(i="0".charCodeAt(0),r=0;r<=9;++r)s[i++]=r;for(i="a".charCodeAt(0),r=10;r<36;++r)s[i++]=r;for(i="A".charCodeAt(0),r=10;r<36;++r)s[i++]=r;function u(t){return o.charAt(t)}function f(t,i){var r=s[t.charCodeAt(i)];return null==r?-1:r}function y(t){var i=b();return i.fromInt(t),i}function D(t){var i,r=1;return 0!=(i=t>>>16)&&(t=i,r+=16),0!=(i=t>>8)&&(t=i,r+=8),0!=(i=t>>4)&&(t=i,r+=4),0!=(i=t>>2)&&(t=i,r+=2),0!=(i=t>>1)&&(t=i,r+=1),r}function T(t){this.m=t}function d(t){this.m=t,this.mp=t.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<t.DB-15)-1,this.mt2=2*t.t}function n(t,i){return t&i}function p(t,i){return t|i}function l(t,i){return t^i}function m(t,i){return t&~i}function v(t){if(0==t)return-1;var i=0;return 0==(65535&t)&&(t>>=16,i+=16),0==(255&t)&&(t>>=8,i+=8),0==(15&t)&&(t>>=4,i+=4),0==(3&t)&&(t>>=2,i+=2),0==(1&t)&&++i,i}function S(t){for(var i=0;0!=t;)t&=t-1,++i;return i}function w(){}function B(t){return t}function E(t){this.r2=b(),this.q3=b(),g.ONE.dlShiftTo(2*t.t,this.r2),this.mu=this.r2.divide(t),this.m=t}T.prototype.convert=function(t){return t.s<0||0<=t.compareTo(this.m)?t.mod(this.m):t},T.prototype.revert=function(t){return t},T.prototype.reduce=function(t){t.divRemTo(this.m,null,t)},T.prototype.mulTo=function(t,i,r){t.multiplyTo(i,r),this.reduce(r)},T.prototype.sqrTo=function(t,i){t.squareTo(i),this.reduce(i)},d.prototype.convert=function(t){var i=b();return t.abs().dlShiftTo(this.m.t,i),i.divRemTo(this.m,null,i),t.s<0&&0<i.compareTo(g.ZERO)&&this.m.subTo(i,i),i},d.prototype.revert=function(t){var i=b();return t.copyTo(i),this.reduce(i),i},d.prototype.reduce=function(t){for(;t.t<=this.mt2;)t[t.t++]=0;for(var i=0;i<this.m.t;++i){var r=32767&t[i],o=r*this.mpl+((r*this.mph+(t[i]>>15)*this.mpl&this.um)<<15)&t.DM;for(t[r=i+this.m.t]+=this.m.am(0,o,t,i,0,this.m.t);t[r]>=t.DV;)t[r]-=t.DV,t[++r]++}t.clamp(),t.drShiftTo(this.m.t,t),0<=t.compareTo(this.m)&&t.subTo(this.m,t)},d.prototype.mulTo=function(t,i,r){t.multiplyTo(i,r),this.reduce(r)},d.prototype.sqrTo=function(t,i){t.squareTo(i),this.reduce(i)},g.prototype.copyTo=function(t){for(var i=this.t-1;0<=i;--i)t[i]=this[i];t.t=this.t,t.s=this.s},g.prototype.fromInt=function(t){this.t=1,this.s=t<0?-1:0,0<t?this[0]=t:t<-1?this[0]=t+this.DV:this.t=0},g.prototype.fromString=function(t,i){var r;if(16==i)r=4;else if(8==i)r=3;else if(256==i)r=8;else if(2==i)r=1;else if(32==i)r=5;else{if(4!=i)return void this.fromRadix(t,i);r=2}this.t=0,this.s=0;for(var o=t.length,s=!1,n=0;0<=--o;){var e=8==r?255&t[o]:f(t,o);e<0?"-"==t.charAt(o)&&(s=!0):(s=!1,0==n?this[this.t++]=e:n+r>this.DB?(this[this.t-1]|=(e&(1<<this.DB-n)-1)<<n,this[this.t++]=e>>this.DB-n):this[this.t-1]|=e<<n,(n+=r)>=this.DB&&(n-=this.DB))}8==r&&0!=(128&t[0])&&(this.s=-1,0<n&&(this[this.t-1]|=(1<<this.DB-n)-1<<n)),this.clamp(),s&&g.ZERO.subTo(this,this)},g.prototype.clamp=function(){for(var t=this.s&this.DM;0<this.t&&this[this.t-1]==t;)--this.t},g.prototype.dlShiftTo=function(t,i){var r;for(r=this.t-1;0<=r;--r)i[r+t]=this[r];for(r=t-1;0<=r;--r)i[r]=0;i.t=this.t+t,i.s=this.s},g.prototype.drShiftTo=function(t,i){for(var r=t;r<this.t;++r)i[r-t]=this[r];i.t=Math.max(this.t-t,0),i.s=this.s},g.prototype.lShiftTo=function(t,i){var r,o=t%this.DB,s=this.DB-o,n=(1<<s)-1,e=Math.floor(t/this.DB),h=this.s<<o&this.DM;for(r=this.t-1;0<=r;--r)i[r+e+1]=this[r]>>s|h,h=(this[r]&n)<<o;for(r=e-1;0<=r;--r)i[r]=0;i[e]=h,i.t=this.t+e+1,i.s=this.s,i.clamp()},g.prototype.rShiftTo=function(t,i){i.s=this.s;var r=Math.floor(t/this.DB);if(r>=this.t)i.t=0;else{var o=t%this.DB,s=this.DB-o,n=(1<<o)-1;i[0]=this[r]>>o;for(var e=r+1;e<this.t;++e)i[e-r-1]|=(this[e]&n)<<s,i[e-r]=this[e]>>o;0<o&&(i[this.t-r-1]|=(this.s&n)<<s),i.t=this.t-r,i.clamp()}},g.prototype.subTo=function(t,i){for(var r=0,o=0,s=Math.min(t.t,this.t);r<s;)o+=this[r]-t[r],i[r++]=o&this.DM,o>>=this.DB;if(t.t<this.t){for(o-=t.s;r<this.t;)o+=this[r],i[r++]=o&this.DM,o>>=this.DB;o+=this.s}else{for(o+=this.s;r<t.t;)o-=t[r],i[r++]=o&this.DM,o>>=this.DB;o-=t.s}i.s=o<0?-1:0,o<-1?i[r++]=this.DV+o:0<o&&(i[r++]=o),i.t=r,i.clamp()},g.prototype.multiplyTo=function(t,i){var r=this.abs(),o=t.abs(),s=r.t;for(i.t=s+o.t;0<=--s;)i[s]=0;for(s=0;s<o.t;++s)i[s+r.t]=r.am(0,o[s],i,s,0,r.t);i.s=0,i.clamp(),this.s!=t.s&&g.ZERO.subTo(i,i)},g.prototype.squareTo=function(t){for(var i=this.abs(),r=t.t=2*i.t;0<=--r;)t[r]=0;for(r=0;r<i.t-1;++r){var o=i.am(r,i[r],t,2*r,0,1);(t[r+i.t]+=i.am(r+1,2*i[r],t,2*r+1,o,i.t-r-1))>=i.DV&&(t[r+i.t]-=i.DV,t[r+i.t+1]=1)}0<t.t&&(t[t.t-1]+=i.am(r,i[r],t,2*r,0,1)),t.s=0,t.clamp()},g.prototype.divRemTo=function(t,i,r){var o=t.abs();if(!(o.t<=0)){var s=this.abs();if(s.t<o.t)return null!=i&&i.fromInt(0),void(null!=r&&this.copyTo(r));null==r&&(r=b());var n=b(),e=this.s,h=t.s,u=this.DB-D(o[o.t-1]);0<u?(o.lShiftTo(u,n),s.lShiftTo(u,r)):(o.copyTo(n),s.copyTo(r));var f=n.t,p=n[f-1];if(0!=p){var a=p*(1<<this.F1)+(1<f?n[f-2]>>this.F2:0),c=this.FV/a,l=(1<<this.F1)/a,m=1<<this.F2,v=r.t,y=v-f,T=null==i?b():i;for(n.dlShiftTo(y,T),0<=r.compareTo(T)&&(r[r.t++]=1,r.subTo(T,r)),g.ONE.dlShiftTo(f,T),T.subTo(n,n);n.t<f;)n[n.t++]=0;for(;0<=--y;){var d=r[--v]==p?this.DM:Math.floor(r[v]*c+(r[v-1]+m)*l);if((r[v]+=n.am(0,d,r,y,0,f))<d)for(n.dlShiftTo(y,T),r.subTo(T,r);r[v]<--d;)r.subTo(T,r)}null!=i&&(r.drShiftTo(f,i),e!=h&&g.ZERO.subTo(i,i)),r.t=f,r.clamp(),0<u&&r.rShiftTo(u,r),e<0&&g.ZERO.subTo(r,r)}}},g.prototype.invDigit=function(){if(this.t<1)return 0;var t=this[0];if(0==(1&t))return 0;var i=3&t;return 0<(i=(i=(i=(i=i*(2-(15&t)*i)&15)*(2-(255&t)*i)&255)*(2-((65535&t)*i&65535))&65535)*(2-t*i%this.DV)%this.DV)?this.DV-i:-i},g.prototype.isEven=function(){return 0==(0<this.t?1&this[0]:this.s)},g.prototype.exp=function(t,i){if(4294967295<t||t<1)return g.ONE;var r=b(),o=b(),s=i.convert(this),n=D(t)-1;for(s.copyTo(r);0<=--n;)if(i.sqrTo(r,o),0<(t&1<<n))i.mulTo(o,s,r);else{var e=r;r=o,o=e}return i.revert(r)},g.prototype.toString=function(t){if(this.s<0)return"-"+this.negate().toString(t);var i;if(16==t)i=4;else if(8==t)i=3;else if(2==t)i=1;else if(32==t)i=5;else{if(4!=t)return this.toRadix(t);i=2}var r,o=(1<<i)-1,s=!1,n="",e=this.t,h=this.DB-e*this.DB%i;if(0<e--)for(h<this.DB&&0<(r=this[e]>>h)&&(s=!0,n=u(r));0<=e;)h<i?(r=(this[e]&(1<<h)-1)<<i-h,r|=this[--e]>>(h+=this.DB-i)):(r=this[e]>>(h-=i)&o,h<=0&&(h+=this.DB,--e)),0<r&&(s=!0),s&&(n+=u(r));return s?n:"0"},g.prototype.negate=function(){var t=b();return g.ZERO.subTo(this,t),t},g.prototype.abs=function(){return this.s<0?this.negate():this},g.prototype.compareTo=function(t){var i=this.s-t.s;if(0!=i)return i;var r=this.t;if(0!=(i=r-t.t))return this.s<0?-i:i;for(;0<=--r;)if(0!=(i=this[r]-t[r]))return i;return 0},g.prototype.bitLength=function(){return this.t<=0?0:this.DB*(this.t-1)+D(this[this.t-1]^this.s&this.DM)},g.prototype.mod=function(t){var i=b();return this.abs().divRemTo(t,null,i),this.s<0&&0<i.compareTo(g.ZERO)&&t.subTo(i,i),i},g.prototype.modPowInt=function(t,i){var r;return r=t<256||i.isEven()?new T(i):new d(i),this.exp(t,r)},g.ZERO=y(0),g.ONE=y(1),w.prototype.convert=B,w.prototype.revert=B,w.prototype.mulTo=function(t,i,r){t.multiplyTo(i,r)},w.prototype.sqrTo=function(t,i){t.squareTo(i)},E.prototype.convert=function(t){if(t.s<0||t.t>2*this.m.t)return t.mod(this.m);if(t.compareTo(this.m)<0)return t;var i=b();return t.copyTo(i),this.reduce(i),i},E.prototype.revert=function(t){return t},E.prototype.reduce=function(t){for(t.drShiftTo(this.m.t-1,this.r2),t.t>this.m.t+1&&(t.t=this.m.t+1,t.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);t.compareTo(this.r2)<0;)t.dAddOffset(1,this.m.t+1);for(t.subTo(this.r2,t);0<=t.compareTo(this.m);)t.subTo(this.m,t)},E.prototype.mulTo=function(t,i,r){t.multiplyTo(i,r),this.reduce(r)},E.prototype.sqrTo=function(t,i){t.squareTo(i),this.reduce(i)};var M=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],A=(1<<26)/M[M.length-1];function O(){this.i=0,this.j=0,this.S=new Array}g.prototype.chunkSize=function(t){return Math.floor(Math.LN2*this.DB/Math.log(t))},g.prototype.toRadix=function(t){if(null==t&&(t=10),0==this.signum()||t<2||36<t)return"0";var i=this.chunkSize(t),r=Math.pow(t,i),o=y(r),s=b(),n=b(),e="";for(this.divRemTo(o,s,n);0<s.signum();)e=(r+n.intValue()).toString(t).substr(1)+e,s.divRemTo(o,s,n);return n.intValue().toString(t)+e},g.prototype.fromRadix=function(t,i){this.fromInt(0),null==i&&(i=10);for(var r=this.chunkSize(i),o=Math.pow(i,r),s=!1,n=0,e=0,h=0;h<t.length;++h){var u=f(t,h);u<0?"-"==t.charAt(h)&&0==this.signum()&&(s=!0):(e=i*e+u,++n>=r&&(this.dMultiply(o),this.dAddOffset(e,0),e=n=0))}0<n&&(this.dMultiply(Math.pow(i,n)),this.dAddOffset(e,0)),s&&g.ZERO.subTo(this,this)},g.prototype.fromNumber=function(t,i,r){if("number"==typeof i)if(t<2)this.fromInt(1);else for(this.fromNumber(t,r),this.testBit(t-1)||this.bitwiseTo(g.ONE.shiftLeft(t-1),p,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(i);)this.dAddOffset(2,0),this.bitLength()>t&&this.subTo(g.ONE.shiftLeft(t-1),this);else{var o=new Array,s=7&t;o.length=1+(t>>3),i.nextBytes(o),0<s?o[0]&=(1<<s)-1:o[0]=0,this.fromString(o,256)}},g.prototype.bitwiseTo=function(t,i,r){var o,s,n=Math.min(t.t,this.t);for(o=0;o<n;++o)r[o]=i(this[o],t[o]);if(t.t<this.t){for(s=t.s&this.DM,o=n;o<this.t;++o)r[o]=i(this[o],s);r.t=this.t}else{for(s=this.s&this.DM,o=n;o<t.t;++o)r[o]=i(s,t[o]);r.t=t.t}r.s=i(this.s,t.s),r.clamp()},g.prototype.changeBit=function(t,i){var r=g.ONE.shiftLeft(t);return this.bitwiseTo(r,i,r),r},g.prototype.addTo=function(t,i){for(var r=0,o=0,s=Math.min(t.t,this.t);r<s;)o+=this[r]+t[r],i[r++]=o&this.DM,o>>=this.DB;if(t.t<this.t){for(o+=t.s;r<this.t;)o+=this[r],i[r++]=o&this.DM,o>>=this.DB;o+=this.s}else{for(o+=this.s;r<t.t;)o+=t[r],i[r++]=o&this.DM,o>>=this.DB;o+=t.s}i.s=o<0?-1:0,0<o?i[r++]=o:o<-1&&(i[r++]=this.DV+o),i.t=r,i.clamp()},g.prototype.dMultiply=function(t){this[this.t]=this.am(0,t-1,this,0,0,this.t),++this.t,this.clamp()},g.prototype.dAddOffset=function(t,i){if(0!=t){for(;this.t<=i;)this[this.t++]=0;for(this[i]+=t;this[i]>=this.DV;)this[i]-=this.DV,++i>=this.t&&(this[this.t++]=0),++this[i]}},g.prototype.multiplyLowerTo=function(t,i,r){var o,s=Math.min(this.t+t.t,i);for(r.s=0,r.t=s;0<s;)r[--s]=0;for(o=r.t-this.t;s<o;++s)r[s+this.t]=this.am(0,t[s],r,s,0,this.t);for(o=Math.min(t.t,i);s<o;++s)this.am(0,t[s],r,s,0,i-s);r.clamp()},g.prototype.multiplyUpperTo=function(t,i,r){--i;var o=r.t=this.t+t.t-i;for(r.s=0;0<=--o;)r[o]=0;for(o=Math.max(i-this.t,0);o<t.t;++o)r[this.t+o-i]=this.am(i-o,t[o],r,0,0,this.t+o-i);r.clamp(),r.drShiftTo(1,r)},g.prototype.modInt=function(t){if(t<=0)return 0;var i=this.DV%t,r=this.s<0?t-1:0;if(0<this.t)if(0==i)r=this[0]%t;else for(var o=this.t-1;0<=o;--o)r=(i*r+this[o])%t;return r},g.prototype.millerRabin=function(t){var i=this.subtract(g.ONE),r=i.getLowestSetBit();if(r<=0)return!1;var o=i.shiftRight(r);(t=t+1>>1)>M.length&&(t=M.length);for(var s=b(),n=0;n<t;++n){s.fromInt(M[Math.floor(Math.random()*M.length)]);var e=s.modPow(o,this);if(0!=e.compareTo(g.ONE)&&0!=e.compareTo(i)){for(var h=1;h++<r&&0!=e.compareTo(i);)if(0==(e=e.modPowInt(2,this)).compareTo(g.ONE))return!1;if(0!=e.compareTo(i))return!1}}return!0},g.prototype.clone=function(){var t=b();return this.copyTo(t),t},g.prototype.intValue=function(){if(this.s<0){if(1==this.t)return this[0]-this.DV;if(0==this.t)return-1}else{if(1==this.t)return this[0];if(0==this.t)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]},g.prototype.byteValue=function(){return 0==this.t?this.s:this[0]<<24>>24},g.prototype.shortValue=function(){return 0==this.t?this.s:this[0]<<16>>16},g.prototype.signum=function(){return this.s<0?-1:this.t<=0||1==this.t&&this[0]<=0?0:1},g.prototype.toByteArray=function(){var t=this.t,i=new Array;i[0]=this.s;var r,o=this.DB-t*this.DB%8,s=0;if(0<t--)for(o<this.DB&&(r=this[t]>>o)!=(this.s&this.DM)>>o&&(i[s++]=r|this.s<<this.DB-o);0<=t;)o<8?(r=(this[t]&(1<<o)-1)<<8-o,r|=this[--t]>>(o+=this.DB-8)):(r=this[t]>>(o-=8)&255,o<=0&&(o+=this.DB,--t)),0!=(128&r)&&(r|=-256),0==s&&(128&this.s)!=(128&r)&&++s,(0<s||r!=this.s)&&(i[s++]=r);return i},g.prototype.equals=function(t){return 0==this.compareTo(t)},g.prototype.min=function(t){return this.compareTo(t)<0?this:t},g.prototype.max=function(t){return 0<this.compareTo(t)?this:t},g.prototype.and=function(t){var i=b();return this.bitwiseTo(t,n,i),i},g.prototype.or=function(t){var i=b();return this.bitwiseTo(t,p,i),i},g.prototype.xor=function(t){var i=b();return this.bitwiseTo(t,l,i),i},g.prototype.andNot=function(t){var i=b();return this.bitwiseTo(t,m,i),i},g.prototype.not=function(){for(var t=b(),i=0;i<this.t;++i)t[i]=this.DM&~this[i];return t.t=this.t,t.s=~this.s,t},g.prototype.shiftLeft=function(t){var i=b();return t<0?this.rShiftTo(-t,i):this.lShiftTo(t,i),i},g.prototype.shiftRight=function(t){var i=b();return t<0?this.lShiftTo(-t,i):this.rShiftTo(t,i),i},g.prototype.getLowestSetBit=function(){for(var t=0;t<this.t;++t)if(0!=this[t])return t*this.DB+v(this[t]);return this.s<0?this.t*this.DB:-1},g.prototype.bitCount=function(){for(var t=0,i=this.s&this.DM,r=0;r<this.t;++r)t+=S(this[r]^i);return t},g.prototype.testBit=function(t){var i=Math.floor(t/this.DB);return i>=this.t?0!=this.s:0!=(this[i]&1<<t%this.DB)},g.prototype.setBit=function(t){return this.changeBit(t,p)},g.prototype.clearBit=function(t){return this.changeBit(t,m)},g.prototype.flipBit=function(t){return this.changeBit(t,l)},g.prototype.add=function(t){var i=b();return this.addTo(t,i),i},g.prototype.subtract=function(t){var i=b();return this.subTo(t,i),i},g.prototype.multiply=function(t){var i=b();return this.multiplyTo(t,i),i},g.prototype.divide=function(t){var i=b();return this.divRemTo(t,i,null),i},g.prototype.remainder=function(t){var i=b();return this.divRemTo(t,null,i),i},g.prototype.divideAndRemainder=function(t){var i=b(),r=b();return this.divRemTo(t,i,r),new Array(i,r)},g.prototype.modPow=function(t,i){var r,o,s=t.bitLength(),n=y(1);if(s<=0)return n;r=s<18?1:s<48?3:s<144?4:s<768?5:6,o=s<8?new T(i):i.isEven()?new E(i):new d(i);var e=new Array,h=3,u=r-1,f=(1<<r)-1;if(e[1]=o.convert(this),1<r){var p=b();for(o.sqrTo(e[1],p);h<=f;)e[h]=b(),o.mulTo(p,e[h-2],e[h]),h+=2}var a,c,l=t.t-1,m=!0,v=b();for(s=D(t[l])-1;0<=l;){for(u<=s?a=t[l]>>s-u&f:(a=(t[l]&(1<<s+1)-1)<<u-s,0<l&&(a|=t[l-1]>>this.DB+s-u)),h=r;0==(1&a);)a>>=1,--h;if((s-=h)<0&&(s+=this.DB,--l),m)e[a].copyTo(n),m=!1;else{for(;1<h;)o.sqrTo(n,v),o.sqrTo(v,n),h-=2;0<h?o.sqrTo(n,v):(c=n,n=v,v=c),o.mulTo(v,e[a],n)}for(;0<=l&&0==(t[l]&1<<s);)o.sqrTo(n,v),c=n,n=v,v=c,--s<0&&(s=this.DB-1,--l)}return o.revert(n)},g.prototype.modInverse=function(t){var i=t.isEven();if(this.isEven()&&i||0==t.signum())return g.ZERO;for(var r=t.clone(),o=this.clone(),s=y(1),n=y(0),e=y(0),h=y(1);0!=r.signum();){for(;r.isEven();)r.rShiftTo(1,r),i?(s.isEven()&&n.isEven()||(s.addTo(this,s),n.subTo(t,n)),s.rShiftTo(1,s)):n.isEven()||n.subTo(t,n),n.rShiftTo(1,n);for(;o.isEven();)o.rShiftTo(1,o),i?(e.isEven()&&h.isEven()||(e.addTo(this,e),h.subTo(t,h)),e.rShiftTo(1,e)):h.isEven()||h.subTo(t,h),h.rShiftTo(1,h);0<=r.compareTo(o)?(r.subTo(o,r),i&&s.subTo(e,s),n.subTo(h,n)):(o.subTo(r,o),i&&e.subTo(s,e),h.subTo(n,h))}return 0!=o.compareTo(g.ONE)?g.ZERO:0<=h.compareTo(t)?h.subtract(t):h.signum()<0?(h.addTo(t,h),h.signum()<0?h.add(t):h):h},g.prototype.pow=function(t){return this.exp(t,new w)},g.prototype.gcd=function(t){var i=this.s<0?this.negate():this.clone(),r=t.s<0?t.negate():t.clone();if(i.compareTo(r)<0){var o=i;i=r,r=o}var s=i.getLowestSetBit(),n=r.getLowestSetBit();if(n<0)return i;for(s<n&&(n=s),0<n&&(i.rShiftTo(n,i),r.rShiftTo(n,r));0<i.signum();)0<(s=i.getLowestSetBit())&&i.rShiftTo(s,i),0<(s=r.getLowestSetBit())&&r.rShiftTo(s,r),0<=i.compareTo(r)?(i.subTo(r,i),i.rShiftTo(1,i)):(r.subTo(i,r),r.rShiftTo(1,r));return 0<n&&r.lShiftTo(n,r),r},g.prototype.isProbablePrime=function(t){var i,r=this.abs();if(1==r.t&&r[0]<=M[M.length-1]){for(i=0;i<M.length;++i)if(r[0]==M[i])return!0;return!1}if(r.isEven())return!1;for(i=1;i<M.length;){for(var o=M[i],s=i+1;s<M.length&&o<A;)o*=M[s++];for(o=r.modInt(o);i<s;)if(o%M[i++]==0)return!1}return r.millerRabin(t)},g.prototype.square=function(){var t=b();return this.squareTo(t),t},O.prototype.init=function(t){var i,r,o;for(i=0;i<256;++i)this.S[i]=i;for(i=r=0;i<256;++i)r=r+this.S[i]+t[i%t.length]&255,o=this.S[i],this.S[i]=this.S[r],this.S[r]=o;this.i=0,this.j=0},O.prototype.next=function(){var t;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,t=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=t,this.S[t+this.S[this.i]&255]};var q,R,I,N=256;function L(){var t;t=(new Date).getTime(),R[I++]^=255&t,R[I++]^=t>>8&255,R[I++]^=t>>16&255,R[I++]^=t>>24&255,N<=I&&(I-=N)}function V(){if(null==q){for(L(),(q=new O).init(R),I=0;I<R.length;++I)R[I]=0;I=0}return q.next()}function x(){}function P(t,i){return new g(t,i)}function C(t){for(var i=t.toByteArray(),r=t.toString(16),o=i.length-r.length/2;1<=o--;)r="0"+r;return c(r)}function Z(t){for(var i,r,o,s,n,e,h,u=this.n.bitLength()/8,f=u-11,p=[],a=t.length;0<a;){for(i=new Array,r=0,o=u;0<a&&0<o;){if((s=t.charCodeAt(a-1))<128){if(f<(r+=1))break;i[--o]=s}else if(127<s&&s<2048){if(f<(r+=2))break;i[--o]=63&s|128,i[--o]=s>>6|192}else{if(f<(r+=3))break;i[--o]=63&s|128,i[--o]=s>>6&63|128,i[--o]=s>>12|224}a--}for(i[--o]=0,n=new x,e=new Array;2<o;){for(e[0]=0;0==e[0];)n.nextBytes(e);i[--o]=e[0]}if(i[--o]=2,i[--o]=0,null==(s=new g(i).modPowInt(this.e,this.n)))return null;0==(1&(h=s.toString(16)).length)?p.push(h):p.push("0"+h)}return p.reverse(),c(p.join(""))}function j(t){t=a(t);var i,r,o,s,n,e,h=this.n.bitLength()/8,u=[],f=t.length/2/h,p=0;if(0<t.length/2%h)return null;for(;0<f;){if(i=h,null==(r=P(t.substr(p,2*h),16).modPow(this.d,this.n)))return null;for(o=r.toByteArray(),s=0;s<o.length&&0==o[s];)++s;if(o.length-s!=i-1||2!=o[s])return null;for(++s;0!=o[s];)if(++s>=o.length)return null;for(n="";++s<o.length;)(e=255&o[s])<128?n+=String.fromCharCode(e):191<e&&e<224?(n+=String.fromCharCode((31&e)<<6|63&o[s+1]),++s):(n+=String.fromCharCode((15&e)<<12|(63&o[s+1])<<6|63&o[s+2]),s+=2);u.push(n),p+=2*h,f--}return u.join("")}function k(t,i){this.getKeyLoop?this.getKeyLoop++:this.getKeyLoop=1,(t<512||t%8)&&(t=2048);var r=new x,o=t>>1;this.e=P(i?a(i):"10001",16);for(var s=new g(i?a(i):"10001",16);;){for(;this.p=new g(t-o,1,r),0!=this.p.subtract(g.ONE).gcd(s).compareTo(g.ONE)||!this.p.isProbablePrime(10););for(;this.q=new g(o,1,r),0!=this.q.subtract(g.ONE).gcd(s).compareTo(g.ONE)||!this.q.isProbablePrime(10););if(this.p.compareTo(this.q)<=0){var n=this.p;this.p=this.q,this.q=n}var e=this.p.subtract(g.ONE),h=this.q.subtract(g.ONE),u=e.multiply(h);if(0==u.gcd(s).compareTo(g.ONE)){this.n=this.p.multiply(this.q),this.d=s.modInverse(u),this.dmp1=this.d.mod(e),this.dmq1=this.d.mod(h),this.coeff=this.q.modInverse(this.p);break}}if(this.modulusInt=this.n,this.publicInt=this.e,this.privateInt=this.d,this.n.bitLength()%8){if(this.getKeyLoop<3)return this.getKey(t,i);throw"生成密钥失败！"}return this.getKeyLoop=0,this}!function(){var t;if(R=new Array,I=0,window.crypto&&window.crypto.getRandomValues){var i=new Uint8Array(32);for(window.crypto.getRandomValues(i),t=0;t<32;++t)R[I++]=i[t]}if("Netscape"==navigator.appName&&navigator.appVersion<"5"&&window.crypto){var r=window.crypto.random(32);for(t=0;t<r.length;++t)R[I++]=255&r.charCodeAt(t)}for(;I<N;)t=Math.floor(65536*Math.random()),R[I++]=t>>>8,R[I++]=255&t;I=0,L()}(),x.prototype.nextBytes=function(t){var i;for(i=0;i<t.length;++i)t[i]=V()};var F={SHA1:"3021300906052b0e03021a05000414",SHA256:"3031300d060960864801650304020105000420"};function K(t,i){i=i||"SHA1";for(var r=this.n.bitLength()/4,o=window[i](t),s="00"+F[i]+o,n="",e=r-"0001".length-s.length,h=0;h<e;h+=2)n+="ff";return c(P("0001"+n+s,16).modPow(this.d,this.n).toString(16))}function H(t,i,r){return r=r||"SHA1",P(a(i),16).modPowInt(this.e,this.n).toString(16).replace(/^1f+00/,"").substring(F[r].length).toUpperCase()==window[r](t).toUpperCase()}var z,U=((z=function(t,i,r){this.n=null,this.e=0,this.d=null,this.p=null,this.q=null,this.dmp1=null,this.dmq1=null,this.coeff=null,"number"==typeof t?this.getKey(t,i):(t&&(this.n=P(a(t),16)),i&&(this.e=P(a(i),16)),r&&(this.d=P(a(r),16))),this.modulusInt=this.n,this.publicInt=this.e,this.privateInt=this.d}).prototype={encrypt:Z,decrypt:j,sign:K,verify:H,size:function(){return this.n.bitLength()},getKey:k,getPublic:function(){return C(this.publicInt)},getPrivate:function(){return C(this.privateInt)},getModulus:function(){return C(this.modulusInt)}},function(t,i,r){try{return new z(t,i,r)}catch(i){return null}});(window.RSA=U).HexToB64=c,U.B64ToHex=a}();