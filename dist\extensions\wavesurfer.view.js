/*
录音
https://github.com/xiangyuecn/Recorder
src: extensions/wavesurfer.view.js
*/
!function(e){var t="object"==typeof window&&!!window.document,a=(t?window:Object).Recorder,r=a.i18n;!function(e,t,v,d){"use strict";var a=function(e){return new r(e)},m="WaveSurferView",r=function(e){var t=this,a={scale:2,fps:50,duration:2500,direction:1,position:0,centerHeight:1,linear:[0,"rgba(0,187,17,1)",.7,"rgba(255,215,0,1)",1,"rgba(255,102,0,1)"],centerColor:""};for(var r in e)a[r]=e[r];t.set=e=a;var n="compatibleCanvas";if(e[n])var i=t.canvas=e[n],o=t.canvas2=e[n+"_2x"];else{if(!d)throw new Error(v.G("NonBrowser-1",[m]));var l=e.elem;l&&("string"==typeof l?l=document.querySelector(l):l.length&&(l=l[0])),l&&(e.width=l.offsetWidth,e.height=l.offsetHeight);var c=t.elem=document.createElement("div");c.style.fontSize=0,c.innerHTML='<canvas style="width:100%;height:100%;"/>';var i=t.canvas=c.querySelector("canvas"),o=t.canvas2=document.createElement("canvas");l&&(l.innerHTML="",l.appendChild(c))}var s=e.scale,h=e.width*s,f=e.height*s;if(!h||!f)throw new Error(v.G("IllegalArgs-1",[m+" width=0 height=0"]));i.width=h,i.height=f;t.ctx=i.getContext("2d");o.width=2*h,o.height=f;t.ctx2=o.getContext("2d");t.x=0};r.prototype=a.prototype={genLinear:function(e,t,a,r){for(var n=e.createLinearGradient(0,a,0,r),i=0;i<t.length;)n.addColorStop(t[i++],t[i++]);return n},input:function(e,t,a){var r=this;r.sampleRate=a,r.pcmData=e,r.pcmPos=0,r.inputTime=Date.now(),r.schedule()},schedule:function(){var e=this,t=e.set,a=Math.floor(1e3/t.fps);e.timer||(e.timer=setInterval(function(){e.schedule()},a));var r=Date.now(),n=e.drawTime||0;if(!(r-n<a)){e.drawTime=r;for(var i=e.sampleRate/t.fps,o=e.pcmData,l=e.pcmPos,c=new Int16Array(Math.min(i,o.length-l)),s=0;s<c.length;s++,l++)c[s]=o[l];e.pcmPos=l,c.length?e.draw(c,e.sampleRate):1300<r-e.inputTime&&(clearInterval(e.timer),e.timer=0)}},draw:function(e,t){var a=this,r=a.set,n=a.ctx2,i=r.scale,o=r.width*i,l=2*o,c=r.height*i,s=1*i,h=r.position,f=Math.abs(r.position),v=1==h?0:c,d=c;f<1&&(v=d/=2,d=Math.floor(d*(1+f)),v=Math.floor(0<h?v*(1-f):v*(1+f)));var m=1e3*e.length/t,w=m*o/r.duration;w+=a.drawLoss||0;var g=0;w<s?a.drawLoss=w:(a.drawLoss=0,g=Math.floor(w/s));for(var p=a.genLinear(n,r.linear,v,v-d),u=a.genLinear(n,r.linear,v,v+d),M=a.x,x=e.length/g,y=0,R=0;y<g;y++){var L=Math.floor(R),b=Math.floor(R+x);R+=x;for(var S=0;L<b;L++)S=Math.max(S,Math.abs(e[L]));var C=d*Math.min(1,S/32767);0!=v&&(n.fillStyle=p,n.fillRect(M,v-C,s,C)),v!=c&&(n.fillStyle=u,n.fillRect(M,v,s,C)),l<=(M+=s)&&(n.clearRect(0,0,o,c),n.drawImage(a.canvas2,o,0,o,c,0,0,o,c),n.clearRect(o,0,o,c),M=o)}a.x=M,(n=a.ctx).clearRect(0,0,o,c);var I=r.centerHeight*i;if(I){var T=v-Math.floor(I/2);T=Math.max(T,0),T=Math.min(T,c-I),n.fillStyle=r.centerColor||r.linear[1],n.fillRect(0,T,o,I)}var H=0,D=M,E=0;o<D?(H=D-o,D=o):E=o-D;var G=r.direction;-1==G?n.drawImage(a.canvas2,H,0,D,c,E,0,D,c):(n.save(),n.scale(-1,1),n.drawImage(a.canvas2,H,0,D,c,-o+E,0,D,c),n.restore())}},e[m]=a}(a,0,r.$T,t)}();