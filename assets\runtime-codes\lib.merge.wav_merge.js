/******************
《【Demo库】【文件合并】-wav多个片段文件合并》
作者：高坚果
时间：2019-11-3 23:36:18

文档：
Recorder.WavMerge(fileBytesList,True,False)
		fileBytesList：[Uint8Array,...] 所有wav文件列表，每项为一个文件Uint8Array二进制数组；仅支持raw pcm、单|双声道、8|16位格式wav，并且列表内的所有wav的位数和采样率和声道数必须一致
		True: fn(fileBytes,duration,info) 合并成功回调
				fileBytes：Uint8Array 为wav二进制文件
				duration：合并后的时长
				info：{
					sampleRate:123 //采样率
					,bitRate:8 16 //位数
					,numChannels:1 2 //声道数
				}
		False: fn(errMsg) 出错回调
	此函数可移植到后端使用
	
测试Tips：可先运行实时转码的demo代码，然后再运行本合并代码，免得人工不好控制片段大小

wav片段文件合并原理：raw编码的wav格式是直接在pcm数据前面加了一个wav头，本库编码的wav头是固定的44字节，其他编码器生成的wav头可能存在其他的数据块超过44字节。因此只需要将所有片段去掉wav头后，通过简单的二进制拼接就能得到完整的长pcm数据，最后在加上一个简单的44字节wav头就能得到完整的wav音频文件。
******************/

//=====wav文件合并核心函数==========
Recorder.WavMerge=function(fileBytesList,True,False){
	//计算所有文件的长度、校验wav头
	var size=0,baseInfo,wavHead44,dataIdxs=[];
	for(var i=0;i<fileBytesList.length;i++){
		var file=fileBytesList[i];
		var info=readWavInfo(file);
		if(!info){
			False&&False("第"+(i+1)+"个文件不是单或双声道wav raw pcm格式音频，无法合并");
			return;
		};
		dataIdxs.push(info.dataPos);
		wavHead44||(wavHead44=info.wavHead44);
		baseInfo||(baseInfo=info);
		if(baseInfo.sampleRate!=info.sampleRate || baseInfo.bitRate!=info.bitRate || baseInfo.numChannels!=info.numChannels){
			False&&False("第"+(i+1)+"个文件位数或采样率或声道数不一致");
			return;
		};
		
		size+=file.byteLength-info.dataPos;
	};
	if(size>50*1024*1024){
		False&&False("文件大小超过限制");
		return;
	};
	
	//去掉wav头后全部拼接到一起
	var fileBytes=new Uint8Array(44+size);
	var pos=44;
	for(var i=0;i<fileBytesList.length;i++){
		var pcm=new Uint8Array(fileBytesList[i].buffer.slice(dataIdxs[i]));
		fileBytes.set(pcm,pos);
		pos+=pcm.byteLength;
	};
	
	//添加新的wav头，直接修改第一个的头就ok了
	write32(wavHead44,4,36+size);
	write32(wavHead44,40,size);
	fileBytes.set(wavHead44,0);
	
	//计算合并后的总时长
	var duration=Math.round(size/info.sampleRate*1000/(info.bitRate==16?2:1));
	
	True(fileBytes,duration,baseInfo);
};
var write32=function(bytes,pos,int32){
	bytes[pos]=(int32)&0xff;
	bytes[pos+1]=(int32>>8)&0xff;
	bytes[pos+2]=(int32>>16)&0xff;
	bytes[pos+3]=(int32>>24)&0xff;
};
var readWavInfo=function(bytes){
	//读取wav文件头，统一成44字节的头
	if(bytes.byteLength<44){
		return null;
	};
	var wavView=bytes;
	var eq=function(p,s){
		for(var i=0;i<s.length;i++){
			if(wavView[p+i]!=s.charCodeAt(i)){
				return false;
			};
		};
		return true;
	};
	if(eq(0,"RIFF")&&eq(8,"WAVEfmt ")){
		var numCh=wavView[22];
		if(wavView[20]==1 && (numCh==1||numCh==2)){//raw pcm 单或双声道
			var sampleRate=wavView[24]+(wavView[25]<<8)+(wavView[26]<<16)+(wavView[27]<<24);
			var bitRate=wavView[34]+(wavView[35]<<8);
			var heads=[wavView.subarray(0,12)],headSize=12;//head只保留必要的块
			//搜索data块的位置
			var dataPos=0; // 44 或有更多块
			for(var i=12,iL=wavView.length-8;i<iL;){
				if(wavView[i]==100&&wavView[i+1]==97&&wavView[i+2]==116&&wavView[i+3]==97){//eq(i,"data")
					heads.push(wavView.subarray(i,i+8));
					headSize+=8;
					dataPos=i+8;break;
				}
				var i0=i;
				i+=4;
				i+=4+wavView[i]+(wavView[i+1]<<8)+(wavView[i+2]<<16)+(wavView[i+3]<<24);
				if(i0==12){//fmt 
					heads.push(wavView.subarray(i0,i));
					headSize+=i-i0;
				}
			}
			if(dataPos){
				var wavHead=new Uint8Array(headSize);
				for(var i=0,n=0;i<heads.length;i++){
					wavHead.set(heads[i],n);n+=heads[i].length;
				}
				return {
					sampleRate:sampleRate
					,bitRate:bitRate
					,numChannels:numCh
					,wavHead44:wavHead
					,dataPos:dataPos
				};
			};
		};
	};
	return null;
};
//=====END=========================




//合并测试
var test=function(){
	var audios=Runtime.LogAudios;
	
	var idx=-1 +1,files=[],exclude=0;
	var read=function(){
		idx++;
		if(idx>=audios.length){
			if(!files.length){
				Runtime.Log("至少需要录1段wav"+(exclude?"，已排除"+exclude+"个非wav文件":""),1);
				return;
			};
			Recorder.WavMerge(files,function(file,duration,info){
				Runtime.Log("合并"+files.length+"个成功"+(exclude?"，排除"+exclude+"个非wav文件":""),2);
				console.log(info);
				info.type="wav";
				Runtime.LogAudio(new Blob([file.buffer],{type:"audio/wav"}),duration,{set:info});
			},function(msg){
				Runtime.Log(msg+"，请清除日志后重试",1);
			});
			return;
		};
		if(!/wav/.test(audios[idx].blob.type)){
			exclude++;
			read();
			return;
		};
		var reader=new FileReader();
		reader.onloadend=function(){
			files.push(new Uint8Array(reader.result));
			read();
		};
		reader.readAsArrayBuffer(audios[idx].blob);
	};
	read();
};






//=====以下代码无关紧要，音频数据源，采集原始音频用的==================
//加载录音框架
Runtime.Import([
	{url:RootFolder+"/src/recorder-core.js",check:function(){return !window.Recorder}}
	,{url:RootFolder+"/src/engine/wav.js",check:function(){return !Recorder.prototype.wav}}
]);

//显示控制按钮
Runtime.Ctrls([
	{name:"16位wav录音",click:"recStart16"}
	,{name:"8位wav录音",click:"recStart8"}
	,{name:"结束录音",click:"recStop"}
	,{name:"合并日志中所有wav",click:"test"}
]);


//调用录音
var rec;
function recStart16(){
	recStart(16);
};
function recStart8(){
	recStart(8);
};
function recStart(bitRate){
	rec=Recorder({
		type:"wav"
		,sampleRate:16000
		,bitRate:bitRate
		,onProcess:function(buffers,powerLevel,bufferDuration,bufferSampleRate){
			Runtime.Process.apply(null,arguments);
		}
	});
	var t=setTimeout(function(){
		Runtime.Log("无法录音：权限请求被忽略（超时假装手动点击了确认对话框）",1);
	},8000);
	
	rec.open(function(){//打开麦克风授权获得相关资源
		clearTimeout(t);
		rec.start();//开始录音
	},function(msg,isUserNotAllow){//用户拒绝未授权或不支持
		clearTimeout(t);
		Runtime.Log((isUserNotAllow?"UserNotAllow，":"")+"无法录音:"+msg, 1);
	});
};
function recStop(){
	rec.stop(function(blob,duration){
		rec.close();//释放录音资源
		
		Runtime.LogAudio(blob,duration,rec);
	},function(msg){
		Runtime.Log("录音失败:"+msg, 1);
	});
};