/*
录音
https://github.com/xiangyuecn/Recorder
src: extensions/asr.aliyun.short.js
*/
!function(e){var t="object"==typeof window&&!!window.document,n=(t?window:Object).Recorder,s=n.i18n;!function(u,e,t,i){"use strict";var n=function(e){return new o(e)},s="ASR_Aliyun_Short",o=function(e){var t=this,n={tokenApi:"",apiArgs:{action:"token",lang:"普通话"},apiRequest:null,compatibleWebSocket:null,log:a,fileSpeed:6};for(var s in e)n[s]=e[s];t.set=e=n,t.state=0,t.started=0,t.sampleRate=16e3,t.pcmBuffers=[],t.pcmTotal=0,t.pcmOffset=0,t.pcmSend=0,t.joinBuffers=[],t.joinSize=0,t.joinSend=0,t.joinOffset=-1,t.joinIsOpen=0,t.joinSendTotal=0,t.sendCurSize=0,t.sendTotal=0,t.resTxts=[],e.asrProcess||t.log("未绑定asrProcess回调无法感知到abort事件",3)},w=function(){var e=arguments;e[0]="["+s+"]"+e[0],u.CLog.apply(null,e)};function a(){}o.prototype=n.prototype={log:function(e,t){w(e,"number"==typeof t?t:0),this.set.log("["+s+"]"+e,3==t?"#f60":t)},inputDuration:function(){return Math.round(this.pcmTotal/this.sampleRate*1e3)},sendDuration:function(e){var t=this.sendTotal;return t+=e||0,Math.round(t/this.sampleRate*1e3)},asrDuration:function(){return this.sendDuration(-this.joinSendTotal)},audioToText:function(t,i,r){var f=this,n=function(e){f.log(e,1),r&&r(e)};if(u.GetContext()){var s=new FileReader;s.onloadend=function(){var e=u.Ctx;e.decodeAudioData(s.result,function(e){for(var t=e.getChannelData(0),n=e.sampleRate,s=new Int16Array(t.length),o=0;o<t.length;o++){var a=Math.max(-1,Math.min(1,t[o]));a=a<0?32768*a:32767*a,s[o]=a}f.pcmToText(s,n,i,r)},function(e){n("音频解码失败["+t.type+"]:"+e.message)})},s.readAsArrayBuffer(t)}else n("浏览器不支持音频解码")},pcmToText:function(e,t,n,s){var o=this;o.start(function(){o.log("单个文件"+Math.round(e.length/t*1e3)+"ms转文字"),o.sendSpeed=o.set.fileSpeed,o.input([e],t),o.stop(n,s)},s)},start:function(e,t){var n=this,s=n.set,o=function(e){n.sendAbortMsg=e,t&&t(e)};if(s.compatibleWebSocket||i)if(0==n.state){n.state=1;var a=function(){n.log("ASR start被stop中断",1),n._send()};n._token(function(){1!=n.state?a():(n.log("OK start",2),n.started=1,e&&e(),n._send())},function(e){e="语音识别token接口出错："+e,n.log(e,1),1!=n.state?a():(o(e),n._send())})}else o("ASR对象不可重复start");else o("非浏览器环境，请提供compatibleWebSocket配置来返回一个兼容的WebSocket")},stop:function(t,n){t=t||a,n=n||a;var e,s=this;if(2==s.state)return e="语音识别stop出错："+(e="ASR对象不可重复stop"),s.log(e,1),void n(e);s.state=2,s.stopWait=function(){if(s.stopWait=null,s.started){var e=s.getText();!e&&s.sendAbortMsg?n(s.sendAbortMsg):t(e,s.sendAbortMsg||"")}else n(s.sendAbortMsg||"未开始语音识别")},s._send()},input:function(e,t,n){var s=this;if(2!=s.state){var o="input输入的采样率低于"+s.sampleRate;if(t<s.sampleRate)return w(o+"，数据已丢弃",3),s.pcmTotal||(s.sendAbortMsg=o),void s._send();if(s.sendAbortMsg==o&&(s.sendAbortMsg=""),n){for(var a=[],i=n;i<e.length;i++)a.push(e[i]);e=a}var r=u.SampleData(e,t,s.sampleRate).data;s.pcmTotal+=r.length,s.pcmBuffers.push(r),s._send()}else s._send()},_send:function(){var s=this,o=s.set;if(!s.sendWait){var e=function(){s.stopWait&&s.stopWait()};if(2!=s.state||s.started&&s.stopWait)if(s.sendAbort)e();else{var t=function(e){s.sendAbort||(s.sendAbort=1,s.sendAbortMsg=e||"-",n(0,1)),s._send()},n=function(e,t){if(!t&&s.sendAbort)return!1;if(e=e||0,!o.asrProcess)return s.sendTotal+e<=i;var n=o.asrProcess(s.getText(),s.sendDuration(e),s.sendAbort?s.sendAbortMsg:"");return s._prsw||"boolean"==typeof n||w("asrProcess返回值必须是boolean类型，true才能继续识别，否则立即超时",1),s._prsw=1,n},a=5*s.sampleRate,i=60*s.sampleRate,r=s.wsCur;if(r){if(!s.wsLock&&2==r._s&&!r.isStop)if(s.pcmSend>=s.pcmTotal){if(1==s.state)return;r.stopWs(function(){e()},function(e){t(e)})}else{var f=s.sampleRate/1e3*50,u=s.sampleRate;if((r.bufferedAmount||0)/2>3*u)s.sendWait=setTimeout(function(){s.sendWait=0,s._send()},100);else{if(s.sendSpeed){var l=(Date.now()-r.okTime)*s.sendSpeed,c=(s.sendCurSize+u/3)/s.sampleRate*1e3,p=Math.floor((c-l)/s.sendSpeed);if(0<p)return w("[ASR]延迟"+p+"ms发送"),void(s.sendWait=setTimeout(function(){s.sendWait=0,s._send()},p))}var d=1,m=function(e,t,n){for(var s=n.length,o=0;o<s&&0<t.length;){var a=t[0];if(!(a.length-e<=s-o)){n.set(a.subarray(e,e+(s-o)),o),e+=s-o;break}n.set(0==e?a:a.subarray(e),o),o+=a.length-e,e=0,t.splice(0,1)}return e};if(s.joinIsOpen){if(-1==s.joinOffset){s.joinSend=0,s.joinOffset=0,s.log("发送上1分钟结尾5秒数据...");for(var g=0,h=s.joinBuffers.length-1;0<=h;h--)if(g+=s.joinBuffers[h].length,a<=g){s.joinBuffers.splice(0,h),s.joinSize=g,s.joinOffset=g-a;break}}var v=s.joinSize-s.joinOffset,_=Math.min(u,v);if(_<=0)return s.log("发送新1分钟数据(重叠"+Math.round(s.joinSend/s.sampleRate*1e3)+"ms)..."),s.joinBuffers=[],s.joinSize=0,s.joinOffset=-1,s.joinIsOpen=0,void s._send();var S=new Int16Array(_);s.joinSend+=_,s.joinSendTotal+=_,s.joinOffset=m(s.joinOffset,s.joinBuffers,S);for(var h=s.joinSize=0;h<s.joinBuffers.length;h++)s.joinSize+=s.joinBuffers[h].length}else{var v=s.pcmTotal-s.pcmSend,T=Math.round(v/s.sampleRate*1e3),k=i-s.sendCurSize,b=Math.min(u,v),_=Math.min(b,k);if(1==s.state&&_<Math.min(f,k))return;var R=0;if(k<=0&&(2==s.state&&v<1.2*s.sampleRate?(_=v,s.log("丢弃结尾"+T+"ms数据","#999"),d=0):R=!0),d&&!n(b)){var j=Math.round(s.asrDuration()/1e3);return s.log("已主动超时，共识别"+j+"秒，丢弃缓冲"+T+"ms，正在终止..."),s.wsLock=1,void r.stopWs(function(){t("已主动超时，共识别"+j+"秒，终止识别")},function(e){t(e)})}if(R)return w("[ASR]新1分钟接续，当前缓冲"+T+"ms..."),s.wsLock=1,void r.stopWs(function(){s._token(function(){s.log("新1分钟接续OK，当前缓冲"+T+"ms",2),s.wsLock=0,s.wsCur=0,s.sendCurSize=0,s.joinIsOpen=1,s.joinOffset=-1,s._send()},function(e){t("语音识别新1分钟token接口出错："+e)})},function(e){t(e)});var S=new Int16Array(_);s.pcmOffset=m(s.pcmOffset,s.pcmBuffers,S),s.pcmSend+=_,s.joinBuffers.push(S),s.joinSize+=_}if(s.sendCurSize+=S.length,s.sendTotal+=S.length,d)try{r.send(S.buffer)}catch(e){w("ws.send",1,e)}s.sendWait=setTimeout(function(){s.sendWait=0,s._send()})}}}else if(s.started){var A={};s.resTxts.push(A),r=s.wsCur=s._wsNew(s.tokenData,"ws:"+s.resTxts.length,A,function(){n()},function(){s._send()},function(e){r==s.wsCur&&t(e)})}}else e()}},getText:function(){for(var e=this.resTxts,t="",n=0;n<e.length;n++){var s=e[n];if(s.fullTxt)t=s.fullTxt;else{var o=s.tempTxt||"";if(s.okTxt&&(o=s.okTxt),t){for(var a=t.substr(-20),i=[],r=0,f=Math.min(17,o.length-3);r<=f;r++)for(var u=0;u<17;u++)if(a[u]==o[r]){for(var l=1;l<17&&a[u+l]==o[r+l];l++);3<=l&&i.push({x:r,i0:u,n:l})}i.sort(function(e,t){var n=t.n-e.n;return 0!=n?n:t.i0-e.i0});var c=i[0];c?(t=t.substr(0,t.length-a.length+c.i0),t+=o.substr(c.x)):t+=o}else t=o;null!=s.okTxt&&o==s.okTxt&&(s.fullTxt=t)}}return t},_wsNew:function(e,l,c,p,d,t){var o=function(){for(var e,t=[],n=0;n<32;n++)e=Math.floor(16*Math.random()),t.push(String.fromCharCode(e<10?e+48:e-10+97));return t.join("")},m=this,n=m.set;w("[ASR "+l+"]正在连接...");var s="wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1?token="+e.token;if(n.compatibleWebSocket)var g=n.compatibleWebSocket(s);else var g=new WebSocket(s);return g.onclose=function(){if(-1!=g._s){var e=4!=g._s;g._s=-1,m.log("["+l+"]close"),e&&t(g._err||"连接"+l+"已关闭")}},g.onerror=function(e){if(-1!=g._s){g._err||(g._err="网络连接错误"),m.log("["+l+"]网络连接错误",1),g.onclose()}},g.onopen=function(){-1!=g._s&&(g._s=1,w("[ASR "+l+"]open"),g._task=o(),g.send(JSON.stringify({header:{message_id:o(),task_id:g._task,appkey:e.appkey,namespace:"SpeechRecognizer",name:"StartRecognition"},payload:{format:"pcm",sample_rate:m.sampleRate,enable_intermediate_result:!0,enable_punctuation_prediction:!0,enable_inverse_text_normalization:!0},context:{}})))},g.onmessage=function(e){var t=e.data,n=!0;if("string"==typeof t&&"{"==t[0]){var s=(t=JSON.parse(t)).header||{},o=t.payload||{},a=s.name||"",i=s.status||0,r="TaskFailed"==a,f="";if(1!=g._s||"RecognitionStarted"!=a&&!r||(r?f="连接"+l+"失败["+i+"]"+s.status_text:(g._s=2,m.log("["+l+"]连接OK"),g.okTime=Date.now(),d())),2!=g._s||"RecognitionResultChanged"!=a&&!r||(r?f="识别出现错误["+i+"]"+s.status_text:(n=!g._clmsg,g._clmsg=1,c.tempTxt=o.result||"",p())),3==g._s&&("RecognitionCompleted"==a||r)){var u="";r?f="停止识别出现错误["+i+"]"+s.status_text:(u=o.result||"",m.log("["+l+"]最终识别结果："+u)),g.stopCall&&g.stopCall(u,f)}f&&(m.log("["+l+"]"+f,1),g._err||(g._err=f))}n&&w("[ASR "+l+"]msg",t)},g.stopWs=function(n,s){2==g._s?(g._s=3,g.isStop=1,g.stopCall=function(e,t){clearTimeout(g.stopInt),g.stopCall=0,g._s=4,g.close(),c.okTxt=e,p(),t?s(t):n()},g.stopInt=setTimeout(function(){g.stopCall&&g.stopCall("","停止识别返回结果超时")},1e4),w("[ASR "+l+"]send stop"),g.send(JSON.stringify({header:{message_id:o(),task_id:g._task,appkey:e.appkey,namespace:"SpeechRecognizer",name:"StopRecognition"}}))):s(l+"状态不正确["+g._s+"]")},g.connect&&g.connect(),g},_token:function(t,n){var s=this,e=s.set;e.tokenApi?(e.apiRequest||function(e,t,n,s){var o=new XMLHttpRequest;o.timeout=2e4,o.open("POST",e),o.onreadystatechange=function(){if(4==o.readyState)if(200==o.status){try{var e=JSON.parse(o.responseText)}catch(e){}if(0!==e.c||!e.v)return void s(e.m||"接口返回非预定义json数据");n(e.v)}else s("请求失败["+o.status+"]")};var a=[];for(var i in t)a.push(i+"="+encodeURIComponent(t[i]));o.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),o.send(a.join("&"))})(e.tokenApi,e.apiArgs||{},function(e){e&&e.appkey&&e.token?(s.tokenData=e,t()):n("apiRequest回调的数据格式不正确")},n):n("未配置tokenApi")}},u[s]=n}(n,0,s.$T,t)}();