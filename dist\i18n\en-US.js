/*
录音
https://github.com/xiangyuecn/Recorder
src: i18n/en-US.js
*/
!function(e){var t="object"==typeof window&&!!window.document;!function(e,t){"use strict";var o=e.i18n;e.CLog('Import Recorder i18n lang="en-US"');var n={lang:o.alias["en-US"]="en"};o.data.rtl$en=!1,o.data.desc$en="English, 英语。This translation mainly comes from: google translation + Baidu translation, translated from Chinese to English. 此翻译主要来自：google翻译+百度翻译，由中文翻译成英文。",o.put(n,["K8zP:Duplicate import {1}","mSxV:There are {1} GetContext unclosed","nMIy: (Note: ctx is not in the running state. At least one of rec.open and start must be called during user operations (touch, click, etc.), otherwise ctx.resume will be attempted during rec.start, which may cause compatibility issues (iOS only), please refer to the runningContext configuration in the documentation) ","eS8i:The sampleRate of the Stream {1} is not equal to {2}, so the sampleRate conversion will be performed (note: the sound quality will not improve and may even deteriorate). This phenomenon mainly occurs when echoCancellation is not disabled on the mobile terminal. When the browser has echoCancellation, it may only return audio data with a sampleRate of 16k. ","ZGlf:. Due to 375 callbacks in 1 second in {1}, there may be performance problems on the mobile side, which may cause the callback to be lost and the recording to be shortened, but it will not affect the PC side. It is not recommended to enable {1} for now.","7TU0:Connect uses the old {1}, ","JwCL:But {1} is set trying to enable {2}","VGjB:Can set {1} try to enable {2}","MxX1:{1} did not return any audio, reverting to {2}","XUap:{1} redundant callback","yOta:Connect uses {1}, set {2} to restore old-fashioned {3}","VwPd: (This browser does not support {1}) ","vHnb:{1} did not return any audio, downgrade to {2}","O9P7:{1} redundant callback","LMEm:Connect uses {1}, set {2} to restore to using {3} or old-fashioned {4}","d48C:The filter sampleRate of {1} has changed, reset the filter","tlbC:{1} seems to have passed in an unreset chunk {2}","VtS4:{1} and {2} must be number","5tWi:Recording open failed: ","dFm8:open cancelled","VtJO:open interrupted","EMJq:, you can try to use the RecordApp solution ","A5bm:Cannot record: ","1iU7:This browser does not support obtaining recordings from stream","BTW2:Failed to open recording from stream: ","Nclz:No permission to record (cross domain, please try adding microphone access policy to iframe, such as: {1})","jBa9:, no microphone available","gyO5:User denied recording permission","oWNo:Browser prohibits recording of unsafe pages, which can be resolved by enabling HTTPS","COxc:This browser does not support recording","upb8:It was found that open was called multiple times at the same time","Q1GA:Invalid recording: no audio stream","KxE2:, will try to disable echoCancellation and try again","xEQR:Error requesting recording permission","bDOG:Unable to record: ","IjL3:Note: The {1} parameter has been configured, which may cause the browser to not correctly select the microphone, or the mobile terminal to not enable echoCancellation, etc. ","RiWe:, when {1} is not configured, the browser may automatically enable echoCancellation. When echoCancellation is not disabled on the mobile terminal, the system playback volume may be reduced (can be restored after closing the recording) and only 16k sampleRate audio stream is provided (when echoCancellation is not required, it can be explicitly configured to disable to obtain 48k high-quality stream). Please refer to the {2} configuration in the document","hWVz:close is ignored (because multiple recs are open at the same time, only the last one will actually close)","UHvm:ignore","Essp:{1} architecture not supported","2XBl:{1} type does not support setting takeoffEncodeChunk","LG7e:(Encoder not loaded)","7uMV:{1} environment does not support real-time processing","4Kfd:Compensation {1}ms","bM5i:Uncompensated {1}ms","gFUF:Callback error is not allowed, you need to ensure that no exception will be thrown","2ghS:Low performance, took {1}ms","ufqH:Buffers cannot be cleared before entering async","6WmN:start failed: not open","kLDN:start recording, ","Bp2y:start was interrupted","upkE:, may fail to record: ","Xq4s:Stop and start time difference: ","3CQP:compensate: ","u8JG:Failed to stop recording: ","1skY:, please set {1}","Wv7l:Stop recording, encoding takes {1}ms, audio duration {2}ms, file size {3}b","Vkbd:{1} encoder returned not {2}","QWnr:After enabling takeoffEncodeChunk, the length of the blob returned by stop is 0 and no audio data is provided","Sz2H:Invalid generated {1}","wf9t:Recording not started","Dl2c:, No user interaction before starting recording, resulting in AudioContext not running","Ltz3:Recording not captured","xGuI:The {1} encoder is not loaded. Please try to find the {1} encoder in the src/engine directory of the {2} and load it","AxOH:Recording error: ","xkKd:Audio buffers are released","CxeT:Sampled: {1}, took: {2}ms","NonBrowser-1:Non-browser environment, does not support {1}","IllegalArgs-1:Illegal argument: {1}","NeedImport-2:Calling {1} needs to import {2} first","NotSupport-1:Not support: {1}","8HO5:Override import {1}"]),o.put(n,["b2mN:AMR-NB (NarrowBand), sampleRate setting is invalid (only 8000hz is provided), bitRate range: {1} (default 12.2kbps), one frame 20ms, {2} bytes; browsers generally do not support playing amr format, available Recorder.amr2wav() transcoding into wav playback","tQBv:AMR Info: does not match the set {1}, has been updated to {2}","q12D:Data sampleRate lower than {1}","TxjV:The current browser version is too low to process in real time","Q7p7:takeoffEncodeChunk takes over the binary data output by the AMR encoder, and only the first callback data (the first frame) contains the AMR header; when merging into an AMR file, if the first frame data is not included, the AMR header must be added at the beginning of the file: Recorder.AMR.AMR_HEADER (converted to binary), otherwise it cannot be played","6o9Z:The current environment does not support Web Worker, and the amr real-time encoder runs in the main thread","yYWs:amr worker left {1} unstopped","jOi8:amr encoder not started"]),o.put(n,["O8Gn:Ogg Vorbis, bitRate 16-100kbps, sampleRate unlimited","5si6:The current browser version is too low to process in real time","R8yz:takeoffEncodeChunk takes over the binary data output by the OggVorbis encoder. Ogg is composed of data pages. One page contains multiple frames of audio data (including a few seconds of audio, and one page of data cannot be decoded and played alone). This encoder outputs a complete page of data each time, so the real-time performance will be relatively low; when merging into a complete ogg file, all the output data must be merged together, otherwise it may not be able to play, and it does not support intercepting the middle part to decode and play separately","hB9D:The current environment does not support Web Worker, and the OggVorbis real-time encoder runs in the main thread","oTiy:There are {1} unstopped ogg workers","dIpw:ogg encoder not started"]),o.put(n,["L49q:This browser does not support webm encoding, MediaRecorder is not implemented","tsTW:Only newer browsers support it, and the compression rate is similar to mp3. Since there is no way to quickly encode the existing pcm data, the data can only be imported into MediaRecorder in a similar manner while playing and listening, and it takes a few seconds to wait for a few seconds. Although the output audio can control the file size through the bitRate, the bitRate in the audio file is not the set bitRate. Since the sampleRate is sampled by ourselves, we can do whatever we want with this encoder.","aG4z:This browser does not support converting recordings to webm format","PIX0:Error encoding webm: {1}"]),o.put(n,["d8YX:{1}; {2} audio files cannot be played directly, and can be transcoded into wav by Recorder.{2}2wav(); the sampleRate bitRate setting is invalid, fixed at 8000hz sampleRate, 16 bits, each sample is compressed into 8 bits for storage, and the audio file size is 8000 bytes/second; if you need any sampleRate support, please use Recorder.{2}_encode() Method","29UK:Data sampleRate lower than {1}","quVJ:{1} encoder not started"]),o.put(n,["Zm7L:sampleRate range: {1}; bitRate range: {2} (the sampleRate range supported by different bitRate is different, when the bitRate is less than 32kbps, the sampleRate must be less than 32000)","eGB9:{1} is not in the value range supported by mp3: {2}","zLTa:sampleRate has been updated to {1}, because {2} is not in the value range supported by mp3: {3}","yhUs:The current browser version is too low to process in real time","k9PT:The current environment does not support Web Worker, and the mp3 real-time encoder runs in the main thread","fT6M:There are {1} unstopped mp3 workers left","mPxH:mp3 encoder not started","uY9i:Does not match the set {1}, has been updated to {2}","iMSm:Fix remove {1} frame","b9zm:Remove too many frames"]),o.put(n,["fWsN:pcm is unencapsulated original audio data, pcm audio files cannot be played directly, and can be transcoded into wav by Recorder.pcm2wav(); it supports 8-bit and 16-bit bits (fill in the bitRate), and the sampleRate is unlimited","uMUJ:PCM Info: {1} bit is not supported, has been updated to {2} bit","KmRz:pcm2wav must provide sampleRate and bitRate","sDkA:pcm encoder not started"]),o.put(n,["gPSE:Supports 8-bit and 16-bit bits (fill in the bitRate), and the sampleRate is unlimited; this encoder only adds a 44-byte wav header before the pcm data, and the encoded 16-bit wav file removes the beginning 44 bytes to get pcm (note: other wav encoders may not be 44 bytes)","wyw9:WAV Info: {1} bit is not supported, has been updated to {2} bit"]),o.put(n,["0XYC:The getAudioSrc method is obsolete: please use getMediaStream directly and then assign it to audio.srcObject, it is only allowed to call this method in browsers that do not support srcObject and assign it to audio.src for compatibility","6DDt:start is terminated by stop","I4h4:{1} repeat start","P6Gs:The browser does not support opening {1}","JwDm: (Note: ctx is not in the running state, start needs to be called when the user operates (touch, click, etc.), otherwise it will try to perform ctx.resume, which may cause compatibility issues (only iOS), please refer to the runningContext configuration in the document) ","qx6X:The AudioBuffer implementation of this browser does not support dynamic features, use compatibility mode","cdOx:Environment detection timeout","S2Bu:Could not play: {1}","ZfGG:input call failed: non-pcm[Int16,...] input must be decoded or converted using transform","N4ke:input call failed: sampleRate not provided","IHZd:input call failed: sampleRate={1} of data is different from previous={2}","L8sC:The delay is too large, {1}ms has been discarded, {2}","TZPq:{1} did not call the start method","iCFC:Browser does not support audio decoding","wE2k:Audio decoding data must be ArrayBuffer","mOaT:Audio decoding failed: {1}"]),o.put(n,["3RBa:Invalid symbol [{1}]: {2}","U212:Invalid note [{1}]: {2}","7qAD:Multiple tones must be aligned, with a difference of {1}ms","QGsW:Happy Birthday to You","emJR:For Elise","GsYy:Canon - Right Hand Notation","bSFZ:Canon"]),o.put(n,["Ikdz:The current environment does not support Web Worker and does not support calling Sonic.Async","IC5Y:There are {1} unflushed sonic workers left"]),o.put(n,["WWoj:The {2} method in {1} is not implemented, please implement this method in the {3} file or configuration file","rCAM:Recording does not start, but Native PCM data is received","t2OF:A cross-domain iframe is detected. NativeRecordReceivePCM cannot be injected into the top layer. It has listened to postMessage to be compatible with data transmission. Please implement it by yourself to forward the data received by the top layer to this iframe (no limit on layer), otherwise the recording data cannot be received.","Z2y2:Recording not started"]),o.put(n,["uXtA:Duplicate import {1}","kIBu:Note: Because other recording-related methods are called concurrently, the current call result of {1} has been discarded and there will be no callback","ha2K:Duplicate registration {1}","wpTL:Clean resources only","bpvP:Recording not started","fLJD:The current environment does not support real-time callback and cannot be performed {1}","YnzX:Recording permission request failed: ","nwKR:Need to call {1} first","citA:This is not a browser environment. You need to import support files for this platform ({1}), or call {2} to implement the access yourself.","ecp9:Failed to start recording: ","EKmS:Cannot record: ","k7Qo:Recording started","Douz:Failed to stop recording: ","wqSH:Time difference from Start: {1}ms","g3VX:Stop recording, takes {1}ms, audio duration {2}ms, file size {3}b, {4}"])}((t?window:Object).Recorder)}();