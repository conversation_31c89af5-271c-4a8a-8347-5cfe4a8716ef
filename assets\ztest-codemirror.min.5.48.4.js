// CodeMirror, copyright (c) by <PERSON><PERSON> and others
// Distributed under an MIT license: https://codemirror.net/LICENSE

// This is CodeMirror (https://codemirror.net), a code editor
// implemented in JavaScript on top of the browser's DOM.
//
// You can find some technical background for some of the code below
// at http://marijnhaverbeke.nl/blog/#cm-internals .


//uglify压缩了一遍lib/codemirror.js，底下已包含lib/codemirror.css，mode/javascript/javascript.js
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.CodeMirror=t()}(this,function(){"use strict";var e=navigator.userAgent,t=navigator.platform,g=/gecko\/\d/i.test(e),r=/MSIE \d/.test(e),n=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(e),i=/Edge\/(\d+)/.exec(e),x=r||n||i,C=x&&(r?document.documentMode||6:+(i||n)[1]),b=!i&&/WebKit\//.test(e),o=b&&/Qt\/\d+\.\d+/.test(e),l=!i&&/Chrome\//.test(e),v=/Opera\//.test(e),a=/Apple Computer/.test(navigator.vendor),s=/Mac OS X 1\d\D([8-9]|\d\d)\D/.test(e),u=/PhantomJS/.test(e),c=!i&&/AppleWebKit/.test(e)&&/Mobile\/\w+/.test(e),h=/Android/.test(e),f=c||h||/webOS|BlackBerry|Opera Mini|Opera Mobi|IEMobile/i.test(e),w=c||/Mac/.test(t),d=/\bCrOS\b/.test(e),p=/win/i.test(t),m=v&&e.match(/Version\/(\d*\.\d*)/);m&&(m=Number(m[1])),m&&15<=m&&(b=!(v=!1));var y=w&&(o||v&&(null==m||m<12.11)),S=g||x&&9<=C;function L(e){return new RegExp("(^|\\s)"+e+"(?:$|\\s)\\s*")}var k,T=function(e,t){var r=e.className,n=L(t).exec(r);if(n){var i=r.slice(n.index+n[0].length);e.className=r.slice(0,n.index)+(i?n[1]+i:"")}};function M(e){for(var t=e.childNodes.length;0<t;--t)e.removeChild(e.firstChild);return e}function N(e,t){return M(e).appendChild(t)}function A(e,t,r,n){var i=document.createElement(e);if(r&&(i.className=r),n&&(i.style.cssText=n),"string"==typeof t)i.appendChild(document.createTextNode(t));else if(t)for(var o=0;o<t.length;++o)i.appendChild(t[o]);return i}function O(e,t,r,n){var i=A(e,t,r,n);return i.setAttribute("role","presentation"),i}function D(e,t){if(3==t.nodeType&&(t=t.parentNode),e.contains)return e.contains(t);do{if(11==t.nodeType&&(t=t.host),t==e)return!0}while(t=t.parentNode)}function W(){var t;try{t=document.activeElement}catch(e){t=document.body||null}for(;t&&t.shadowRoot&&t.shadowRoot.activeElement;)t=t.shadowRoot.activeElement;return t}function H(e,t){var r=e.className;L(t).test(r)||(e.className+=(r?" ":"")+t)}function F(e,t){for(var r=e.split(" "),n=0;n<r.length;n++)r[n]&&!L(r[n]).test(t)&&(t+=" "+r[n]);return t}k=document.createRange?function(e,t,r,n){var i=document.createRange();return i.setEnd(n||e,r),i.setStart(e,t),i}:function(e,t,r){var n=document.body.createTextRange();try{n.moveToElementText(e.parentNode)}catch(e){return n}return n.collapse(!0),n.moveEnd("character",r),n.moveStart("character",t),n};var P=function(e){e.select()};function E(e){var t=Array.prototype.slice.call(arguments,1);return function(){return e.apply(null,t)}}function I(e,t,r){for(var n in t||(t={}),e)!e.hasOwnProperty(n)||!1===r&&t.hasOwnProperty(n)||(t[n]=e[n]);return t}function z(e,t,r,n,i){null==t&&-1==(t=e.search(/[^\s\u00a0]/))&&(t=e.length);for(var o=n||0,l=i||0;;){var s=e.indexOf("\t",o);if(s<0||t<=s)return l+(t-o);l+=s-o,l+=r-l%r,o=s+1}}c?P=function(e){e.selectionStart=0,e.selectionEnd=e.value.length}:x&&(P=function(e){try{e.select()}catch(e){}});var R=function(){this.id=null};function B(e,t){for(var r=0;r<e.length;++r)if(e[r]==t)return r;return-1}R.prototype.set=function(e,t){clearTimeout(this.id),this.id=setTimeout(t,e)};var G=30,U={toString:function(){return"CodeMirror.Pass"}},V={scroll:!1},K={origin:"*mouse"},j={origin:"+move"};function X(e,t,r){for(var n=0,i=0;;){var o=e.indexOf("\t",n);-1==o&&(o=e.length);var l=o-n;if(o==e.length||t<=i+l)return n+Math.min(l,t-i);if(i+=o-n,n=o+1,t<=(i+=r-i%r))return n}}var Y=[""];function _(e){for(;Y.length<=e;)Y.push($(Y)+" ");return Y[e]}function $(e){return e[e.length-1]}function q(e,t){for(var r=[],n=0;n<e.length;n++)r[n]=t(e[n],n);return r}function Z(){}function Q(e,t){var r;return Object.create?r=Object.create(e):(Z.prototype=e,r=new Z),t&&I(t,r),r}var J=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;function ee(e){return/\w/.test(e)||""<e&&(e.toUpperCase()!=e.toLowerCase()||J.test(e))}function te(e,t){return t?!!(-1<t.source.indexOf("\\w")&&ee(e))||t.test(e):ee(e)}function re(e){for(var t in e)if(e.hasOwnProperty(t)&&e[t])return!1;return!0}var ne=/[\u0300-\u036f\u0483-\u0489\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u0610-\u061a\u064b-\u065e\u0670\u06d6-\u06dc\u06de-\u06e4\u06e7\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0900-\u0902\u093c\u0941-\u0948\u094d\u0951-\u0955\u0962\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2\u09e3\u0a01\u0a02\u0a3c\u0a41\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a70\u0a71\u0a75\u0a81\u0a82\u0abc\u0ac1-\u0ac5\u0ac7\u0ac8\u0acd\u0ae2\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c62\u0c63\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc\u0ccd\u0cd5\u0cd6\u0ce2\u0ce3\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb\u0ebc\u0ec8-\u0ecd\u0f18\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86\u0f87\u0f90-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039\u103a\u103d\u103e\u1058\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085\u1086\u108d\u109d\u135f\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193b\u1a17\u1a18\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80\u1b81\u1ba2-\u1ba5\u1ba8\u1ba9\u1c2c-\u1c33\u1c36\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1dc0-\u1de6\u1dfd-\u1dff\u200c\u200d\u20d0-\u20f0\u2cef-\u2cf1\u2de0-\u2dff\u302a-\u302f\u3099\u309a\ua66f-\ua672\ua67c\ua67d\ua6f0\ua6f1\ua802\ua806\ua80b\ua825\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\uaa29-\uaa2e\uaa31\uaa32\uaa35\uaa36\uaa43\uaa4c\uaab0\uaab2-\uaab4\uaab7\uaab8\uaabe\uaabf\uaac1\uabe5\uabe8\uabed\udc00-\udfff\ufb1e\ufe00-\ufe0f\ufe20-\ufe26\uff9e\uff9f]/;function ie(e){return 768<=e.charCodeAt(0)&&ne.test(e)}function oe(e,t,r){for(;(r<0?0<t:t<e.length)&&ie(e.charAt(t));)t+=r;return t}function le(e,t,r){for(var n=r<t?-1:1;;){if(t==r)return t;var i=(t+r)/2,o=n<0?Math.ceil(i):Math.floor(i);if(o==t)return e(o)?t:r;e(o)?r=o:t=o+n}}var se=null;function ae(e,t,r){var n;se=null;for(var i=0;i<e.length;++i){var o=e[i];if(o.from<t&&o.to>t)return i;o.to==t&&(o.from!=o.to&&"before"==r?n=i:se=i),o.from==t&&(o.from!=o.to&&"before"!=r?n=i:se=i)}return null!=n?n:se}var ue=function(){var I="bbbbbbbbbtstwsbbbbbbbbbbbbbbssstwNN%%%NNNNNN,N,N1111111111NNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNbbbbbbsbbbbbbbbbbbbbbbbbbbbbbbbbb,N%%%%NNNNLNNNNN%%11NLNNN1LNNNNNLLLLLLLLLLLLLLLLLLLLLLLNLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLN",z="nnnnnnNNr%%r,rNNmmmmmmmmmmmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmmmmmmmmnnnnnnnnnn%nnrrrmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmnNmmmmmmrrmmNmmmmrr1111111111";var R=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/,B=/[stwN]/,G=/[LRr]/,U=/[Lb1n]/,V=/[1n]/;function K(e,t,r){this.level=e,this.from=t,this.to=r}return function(e,t){var r="ltr"==t?"L":"R";if(0==e.length||"ltr"==t&&!R.test(e))return!1;for(var n,i=e.length,o=[],l=0;l<i;++l)o.push((n=e.charCodeAt(l))<=247?I.charAt(n):1424<=n&&n<=1524?"R":1536<=n&&n<=1785?z.charAt(n-1536):1774<=n&&n<=2220?"r":8192<=n&&n<=8203?"w":8204==n?"b":"L");for(var s=0,a=r;s<i;++s){var u=o[s];"m"==u?o[s]=a:a=u}for(var c=0,h=r;c<i;++c){var f=o[c];"1"==f&&"r"==h?o[c]="n":G.test(f)&&"r"==(h=f)&&(o[c]="R")}for(var d=1,p=o[0];d<i-1;++d){var g=o[d];"+"==g&&"1"==p&&"1"==o[d+1]?o[d]="1":","!=g||p!=o[d+1]||"1"!=p&&"n"!=p||(o[d]=p),p=g}for(var v=0;v<i;++v){var m=o[v];if(","==m)o[v]="N";else if("%"==m){var y=void 0;for(y=v+1;y<i&&"%"==o[y];++y);for(var b=v&&"!"==o[v-1]||y<i&&"1"==o[y]?"1":"N",w=v;w<y;++w)o[w]=b;v=y-1}}for(var x=0,C=r;x<i;++x){var S=o[x];"L"==C&&"1"==S?o[x]="L":G.test(S)&&(C=S)}for(var L=0;L<i;++L)if(B.test(o[L])){var k=void 0;for(k=L+1;k<i&&B.test(o[k]);++k);for(var T="L"==(L?o[L-1]:r),M=T==("L"==(k<i?o[k]:r))?T?"L":"R":r,N=L;N<k;++N)o[N]=M;L=k-1}for(var O,A=[],D=0;D<i;)if(U.test(o[D])){var W=D;for(++D;D<i&&U.test(o[D]);++D);A.push(new K(0,W,D))}else{var H=D,F=A.length;for(++D;D<i&&"L"!=o[D];++D);for(var P=H;P<D;)if(V.test(o[P])){H<P&&A.splice(F,0,new K(1,H,P));var E=P;for(++P;P<D&&V.test(o[P]);++P);A.splice(F,0,new K(2,E,P)),H=P}else++P;H<D&&A.splice(F,0,new K(1,H,D))}return"ltr"==t&&(1==A[0].level&&(O=e.match(/^\s+/))&&(A[0].from=O[0].length,A.unshift(new K(0,0,O[0].length))),1==$(A).level&&(O=e.match(/\s+$/))&&($(A).to-=O[0].length,A.push(new K(0,i-O[0].length,i)))),"rtl"==t?A.reverse():A}}();function ce(e,t){var r=e.order;return null==r&&(r=e.order=ue(e.text,t)),r}var he=[],fe=function(e,t,r){if(e.addEventListener)e.addEventListener(t,r,!1);else if(e.attachEvent)e.attachEvent("on"+t,r);else{var n=e._handlers||(e._handlers={});n[t]=(n[t]||he).concat(r)}};function de(e,t){return e._handlers&&e._handlers[t]||he}function pe(e,t,r){if(e.removeEventListener)e.removeEventListener(t,r,!1);else if(e.detachEvent)e.detachEvent("on"+t,r);else{var n=e._handlers,i=n&&n[t];if(i){var o=B(i,r);-1<o&&(n[t]=i.slice(0,o).concat(i.slice(o+1)))}}}function ge(e,t){var r=de(e,t);if(r.length)for(var n=Array.prototype.slice.call(arguments,2),i=0;i<r.length;++i)r[i].apply(null,n)}function ve(e,t,r){return"string"==typeof t&&(t={type:t,preventDefault:function(){this.defaultPrevented=!0}}),ge(e,r||t.type,e,t),Ce(t)||t.codemirrorIgnore}function me(e){var t=e._handlers&&e._handlers.cursorActivity;if(t)for(var r=e.curOp.cursorActivityHandlers||(e.curOp.cursorActivityHandlers=[]),n=0;n<t.length;++n)-1==B(r,t[n])&&r.push(t[n])}function ye(e,t){return 0<de(e,t).length}function be(e){e.prototype.on=function(e,t){fe(this,e,t)},e.prototype.off=function(e,t){pe(this,e,t)}}function we(e){e.preventDefault?e.preventDefault():e.returnValue=!1}function xe(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}function Ce(e){return null!=e.defaultPrevented?e.defaultPrevented:0==e.returnValue}function Se(e){we(e),xe(e)}function Le(e){return e.target||e.srcElement}function ke(e){var t=e.which;return null==t&&(1&e.button?t=1:2&e.button?t=3:4&e.button&&(t=2)),w&&e.ctrlKey&&1==t&&(t=3),t}var Te,Me,Ne=function(){if(x&&C<9)return!1;var e=A("div");return"draggable"in e||"dragDrop"in e}();function Oe(e){if(null==Te){var t=A("span","​");N(e,A("span",[t,document.createTextNode("x")])),0!=e.firstChild.offsetHeight&&(Te=t.offsetWidth<=1&&2<t.offsetHeight&&!(x&&C<8))}var r=Te?A("span","​"):A("span"," ",null,"display: inline-block; width: 1px; margin-right: -1px");return r.setAttribute("cm-text",""),r}function Ae(e){if(null!=Me)return Me;var t=N(e,document.createTextNode("AخA")),r=k(t,0,1).getBoundingClientRect(),n=k(t,1,2).getBoundingClientRect();return M(e),!(!r||r.left==r.right)&&(Me=n.right-r.right<3)}var De,We=3!="\n\nb".split(/\n/).length?function(e){for(var t=0,r=[],n=e.length;t<=n;){var i=e.indexOf("\n",t);-1==i&&(i=e.length);var o=e.slice(t,"\r"==e.charAt(i-1)?i-1:i),l=o.indexOf("\r");-1!=l?(r.push(o.slice(0,l)),t+=l+1):(r.push(o),t=i+1)}return r}:function(e){return e.split(/\r\n?|\n/)},He=window.getSelection?function(e){try{return e.selectionStart!=e.selectionEnd}catch(e){return!1}}:function(e){var t;try{t=e.ownerDocument.selection.createRange()}catch(e){}return!(!t||t.parentElement()!=e)&&0!=t.compareEndPoints("StartToEnd",t)},Fe="oncopy"in(De=A("div"))||(De.setAttribute("oncopy","return;"),"function"==typeof De.oncopy),Pe=null;var Ee={},Ie={};function ze(e){if("string"==typeof e&&Ie.hasOwnProperty(e))e=Ie[e];else if(e&&"string"==typeof e.name&&Ie.hasOwnProperty(e.name)){var t=Ie[e.name];"string"==typeof t&&(t={name:t}),(e=Q(t,e)).name=t.name}else{if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+xml$/.test(e))return ze("application/xml");if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+json$/.test(e))return ze("application/json")}return"string"==typeof e?{name:e}:e||{name:"null"}}function Re(e,t){t=ze(t);var r=Ee[t.name];if(!r)return Re(e,"text/plain");var n=r(e,t);if(Be.hasOwnProperty(t.name)){var i=Be[t.name];for(var o in i)i.hasOwnProperty(o)&&(n.hasOwnProperty(o)&&(n["_"+o]=n[o]),n[o]=i[o])}if(n.name=t.name,t.helperType&&(n.helperType=t.helperType),t.modeProps)for(var l in t.modeProps)n[l]=t.modeProps[l];return n}var Be={};function Ge(e,t){I(t,Be.hasOwnProperty(e)?Be[e]:Be[e]={})}function Ue(e,t){if(!0===t)return t;if(e.copyState)return e.copyState(t);var r={};for(var n in t){var i=t[n];i instanceof Array&&(i=i.concat([])),r[n]=i}return r}function Ve(e,t){for(var r;e.innerMode&&(r=e.innerMode(t))&&r.mode!=e;)t=r.state,e=r.mode;return r||{mode:e,state:t}}function Ke(e,t,r){return!e.startState||e.startState(t,r)}var je=function(e,t,r){this.pos=this.start=0,this.string=e,this.tabSize=t||8,this.lastColumnPos=this.lastColumnValue=0,this.lineStart=0,this.lineOracle=r};function Xe(e,t){if((t-=e.first)<0||t>=e.size)throw new Error("There is no line "+(t+e.first)+" in the document.");for(var r=e;!r.lines;)for(var n=0;;++n){var i=r.children[n],o=i.chunkSize();if(t<o){r=i;break}t-=o}return r.lines[t]}function Ye(e,r,n){var i=[],o=r.line;return e.iter(r.line,n.line+1,function(e){var t=e.text;o==n.line&&(t=t.slice(0,n.ch)),o==r.line&&(t=t.slice(r.ch)),i.push(t),++o}),i}function _e(e,t,r){var n=[];return e.iter(t,r,function(e){n.push(e.text)}),n}function $e(e,t){var r=t-e.height;if(r)for(var n=e;n;n=n.parent)n.height+=r}function qe(e){if(null==e.parent)return null;for(var t=e.parent,r=B(t.lines,e),n=t.parent;n;n=(t=n).parent)for(var i=0;n.children[i]!=t;++i)r+=n.children[i].chunkSize();return r+t.first}function Ze(e,t){var r=e.first;e:do{for(var n=0;n<e.children.length;++n){var i=e.children[n],o=i.height;if(t<o){e=i;continue e}t-=o,r+=i.chunkSize()}return r}while(!e.lines);for(var l=0;l<e.lines.length;++l){var s=e.lines[l].height;if(t<s)break;t-=s}return r+l}function Qe(e,t){return t>=e.first&&t<e.first+e.size}function Je(e,t){return String(e.lineNumberFormatter(t+e.firstLineNumber))}function et(e,t,r){if(void 0===r&&(r=null),!(this instanceof et))return new et(e,t,r);this.line=e,this.ch=t,this.sticky=r}function tt(e,t){return e.line-t.line||e.ch-t.ch}function rt(e,t){return e.sticky==t.sticky&&0==tt(e,t)}function nt(e){return et(e.line,e.ch)}function it(e,t){return tt(e,t)<0?t:e}function ot(e,t){return tt(e,t)<0?e:t}function lt(e,t){return Math.max(e.first,Math.min(t,e.first+e.size-1))}function st(e,t){if(t.line<e.first)return et(e.first,0);var r,n,i,o=e.first+e.size-1;return t.line>o?et(o,Xe(e,o).text.length):(n=Xe(e,(r=t).line).text.length,null==(i=r.ch)||n<i?et(r.line,n):i<0?et(r.line,0):r)}function at(e,t){for(var r=[],n=0;n<t.length;n++)r[n]=st(e,t[n]);return r}je.prototype.eol=function(){return this.pos>=this.string.length},je.prototype.sol=function(){return this.pos==this.lineStart},je.prototype.peek=function(){return this.string.charAt(this.pos)||void 0},je.prototype.next=function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},je.prototype.eat=function(e){var t=this.string.charAt(this.pos);if("string"==typeof e?t==e:t&&(e.test?e.test(t):e(t)))return++this.pos,t},je.prototype.eatWhile=function(e){for(var t=this.pos;this.eat(e););return this.pos>t},je.prototype.eatSpace=function(){for(var e=this.pos;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>e},je.prototype.skipToEnd=function(){this.pos=this.string.length},je.prototype.skipTo=function(e){var t=this.string.indexOf(e,this.pos);if(-1<t)return this.pos=t,!0},je.prototype.backUp=function(e){this.pos-=e},je.prototype.column=function(){return this.lastColumnPos<this.start&&(this.lastColumnValue=z(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue-(this.lineStart?z(this.string,this.lineStart,this.tabSize):0)},je.prototype.indentation=function(){return z(this.string,null,this.tabSize)-(this.lineStart?z(this.string,this.lineStart,this.tabSize):0)},je.prototype.match=function(e,t,r){if("string"!=typeof e){var n=this.string.slice(this.pos).match(e);return n&&0<n.index?null:(n&&!1!==t&&(this.pos+=n[0].length),n)}var i=function(e){return r?e.toLowerCase():e};if(i(this.string.substr(this.pos,e.length))==i(e))return!1!==t&&(this.pos+=e.length),!0},je.prototype.current=function(){return this.string.slice(this.start,this.pos)},je.prototype.hideFirstChars=function(e,t){this.lineStart+=e;try{return t()}finally{this.lineStart-=e}},je.prototype.lookAhead=function(e){var t=this.lineOracle;return t&&t.lookAhead(e)},je.prototype.baseToken=function(){var e=this.lineOracle;return e&&e.baseToken(this.pos)};var ut=function(e,t){this.state=e,this.lookAhead=t},ct=function(e,t,r,n){this.state=t,this.doc=e,this.line=r,this.maxLookAhead=n||0,this.baseTokens=null,this.baseTokenPos=1};function ht(t,r,n,e){var a=[t.state.modeGen],i={};wt(t,r.text,t.doc.mode,n,function(e,t){return a.push(e,t)},i,e);for(var u=n.state,o=function(e){n.baseTokens=a;var o=t.state.overlays[e],l=1,s=0;n.state=!0,wt(t,r.text,o.mode,n,function(e,t){for(var r=l;s<e;){var n=a[l];e<n&&a.splice(l,1,e,a[l+1],n),l+=2,s=Math.min(e,n)}if(t)if(o.opaque)a.splice(r,l-r,e,"overlay "+t),l=r+2;else for(;r<l;r+=2){var i=a[r+1];a[r+1]=(i?i+" ":"")+"overlay "+t}},i),n.state=u,n.baseTokens=null,n.baseTokenPos=1},l=0;l<t.state.overlays.length;++l)o(l);return{styles:a,classes:i.bgClass||i.textClass?i:null}}function ft(e,t,r){if(!t.styles||t.styles[0]!=e.state.modeGen){var n=dt(e,qe(t)),i=t.text.length>e.options.maxHighlightLength&&Ue(e.doc.mode,n.state),o=ht(e,t,n);i&&(n.state=i),t.stateAfter=n.save(!i),t.styles=o.styles,o.classes?t.styleClasses=o.classes:t.styleClasses&&(t.styleClasses=null),r===e.doc.highlightFrontier&&(e.doc.modeFrontier=Math.max(e.doc.modeFrontier,++e.doc.highlightFrontier))}return t.styles}function dt(r,n,e){var t=r.doc,i=r.display;if(!t.mode.startState)return new ct(t,!0,n);var o=function(e,t,r){for(var n,i,o=e.doc,l=r?-1:t-(e.doc.mode.innerMode?1e3:100),s=t;l<s;--s){if(s<=o.first)return o.first;var a=Xe(o,s-1),u=a.stateAfter;if(u&&(!r||s+(u instanceof ut?u.lookAhead:0)<=o.modeFrontier))return s;var c=z(a.text,null,e.options.tabSize);(null==i||c<n)&&(i=s-1,n=c)}return i}(r,n,e),l=o>t.first&&Xe(t,o-1).stateAfter,s=l?ct.fromSaved(t,l,o):new ct(t,Ke(t.mode),o);return t.iter(o,n,function(e){pt(r,e.text,s);var t=s.line;e.stateAfter=t==n-1||t%5==0||t>=i.viewFrom&&t<i.viewTo?s.save():null,s.nextLine()}),e&&(t.modeFrontier=s.line),s}function pt(e,t,r,n){var i=e.doc.mode,o=new je(t,e.options.tabSize,r);for(o.start=o.pos=n||0,""==t&&gt(i,r.state);!o.eol();)vt(i,o,r.state),o.start=o.pos}function gt(e,t){if(e.blankLine)return e.blankLine(t);if(e.innerMode){var r=Ve(e,t);return r.mode.blankLine?r.mode.blankLine(r.state):void 0}}function vt(e,t,r,n){for(var i=0;i<10;i++){n&&(n[0]=Ve(e,r).mode);var o=e.token(t,r);if(t.pos>t.start)return o}throw new Error("Mode "+e.name+" failed to advance stream.")}ct.prototype.lookAhead=function(e){var t=this.doc.getLine(this.line+e);return null!=t&&e>this.maxLookAhead&&(this.maxLookAhead=e),t},ct.prototype.baseToken=function(e){if(!this.baseTokens)return null;for(;this.baseTokens[this.baseTokenPos]<=e;)this.baseTokenPos+=2;var t=this.baseTokens[this.baseTokenPos+1];return{type:t&&t.replace(/( |^)overlay .*/,""),size:this.baseTokens[this.baseTokenPos]-e}},ct.prototype.nextLine=function(){this.line++,0<this.maxLookAhead&&this.maxLookAhead--},ct.fromSaved=function(e,t,r){return t instanceof ut?new ct(e,Ue(e.mode,t.state),r,t.lookAhead):new ct(e,Ue(e.mode,t),r)},ct.prototype.save=function(e){var t=!1!==e?Ue(this.doc.mode,this.state):this.state;return 0<this.maxLookAhead?new ut(t,this.maxLookAhead):t};var mt=function(e,t,r){this.start=e.start,this.end=e.pos,this.string=e.current(),this.type=t||null,this.state=r};function yt(e,t,r,n){var i,o,l=e.doc,s=l.mode,a=Xe(l,(t=st(l,t)).line),u=dt(e,t.line,r),c=new je(a.text,e.options.tabSize,u);for(n&&(o=[]);(n||c.pos<t.ch)&&!c.eol();)c.start=c.pos,i=vt(s,c,u.state),n&&o.push(new mt(c,i,Ue(l.mode,u.state)));return n?o:new mt(c,i,u.state)}function bt(e,t){if(e)for(;;){var r=e.match(/(?:^|\s+)line-(background-)?(\S+)/);if(!r)break;e=e.slice(0,r.index)+e.slice(r.index+r[0].length);var n=r[1]?"bgClass":"textClass";null==t[n]?t[n]=r[2]:new RegExp("(?:^|s)"+r[2]+"(?:$|s)").test(t[n])||(t[n]+=" "+r[2])}return e}function wt(e,t,r,n,i,o,l){var s=r.flattenSpans;null==s&&(s=e.options.flattenSpans);var a,u=0,c=null,h=new je(t,e.options.tabSize,n),f=e.options.addModeClass&&[null];for(""==t&&bt(gt(r,n.state),o);!h.eol();){if(h.pos>e.options.maxHighlightLength?(s=!1,l&&pt(e,t,n,h.pos),h.pos=t.length,a=null):a=bt(vt(r,h,n.state,f),o),f){var d=f[0].name;d&&(a="m-"+(a?d+" "+a:d))}if(!s||c!=a){for(;u<h.start;)i(u=Math.min(h.start,u+5e3),c);c=a}h.start=h.pos}for(;u<h.pos;){var p=Math.min(h.pos,u+5e3);i(p,c),u=p}}var xt=!1,Ct=!1;function St(e,t,r){this.marker=e,this.from=t,this.to=r}function Lt(e,t){if(e)for(var r=0;r<e.length;++r){var n=e[r];if(n.marker==t)return n}}function kt(e,t){for(var r,n=0;n<e.length;++n)e[n]!=t&&(r||(r=[])).push(e[n]);return r}function Tt(e,t){if(t.full)return null;var r=Qe(e,t.from.line)&&Xe(e,t.from.line).markedSpans,n=Qe(e,t.to.line)&&Xe(e,t.to.line).markedSpans;if(!r&&!n)return null;var i=t.from.ch,o=t.to.ch,l=0==tt(t.from,t.to),s=function(e,t,r){var n;if(e)for(var i=0;i<e.length;++i){var o=e[i],l=o.marker;if(null==o.from||(l.inclusiveLeft?o.from<=t:o.from<t)||o.from==t&&"bookmark"==l.type&&(!r||!o.marker.insertLeft)){var s=null==o.to||(l.inclusiveRight?o.to>=t:o.to>t);(n||(n=[])).push(new St(l,o.from,s?null:o.to))}}return n}(r,i,l),a=function(e,t,r){var n;if(e)for(var i=0;i<e.length;++i){var o=e[i],l=o.marker;if(null==o.to||(l.inclusiveRight?o.to>=t:o.to>t)||o.from==t&&"bookmark"==l.type&&(!r||o.marker.insertLeft)){var s=null==o.from||(l.inclusiveLeft?o.from<=t:o.from<t);(n||(n=[])).push(new St(l,s?null:o.from-t,null==o.to?null:o.to-t))}}return n}(n,o,l),u=1==t.text.length,c=$(t.text).length+(u?i:0);if(s)for(var h=0;h<s.length;++h){var f=s[h];if(null==f.to){var d=Lt(a,f.marker);d?u&&(f.to=null==d.to?null:d.to+c):f.to=i}}if(a)for(var p=0;p<a.length;++p){var g=a[p];if(null!=g.to&&(g.to+=c),null==g.from)Lt(s,g.marker)||(g.from=c,u&&(s||(s=[])).push(g));else g.from+=c,u&&(s||(s=[])).push(g)}s&&(s=Mt(s)),a&&a!=s&&(a=Mt(a));var v=[s];if(!u){var m,y=t.text.length-2;if(0<y&&s)for(var b=0;b<s.length;++b)null==s[b].to&&(m||(m=[])).push(new St(s[b].marker,null,null));for(var w=0;w<y;++w)v.push(m);v.push(a)}return v}function Mt(e){for(var t=0;t<e.length;++t){var r=e[t];null!=r.from&&r.from==r.to&&!1!==r.marker.clearWhenEmpty&&e.splice(t--,1)}return e.length?e:null}function Nt(e){var t=e.markedSpans;if(t){for(var r=0;r<t.length;++r)t[r].marker.detachLine(e);e.markedSpans=null}}function Ot(e,t){if(t){for(var r=0;r<t.length;++r)t[r].marker.attachLine(e);e.markedSpans=t}}function At(e){return e.inclusiveLeft?-1:0}function Dt(e){return e.inclusiveRight?1:0}function Wt(e,t){var r=e.lines.length-t.lines.length;if(0!=r)return r;var n=e.find(),i=t.find(),o=tt(n.from,i.from)||At(e)-At(t);if(o)return-o;var l=tt(n.to,i.to)||Dt(e)-Dt(t);return l||t.id-e.id}function Ht(e,t){var r,n=Ct&&e.markedSpans;if(n)for(var i=void 0,o=0;o<n.length;++o)(i=n[o]).marker.collapsed&&null==(t?i.from:i.to)&&(!r||Wt(r,i.marker)<0)&&(r=i.marker);return r}function Ft(e){return Ht(e,!0)}function Pt(e){return Ht(e,!1)}function Et(e,t){var r,n=Ct&&e.markedSpans;if(n)for(var i=0;i<n.length;++i){var o=n[i];o.marker.collapsed&&(null==o.from||o.from<t)&&(null==o.to||o.to>t)&&(!r||Wt(r,o.marker)<0)&&(r=o.marker)}return r}function It(e,t,r,n,i){var o=Xe(e,t),l=Ct&&o.markedSpans;if(l)for(var s=0;s<l.length;++s){var a=l[s];if(a.marker.collapsed){var u=a.marker.find(0),c=tt(u.from,r)||At(a.marker)-At(i),h=tt(u.to,n)||Dt(a.marker)-Dt(i);if(!(0<=c&&h<=0||c<=0&&0<=h)&&(c<=0&&(a.marker.inclusiveRight&&i.inclusiveLeft?0<=tt(u.to,r):0<tt(u.to,r))||0<=c&&(a.marker.inclusiveRight&&i.inclusiveLeft?tt(u.from,n)<=0:tt(u.from,n)<0)))return!0}}}function zt(e){for(var t;t=Ft(e);)e=t.find(-1,!0).line;return e}function Rt(e,t){var r=Xe(e,t),n=zt(r);return r==n?t:qe(n)}function Bt(e,t){if(t>e.lastLine())return t;var r,n=Xe(e,t);if(!Gt(e,n))return t;for(;r=Pt(n);)n=r.find(1,!0).line;return qe(n)+1}function Gt(e,t){var r=Ct&&t.markedSpans;if(r)for(var n=void 0,i=0;i<r.length;++i)if((n=r[i]).marker.collapsed){if(null==n.from)return!0;if(!n.marker.widgetNode&&0==n.from&&n.marker.inclusiveLeft&&Ut(e,t,n))return!0}}function Ut(e,t,r){if(null==r.to){var n=r.marker.find(1,!0);return Ut(e,n.line,Lt(n.line.markedSpans,r.marker))}if(r.marker.inclusiveRight&&r.to==t.text.length)return!0;for(var i=void 0,o=0;o<t.markedSpans.length;++o)if((i=t.markedSpans[o]).marker.collapsed&&!i.marker.widgetNode&&i.from==r.to&&(null==i.to||i.to!=r.from)&&(i.marker.inclusiveLeft||r.marker.inclusiveRight)&&Ut(e,t,i))return!0}function Vt(e){for(var t=0,r=(e=zt(e)).parent,n=0;n<r.lines.length;++n){var i=r.lines[n];if(i==e)break;t+=i.height}for(var o=r.parent;o;o=(r=o).parent)for(var l=0;l<o.children.length;++l){var s=o.children[l];if(s==r)break;t+=s.height}return t}function Kt(e){if(0==e.height)return 0;for(var t,r=e.text.length,n=e;t=Ft(n);){var i=t.find(0,!0);n=i.from.line,r+=i.from.ch-i.to.ch}for(n=e;t=Pt(n);){var o=t.find(0,!0);r-=n.text.length-o.from.ch,r+=(n=o.to.line).text.length-o.to.ch}return r}function jt(e){var r=e.display,t=e.doc;r.maxLine=Xe(t,t.first),r.maxLineLength=Kt(r.maxLine),r.maxLineChanged=!0,t.iter(function(e){var t=Kt(e);t>r.maxLineLength&&(r.maxLineLength=t,r.maxLine=e)})}var Xt=function(e,t,r){this.text=e,Ot(this,t),this.height=r?r(this):1};Xt.prototype.lineNo=function(){return qe(this)},be(Xt);var Yt={},_t={};function $t(e,t){if(!e||/^\s*$/.test(e))return null;var r=t.addModeClass?_t:Yt;return r[e]||(r[e]=e.replace(/\S+/g,"cm-$&"))}function qt(e,t){var r=O("span",null,null,b?"padding-right: .1px":null),n={pre:O("pre",[r],"CodeMirror-line"),content:r,col:0,pos:0,cm:e,trailingSpace:!1,splitSpaces:e.getOption("lineWrapping")};t.measure={};for(var i=0;i<=(t.rest?t.rest.length:0);i++){var o=i?t.rest[i-1]:t.line,l=void 0;n.pos=0,n.addToken=Qt,Ae(e.display.measure)&&(l=ce(o,e.doc.direction))&&(n.addToken=Jt(n.addToken,l)),n.map=[],tr(o,n,ft(e,o,t!=e.display.externalMeasured&&qe(o))),o.styleClasses&&(o.styleClasses.bgClass&&(n.bgClass=F(o.styleClasses.bgClass,n.bgClass||"")),o.styleClasses.textClass&&(n.textClass=F(o.styleClasses.textClass,n.textClass||""))),0==n.map.length&&n.map.push(0,0,n.content.appendChild(Oe(e.display.measure))),0==i?(t.measure.map=n.map,t.measure.cache={}):((t.measure.maps||(t.measure.maps=[])).push(n.map),(t.measure.caches||(t.measure.caches=[])).push({}))}if(b){var s=n.content.lastChild;(/\bcm-tab\b/.test(s.className)||s.querySelector&&s.querySelector(".cm-tab"))&&(n.content.className="cm-tab-wrap-hack")}return ge(e,"renderLine",e,t.line,n.pre),n.pre.className&&(n.textClass=F(n.pre.className,n.textClass||"")),n}function Zt(e){var t=A("span","•","cm-invalidchar");return t.title="\\u"+e.charCodeAt(0).toString(16),t.setAttribute("aria-label",t.title),t}function Qt(e,t,r,n,i,o,l){if(t){var s,a=e.splitSpaces?function(e,t){if(1<e.length&&!/  /.test(e))return e;for(var r=t,n="",i=0;i<e.length;i++){var o=e.charAt(i);" "!=o||!r||i!=e.length-1&&32!=e.charCodeAt(i+1)||(o=" "),n+=o,r=" "==o}return n}(t,e.trailingSpace):t,u=e.cm.state.specialChars,c=!1;if(u.test(t)){s=document.createDocumentFragment();for(var h=0;;){u.lastIndex=h;var f=u.exec(t),d=f?f.index-h:t.length-h;if(d){var p=document.createTextNode(a.slice(h,h+d));x&&C<9?s.appendChild(A("span",[p])):s.appendChild(p),e.map.push(e.pos,e.pos+d,p),e.col+=d,e.pos+=d}if(!f)break;h+=d+1;var g=void 0;if("\t"==f[0]){var v=e.cm.options.tabSize,m=v-e.col%v;(g=s.appendChild(A("span",_(m),"cm-tab"))).setAttribute("role","presentation"),g.setAttribute("cm-text","\t"),e.col+=m}else"\r"==f[0]||"\n"==f[0]?(g=s.appendChild(A("span","\r"==f[0]?"␍":"␤","cm-invalidchar"))).setAttribute("cm-text",f[0]):((g=e.cm.options.specialCharPlaceholder(f[0])).setAttribute("cm-text",f[0]),x&&C<9?s.appendChild(A("span",[g])):s.appendChild(g)),e.col+=1;e.map.push(e.pos,e.pos+1,g),e.pos++}}else e.col+=t.length,s=document.createTextNode(a),e.map.push(e.pos,e.pos+t.length,s),x&&C<9&&(c=!0),e.pos+=t.length;if(e.trailingSpace=32==a.charCodeAt(t.length-1),r||n||i||c||o){var y=r||"";n&&(y+=n),i&&(y+=i);var b=A("span",[s],y,o);if(l)for(var w in l)l.hasOwnProperty(w)&&"style"!=w&&"class"!=w&&b.setAttribute(w,l[w]);return e.content.appendChild(b)}e.content.appendChild(s)}}function Jt(h,f){return function(e,t,r,n,i,o,l){r=r?r+" cm-force-border":"cm-force-border";for(var s=e.pos,a=s+t.length;;){for(var u=void 0,c=0;c<f.length&&!((u=f[c]).to>s&&u.from<=s);c++);if(u.to>=a)return h(e,t,r,n,i,o,l);h(e,t.slice(0,u.to-s),r,n,null,o,l),n=null,t=t.slice(u.to-s),s=u.to}}}function er(e,t,r,n){var i=!n&&r.widgetNode;i&&e.map.push(e.pos,e.pos+t,i),!n&&e.cm.display.input.needsContentAttribute&&(i||(i=e.content.appendChild(document.createElement("span"))),i.setAttribute("cm-marker",r.id)),i&&(e.cm.display.input.setUneditable(i),e.content.appendChild(i)),e.pos+=t,e.trailingSpace=!1}function tr(e,t,r){var n=e.markedSpans,i=e.text,o=0;if(n)for(var l,s,a,u,c,h,f,d=i.length,p=0,g=1,v="",m=0;;){if(m==p){a=u=c=s="",h=f=null,m=1/0;for(var y=[],b=void 0,w=0;w<n.length;++w){var x=n[w],C=x.marker;if("bookmark"==C.type&&x.from==p&&C.widgetNode)y.push(C);else if(x.from<=p&&(null==x.to||x.to>p||C.collapsed&&x.to==p&&x.from==p)){if(null!=x.to&&x.to!=p&&m>x.to&&(m=x.to,u=""),C.className&&(a+=" "+C.className),C.css&&(s=(s?s+";":"")+C.css),C.startStyle&&x.from==p&&(c+=" "+C.startStyle),C.endStyle&&x.to==m&&(b||(b=[])).push(C.endStyle,x.to),C.title&&((f||(f={})).title=C.title),C.attributes)for(var S in C.attributes)(f||(f={}))[S]=C.attributes[S];C.collapsed&&(!h||Wt(h.marker,C)<0)&&(h=x)}else x.from>p&&m>x.from&&(m=x.from)}if(b)for(var L=0;L<b.length;L+=2)b[L+1]==m&&(u+=" "+b[L]);if(!h||h.from==p)for(var k=0;k<y.length;++k)er(t,0,y[k]);if(h&&(h.from||0)==p){if(er(t,(null==h.to?d+1:h.to)-p,h.marker,null==h.from),null==h.to)return;h.to==p&&(h=!1)}}if(d<=p)break;for(var T=Math.min(d,m);;){if(v){var M=p+v.length;if(!h){var N=T<M?v.slice(0,T-p):v;t.addToken(t,N,l?l+a:a,c,p+N.length==m?u:"",s,f)}if(T<=M){v=v.slice(T-p),p=T;break}p=M,c=""}v=i.slice(o,o=r[g++]),l=$t(r[g++],t.cm.options)}}else for(var O=1;O<r.length;O+=2)t.addToken(t,i.slice(o,o=r[O]),$t(r[O+1],t.cm.options))}function rr(e,t,r){this.line=t,this.rest=function(e){for(var t,r;t=Pt(e);)e=t.find(1,!0).line,(r||(r=[])).push(e);return r}(t),this.size=this.rest?qe($(this.rest))-r+1:1,this.node=this.text=null,this.hidden=Gt(e,t)}function nr(e,t,r){for(var n,i=[],o=t;o<r;o=n){var l=new rr(e.doc,Xe(e.doc,o),o);n=o+l.size,i.push(l)}return i}var ir=null;var or=null;function lr(e,t){var r=de(e,t);if(r.length){var n,i=Array.prototype.slice.call(arguments,2);ir?n=ir.delayedCallbacks:or?n=or:(n=or=[],setTimeout(sr,0));for(var o=function(e){n.push(function(){return r[e].apply(null,i)})},l=0;l<r.length;++l)o(l)}}function sr(){var e=or;or=null;for(var t=0;t<e.length;++t)e[t]()}function ar(e,t,r,n){for(var i=0;i<t.changes.length;i++){var o=t.changes[i];"text"==o?hr(e,t):"gutter"==o?dr(e,t,r,n):"class"==o?fr(e,t):"widget"==o&&pr(e,t,n)}t.changes=null}function ur(e){return e.node==e.text&&(e.node=A("div",null,null,"position: relative"),e.text.parentNode&&e.text.parentNode.replaceChild(e.node,e.text),e.node.appendChild(e.text),x&&C<8&&(e.node.style.zIndex=2)),e.node}function cr(e,t){var r=e.display.externalMeasured;return r&&r.line==t.line?(e.display.externalMeasured=null,t.measure=r.measure,r.built):qt(e,t)}function hr(e,t){var r=t.text.className,n=cr(e,t);t.text==t.node&&(t.node=n.pre),t.text.parentNode.replaceChild(n.pre,t.text),t.text=n.pre,n.bgClass!=t.bgClass||n.textClass!=t.textClass?(t.bgClass=n.bgClass,t.textClass=n.textClass,fr(e,t)):r&&(t.text.className=r)}function fr(e,t){!function(e,t){var r=t.bgClass?t.bgClass+" "+(t.line.bgClass||""):t.line.bgClass;if(r&&(r+=" CodeMirror-linebackground"),t.background)r?t.background.className=r:(t.background.parentNode.removeChild(t.background),t.background=null);else if(r){var n=ur(t);t.background=n.insertBefore(A("div",null,r),n.firstChild),e.display.input.setUneditable(t.background)}}(e,t),t.line.wrapClass?ur(t).className=t.line.wrapClass:t.node!=t.text&&(t.node.className="");var r=t.textClass?t.textClass+" "+(t.line.textClass||""):t.line.textClass;t.text.className=r||""}function dr(e,t,r,n){if(t.gutter&&(t.node.removeChild(t.gutter),t.gutter=null),t.gutterBackground&&(t.node.removeChild(t.gutterBackground),t.gutterBackground=null),t.line.gutterClass){var i=ur(t);t.gutterBackground=A("div",null,"CodeMirror-gutter-background "+t.line.gutterClass,"left: "+(e.options.fixedGutter?n.fixedPos:-n.gutterTotalWidth)+"px; width: "+n.gutterTotalWidth+"px"),e.display.input.setUneditable(t.gutterBackground),i.insertBefore(t.gutterBackground,t.text)}var o=t.line.gutterMarkers;if(e.options.lineNumbers||o){var l=ur(t),s=t.gutter=A("div",null,"CodeMirror-gutter-wrapper","left: "+(e.options.fixedGutter?n.fixedPos:-n.gutterTotalWidth)+"px");if(e.display.input.setUneditable(s),l.insertBefore(s,t.text),t.line.gutterClass&&(s.className+=" "+t.line.gutterClass),!e.options.lineNumbers||o&&o["CodeMirror-linenumbers"]||(t.lineNumber=s.appendChild(A("div",Je(e.options,r),"CodeMirror-linenumber CodeMirror-gutter-elt","left: "+n.gutterLeft["CodeMirror-linenumbers"]+"px; width: "+e.display.lineNumInnerWidth+"px"))),o)for(var a=0;a<e.display.gutterSpecs.length;++a){var u=e.display.gutterSpecs[a].className,c=o.hasOwnProperty(u)&&o[u];c&&s.appendChild(A("div",[c],"CodeMirror-gutter-elt","left: "+n.gutterLeft[u]+"px; width: "+n.gutterWidth[u]+"px"))}}}function pr(e,t,r){t.alignable&&(t.alignable=null);for(var n=t.node.firstChild,i=void 0;n;n=i)i=n.nextSibling,"CodeMirror-linewidget"==n.className&&t.node.removeChild(n);gr(e,t,r)}function gr(e,t,r){if(vr(e,t.line,t,r,!0),t.rest)for(var n=0;n<t.rest.length;n++)vr(e,t.rest[n],t,r,!1)}function vr(e,t,r,n,i){if(t.widgets)for(var o=ur(r),l=0,s=t.widgets;l<s.length;++l){var a=s[l],u=A("div",[a.node],"CodeMirror-linewidget");a.handleMouseEvents||u.setAttribute("cm-ignore-events","true"),mr(a,u,r,n),e.display.input.setUneditable(u),i&&a.above?o.insertBefore(u,r.gutter||r.text):o.appendChild(u),lr(a,"redraw")}}function mr(e,t,r,n){if(e.noHScroll){(r.alignable||(r.alignable=[])).push(t);var i=n.wrapperWidth;t.style.left=n.fixedPos+"px",e.coverGutter||(i-=n.gutterTotalWidth,t.style.paddingLeft=n.gutterTotalWidth+"px"),t.style.width=i+"px"}e.coverGutter&&(t.style.zIndex=5,t.style.position="relative",e.noHScroll||(t.style.marginLeft=-n.gutterTotalWidth+"px"))}function yr(e){if(null!=e.height)return e.height;var t=e.doc.cm;if(!t)return 0;if(!D(document.body,e.node)){var r="position: relative;";e.coverGutter&&(r+="margin-left: -"+t.display.gutters.offsetWidth+"px;"),e.noHScroll&&(r+="width: "+t.display.wrapper.clientWidth+"px;"),N(t.display.measure,A("div",[e.node],null,r))}return e.height=e.node.parentNode.offsetHeight}function br(e,t){for(var r=Le(t);r!=e.wrapper;r=r.parentNode)if(!r||1==r.nodeType&&"true"==r.getAttribute("cm-ignore-events")||r.parentNode==e.sizer&&r!=e.mover)return!0}function wr(e){return e.lineSpace.offsetTop}function xr(e){return e.mover.offsetHeight-e.lineSpace.offsetHeight}function Cr(e){if(e.cachedPaddingH)return e.cachedPaddingH;var t=N(e.measure,A("pre","x","CodeMirror-line-like")),r=window.getComputedStyle?window.getComputedStyle(t):t.currentStyle,n={left:parseInt(r.paddingLeft),right:parseInt(r.paddingRight)};return isNaN(n.left)||isNaN(n.right)||(e.cachedPaddingH=n),n}function Sr(e){return G-e.display.nativeBarWidth}function Lr(e){return e.display.scroller.clientWidth-Sr(e)-e.display.barWidth}function kr(e){return e.display.scroller.clientHeight-Sr(e)-e.display.barHeight}function Tr(e,t,r){if(e.line==t)return{map:e.measure.map,cache:e.measure.cache};for(var n=0;n<e.rest.length;n++)if(e.rest[n]==t)return{map:e.measure.maps[n],cache:e.measure.caches[n]};for(var i=0;i<e.rest.length;i++)if(qe(e.rest[i])>r)return{map:e.measure.maps[i],cache:e.measure.caches[i],before:!0}}function Mr(e,t,r,n){return Ar(e,Or(e,t),r,n)}function Nr(e,t){if(t>=e.display.viewFrom&&t<e.display.viewTo)return e.display.view[ln(e,t)];var r=e.display.externalMeasured;return r&&t>=r.lineN&&t<r.lineN+r.size?r:void 0}function Or(e,t){var r=qe(t),n=Nr(e,r);n&&!n.text?n=null:n&&n.changes&&(ar(e,n,r,en(e)),e.curOp.forceUpdate=!0),n||(n=function(e,t){var r=qe(t=zt(t)),n=e.display.externalMeasured=new rr(e.doc,t,r);n.lineN=r;var i=n.built=qt(e,n);return n.text=i.pre,N(e.display.lineMeasure,i.pre),n}(e,t));var i=Tr(n,t,r);return{line:t,view:n,rect:null,map:i.map,cache:i.cache,before:i.before,hasHeights:!1}}function Ar(e,t,r,n,i){t.before&&(r=-1);var o,l=r+(n||"");return t.cache.hasOwnProperty(l)?o=t.cache[l]:(t.rect||(t.rect=t.view.text.getBoundingClientRect()),t.hasHeights||(!function(e,t,r){var n=e.options.lineWrapping,i=n&&Lr(e);if(!t.measure.heights||n&&t.measure.width!=i){var o=t.measure.heights=[];if(n){t.measure.width=i;for(var l=t.text.firstChild.getClientRects(),s=0;s<l.length-1;s++){var a=l[s],u=l[s+1];2<Math.abs(a.bottom-u.bottom)&&o.push((a.bottom+u.top)/2-r.top)}}o.push(r.bottom-r.top)}}(e,t.view,t.rect),t.hasHeights=!0),(o=function(e,t,r,n){var i,o=Hr(t.map,r,n),l=o.node,s=o.start,a=o.end,u=o.collapse;if(3==l.nodeType){for(var c=0;c<4;c++){for(;s&&ie(t.line.text.charAt(o.coverStart+s));)--s;for(;o.coverStart+a<o.coverEnd&&ie(t.line.text.charAt(o.coverStart+a));)++a;if((i=x&&C<9&&0==s&&a==o.coverEnd-o.coverStart?l.parentNode.getBoundingClientRect():Fr(k(l,s,a).getClientRects(),n)).left||i.right||0==s)break;a=s,s-=1,u="right"}x&&C<11&&(i=function(e,t){if(!window.screen||null==screen.logicalXDPI||screen.logicalXDPI==screen.deviceXDPI||!function(e){if(null!=Pe)return Pe;var t=N(e,A("span","x")),r=t.getBoundingClientRect(),n=k(t,0,1).getBoundingClientRect();return Pe=1<Math.abs(r.left-n.left)}(e))return t;var r=screen.logicalXDPI/screen.deviceXDPI,n=screen.logicalYDPI/screen.deviceYDPI;return{left:t.left*r,right:t.right*r,top:t.top*n,bottom:t.bottom*n}}(e.display.measure,i))}else{var h;0<s&&(u=n="right"),i=e.options.lineWrapping&&1<(h=l.getClientRects()).length?h["right"==n?h.length-1:0]:l.getBoundingClientRect()}if(x&&C<9&&!s&&(!i||!i.left&&!i.right)){var f=l.parentNode.getClientRects()[0];i=f?{left:f.left,right:f.left+Jr(e.display),top:f.top,bottom:f.bottom}:Wr}for(var d=i.top-t.rect.top,p=i.bottom-t.rect.top,g=(d+p)/2,v=t.view.measure.heights,m=0;m<v.length-1&&!(g<v[m]);m++);var y=m?v[m-1]:0,b=v[m],w={left:("right"==u?i.right:i.left)-t.rect.left,right:("left"==u?i.left:i.right)-t.rect.left,top:y,bottom:b};i.left||i.right||(w.bogus=!0);e.options.singleCursorHeightPerLine||(w.rtop=d,w.rbottom=p);return w}(e,t,r,n)).bogus||(t.cache[l]=o)),{left:o.left,right:o.right,top:i?o.rtop:o.top,bottom:i?o.rbottom:o.bottom}}var Dr,Wr={left:0,right:0,top:0,bottom:0};function Hr(e,t,r){for(var n,i,o,l,s,a,u=0;u<e.length;u+=3)if(s=e[u],a=e[u+1],t<s?(i=0,o=1,l="left"):t<a?o=(i=t-s)+1:(u==e.length-3||t==a&&e[u+3]>t)&&(i=(o=a-s)-1,a<=t&&(l="right")),null!=i){if(n=e[u+2],s==a&&r==(n.insertLeft?"left":"right")&&(l=r),"left"==r&&0==i)for(;u&&e[u-2]==e[u-3]&&e[u-1].insertLeft;)n=e[2+(u-=3)],l="left";if("right"==r&&i==a-s)for(;u<e.length-3&&e[u+3]==e[u+4]&&!e[u+5].insertLeft;)n=e[(u+=3)+2],l="right";break}return{node:n,start:i,end:o,collapse:l,coverStart:s,coverEnd:a}}function Fr(e,t){var r=Wr;if("left"==t)for(var n=0;n<e.length&&(r=e[n]).left==r.right;n++);else for(var i=e.length-1;0<=i&&(r=e[i]).left==r.right;i--);return r}function Pr(e){if(e.measure&&(e.measure.cache={},e.measure.heights=null,e.rest))for(var t=0;t<e.rest.length;t++)e.measure.caches[t]={}}function Er(e){e.display.externalMeasure=null,M(e.display.lineMeasure);for(var t=0;t<e.display.view.length;t++)Pr(e.display.view[t])}function Ir(e){Er(e),e.display.cachedCharWidth=e.display.cachedTextHeight=e.display.cachedPaddingH=null,e.options.lineWrapping||(e.display.maxLineChanged=!0),e.display.lineNumChars=null}function zr(){return l&&h?-(document.body.getBoundingClientRect().left-parseInt(getComputedStyle(document.body).marginLeft)):window.pageXOffset||(document.documentElement||document.body).scrollLeft}function Rr(){return l&&h?-(document.body.getBoundingClientRect().top-parseInt(getComputedStyle(document.body).marginTop)):window.pageYOffset||(document.documentElement||document.body).scrollTop}function Br(e){var t=0;if(e.widgets)for(var r=0;r<e.widgets.length;++r)e.widgets[r].above&&(t+=yr(e.widgets[r]));return t}function Gr(e,t,r,n,i){if(!i){var o=Br(t);r.top+=o,r.bottom+=o}if("line"==n)return r;n||(n="local");var l=Vt(t);if("local"==n?l+=wr(e.display):l-=e.display.viewOffset,"page"==n||"window"==n){var s=e.display.lineSpace.getBoundingClientRect();l+=s.top+("window"==n?0:Rr());var a=s.left+("window"==n?0:zr());r.left+=a,r.right+=a}return r.top+=l,r.bottom+=l,r}function Ur(e,t,r){if("div"==r)return t;var n=t.left,i=t.top;if("page"==r)n-=zr(),i-=Rr();else if("local"==r||!r){var o=e.display.sizer.getBoundingClientRect();n+=o.left,i+=o.top}var l=e.display.lineSpace.getBoundingClientRect();return{left:n-l.left,top:i-l.top}}function Vr(e,t,r,n,i){return n||(n=Xe(e.doc,t.line)),Gr(e,n,Mr(e,n,t.ch,i),r)}function Kr(n,e,i,o,l,s){function a(e,t){var r=Ar(n,l,e,t?"right":"left",s);return t?r.left=r.right:r.right=r.left,Gr(n,o,r,i)}o=o||Xe(n.doc,e.line),l||(l=Or(n,o));var u=ce(o,n.doc.direction),t=e.ch,r=e.sticky;if(t>=o.text.length?(t=o.text.length,r="before"):t<=0&&(t=0,r="after"),!u)return a("before"==r?t-1:t,"before"==r);function c(e,t,r){return a(r?e-1:e,1==u[t].level!=r)}var h=ae(u,t,r),f=se,d=c(t,h,"before"==r);return null!=f&&(d.other=c(t,f,"before"!=r)),d}function jr(e,t){var r=0;t=st(e.doc,t),e.options.lineWrapping||(r=Jr(e.display)*t.ch);var n=Xe(e.doc,t.line),i=Vt(n)+wr(e.display);return{left:r,right:r,top:i,bottom:i+n.height}}function Xr(e,t,r,n,i){var o=et(e,t,r);return o.xRel=i,n&&(o.outside=n),o}function Yr(e,t,r){var n=e.doc;if((r+=e.display.viewOffset)<0)return Xr(n.first,0,null,-1,-1);var i=Ze(n,r),o=n.first+n.size-1;if(o<i)return Xr(n.first+n.size-1,Xe(n,o).text.length,null,1,1);t<0&&(t=0);for(var l=Xe(n,i);;){var s=Zr(e,l,i,t,r),a=Et(l,s.ch+(0<s.xRel||0<s.outside?1:0));if(!a)return s;var u=a.find(1);if(u.line==i)return u;l=Xe(n,i=u.line)}}function _r(t,e,r,n){n-=Br(e);var i=e.text.length,o=le(function(e){return Ar(t,r,e-1).bottom<=n},i,0);return{begin:o,end:i=le(function(e){return Ar(t,r,e).top>n},o,i)}}function $r(e,t,r,n){return r||(r=Or(e,t)),_r(e,t,r,Gr(e,t,Ar(e,r,n),"line").top)}function qr(e,t,r,n){return!(e.bottom<=r)&&(e.top>r||(n?e.left:e.right)>t)}function Zr(r,e,t,n,i){i-=Vt(e);var o=Or(r,e),l=Br(e),s=0,a=e.text.length,u=!0,c=ce(e,r.doc.direction);if(c){var h=(r.options.lineWrapping?function(e,t,r,n,i,o,l){var s=_r(e,t,n,l),a=s.begin,u=s.end;/\s/.test(t.text.charAt(u-1))&&u--;for(var c=null,h=null,f=0;f<i.length;f++){var d=i[f];if(!(d.from>=u||d.to<=a)){var p=1!=d.level,g=Ar(e,n,p?Math.min(u,d.to)-1:Math.max(a,d.from)).right,v=g<o?o-g+1e9:g-o;(!c||v<h)&&(c=d,h=v)}}c||(c=i[i.length-1]);c.from<a&&(c={from:a,to:c.to,level:c.level});c.to>u&&(c={from:c.from,to:u,level:c.level});return c}:function(n,i,o,l,s,a,u){var e=le(function(e){var t=s[e],r=1!=t.level;return qr(Kr(n,et(o,r?t.to:t.from,r?"before":"after"),"line",i,l),a,u,!0)},0,s.length-1),t=s[e];if(0<e){var r=1!=t.level,c=Kr(n,et(o,r?t.from:t.to,r?"after":"before"),"line",i,l);qr(c,a,u,!0)&&c.top>u&&(t=s[e-1])}return t})(r,e,t,o,c,n,i);s=(u=1!=h.level)?h.from:h.to-1,a=u?h.to:h.from-1}var f,d,p=null,g=null,v=le(function(e){var t=Ar(r,o,e);return t.top+=l,t.bottom+=l,!!qr(t,n,i,!1)&&(t.top<=i&&t.left<=n&&(p=e,g=t),!0)},s,a),m=!1;if(g){var y=n-g.left<g.right-n,b=y==u;v=p+(b?0:1),d=b?"after":"before",f=y?g.left:g.right}else{u||v!=a&&v!=s||v++,d=0==v?"after":v==e.text.length?"before":Ar(r,o,v-(u?1:0)).bottom+l<=i==u?"after":"before";var w=Kr(r,et(t,v,d),"line",e,o);f=w.left,m=i<w.top?-1:i>=w.bottom?1:0}return Xr(t,v=oe(e.text,v,1),d,m,n-f)}function Qr(e){if(null!=e.cachedTextHeight)return e.cachedTextHeight;if(null==Dr){Dr=A("pre",null,"CodeMirror-line-like");for(var t=0;t<49;++t)Dr.appendChild(document.createTextNode("x")),Dr.appendChild(A("br"));Dr.appendChild(document.createTextNode("x"))}N(e.measure,Dr);var r=Dr.offsetHeight/50;return 3<r&&(e.cachedTextHeight=r),M(e.measure),r||1}function Jr(e){if(null!=e.cachedCharWidth)return e.cachedCharWidth;var t=A("span","xxxxxxxxxx"),r=A("pre",[t],"CodeMirror-line-like");N(e.measure,r);var n=t.getBoundingClientRect(),i=(n.right-n.left)/10;return 2<i&&(e.cachedCharWidth=i),i||10}function en(e){for(var t=e.display,r={},n={},i=t.gutters.clientLeft,o=t.gutters.firstChild,l=0;o;o=o.nextSibling,++l){var s=e.display.gutterSpecs[l].className;r[s]=o.offsetLeft+o.clientLeft+i,n[s]=o.clientWidth}return{fixedPos:tn(t),gutterTotalWidth:t.gutters.offsetWidth,gutterLeft:r,gutterWidth:n,wrapperWidth:t.wrapper.clientWidth}}function tn(e){return e.scroller.getBoundingClientRect().left-e.sizer.getBoundingClientRect().left}function rn(n){var i=Qr(n.display),o=n.options.lineWrapping,l=o&&Math.max(5,n.display.scroller.clientWidth/Jr(n.display)-3);return function(e){if(Gt(n.doc,e))return 0;var t=0;if(e.widgets)for(var r=0;r<e.widgets.length;r++)e.widgets[r].height&&(t+=e.widgets[r].height);return o?t+(Math.ceil(e.text.length/l)||1)*i:t+i}}function nn(e){var t=e.doc,r=rn(e);t.iter(function(e){var t=r(e);t!=e.height&&$e(e,t)})}function on(e,t,r,n){var i=e.display;if(!r&&"true"==Le(t).getAttribute("cm-not-content"))return null;var o,l,s=i.lineSpace.getBoundingClientRect();try{o=t.clientX-s.left,l=t.clientY-s.top}catch(t){return null}var a,u=Yr(e,o,l);if(n&&1==u.xRel&&(a=Xe(e.doc,u.line).text).length==u.ch){var c=z(a,a.length,e.options.tabSize)-a.length;u=et(u.line,Math.max(0,Math.round((o-Cr(e.display).left)/Jr(e.display))-c))}return u}function ln(e,t){if(t>=e.display.viewTo)return null;if((t-=e.display.viewFrom)<0)return null;for(var r=e.display.view,n=0;n<r.length;n++)if((t-=r[n].size)<0)return n}function sn(e,t,r,n){null==t&&(t=e.doc.first),null==r&&(r=e.doc.first+e.doc.size),n||(n=0);var i=e.display;if(n&&r<i.viewTo&&(null==i.updateLineNumbers||i.updateLineNumbers>t)&&(i.updateLineNumbers=t),e.curOp.viewChanged=!0,t>=i.viewTo)Ct&&Rt(e.doc,t)<i.viewTo&&un(e);else if(r<=i.viewFrom)Ct&&Bt(e.doc,r+n)>i.viewFrom?un(e):(i.viewFrom+=n,i.viewTo+=n);else if(t<=i.viewFrom&&r>=i.viewTo)un(e);else if(t<=i.viewFrom){var o=cn(e,r,r+n,1);o?(i.view=i.view.slice(o.index),i.viewFrom=o.lineN,i.viewTo+=n):un(e)}else if(r>=i.viewTo){var l=cn(e,t,t,-1);l?(i.view=i.view.slice(0,l.index),i.viewTo=l.lineN):un(e)}else{var s=cn(e,t,t,-1),a=cn(e,r,r+n,1);s&&a?(i.view=i.view.slice(0,s.index).concat(nr(e,s.lineN,a.lineN)).concat(i.view.slice(a.index)),i.viewTo+=n):un(e)}var u=i.externalMeasured;u&&(r<u.lineN?u.lineN+=n:t<u.lineN+u.size&&(i.externalMeasured=null))}function an(e,t,r){e.curOp.viewChanged=!0;var n=e.display,i=e.display.externalMeasured;if(i&&t>=i.lineN&&t<i.lineN+i.size&&(n.externalMeasured=null),!(t<n.viewFrom||t>=n.viewTo)){var o=n.view[ln(e,t)];if(null!=o.node){var l=o.changes||(o.changes=[]);-1==B(l,r)&&l.push(r)}}}function un(e){e.display.viewFrom=e.display.viewTo=e.doc.first,e.display.view=[],e.display.viewOffset=0}function cn(e,t,r,n){var i,o=ln(e,t),l=e.display.view;if(!Ct||r==e.doc.first+e.doc.size)return{index:o,lineN:r};for(var s=e.display.viewFrom,a=0;a<o;a++)s+=l[a].size;if(s!=t){if(0<n){if(o==l.length-1)return null;i=s+l[o].size-t,o++}else i=s-t;t+=i,r+=i}for(;Rt(e.doc,r)!=r;){if(o==(n<0?0:l.length-1))return null;r+=n*l[o-(n<0?1:0)].size,o+=n}return{index:o,lineN:r}}function hn(e){for(var t=e.display.view,r=0,n=0;n<t.length;n++){var i=t[n];i.hidden||i.node&&!i.changes||++r}return r}function fn(e){e.display.input.showSelection(e.display.input.prepareSelection())}function dn(e,t){void 0===t&&(t=!0);for(var r=e.doc,n={},i=n.cursors=document.createDocumentFragment(),o=n.selection=document.createDocumentFragment(),l=0;l<r.sel.ranges.length;l++)if(t||l!=r.sel.primIndex){var s=r.sel.ranges[l];if(!(s.from().line>=e.display.viewTo||s.to().line<e.display.viewFrom)){var a=s.empty();(a||e.options.showCursorWhenSelecting)&&pn(e,s.head,i),a||vn(e,s,o)}}return n}function pn(e,t,r){var n=Kr(e,t,"div",null,null,!e.options.singleCursorHeightPerLine),i=r.appendChild(A("div"," ","CodeMirror-cursor"));if(i.style.left=n.left+"px",i.style.top=n.top+"px",i.style.height=Math.max(0,n.bottom-n.top)*e.options.cursorHeight+"px",n.other){var o=r.appendChild(A("div"," ","CodeMirror-cursor CodeMirror-secondarycursor"));o.style.display="",o.style.left=n.other.left+"px",o.style.top=n.other.top+"px",o.style.height=.85*(n.other.bottom-n.other.top)+"px"}}function gn(e,t){return e.top-t.top||e.left-t.left}function vn(l,e,t){var r=l.display,n=l.doc,i=document.createDocumentFragment(),o=Cr(l.display),T=o.left,M=Math.max(r.sizerWidth,Lr(l)-r.sizer.offsetLeft)-o.right,N="ltr"==n.direction;function O(e,t,r,n){t<0&&(t=0),t=Math.round(t),n=Math.round(n),i.appendChild(A("div",null,"CodeMirror-selected","position: absolute; left: "+e+"px;\n                             top: "+t+"px; width: "+(null==r?M-e:r)+"px;\n                             height: "+(n-t)+"px"))}function s(r,y,b){var w,x,o=Xe(n,r),C=o.text.length;function S(e,t){return Vr(l,et(r,e),"div",o,t)}function L(e,t,r){var n=$r(l,o,null,e),i="ltr"==t==("after"==r)?"left":"right";return S("after"==r?n.begin:n.end-(/\s/.test(o.text.charAt(n.end-1))?2:1),i)[i]}var k=ce(o,n.direction);return function(e,t,r,n){if(!e)return n(t,r,"ltr",0);for(var i=!1,o=0;o<e.length;++o){var l=e[o];(l.from<r&&l.to>t||t==r&&l.to==t)&&(n(Math.max(l.from,t),Math.min(l.to,r),1==l.level?"rtl":"ltr",o),i=!0)}i||n(t,r,"ltr")}(k,y||0,null==b?C:b,function(e,t,r,n){var i="ltr"==r,o=S(e,i?"left":"right"),l=S(t-1,i?"right":"left"),s=null==y&&0==e,a=null==b&&t==C,u=0==n,c=!k||n==k.length-1;if(l.top-o.top<=3){var h=(N?a:s)&&c,f=(N?s:a)&&u?T:(i?o:l).left,d=h?M:(i?l:o).right;O(f,o.top,d-f,o.bottom)}else{var p,g,v,m;i?(p=N&&s&&u?T:o.left,g=N?M:L(e,r,"before"),v=N?T:L(t,r,"after"),m=N&&a&&c?M:l.right):(p=N?L(e,r,"before"):T,g=!N&&s&&u?M:o.right,v=!N&&a&&c?T:l.left,m=N?L(t,r,"after"):M),O(p,o.top,g-p,o.bottom),o.bottom<l.top&&O(T,o.bottom,null,l.top),O(v,l.top,m-v,l.bottom)}(!w||gn(o,w)<0)&&(w=o),gn(l,w)<0&&(w=l),(!x||gn(o,x)<0)&&(x=o),gn(l,x)<0&&(x=l)}),{start:w,end:x}}var a=e.from(),u=e.to();if(a.line==u.line)s(a.line,a.ch,u.ch);else{var c=Xe(n,a.line),h=Xe(n,u.line),f=zt(c)==zt(h),d=s(a.line,a.ch,f?c.text.length+1:null).end,p=s(u.line,f?0:null,u.ch).start;f&&(d.top<p.top-2?(O(d.right,d.top,null,d.bottom),O(T,p.top,p.left,p.bottom)):O(d.right,d.top,p.left-d.right,d.bottom)),d.bottom<p.top&&O(T,d.bottom,null,p.top)}t.appendChild(i)}function mn(e){if(e.state.focused){var t=e.display;clearInterval(t.blinker);var r=!0;t.cursorDiv.style.visibility="",0<e.options.cursorBlinkRate?t.blinker=setInterval(function(){return t.cursorDiv.style.visibility=(r=!r)?"":"hidden"},e.options.cursorBlinkRate):e.options.cursorBlinkRate<0&&(t.cursorDiv.style.visibility="hidden")}}function yn(e){e.state.focused||(e.display.input.focus(),wn(e))}function bn(e){e.state.delayingBlurEvent=!0,setTimeout(function(){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1,xn(e))},100)}function wn(e,t){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1),"nocursor"!=e.options.readOnly&&(e.state.focused||(ge(e,"focus",e,t),e.state.focused=!0,H(e.display.wrapper,"CodeMirror-focused"),e.curOp||e.display.selForContextMenu==e.doc.sel||(e.display.input.reset(),b&&setTimeout(function(){return e.display.input.reset(!0)},20)),e.display.input.receivedFocus()),mn(e))}function xn(e,t){e.state.delayingBlurEvent||(e.state.focused&&(ge(e,"blur",e,t),e.state.focused=!1,T(e.display.wrapper,"CodeMirror-focused")),clearInterval(e.display.blinker),setTimeout(function(){e.state.focused||(e.display.shift=!1)},150))}function Cn(e){for(var t=e.display,r=t.lineDiv.offsetTop,n=0;n<t.view.length;n++){var i=t.view[n],o=e.options.lineWrapping,l=void 0,s=0;if(!i.hidden){if(x&&C<8){var a=i.node.offsetTop+i.node.offsetHeight;l=a-r,r=a}else{var u=i.node.getBoundingClientRect();l=u.bottom-u.top,!o&&i.text.firstChild&&(s=i.text.firstChild.getBoundingClientRect().right-u.left-1)}var c=i.line.height-l;if((.005<c||c<-.005)&&($e(i.line,l),Sn(i.line),i.rest))for(var h=0;h<i.rest.length;h++)Sn(i.rest[h]);if(s>e.display.sizerWidth){var f=Math.ceil(s/Jr(e.display));f>e.display.maxLineLength&&(e.display.maxLineLength=f,e.display.maxLine=i.line,e.display.maxLineChanged=!0)}}}}function Sn(e){if(e.widgets)for(var t=0;t<e.widgets.length;++t){var r=e.widgets[t],n=r.node.parentNode;n&&(r.height=n.offsetHeight)}}function Ln(e,t,r){var n=r&&null!=r.top?Math.max(0,r.top):e.scroller.scrollTop;n=Math.floor(n-wr(e));var i=r&&null!=r.bottom?r.bottom:n+e.wrapper.clientHeight,o=Ze(t,n),l=Ze(t,i);if(r&&r.ensure){var s=r.ensure.from.line,a=r.ensure.to.line;s<o?l=Ze(t,Vt(Xe(t,o=s))+e.wrapper.clientHeight):Math.min(a,t.lastLine())>=l&&(o=Ze(t,Vt(Xe(t,a))-e.wrapper.clientHeight),l=a)}return{from:o,to:Math.max(l,o+1)}}function kn(e,t){var r=e.display,n=Qr(e.display);t.top<0&&(t.top=0);var i=e.curOp&&null!=e.curOp.scrollTop?e.curOp.scrollTop:r.scroller.scrollTop,o=kr(e),l={};t.bottom-t.top>o&&(t.bottom=t.top+o);var s=e.doc.height+xr(r),a=t.top<n,u=t.bottom>s-n;if(t.top<i)l.scrollTop=a?0:t.top;else if(t.bottom>i+o){var c=Math.min(t.top,(u?s:t.bottom)-o);c!=i&&(l.scrollTop=c)}var h=e.curOp&&null!=e.curOp.scrollLeft?e.curOp.scrollLeft:r.scroller.scrollLeft,f=Lr(e)-(e.options.fixedGutter?r.gutters.offsetWidth:0),d=t.right-t.left>f;return d&&(t.right=t.left+f),t.left<10?l.scrollLeft=0:t.left<h?l.scrollLeft=Math.max(0,t.left-(d?0:10)):t.right>f+h-3&&(l.scrollLeft=t.right+(d?0:10)-f),l}function Tn(e,t){null!=t&&(On(e),e.curOp.scrollTop=(null==e.curOp.scrollTop?e.doc.scrollTop:e.curOp.scrollTop)+t)}function Mn(e){On(e);var t=e.getCursor();e.curOp.scrollToPos={from:t,to:t,margin:e.options.cursorScrollMargin}}function Nn(e,t,r){null==t&&null==r||On(e),null!=t&&(e.curOp.scrollLeft=t),null!=r&&(e.curOp.scrollTop=r)}function On(e){var t=e.curOp.scrollToPos;t&&(e.curOp.scrollToPos=null,An(e,jr(e,t.from),jr(e,t.to),t.margin))}function An(e,t,r,n){var i=kn(e,{left:Math.min(t.left,r.left),top:Math.min(t.top,r.top)-n,right:Math.max(t.right,r.right),bottom:Math.max(t.bottom,r.bottom)+n});Nn(e,i.scrollLeft,i.scrollTop)}function Dn(e,t){Math.abs(e.doc.scrollTop-t)<2||(g||ni(e,{top:t}),Wn(e,t,!0),g&&ni(e),Qn(e,100))}function Wn(e,t,r){t=Math.min(e.display.scroller.scrollHeight-e.display.scroller.clientHeight,t),(e.display.scroller.scrollTop!=t||r)&&(e.doc.scrollTop=t,e.display.scrollbars.setScrollTop(t),e.display.scroller.scrollTop!=t&&(e.display.scroller.scrollTop=t))}function Hn(e,t,r,n){t=Math.min(t,e.display.scroller.scrollWidth-e.display.scroller.clientWidth),(r?t==e.doc.scrollLeft:Math.abs(e.doc.scrollLeft-t)<2)&&!n||(e.doc.scrollLeft=t,li(e),e.display.scroller.scrollLeft!=t&&(e.display.scroller.scrollLeft=t),e.display.scrollbars.setScrollLeft(t))}function Fn(e){var t=e.display,r=t.gutters.offsetWidth,n=Math.round(e.doc.height+xr(e.display));return{clientHeight:t.scroller.clientHeight,viewHeight:t.wrapper.clientHeight,scrollWidth:t.scroller.scrollWidth,clientWidth:t.scroller.clientWidth,viewWidth:t.wrapper.clientWidth,barLeft:e.options.fixedGutter?r:0,docHeight:n,scrollHeight:n+Sr(e)+t.barHeight,nativeBarWidth:t.nativeBarWidth,gutterWidth:r}}var Pn=function(e,t,r){this.cm=r;var n=this.vert=A("div",[A("div",null,null,"min-width: 1px")],"CodeMirror-vscrollbar"),i=this.horiz=A("div",[A("div",null,null,"height: 100%; min-height: 1px")],"CodeMirror-hscrollbar");n.tabIndex=i.tabIndex=-1,e(n),e(i),fe(n,"scroll",function(){n.clientHeight&&t(n.scrollTop,"vertical")}),fe(i,"scroll",function(){i.clientWidth&&t(i.scrollLeft,"horizontal")}),this.checkedZeroWidth=!1,x&&C<8&&(this.horiz.style.minHeight=this.vert.style.minWidth="18px")};Pn.prototype.update=function(e){var t=e.scrollWidth>e.clientWidth+1,r=e.scrollHeight>e.clientHeight+1,n=e.nativeBarWidth;if(r){this.vert.style.display="block",this.vert.style.bottom=t?n+"px":"0";var i=e.viewHeight-(t?n:0);this.vert.firstChild.style.height=Math.max(0,e.scrollHeight-e.clientHeight+i)+"px"}else this.vert.style.display="",this.vert.firstChild.style.height="0";if(t){this.horiz.style.display="block",this.horiz.style.right=r?n+"px":"0",this.horiz.style.left=e.barLeft+"px";var o=e.viewWidth-e.barLeft-(r?n:0);this.horiz.firstChild.style.width=Math.max(0,e.scrollWidth-e.clientWidth+o)+"px"}else this.horiz.style.display="",this.horiz.firstChild.style.width="0";return!this.checkedZeroWidth&&0<e.clientHeight&&(0==n&&this.zeroWidthHack(),this.checkedZeroWidth=!0),{right:r?n:0,bottom:t?n:0}},Pn.prototype.setScrollLeft=function(e){this.horiz.scrollLeft!=e&&(this.horiz.scrollLeft=e),this.disableHoriz&&this.enableZeroWidthBar(this.horiz,this.disableHoriz,"horiz")},Pn.prototype.setScrollTop=function(e){this.vert.scrollTop!=e&&(this.vert.scrollTop=e),this.disableVert&&this.enableZeroWidthBar(this.vert,this.disableVert,"vert")},Pn.prototype.zeroWidthHack=function(){var e=w&&!s?"12px":"18px";this.horiz.style.height=this.vert.style.width=e,this.horiz.style.pointerEvents=this.vert.style.pointerEvents="none",this.disableHoriz=new R,this.disableVert=new R},Pn.prototype.enableZeroWidthBar=function(r,n,i){r.style.pointerEvents="auto",n.set(1e3,function e(){var t=r.getBoundingClientRect();("vert"==i?document.elementFromPoint(t.right-1,(t.top+t.bottom)/2):document.elementFromPoint((t.right+t.left)/2,t.bottom-1))!=r?r.style.pointerEvents="none":n.set(1e3,e)})},Pn.prototype.clear=function(){var e=this.horiz.parentNode;e.removeChild(this.horiz),e.removeChild(this.vert)};var En=function(){};function In(e,t){t||(t=Fn(e));var r=e.display.barWidth,n=e.display.barHeight;zn(e,t);for(var i=0;i<4&&r!=e.display.barWidth||n!=e.display.barHeight;i++)r!=e.display.barWidth&&e.options.lineWrapping&&Cn(e),zn(e,Fn(e)),r=e.display.barWidth,n=e.display.barHeight}function zn(e,t){var r=e.display,n=r.scrollbars.update(t);r.sizer.style.paddingRight=(r.barWidth=n.right)+"px",r.sizer.style.paddingBottom=(r.barHeight=n.bottom)+"px",r.heightForcer.style.borderBottom=n.bottom+"px solid transparent",n.right&&n.bottom?(r.scrollbarFiller.style.display="block",r.scrollbarFiller.style.height=n.bottom+"px",r.scrollbarFiller.style.width=n.right+"px"):r.scrollbarFiller.style.display="",n.bottom&&e.options.coverGutterNextToScrollbar&&e.options.fixedGutter?(r.gutterFiller.style.display="block",r.gutterFiller.style.height=n.bottom+"px",r.gutterFiller.style.width=t.gutterWidth+"px"):r.gutterFiller.style.display=""}En.prototype.update=function(){return{bottom:0,right:0}},En.prototype.setScrollLeft=function(){},En.prototype.setScrollTop=function(){},En.prototype.clear=function(){};var Rn={"native":Pn,"null":En};function Bn(r){r.display.scrollbars&&(r.display.scrollbars.clear(),r.display.scrollbars.addClass&&T(r.display.wrapper,r.display.scrollbars.addClass)),r.display.scrollbars=new Rn[r.options.scrollbarStyle](function(e){r.display.wrapper.insertBefore(e,r.display.scrollbarFiller),fe(e,"mousedown",function(){r.state.focused&&setTimeout(function(){return r.display.input.focus()},0)}),e.setAttribute("cm-not-content","true")},function(e,t){"horizontal"==t?Hn(r,e):Dn(r,e)},r),r.display.scrollbars.addClass&&H(r.display.wrapper,r.display.scrollbars.addClass)}var Gn=0;function Un(e){var t;e.curOp={cm:e,viewChanged:!1,startHeight:e.doc.height,forceUpdate:!1,updateInput:0,typing:!1,changeObjs:null,cursorActivityHandlers:null,cursorActivityCalled:0,selectionChanged:!1,updateMaxLine:!1,scrollLeft:null,scrollTop:null,scrollToPos:null,focus:!1,id:++Gn},t=e.curOp,ir?ir.ops.push(t):t.ownsGroup=ir={ops:[t],delayedCallbacks:[]}}function Vn(e){var t=e.curOp;t&&function(e,t){var r=e.ownsGroup;if(r)try{!function(e){var t=e.delayedCallbacks,r=0;do{for(;r<t.length;r++)t[r].call(null);for(var n=0;n<e.ops.length;n++){var i=e.ops[n];if(i.cursorActivityHandlers)for(;i.cursorActivityCalled<i.cursorActivityHandlers.length;)i.cursorActivityHandlers[i.cursorActivityCalled++].call(null,i.cm)}}while(r<t.length)}(r)}finally{ir=null,t(r)}}(t,function(e){for(var t=0;t<e.ops.length;t++)e.ops[t].cm.curOp=null;!function(e){for(var t=e.ops,r=0;r<t.length;r++)Kn(t[r]);for(var n=0;n<t.length;n++)(i=t[n]).updatedDisplay=i.mustUpdate&&ti(i.cm,i.update);var i;for(var o=0;o<t.length;o++)jn(t[o]);for(var l=0;l<t.length;l++)Xn(t[l]);for(var s=0;s<t.length;s++)Yn(t[s])}(e)})}function Kn(e){var t,r,n=e.cm,i=n.display;!(r=(t=n).display).scrollbarsClipped&&r.scroller.offsetWidth&&(r.nativeBarWidth=r.scroller.offsetWidth-r.scroller.clientWidth,r.heightForcer.style.height=Sr(t)+"px",r.sizer.style.marginBottom=-r.nativeBarWidth+"px",r.sizer.style.borderRightWidth=Sr(t)+"px",r.scrollbarsClipped=!0),e.updateMaxLine&&jt(n),e.mustUpdate=e.viewChanged||e.forceUpdate||null!=e.scrollTop||e.scrollToPos&&(e.scrollToPos.from.line<i.viewFrom||e.scrollToPos.to.line>=i.viewTo)||i.maxLineChanged&&n.options.lineWrapping,e.update=e.mustUpdate&&new ei(n,e.mustUpdate&&{top:e.scrollTop,ensure:e.scrollToPos},e.forceUpdate)}function jn(e){var t=e.cm,r=t.display;e.updatedDisplay&&Cn(t),e.barMeasure=Fn(t),r.maxLineChanged&&!t.options.lineWrapping&&(e.adjustWidthTo=Mr(t,r.maxLine,r.maxLine.text.length).left+3,t.display.sizerWidth=e.adjustWidthTo,e.barMeasure.scrollWidth=Math.max(r.scroller.clientWidth,r.sizer.offsetLeft+e.adjustWidthTo+Sr(t)+t.display.barWidth),e.maxScrollLeft=Math.max(0,r.sizer.offsetLeft+e.adjustWidthTo-Lr(t))),(e.updatedDisplay||e.selectionChanged)&&(e.preparedSelection=r.input.prepareSelection())}function Xn(e){var t=e.cm;null!=e.adjustWidthTo&&(t.display.sizer.style.minWidth=e.adjustWidthTo+"px",e.maxScrollLeft<t.doc.scrollLeft&&Hn(t,Math.min(t.display.scroller.scrollLeft,e.maxScrollLeft),!0),t.display.maxLineChanged=!1);var r=e.focus&&e.focus==W();e.preparedSelection&&t.display.input.showSelection(e.preparedSelection,r),(e.updatedDisplay||e.startHeight!=t.doc.height)&&In(t,e.barMeasure),e.updatedDisplay&&oi(t,e.barMeasure),e.selectionChanged&&mn(t),t.state.focused&&e.updateInput&&t.display.input.reset(e.typing),r&&yn(e.cm)}function Yn(e){var t=e.cm,r=t.display,n=t.doc;(e.updatedDisplay&&ri(t,e.update),null==r.wheelStartX||null==e.scrollTop&&null==e.scrollLeft&&!e.scrollToPos||(r.wheelStartX=r.wheelStartY=null),null!=e.scrollTop&&Wn(t,e.scrollTop,e.forceScroll),null!=e.scrollLeft&&Hn(t,e.scrollLeft,!0,!0),e.scrollToPos)&&function(e,t){if(!ve(e,"scrollCursorIntoView")){var r=e.display,n=r.sizer.getBoundingClientRect(),i=null;if(t.top+n.top<0?i=!0:t.bottom+n.top>(window.innerHeight||document.documentElement.clientHeight)&&(i=!1),null!=i&&!u){var o=A("div","​",null,"position: absolute;\n                         top: "+(t.top-r.viewOffset-wr(e.display))+"px;\n                         height: "+(t.bottom-t.top+Sr(e)+r.barHeight)+"px;\n                         left: "+t.left+"px; width: "+Math.max(2,t.right-t.left)+"px;");e.display.lineSpace.appendChild(o),o.scrollIntoView(i),e.display.lineSpace.removeChild(o)}}}(t,function(e,t,r,n){var i;null==n&&(n=0),e.options.lineWrapping||t!=r||(r="before"==(t=t.ch?et(t.line,"before"==t.sticky?t.ch-1:t.ch,"after"):t).sticky?et(t.line,t.ch+1,"before"):t);for(var o=0;o<5;o++){var l=!1,s=Kr(e,t),a=r&&r!=t?Kr(e,r):s,u=kn(e,i={left:Math.min(s.left,a.left),top:Math.min(s.top,a.top)-n,right:Math.max(s.left,a.left),bottom:Math.max(s.bottom,a.bottom)+n}),c=e.doc.scrollTop,h=e.doc.scrollLeft;if(null!=u.scrollTop&&(Dn(e,u.scrollTop),1<Math.abs(e.doc.scrollTop-c)&&(l=!0)),null!=u.scrollLeft&&(Hn(e,u.scrollLeft),1<Math.abs(e.doc.scrollLeft-h)&&(l=!0)),!l)break}return i}(t,st(n,e.scrollToPos.from),st(n,e.scrollToPos.to),e.scrollToPos.margin));var i=e.maybeHiddenMarkers,o=e.maybeUnhiddenMarkers;if(i)for(var l=0;l<i.length;++l)i[l].lines.length||ge(i[l],"hide");if(o)for(var s=0;s<o.length;++s)o[s].lines.length&&ge(o[s],"unhide");r.wrapper.offsetHeight&&(n.scrollTop=t.display.scroller.scrollTop),e.changeObjs&&ge(t,"changes",t,e.changeObjs),e.update&&e.update.finish()}function _n(e,t){if(e.curOp)return t();Un(e);try{return t()}finally{Vn(e)}}function $n(e,t){return function(){if(e.curOp)return t.apply(e,arguments);Un(e);try{return t.apply(e,arguments)}finally{Vn(e)}}}function qn(e){return function(){if(this.curOp)return e.apply(this,arguments);Un(this);try{return e.apply(this,arguments)}finally{Vn(this)}}}function Zn(t){return function(){var e=this.cm;if(!e||e.curOp)return t.apply(this,arguments);Un(e);try{return t.apply(this,arguments)}finally{Vn(e)}}}function Qn(e,t){e.doc.highlightFrontier<e.display.viewTo&&e.state.highlight.set(t,E(Jn,e))}function Jn(a){var u=a.doc;if(!(u.highlightFrontier>=a.display.viewTo)){var c=+new Date+a.options.workTime,h=dt(a,u.highlightFrontier),f=[];u.iter(h.line,Math.min(u.first+u.size,a.display.viewTo+500),function(e){if(h.line>=a.display.viewFrom){var t=e.styles,r=e.text.length>a.options.maxHighlightLength?Ue(u.mode,h.state):null,n=ht(a,e,h,!0);r&&(h.state=r),e.styles=n.styles;var i=e.styleClasses,o=n.classes;o?e.styleClasses=o:i&&(e.styleClasses=null);for(var l=!t||t.length!=e.styles.length||i!=o&&(!i||!o||i.bgClass!=o.bgClass||i.textClass!=o.textClass),s=0;!l&&s<t.length;++s)l=t[s]!=e.styles[s];l&&f.push(h.line),e.stateAfter=h.save(),h.nextLine()}else e.text.length<=a.options.maxHighlightLength&&pt(a,e.text,h),e.stateAfter=h.line%5==0?h.save():null,h.nextLine();if(+new Date>c)return Qn(a,a.options.workDelay),!0}),u.highlightFrontier=h.line,u.modeFrontier=Math.max(u.modeFrontier,h.line),f.length&&_n(a,function(){for(var e=0;e<f.length;e++)an(a,f[e],"text")})}}var ei=function(e,t,r){var n=e.display;this.viewport=t,this.visible=Ln(n,e.doc,t),this.editorIsHidden=!n.wrapper.offsetWidth,this.wrapperHeight=n.wrapper.clientHeight,this.wrapperWidth=n.wrapper.clientWidth,this.oldDisplayWidth=Lr(e),this.force=r,this.dims=en(e),this.events=[]};function ti(e,t){var r=e.display,n=e.doc;if(t.editorIsHidden)return un(e),!1;if(!t.force&&t.visible.from>=r.viewFrom&&t.visible.to<=r.viewTo&&(null==r.updateLineNumbers||r.updateLineNumbers>=r.viewTo)&&r.renderedView==r.view&&0==hn(e))return!1;si(e)&&(un(e),t.dims=en(e));var i=n.first+n.size,o=Math.max(t.visible.from-e.options.viewportMargin,n.first),l=Math.min(i,t.visible.to+e.options.viewportMargin);r.viewFrom<o&&o-r.viewFrom<20&&(o=Math.max(n.first,r.viewFrom)),r.viewTo>l&&r.viewTo-l<20&&(l=Math.min(i,r.viewTo)),Ct&&(o=Rt(e.doc,o),l=Bt(e.doc,l));var s,a,u,c,h=o!=r.viewFrom||l!=r.viewTo||r.lastWrapHeight!=t.wrapperHeight||r.lastWrapWidth!=t.wrapperWidth;a=o,u=l,0==(c=(s=e).display).view.length||a>=c.viewTo||u<=c.viewFrom?(c.view=nr(s,a,u),c.viewFrom=a):(c.viewFrom>a?c.view=nr(s,a,c.viewFrom).concat(c.view):c.viewFrom<a&&(c.view=c.view.slice(ln(s,a))),c.viewFrom=a,c.viewTo<u?c.view=c.view.concat(nr(s,c.viewTo,u)):c.viewTo>u&&(c.view=c.view.slice(0,ln(s,u)))),c.viewTo=u,r.viewOffset=Vt(Xe(e.doc,r.viewFrom)),e.display.mover.style.top=r.viewOffset+"px";var f=hn(e);if(!h&&0==f&&!t.force&&r.renderedView==r.view&&(null==r.updateLineNumbers||r.updateLineNumbers>=r.viewTo))return!1;var d=function(e){if(e.hasFocus())return null;var t=W();if(!t||!D(e.display.lineDiv,t))return null;var r={activeElt:t};if(window.getSelection){var n=window.getSelection();n.anchorNode&&n.extend&&D(e.display.lineDiv,n.anchorNode)&&(r.anchorNode=n.anchorNode,r.anchorOffset=n.anchorOffset,r.focusNode=n.focusNode,r.focusOffset=n.focusOffset)}return r}(e);return 4<f&&(r.lineDiv.style.display="none"),function(r,e,t){var n=r.display,i=r.options.lineNumbers,o=n.lineDiv,l=o.firstChild;function s(e){var t=e.nextSibling;return b&&w&&r.display.currentWheelTarget==e?e.style.display="none":e.parentNode.removeChild(e),t}for(var a=n.view,u=n.viewFrom,c=0;c<a.length;c++){var h=a[c];if(h.hidden);else if(h.node&&h.node.parentNode==o){for(;l!=h.node;)l=s(l);var f=i&&null!=e&&e<=u&&h.lineNumber;h.changes&&(-1<B(h.changes,"gutter")&&(f=!1),ar(r,h,u,t)),f&&(M(h.lineNumber),h.lineNumber.appendChild(document.createTextNode(Je(r.options,u)))),l=h.node.nextSibling}else{var d=(v=u,m=t,void 0,y=cr(p=r,g=h),g.text=g.node=y.pre,y.bgClass&&(g.bgClass=y.bgClass),y.textClass&&(g.textClass=y.textClass),fr(p,g),dr(p,g,v,m),gr(p,g,m),g.node);o.insertBefore(d,l)}u+=h.size}var p,g,v,m,y;for(;l;)l=s(l)}(e,r.updateLineNumbers,t.dims),4<f&&(r.lineDiv.style.display=""),r.renderedView=r.view,function(e){if(e&&e.activeElt&&e.activeElt!=W()&&(e.activeElt.focus(),e.anchorNode&&D(document.body,e.anchorNode)&&D(document.body,e.focusNode))){var t=window.getSelection(),r=document.createRange();r.setEnd(e.anchorNode,e.anchorOffset),r.collapse(!1),t.removeAllRanges(),t.addRange(r),t.extend(e.focusNode,e.focusOffset)}}(d),M(r.cursorDiv),M(r.selectionDiv),r.gutters.style.height=r.sizer.style.minHeight=0,h&&(r.lastWrapHeight=t.wrapperHeight,r.lastWrapWidth=t.wrapperWidth,Qn(e,400)),!(r.updateLineNumbers=null)}function ri(e,t){for(var r=t.viewport,n=!0;(n&&e.options.lineWrapping&&t.oldDisplayWidth!=Lr(e)||(r&&null!=r.top&&(r={top:Math.min(e.doc.height+xr(e.display)-kr(e),r.top)}),t.visible=Ln(e.display,e.doc,r),!(t.visible.from>=e.display.viewFrom&&t.visible.to<=e.display.viewTo)))&&ti(e,t);n=!1){Cn(e);var i=Fn(e);fn(e),In(e,i),oi(e,i),t.force=!1}t.signal(e,"update",e),e.display.viewFrom==e.display.reportedViewFrom&&e.display.viewTo==e.display.reportedViewTo||(t.signal(e,"viewportChange",e,e.display.viewFrom,e.display.viewTo),e.display.reportedViewFrom=e.display.viewFrom,e.display.reportedViewTo=e.display.viewTo)}function ni(e,t){var r=new ei(e,t);if(ti(e,r)){Cn(e),ri(e,r);var n=Fn(e);fn(e),In(e,n),oi(e,n),r.finish()}}function ii(e){var t=e.gutters.offsetWidth;e.sizer.style.marginLeft=t+"px"}function oi(e,t){e.display.sizer.style.minHeight=t.docHeight+"px",e.display.heightForcer.style.top=t.docHeight+"px",e.display.gutters.style.height=t.docHeight+e.display.barHeight+Sr(e)+"px"}function li(e){var t=e.display,r=t.view;if(t.alignWidgets||t.gutters.firstChild&&e.options.fixedGutter){for(var n=tn(t)-t.scroller.scrollLeft+e.doc.scrollLeft,i=t.gutters.offsetWidth,o=n+"px",l=0;l<r.length;l++)if(!r[l].hidden){e.options.fixedGutter&&(r[l].gutter&&(r[l].gutter.style.left=o),r[l].gutterBackground&&(r[l].gutterBackground.style.left=o));var s=r[l].alignable;if(s)for(var a=0;a<s.length;a++)s[a].style.left=o}e.options.fixedGutter&&(t.gutters.style.left=n+i+"px")}}function si(e){if(!e.options.lineNumbers)return!1;var t=e.doc,r=Je(e.options,t.first+t.size-1),n=e.display;if(r.length!=n.lineNumChars){var i=n.measure.appendChild(A("div",[A("div",r)],"CodeMirror-linenumber CodeMirror-gutter-elt")),o=i.firstChild.offsetWidth,l=i.offsetWidth-o;return n.lineGutter.style.width="",n.lineNumInnerWidth=Math.max(o,n.lineGutter.offsetWidth-l)+1,n.lineNumWidth=n.lineNumInnerWidth+l,n.lineNumChars=n.lineNumInnerWidth?r.length:-1,n.lineGutter.style.width=n.lineNumWidth+"px",ii(e.display),!0}return!1}function ai(e,t){for(var r=[],n=!1,i=0;i<e.length;i++){var o=e[i],l=null;if("string"!=typeof o&&(l=o.style,o=o.className),"CodeMirror-linenumbers"==o){if(!t)continue;n=!0}r.push({className:o,style:l})}return t&&!n&&r.push({className:"CodeMirror-linenumbers",style:null}),r}function ui(e){var t=e.gutters,r=e.gutterSpecs;M(t),e.lineGutter=null;for(var n=0;n<r.length;++n){var i=r[n],o=i.className,l=i.style,s=t.appendChild(A("div",null,"CodeMirror-gutter "+o));l&&(s.style.cssText=l),"CodeMirror-linenumbers"==o&&((e.lineGutter=s).style.width=(e.lineNumWidth||1)+"px")}t.style.display=r.length?"":"none",ii(e)}function ci(e){ui(e.display),sn(e),li(e)}ei.prototype.signal=function(e,t){ye(e,t)&&this.events.push(arguments)},ei.prototype.finish=function(){for(var e=0;e<this.events.length;e++)ge.apply(null,this.events[e])};var hi=0,fi=null;function di(e){var t=e.wheelDeltaX,r=e.wheelDeltaY;return null==t&&e.detail&&e.axis==e.HORIZONTAL_AXIS&&(t=e.detail),null==r&&e.detail&&e.axis==e.VERTICAL_AXIS?r=e.detail:null==r&&(r=e.wheelDelta),{x:t,y:r}}function pi(e){var t=di(e);return t.x*=fi,t.y*=fi,t}function gi(e,t){var r=di(t),n=r.x,i=r.y,o=e.display,l=o.scroller,s=l.scrollWidth>l.clientWidth,a=l.scrollHeight>l.clientHeight;if(n&&s||i&&a){if(i&&w&&b)e:for(var u=t.target,c=o.view;u!=l;u=u.parentNode)for(var h=0;h<c.length;h++)if(c[h].node==u){e.display.currentWheelTarget=u;break e}if(n&&!g&&!v&&null!=fi)return i&&a&&Dn(e,Math.max(0,l.scrollTop+i*fi)),Hn(e,Math.max(0,l.scrollLeft+n*fi)),(!i||i&&a)&&we(t),void(o.wheelStartX=null);if(i&&null!=fi){var f=i*fi,d=e.doc.scrollTop,p=d+o.wrapper.clientHeight;f<0?d=Math.max(0,d+f-50):p=Math.min(e.doc.height,p+f+50),ni(e,{top:d,bottom:p})}hi<20&&(null==o.wheelStartX?(o.wheelStartX=l.scrollLeft,o.wheelStartY=l.scrollTop,o.wheelDX=n,o.wheelDY=i,setTimeout(function(){if(null!=o.wheelStartX){var e=l.scrollLeft-o.wheelStartX,t=l.scrollTop-o.wheelStartY,r=t&&o.wheelDY&&t/o.wheelDY||e&&o.wheelDX&&e/o.wheelDX;o.wheelStartX=o.wheelStartY=null,r&&(fi=(fi*hi+r)/(hi+1),++hi)}},200)):(o.wheelDX+=n,o.wheelDY+=i))}}x?fi=-.53:g?fi=15:l?fi=-.7:a&&(fi=-1/3);var vi=function(e,t){this.ranges=e,this.primIndex=t};vi.prototype.primary=function(){return this.ranges[this.primIndex]},vi.prototype.equals=function(e){if(e==this)return!0;if(e.primIndex!=this.primIndex||e.ranges.length!=this.ranges.length)return!1;for(var t=0;t<this.ranges.length;t++){var r=this.ranges[t],n=e.ranges[t];if(!rt(r.anchor,n.anchor)||!rt(r.head,n.head))return!1}return!0},vi.prototype.deepCopy=function(){for(var e=[],t=0;t<this.ranges.length;t++)e[t]=new mi(nt(this.ranges[t].anchor),nt(this.ranges[t].head));return new vi(e,this.primIndex)},vi.prototype.somethingSelected=function(){for(var e=0;e<this.ranges.length;e++)if(!this.ranges[e].empty())return!0;return!1},vi.prototype.contains=function(e,t){t||(t=e);for(var r=0;r<this.ranges.length;r++){var n=this.ranges[r];if(0<=tt(t,n.from())&&tt(e,n.to())<=0)return r}return-1};var mi=function(e,t){this.anchor=e,this.head=t};function yi(e,t,r){var n=e&&e.options.selectionsMayTouch,i=t[r];t.sort(function(e,t){return tt(e.from(),t.from())}),r=B(t,i);for(var o=1;o<t.length;o++){var l=t[o],s=t[o-1],a=tt(s.to(),l.from());if(n&&!l.empty()?0<a:0<=a){var u=ot(s.from(),l.from()),c=it(s.to(),l.to()),h=s.empty()?l.from()==l.head:s.from()==s.head;o<=r&&--r,t.splice(--o,2,new mi(h?c:u,h?u:c))}}return new vi(t,r)}function bi(e,t){return new vi([new mi(e,t||e)],0)}function wi(e){return e.text?et(e.from.line+e.text.length-1,$(e.text).length+(1==e.text.length?e.from.ch:0)):e.to}function xi(e,t){if(tt(e,t.from)<0)return e;if(tt(e,t.to)<=0)return wi(t);var r=e.line+t.text.length-(t.to.line-t.from.line)-1,n=e.ch;return e.line==t.to.line&&(n+=wi(t).ch-t.to.ch),et(r,n)}function Ci(e,t){for(var r=[],n=0;n<e.sel.ranges.length;n++){var i=e.sel.ranges[n];r.push(new mi(xi(i.anchor,t),xi(i.head,t)))}return yi(e.cm,r,e.sel.primIndex)}function Si(e,t,r){return e.line==t.line?et(r.line,e.ch-t.ch+r.ch):et(r.line+(e.line-t.line),e.ch)}function Li(e){e.doc.mode=Re(e.options,e.doc.modeOption),ki(e)}function ki(e){e.doc.iter(function(e){e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null)}),e.doc.modeFrontier=e.doc.highlightFrontier=e.doc.first,Qn(e,100),e.state.modeGen++,e.curOp&&sn(e)}function Ti(e,t){return 0==t.from.ch&&0==t.to.ch&&""==$(t.text)&&(!e.cm||e.cm.options.wholeLineUpdateBefore)}function Mi(e,n,t,i){function o(e){return t?t[e]:null}function r(e,t,r){!function(e,t,r,n){e.text=t,e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null),null!=e.order&&(e.order=null),Nt(e),Ot(e,r);var i=n?n(e):1;i!=e.height&&$e(e,i)}(e,t,r,i),lr(e,"change",e,n)}function l(e,t){for(var r=[],n=e;n<t;++n)r.push(new Xt(u[n],o(n),i));return r}var s=n.from,a=n.to,u=n.text,c=Xe(e,s.line),h=Xe(e,a.line),f=$(u),d=o(u.length-1),p=a.line-s.line;if(n.full)e.insert(0,l(0,u.length)),e.remove(u.length,e.size-u.length);else if(Ti(e,n)){var g=l(0,u.length-1);r(h,h.text,d),p&&e.remove(s.line,p),g.length&&e.insert(s.line,g)}else if(c==h)if(1==u.length)r(c,c.text.slice(0,s.ch)+f+c.text.slice(a.ch),d);else{var v=l(1,u.length-1);v.push(new Xt(f+c.text.slice(a.ch),d,i)),r(c,c.text.slice(0,s.ch)+u[0],o(0)),e.insert(s.line+1,v)}else if(1==u.length)r(c,c.text.slice(0,s.ch)+u[0]+h.text.slice(a.ch),o(0)),e.remove(s.line+1,p);else{r(c,c.text.slice(0,s.ch)+u[0],o(0)),r(h,f+h.text.slice(a.ch),d);var m=l(1,u.length-1);1<p&&e.remove(s.line+1,p-1),e.insert(s.line+1,m)}lr(e,"change",e,n)}function Ni(e,s,a){!function e(t,r,n){if(t.linked)for(var i=0;i<t.linked.length;++i){var o=t.linked[i];if(o.doc!=r){var l=n&&o.sharedHist;a&&!l||(s(o.doc,l),e(o.doc,t,l))}}}(e,null,!0)}function Oi(e,t){if(t.cm)throw new Error("This document is already in use.");nn((e.doc=t).cm=e),Li(e),Ai(e),e.options.lineWrapping||jt(e),e.options.mode=t.modeOption,sn(e)}function Ai(e){("rtl"==e.doc.direction?H:T)(e.display.lineDiv,"CodeMirror-rtl")}function Di(e){this.done=[],this.undone=[],this.undoDepth=1/0,this.lastModTime=this.lastSelTime=0,this.lastOp=this.lastSelOp=null,this.lastOrigin=this.lastSelOrigin=null,this.generation=this.maxGeneration=e||1}function Wi(e,t){var r={from:nt(t.from),to:wi(t),text:Ye(e,t.from,t.to)};return Ii(e,r,t.from.line,t.to.line+1),Ni(e,function(e){return Ii(e,r,t.from.line,t.to.line+1)},!0),r}function Hi(e){for(;e.length;){if(!$(e).ranges)break;e.pop()}}function Fi(e,t,r,n){var i=e.history;i.undone.length=0;var o,l,s,a=+new Date;if((i.lastOp==n||i.lastOrigin==t.origin&&t.origin&&("+"==t.origin.charAt(0)&&i.lastModTime>a-(e.cm?e.cm.options.historyEventDelay:500)||"*"==t.origin.charAt(0)))&&(o=(s=i).lastOp==n?(Hi(s.done),$(s.done)):s.done.length&&!$(s.done).ranges?$(s.done):1<s.done.length&&!s.done[s.done.length-2].ranges?(s.done.pop(),$(s.done)):void 0))l=$(o.changes),0==tt(t.from,t.to)&&0==tt(t.from,l.to)?l.to=wi(t):o.changes.push(Wi(e,t));else{var u=$(i.done);for(u&&u.ranges||Ei(e.sel,i.done),o={changes:[Wi(e,t)],generation:i.generation},i.done.push(o);i.done.length>i.undoDepth;)i.done.shift(),i.done[0].ranges||i.done.shift()}i.done.push(r),i.generation=++i.maxGeneration,i.lastModTime=i.lastSelTime=a,i.lastOp=i.lastSelOp=n,i.lastOrigin=i.lastSelOrigin=t.origin,l||ge(e,"historyAdded")}function Pi(e,t,r,n){var i,o,l,s,a,u=e.history,c=n&&n.origin;r==u.lastSelOp||c&&u.lastSelOrigin==c&&(u.lastModTime==u.lastSelTime&&u.lastOrigin==c||(i=e,o=c,l=$(u.done),s=t,"*"==(a=o.charAt(0))||"+"==a&&l.ranges.length==s.ranges.length&&l.somethingSelected()==s.somethingSelected()&&new Date-i.history.lastSelTime<=(i.cm?i.cm.options.historyEventDelay:500)))?u.done[u.done.length-1]=t:Ei(t,u.done),u.lastSelTime=+new Date,u.lastSelOrigin=c,u.lastSelOp=r,n&&!1!==n.clearRedo&&Hi(u.undone)}function Ei(e,t){var r=$(t);r&&r.ranges&&r.equals(e)||t.push(e)}function Ii(t,r,e,n){var i=r["spans_"+t.id],o=0;t.iter(Math.max(t.first,e),Math.min(t.first+t.size,n),function(e){e.markedSpans&&((i||(i=r["spans_"+t.id]={}))[o]=e.markedSpans),++o})}function zi(e){if(!e)return null;for(var t,r=0;r<e.length;++r)e[r].marker.explicitlyCleared?t||(t=e.slice(0,r)):t&&t.push(e[r]);return t?t.length?t:null:e}function Ri(e,t){var r=function(e,t){var r=t["spans_"+e.id];if(!r)return null;for(var n=[],i=0;i<t.text.length;++i)n.push(zi(r[i]));return n}(e,t),n=Tt(e,t);if(!r)return n;if(!n)return r;for(var i=0;i<r.length;++i){var o=r[i],l=n[i];if(o&&l)e:for(var s=0;s<l.length;++s){for(var a=l[s],u=0;u<o.length;++u)if(o[u].marker==a.marker)continue e;o.push(a)}else l&&(r[i]=l)}return r}function Bi(e,t,r){for(var n=[],i=0;i<e.length;++i){var o=e[i];if(o.ranges)n.push(r?vi.prototype.deepCopy.call(o):o);else{var l=o.changes,s=[];n.push({changes:s});for(var a=0;a<l.length;++a){var u=l[a],c=void 0;if(s.push({from:u.from,to:u.to,text:u.text}),t)for(var h in u)(c=h.match(/^spans_(\d+)$/))&&-1<B(t,Number(c[1]))&&($(s)[h]=u[h],delete u[h])}}}return n}function Gi(e,t,r,n){if(n){var i=e.anchor;if(r){var o=tt(t,i)<0;o!=tt(r,i)<0?(i=t,t=r):o!=tt(t,r)<0&&(t=r)}return new mi(i,t)}return new mi(r||t,t)}function Ui(e,t,r,n,i){null==i&&(i=e.cm&&(e.cm.display.shift||e.extend)),Yi(e,new vi([Gi(e.sel.primary(),t,r,i)],0),n)}function Vi(e,t,r){for(var n=[],i=e.cm&&(e.cm.display.shift||e.extend),o=0;o<e.sel.ranges.length;o++)n[o]=Gi(e.sel.ranges[o],t[o],null,i);Yi(e,yi(e.cm,n,e.sel.primIndex),r)}function Ki(e,t,r,n){var i=e.sel.ranges.slice(0);i[t]=r,Yi(e,yi(e.cm,i,e.sel.primIndex),n)}function ji(e,t,r,n){Yi(e,bi(t,r),n)}function Xi(e,t,r){var n=e.history.done,i=$(n);i&&i.ranges?_i(e,n[n.length-1]=t,r):Yi(e,t,r)}function Yi(e,t,r){_i(e,t,r),Pi(e,e.sel,e.cm?e.cm.curOp.id:NaN,r)}function _i(e,t,r){var n,i,o,l;(ye(e,"beforeSelectionChange")||e.cm&&ye(e.cm,"beforeSelectionChange"))&&(n=e,o=r,l={ranges:(i=t).ranges,update:function(e){this.ranges=[];for(var t=0;t<e.length;t++)this.ranges[t]=new mi(st(n,e[t].anchor),st(n,e[t].head))},origin:o&&o.origin},ge(n,"beforeSelectionChange",n,l),n.cm&&ge(n.cm,"beforeSelectionChange",n.cm,l),t=l.ranges!=i.ranges?yi(n.cm,l.ranges,l.ranges.length-1):i),$i(e,Zi(e,t,r&&r.bias||(tt(t.primary().head,e.sel.primary().head)<0?-1:1),!0)),r&&!1===r.scroll||!e.cm||Mn(e.cm)}function $i(e,t){t.equals(e.sel)||(e.sel=t,e.cm&&(e.cm.curOp.updateInput=1,e.cm.curOp.selectionChanged=!0,me(e.cm)),lr(e,"cursorActivity",e))}function qi(e){$i(e,Zi(e,e.sel,null,!1))}function Zi(e,t,r,n){for(var i,o=0;o<t.ranges.length;o++){var l=t.ranges[o],s=t.ranges.length==e.sel.ranges.length&&e.sel.ranges[o],a=Ji(e,l.anchor,s&&s.anchor,r,n),u=Ji(e,l.head,s&&s.head,r,n);(i||a!=l.anchor||u!=l.head)&&(i||(i=t.ranges.slice(0,o)),i[o]=new mi(a,u))}return i?yi(e.cm,i,t.primIndex):t}function Qi(e,t,r,n,i){var o=Xe(e,t.line);if(o.markedSpans)for(var l=0;l<o.markedSpans.length;++l){var s=o.markedSpans[l],a=s.marker,u="selectLeft"in a?!a.selectLeft:a.inclusiveLeft,c="selectRight"in a?!a.selectRight:a.inclusiveRight;if((null==s.from||(u?s.from<=t.ch:s.from<t.ch))&&(null==s.to||(c?s.to>=t.ch:s.to>t.ch))){if(i&&(ge(a,"beforeCursorEnter"),a.explicitlyCleared)){if(o.markedSpans){--l;continue}break}if(!a.atomic)continue;if(r){var h=a.find(n<0?1:-1),f=void 0;if((n<0?c:u)&&(h=eo(e,h,-n,h&&h.line==t.line?o:null)),h&&h.line==t.line&&(f=tt(h,r))&&(n<0?f<0:0<f))return Qi(e,h,t,n,i)}var d=a.find(n<0?-1:1);return(n<0?u:c)&&(d=eo(e,d,n,d.line==t.line?o:null)),d?Qi(e,d,t,n,i):null}}return t}function Ji(e,t,r,n,i){var o=n||1,l=Qi(e,t,r,o,i)||!i&&Qi(e,t,r,o,!0)||Qi(e,t,r,-o,i)||!i&&Qi(e,t,r,-o,!0);return l||(e.cantEdit=!0,et(e.first,0))}function eo(e,t,r,n){return r<0&&0==t.ch?t.line>e.first?st(e,et(t.line-1)):null:0<r&&t.ch==(n||Xe(e,t.line)).text.length?t.line<e.first+e.size-1?et(t.line+1,0):null:new et(t.line,t.ch+r)}function to(e){e.setSelection(et(e.firstLine(),0),et(e.lastLine()),V)}function ro(i,e,t){var o={canceled:!1,from:e.from,to:e.to,text:e.text,origin:e.origin,cancel:function(){return o.canceled=!0}};return t&&(o.update=function(e,t,r,n){e&&(o.from=st(i,e)),t&&(o.to=st(i,t)),r&&(o.text=r),void 0!==n&&(o.origin=n)}),ge(i,"beforeChange",i,o),i.cm&&ge(i.cm,"beforeChange",i.cm,o),o.canceled?(i.cm&&(i.cm.curOp.updateInput=2),null):{from:o.from,to:o.to,text:o.text,origin:o.origin}}function no(e,t,r){if(e.cm){if(!e.cm.curOp)return $n(e.cm,no)(e,t,r);if(e.cm.state.suppressEdits)return}if(!(ye(e,"beforeChange")||e.cm&&ye(e.cm,"beforeChange"))||(t=ro(e,t,!0))){var n=xt&&!r&&function(e,t,r){var n=null;if(e.iter(t.line,r.line+1,function(e){if(e.markedSpans)for(var t=0;t<e.markedSpans.length;++t){var r=e.markedSpans[t].marker;!r.readOnly||n&&-1!=B(n,r)||(n||(n=[])).push(r)}}),!n)return null;for(var i=[{from:t,to:r}],o=0;o<n.length;++o)for(var l=n[o],s=l.find(0),a=0;a<i.length;++a){var u=i[a];if(!(tt(u.to,s.from)<0||0<tt(u.from,s.to))){var c=[a,1],h=tt(u.from,s.from),f=tt(u.to,s.to);(h<0||!l.inclusiveLeft&&!h)&&c.push({from:u.from,to:s.from}),(0<f||!l.inclusiveRight&&!f)&&c.push({from:s.to,to:u.to}),i.splice.apply(i,c),a+=c.length-3}}return i}(e,t.from,t.to);if(n)for(var i=n.length-1;0<=i;--i)io(e,{from:n[i].from,to:n[i].to,text:i?[""]:t.text,origin:t.origin});else io(e,t)}}function io(e,r){if(1!=r.text.length||""!=r.text[0]||0!=tt(r.from,r.to)){var t=Ci(e,r);Fi(e,r,t,e.cm?e.cm.curOp.id:NaN),so(e,r,t,Tt(e,r));var n=[];Ni(e,function(e,t){t||-1!=B(n,e.history)||(ho(e.history,r),n.push(e.history)),so(e,r,null,Tt(e,r))})}}function oo(i,o,e){var t=i.cm&&i.cm.state.suppressEdits;if(!t||e){for(var l,r=i.history,n=i.sel,s="undo"==o?r.done:r.undone,a="undo"==o?r.undone:r.done,u=0;u<s.length&&(l=s[u],e?!l.ranges||l.equals(i.sel):l.ranges);u++);if(u!=s.length){for(r.lastOrigin=r.lastSelOrigin=null;;){if(!(l=s.pop()).ranges){if(t)return void s.push(l);break}if(Ei(l,a),e&&!l.equals(i.sel))return void Yi(i,l,{clearRedo:!1});n=l}var c=[];Ei(n,a),a.push({changes:c,generation:r.generation}),r.generation=l.generation||++r.maxGeneration;for(var h=ye(i,"beforeChange")||i.cm&&ye(i.cm,"beforeChange"),f=function(e){var r=l.changes[e];if(r.origin=o,h&&!ro(i,r,!1))return s.length=0,{};c.push(Wi(i,r));var t=e?Ci(i,r):$(s);so(i,r,t,Ri(i,r)),!e&&i.cm&&i.cm.scrollIntoView({from:r.from,to:wi(r)});var n=[];Ni(i,function(e,t){t||-1!=B(n,e.history)||(ho(e.history,r),n.push(e.history)),so(e,r,null,Ri(e,r))})},d=l.changes.length-1;0<=d;--d){var p=f(d);if(p)return p.v}}}}function lo(e,t){if(0!=t&&(e.first+=t,e.sel=new vi(q(e.sel.ranges,function(e){return new mi(et(e.anchor.line+t,e.anchor.ch),et(e.head.line+t,e.head.ch))}),e.sel.primIndex),e.cm)){sn(e.cm,e.first,e.first-t,t);for(var r=e.cm.display,n=r.viewFrom;n<r.viewTo;n++)an(e.cm,n,"gutter")}}function so(e,t,r,n){if(e.cm&&!e.cm.curOp)return $n(e.cm,so)(e,t,r,n);if(t.to.line<e.first)lo(e,t.text.length-1-(t.to.line-t.from.line));else if(!(t.from.line>e.lastLine())){if(t.from.line<e.first){var i=t.text.length-1-(e.first-t.from.line);lo(e,i),t={from:et(e.first,0),to:et(t.to.line+i,t.to.ch),text:[$(t.text)],origin:t.origin}}var o=e.lastLine();t.to.line>o&&(t={from:t.from,to:et(o,Xe(e,o).text.length),text:[t.text[0]],origin:t.origin}),t.removed=Ye(e,t.from,t.to),r||(r=Ci(e,t)),e.cm?function(e,t,r){var n=e.doc,i=e.display,o=t.from,l=t.to,s=!1,a=o.line;e.options.lineWrapping||(a=qe(zt(Xe(n,o.line))),n.iter(a,l.line+1,function(e){if(e==i.maxLine)return s=!0}));-1<n.sel.contains(t.from,t.to)&&me(e);Mi(n,t,r,rn(e)),e.options.lineWrapping||(n.iter(a,o.line+t.text.length,function(e){var t=Kt(e);t>i.maxLineLength&&(i.maxLine=e,i.maxLineLength=t,i.maxLineChanged=!0,s=!1)}),s&&(e.curOp.updateMaxLine=!0));(function(e,t){if(e.modeFrontier=Math.min(e.modeFrontier,t),!(e.highlightFrontier<t-10)){for(var r=e.first,n=t-1;r<n;n--){var i=Xe(e,n).stateAfter;if(i&&(!(i instanceof ut)||n+i.lookAhead<t)){r=n+1;break}}e.highlightFrontier=Math.min(e.highlightFrontier,r)}})(n,o.line),Qn(e,400);var u=t.text.length-(l.line-o.line)-1;t.full?sn(e):o.line!=l.line||1!=t.text.length||Ti(e.doc,t)?sn(e,o.line,l.line+1,u):an(e,o.line,"text");var c=ye(e,"changes"),h=ye(e,"change");if(h||c){var f={from:o,to:l,text:t.text,removed:t.removed,origin:t.origin};h&&lr(e,"change",e,f),c&&(e.curOp.changeObjs||(e.curOp.changeObjs=[])).push(f)}e.display.selForContextMenu=null}(e.cm,t,n):Mi(e,t,n),_i(e,r,V),e.cantEdit&&Ji(e,et(e.firstLine(),0))&&(e.cantEdit=!1)}}function ao(e,t,r,n,i){var o;n||(n=r),tt(n,r)<0&&(r=(o=[n,r])[0],n=o[1]),"string"==typeof t&&(t=e.splitLines(t)),no(e,{from:r,to:n,text:t,origin:i})}function uo(e,t,r,n){r<e.line?e.line+=n:t<e.line&&(e.line=t,e.ch=0)}function co(e,t,r,n){for(var i=0;i<e.length;++i){var o=e[i],l=!0;if(o.ranges){o.copied||((o=e[i]=o.deepCopy()).copied=!0);for(var s=0;s<o.ranges.length;s++)uo(o.ranges[s].anchor,t,r,n),uo(o.ranges[s].head,t,r,n)}else{for(var a=0;a<o.changes.length;++a){var u=o.changes[a];if(r<u.from.line)u.from=et(u.from.line+n,u.from.ch),u.to=et(u.to.line+n,u.to.ch);else if(t<=u.to.line){l=!1;break}}l||(e.splice(0,i+1),i=0)}}}function ho(e,t){var r=t.from.line,n=t.to.line,i=t.text.length-(n-r)-1;co(e.done,r,n,i),co(e.undone,r,n,i)}function fo(e,t,r,n){var i=t,o=t;return"number"==typeof t?o=Xe(e,lt(e,t)):i=qe(t),null==i?null:(n(o,i)&&e.cm&&an(e.cm,i,r),o)}function po(e){this.lines=e,this.parent=null;for(var t=0,r=0;r<e.length;++r)e[r].parent=this,t+=e[r].height;this.height=t}function go(e){this.children=e;for(var t=0,r=0,n=0;n<e.length;++n){var i=e[n];t+=i.chunkSize(),r+=i.height,i.parent=this}this.size=t,this.height=r,this.parent=null}mi.prototype.from=function(){return ot(this.anchor,this.head)},mi.prototype.to=function(){return it(this.anchor,this.head)},mi.prototype.empty=function(){return this.head.line==this.anchor.line&&this.head.ch==this.anchor.ch},po.prototype={chunkSize:function(){return this.lines.length},removeInner:function(e,t){for(var r,n=e,i=e+t;n<i;++n){var o=this.lines[n];this.height-=o.height,(r=o).parent=null,Nt(r),lr(o,"delete")}this.lines.splice(e,t)},collapse:function(e){e.push.apply(e,this.lines)},insertInner:function(e,t,r){this.height+=r,this.lines=this.lines.slice(0,e).concat(t).concat(this.lines.slice(e));for(var n=0;n<t.length;++n)t[n].parent=this},iterN:function(e,t,r){for(var n=e+t;e<n;++e)if(r(this.lines[e]))return!0}},go.prototype={chunkSize:function(){return this.size},removeInner:function(e,t){this.size-=t;for(var r=0;r<this.children.length;++r){var n=this.children[r],i=n.chunkSize();if(e<i){var o=Math.min(t,i-e),l=n.height;if(n.removeInner(e,o),this.height-=l-n.height,i==o&&(this.children.splice(r--,1),n.parent=null),0==(t-=o))break;e=0}else e-=i}if(this.size-t<25&&(1<this.children.length||!(this.children[0]instanceof po))){var s=[];this.collapse(s),this.children=[new po(s)],this.children[0].parent=this}},collapse:function(e){for(var t=0;t<this.children.length;++t)this.children[t].collapse(e)},insertInner:function(e,t,r){this.size+=t.length,this.height+=r;for(var n=0;n<this.children.length;++n){var i=this.children[n],o=i.chunkSize();if(e<=o){if(i.insertInner(e,t,r),i.lines&&50<i.lines.length){for(var l=i.lines.length%25+25,s=l;s<i.lines.length;){var a=new po(i.lines.slice(s,s+=25));i.height-=a.height,this.children.splice(++n,0,a),a.parent=this}i.lines=i.lines.slice(0,l),this.maybeSpill()}break}e-=o}},maybeSpill:function(){if(!(this.children.length<=10)){var e=this;do{var t=new go(e.children.splice(e.children.length-5,5));if(e.parent){e.size-=t.size,e.height-=t.height;var r=B(e.parent.children,e);e.parent.children.splice(r+1,0,t)}else{var n=new go(e.children);(n.parent=e).children=[n,t],e=n}t.parent=e.parent}while(10<e.children.length);e.parent.maybeSpill()}},iterN:function(e,t,r){for(var n=0;n<this.children.length;++n){var i=this.children[n],o=i.chunkSize();if(e<o){var l=Math.min(t,o-e);if(i.iterN(e,l,r))return!0;if(0==(t-=l))break;e=0}else e-=o}}};var vo=function(e,t,r){if(r)for(var n in r)r.hasOwnProperty(n)&&(this[n]=r[n]);this.doc=e,this.node=t};function mo(e,t,r){Vt(t)<(e.curOp&&e.curOp.scrollTop||e.doc.scrollTop)&&Tn(e,r)}vo.prototype.clear=function(){var e=this.doc.cm,t=this.line.widgets,r=this.line,n=qe(r);if(null!=n&&t){for(var i=0;i<t.length;++i)t[i]==this&&t.splice(i--,1);t.length||(r.widgets=null);var o=yr(this);$e(r,Math.max(0,r.height-o)),e&&(_n(e,function(){mo(e,r,-o),an(e,n,"widget")}),lr(e,"lineWidgetCleared",e,this,n))}},vo.prototype.changed=function(){var e=this,t=this.height,r=this.doc.cm,n=this.line;this.height=null;var i=yr(this)-t;i&&(Gt(this.doc,n)||$e(n,n.height+i),r&&_n(r,function(){r.curOp.forceUpdate=!0,mo(r,n,i),lr(r,"lineWidgetChanged",r,e,qe(n))}))},be(vo);var yo=0,bo=function(e,t){this.lines=[],this.type=t,this.doc=e,this.id=++yo};function wo(t,n,i,e,r){if(e&&e.shared)return function(e,r,n,i,o){(i=I(i)).shared=!1;var l=[wo(e,r,n,i,o)],s=l[0],a=i.widgetNode;return Ni(e,function(e){a&&(i.widgetNode=a.cloneNode(!0)),l.push(wo(e,st(e,r),st(e,n),i,o));for(var t=0;t<e.linked.length;++t)if(e.linked[t].isParent)return;s=$(l)}),new xo(l,s)}(t,n,i,e,r);if(t.cm&&!t.cm.curOp)return $n(t.cm,wo)(t,n,i,e,r);var o=new bo(t,r),l=tt(n,i);if(e&&I(e,o,!1),0<l||0==l&&!1!==o.clearWhenEmpty)return o;if(o.replacedWith&&(o.collapsed=!0,o.widgetNode=O("span",[o.replacedWith],"CodeMirror-widget"),e.handleMouseEvents||o.widgetNode.setAttribute("cm-ignore-events","true"),e.insertLeft&&(o.widgetNode.insertLeft=!0)),o.collapsed){if(It(t,n.line,n,i,o)||n.line!=i.line&&It(t,i.line,n,i,o))throw new Error("Inserting collapsed marker partially overlapping an existing one");Ct=!0}o.addToHistory&&Fi(t,{from:n,to:i,origin:"markText"},t.sel,NaN);var s,a=n.line,u=t.cm;if(t.iter(a,i.line+1,function(e){var t,r;u&&o.collapsed&&!u.options.lineWrapping&&zt(e)==u.display.maxLine&&(s=!0),o.collapsed&&a!=n.line&&$e(e,0),t=e,r=new St(o,a==n.line?n.ch:null,a==i.line?i.ch:null),t.markedSpans=t.markedSpans?t.markedSpans.concat([r]):[r],r.marker.attachLine(t),++a}),o.collapsed&&t.iter(n.line,i.line+1,function(e){Gt(t,e)&&$e(e,0)}),o.clearOnEnter&&fe(o,"beforeCursorEnter",function(){return o.clear()}),o.readOnly&&(xt=!0,(t.history.done.length||t.history.undone.length)&&t.clearHistory()),o.collapsed&&(o.id=++yo,o.atomic=!0),u){if(s&&(u.curOp.updateMaxLine=!0),o.collapsed)sn(u,n.line,i.line+1);else if(o.className||o.startStyle||o.endStyle||o.css||o.attributes||o.title)for(var c=n.line;c<=i.line;c++)an(u,c,"text");o.atomic&&qi(u.doc),lr(u,"markerAdded",u,o)}return o}bo.prototype.clear=function(){var e=this;if(!this.explicitlyCleared){var t=this.doc.cm,r=t&&!t.curOp;if(r&&Un(t),ye(this,"clear")){var n=this.find();n&&lr(this,"clear",n.from,n.to)}for(var i=null,o=null,l=0;l<this.lines.length;++l){var s=e.lines[l],a=Lt(s.markedSpans,e);t&&!e.collapsed?an(t,qe(s),"text"):t&&(null!=a.to&&(o=qe(s)),null!=a.from&&(i=qe(s))),s.markedSpans=kt(s.markedSpans,a),null==a.from&&e.collapsed&&!Gt(e.doc,s)&&t&&$e(s,Qr(t.display))}if(t&&this.collapsed&&!t.options.lineWrapping)for(var u=0;u<this.lines.length;++u){var c=zt(e.lines[u]),h=Kt(c);h>t.display.maxLineLength&&(t.display.maxLine=c,t.display.maxLineLength=h,t.display.maxLineChanged=!0)}null!=i&&t&&this.collapsed&&sn(t,i,o+1),this.lines.length=0,this.explicitlyCleared=!0,this.atomic&&this.doc.cantEdit&&(this.doc.cantEdit=!1,t&&qi(t.doc)),t&&lr(t,"markerCleared",t,this,i,o),r&&Vn(t),this.parent&&this.parent.clear()}},bo.prototype.find=function(e,t){var r,n;null==e&&"bookmark"==this.type&&(e=1);for(var i=0;i<this.lines.length;++i){var o=this.lines[i],l=Lt(o.markedSpans,this);if(null!=l.from&&(r=et(t?o:qe(o),l.from),-1==e))return r;if(null!=l.to&&(n=et(t?o:qe(o),l.to),1==e))return n}return r&&{from:r,to:n}},bo.prototype.changed=function(){var o=this,l=this.find(-1,!0),s=this,a=this.doc.cm;l&&a&&_n(a,function(){var e=l.line,t=qe(l.line),r=Nr(a,t);if(r&&(Pr(r),a.curOp.selectionChanged=a.curOp.forceUpdate=!0),a.curOp.updateMaxLine=!0,!Gt(s.doc,e)&&null!=s.height){var n=s.height;s.height=null;var i=yr(s)-n;i&&$e(e,e.height+i)}lr(a,"markerChanged",a,o)})},bo.prototype.attachLine=function(e){if(!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;t.maybeHiddenMarkers&&-1!=B(t.maybeHiddenMarkers,this)||(t.maybeUnhiddenMarkers||(t.maybeUnhiddenMarkers=[])).push(this)}this.lines.push(e)},bo.prototype.detachLine=function(e){if(this.lines.splice(B(this.lines,e),1),!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;(t.maybeHiddenMarkers||(t.maybeHiddenMarkers=[])).push(this)}},be(bo);var xo=function(e,t){this.markers=e,this.primary=t;for(var r=0;r<e.length;++r)e[r].parent=this};function Co(e){return e.findMarks(et(e.first,0),e.clipPos(et(e.lastLine())),function(e){return e.parent})}function So(o){for(var e=function(e){var t=o[e],r=[t.primary.doc];Ni(t.primary.doc,function(e){return r.push(e)});for(var n=0;n<t.markers.length;n++){var i=t.markers[n];-1==B(r,i.doc)&&(i.parent=null,t.markers.splice(n--,1))}},t=0;t<o.length;t++)e(t)}xo.prototype.clear=function(){if(!this.explicitlyCleared){this.explicitlyCleared=!0;for(var e=0;e<this.markers.length;++e)this.markers[e].clear();lr(this,"clear")}},xo.prototype.find=function(e,t){return this.primary.find(e,t)},be(xo);var Lo=0,ko=function(e,t,r,n,i){if(!(this instanceof ko))return new ko(e,t,r,n,i);null==r&&(r=0),go.call(this,[new po([new Xt("",null)])]),this.first=r,this.scrollTop=this.scrollLeft=0,this.cantEdit=!1,this.cleanGeneration=1,this.modeFrontier=this.highlightFrontier=r;var o=et(r,0);this.sel=bi(o),this.history=new Di(null),this.id=++Lo,this.modeOption=t,this.lineSep=n,this.direction="rtl"==i?"rtl":"ltr",this.extend=!1,"string"==typeof e&&(e=this.splitLines(e)),Mi(this,{from:o,to:o,text:e}),Yi(this,bi(o),V)};ko.prototype=Q(go.prototype,{constructor:ko,iter:function(e,t,r){r?this.iterN(e-this.first,t-e,r):this.iterN(this.first,this.first+this.size,e)},insert:function(e,t){for(var r=0,n=0;n<t.length;++n)r+=t[n].height;this.insertInner(e-this.first,t,r)},remove:function(e,t){this.removeInner(e-this.first,t)},getValue:function(e){var t=_e(this,this.first,this.first+this.size);return!1===e?t:t.join(e||this.lineSeparator())},setValue:Zn(function(e){var t=et(this.first,0),r=this.first+this.size-1;no(this,{from:t,to:et(r,Xe(this,r).text.length),text:this.splitLines(e),origin:"setValue",full:!0},!0),this.cm&&Nn(this.cm,0,0),Yi(this,bi(t),V)}),replaceRange:function(e,t,r,n){ao(this,e,t=st(this,t),r=r?st(this,r):t,n)},getRange:function(e,t,r){var n=Ye(this,st(this,e),st(this,t));return!1===r?n:n.join(r||this.lineSeparator())},getLine:function(e){var t=this.getLineHandle(e);return t&&t.text},getLineHandle:function(e){if(Qe(this,e))return Xe(this,e)},getLineNumber:function(e){return qe(e)},getLineHandleVisualStart:function(e){return"number"==typeof e&&(e=Xe(this,e)),zt(e)},lineCount:function(){return this.size},firstLine:function(){return this.first},lastLine:function(){return this.first+this.size-1},clipPos:function(e){return st(this,e)},getCursor:function(e){var t=this.sel.primary();return null==e||"head"==e?t.head:"anchor"==e?t.anchor:"end"==e||"to"==e||!1===e?t.to():t.from()},listSelections:function(){return this.sel.ranges},somethingSelected:function(){return this.sel.somethingSelected()},setCursor:Zn(function(e,t,r){ji(this,st(this,"number"==typeof e?et(e,t||0):e),null,r)}),setSelection:Zn(function(e,t,r){ji(this,st(this,e),st(this,t||e),r)}),extendSelection:Zn(function(e,t,r){Ui(this,st(this,e),t&&st(this,t),r)}),extendSelections:Zn(function(e,t){Vi(this,at(this,e),t)}),extendSelectionsBy:Zn(function(e,t){Vi(this,at(this,q(this.sel.ranges,e)),t)}),setSelections:Zn(function(e,t,r){if(e.length){for(var n=[],i=0;i<e.length;i++)n[i]=new mi(st(this,e[i].anchor),st(this,e[i].head));null==t&&(t=Math.min(e.length-1,this.sel.primIndex)),Yi(this,yi(this.cm,n,t),r)}}),addSelection:Zn(function(e,t,r){var n=this.sel.ranges.slice(0);n.push(new mi(st(this,e),st(this,t||e))),Yi(this,yi(this.cm,n,n.length-1),r)}),getSelection:function(e){for(var t,r=this.sel.ranges,n=0;n<r.length;n++){var i=Ye(this,r[n].from(),r[n].to());t=t?t.concat(i):i}return!1===e?t:t.join(e||this.lineSeparator())},getSelections:function(e){for(var t=[],r=this.sel.ranges,n=0;n<r.length;n++){var i=Ye(this,r[n].from(),r[n].to());!1!==e&&(i=i.join(e||this.lineSeparator())),t[n]=i}return t},replaceSelection:function(e,t,r){for(var n=[],i=0;i<this.sel.ranges.length;i++)n[i]=e;this.replaceSelections(n,t,r||"+input")},replaceSelections:Zn(function(e,t,r){for(var n=[],i=this.sel,o=0;o<i.ranges.length;o++){var l=i.ranges[o];n[o]={from:l.from(),to:l.to(),text:this.splitLines(e[o]),origin:r}}for(var s=t&&"end"!=t&&function(e,t,r){for(var n=[],i=et(e.first,0),o=i,l=0;l<t.length;l++){var s=t[l],a=Si(s.from,i,o),u=Si(wi(s),i,o);if(i=s.to,o=u,"around"==r){var c=e.sel.ranges[l],h=tt(c.head,c.anchor)<0;n[l]=new mi(h?u:a,h?a:u)}else n[l]=new mi(a,a)}return new vi(n,e.sel.primIndex)}(this,n,t),a=n.length-1;0<=a;a--)no(this,n[a]);s?Xi(this,s):this.cm&&Mn(this.cm)}),undo:Zn(function(){oo(this,"undo")}),redo:Zn(function(){oo(this,"redo")}),undoSelection:Zn(function(){oo(this,"undo",!0)}),redoSelection:Zn(function(){oo(this,"redo",!0)}),setExtending:function(e){this.extend=e},getExtending:function(){return this.extend},historySize:function(){for(var e=this.history,t=0,r=0,n=0;n<e.done.length;n++)e.done[n].ranges||++t;for(var i=0;i<e.undone.length;i++)e.undone[i].ranges||++r;return{undo:t,redo:r}},clearHistory:function(){this.history=new Di(this.history.maxGeneration)},markClean:function(){this.cleanGeneration=this.changeGeneration(!0)},changeGeneration:function(e){return e&&(this.history.lastOp=this.history.lastSelOp=this.history.lastOrigin=null),this.history.generation},isClean:function(e){return this.history.generation==(e||this.cleanGeneration)},getHistory:function(){return{done:Bi(this.history.done),undone:Bi(this.history.undone)}},setHistory:function(e){var t=this.history=new Di(this.history.maxGeneration);t.done=Bi(e.done.slice(0),null,!0),t.undone=Bi(e.undone.slice(0),null,!0)},setGutterMarker:Zn(function(e,r,n){return fo(this,e,"gutter",function(e){var t=e.gutterMarkers||(e.gutterMarkers={});return!(t[r]=n)&&re(t)&&(e.gutterMarkers=null),!0})}),clearGutter:Zn(function(t){var r=this;this.iter(function(e){e.gutterMarkers&&e.gutterMarkers[t]&&fo(r,e,"gutter",function(){return e.gutterMarkers[t]=null,re(e.gutterMarkers)&&(e.gutterMarkers=null),!0})})}),lineInfo:function(e){var t;if("number"==typeof e){if(!Qe(this,e))return null;if(!(e=Xe(this,t=e)))return null}else if(null==(t=qe(e)))return null;return{line:t,handle:e,text:e.text,gutterMarkers:e.gutterMarkers,textClass:e.textClass,bgClass:e.bgClass,wrapClass:e.wrapClass,widgets:e.widgets}},addLineClass:Zn(function(e,r,n){return fo(this,e,"gutter"==r?"gutter":"class",function(e){var t="text"==r?"textClass":"background"==r?"bgClass":"gutter"==r?"gutterClass":"wrapClass";if(e[t]){if(L(n).test(e[t]))return!1;e[t]+=" "+n}else e[t]=n;return!0})}),removeLineClass:Zn(function(e,o,l){return fo(this,e,"gutter"==o?"gutter":"class",function(e){var t="text"==o?"textClass":"background"==o?"bgClass":"gutter"==o?"gutterClass":"wrapClass",r=e[t];if(!r)return!1;if(null==l)e[t]=null;else{var n=r.match(L(l));if(!n)return!1;var i=n.index+n[0].length;e[t]=r.slice(0,n.index)+(n.index&&i!=r.length?" ":"")+r.slice(i)||null}return!0})}),addLineWidget:Zn(function(e,t,r){return i=e,o=new vo(n=this,t,r),(l=n.cm)&&o.noHScroll&&(l.display.alignWidgets=!0),fo(n,i,"widget",function(e){var t=e.widgets||(e.widgets=[]);if(null==o.insertAt?t.push(o):t.splice(Math.min(t.length-1,Math.max(0,o.insertAt)),0,o),o.line=e,l&&!Gt(n,e)){var r=Vt(e)<n.scrollTop;$e(e,e.height+yr(o)),r&&Tn(l,o.height),l.curOp.forceUpdate=!0}return!0}),l&&lr(l,"lineWidgetAdded",l,o,"number"==typeof i?i:qe(i)),o;var n,i,o,l}),removeLineWidget:function(e){e.clear()},markText:function(e,t,r){return wo(this,st(this,e),st(this,t),r,r&&r.type||"range")},setBookmark:function(e,t){var r={replacedWith:t&&(null==t.nodeType?t.widget:t),insertLeft:t&&t.insertLeft,clearWhenEmpty:!1,shared:t&&t.shared,handleMouseEvents:t&&t.handleMouseEvents};return wo(this,e=st(this,e),e,r,"bookmark")},findMarksAt:function(e){var t=[],r=Xe(this,(e=st(this,e)).line).markedSpans;if(r)for(var n=0;n<r.length;++n){var i=r[n];(null==i.from||i.from<=e.ch)&&(null==i.to||i.to>=e.ch)&&t.push(i.marker.parent||i.marker)}return t},findMarks:function(i,o,l){i=st(this,i),o=st(this,o);var s=[],a=i.line;return this.iter(i.line,o.line+1,function(e){var t=e.markedSpans;if(t)for(var r=0;r<t.length;r++){var n=t[r];null!=n.to&&a==i.line&&i.ch>=n.to||null==n.from&&a!=i.line||null!=n.from&&a==o.line&&n.from>=o.ch||l&&!l(n.marker)||s.push(n.marker.parent||n.marker)}++a}),s},getAllMarks:function(){var n=[];return this.iter(function(e){var t=e.markedSpans;if(t)for(var r=0;r<t.length;++r)null!=t[r].from&&n.push(t[r].marker)}),n},posFromIndex:function(r){var n,i=this.first,o=this.lineSeparator().length;return this.iter(function(e){var t=e.text.length+o;if(r<t)return n=r,!0;r-=t,++i}),st(this,et(i,n))},indexFromPos:function(e){var t=(e=st(this,e)).ch;if(e.line<this.first||e.ch<0)return 0;var r=this.lineSeparator().length;return this.iter(this.first,e.line,function(e){t+=e.text.length+r}),t},copy:function(e){var t=new ko(_e(this,this.first,this.first+this.size),this.modeOption,this.first,this.lineSep,this.direction);return t.scrollTop=this.scrollTop,t.scrollLeft=this.scrollLeft,t.sel=this.sel,t.extend=!1,e&&(t.history.undoDepth=this.history.undoDepth,t.setHistory(this.getHistory())),t},linkedDoc:function(e){e||(e={});var t=this.first,r=this.first+this.size;null!=e.from&&e.from>t&&(t=e.from),null!=e.to&&e.to<r&&(r=e.to);var n=new ko(_e(this,t,r),e.mode||this.modeOption,t,this.lineSep,this.direction);return e.sharedHist&&(n.history=this.history),(this.linked||(this.linked=[])).push({doc:n,sharedHist:e.sharedHist}),n.linked=[{doc:this,isParent:!0,sharedHist:e.sharedHist}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r],i=n.find(),o=e.clipPos(i.from),l=e.clipPos(i.to);if(tt(o,l)){var s=wo(e,o,l,n.primary,n.primary.type);n.markers.push(s),s.parent=n}}}(n,Co(this)),n},unlinkDoc:function(e){if(e instanceof xl&&(e=e.doc),this.linked)for(var t=0;t<this.linked.length;++t){if(this.linked[t].doc==e){this.linked.splice(t,1),e.unlinkDoc(this),So(Co(this));break}}if(e.history==this.history){var r=[e.id];Ni(e,function(e){return r.push(e.id)},!0),e.history=new Di(null),e.history.done=Bi(this.history.done,r),e.history.undone=Bi(this.history.undone,r)}},iterLinkedDocs:function(e){Ni(this,e)},getMode:function(){return this.mode},getEditor:function(){return this.cm},splitLines:function(e){return this.lineSep?e.split(this.lineSep):We(e)},lineSeparator:function(){return this.lineSep||"\n"},setDirection:Zn(function(e){var t;("rtl"!=e&&(e="ltr"),e!=this.direction)&&(this.direction=e,this.iter(function(e){return e.order=null}),this.cm&&_n(t=this.cm,function(){Ai(t),sn(t)}))})}),ko.prototype.eachLine=ko.prototype.iter;var To=0;function Mo(e){var i=this;if(No(i),!ve(i,e)&&!br(i.display,e)){we(e),x&&(To=+new Date);var o=on(i,e,!0),t=e.dataTransfer.files;if(o&&!i.isReadOnly())if(t&&t.length&&window.FileReader&&window.File)for(var l=t.length,s=Array(l),a=0,r=function(e,r){if(!i.options.allowDropFileTypes||-1!=B(i.options.allowDropFileTypes,e.type)){var n=new FileReader;n.onload=$n(i,function(){var e=n.result;if(/[\x00-\x08\x0e-\x1f]{2}/.test(e)&&(e=""),s[r]=e,++a==l){var t={from:o=st(i.doc,o),to:o,text:i.doc.splitLines(s.join(i.doc.lineSeparator())),origin:"paste"};no(i.doc,t),Xi(i.doc,bi(o,wi(t)))}}),n.readAsText(e)}},n=0;n<l;++n)r(t[n],n);else{if(i.state.draggingText&&-1<i.doc.sel.contains(o))return i.state.draggingText(e),void setTimeout(function(){return i.display.input.focus()},20);try{var u=e.dataTransfer.getData("Text");if(u){var c;if(i.state.draggingText&&!i.state.draggingText.copy&&(c=i.listSelections()),_i(i.doc,bi(o,o)),c)for(var h=0;h<c.length;++h)ao(i.doc,"",c[h].anchor,c[h].head,"drag");i.replaceSelection(u,"around","paste"),i.display.input.focus()}}catch(e){}}}}function No(e){e.display.dragCursor&&(e.display.lineSpace.removeChild(e.display.dragCursor),e.display.dragCursor=null)}function Oo(t){if(document.getElementsByClassName){for(var e=document.getElementsByClassName("CodeMirror"),r=[],n=0;n<e.length;n++){var i=e[n].CodeMirror;i&&r.push(i)}r.length&&r[0].operation(function(){for(var e=0;e<r.length;e++)t(r[e])})}}var Ao=!1;function Do(){var e;Ao||(fe(window,"resize",function(){null==e&&(e=setTimeout(function(){e=null,Oo(Wo)},100))}),fe(window,"blur",function(){return Oo(xn)}),Ao=!0)}function Wo(e){var t=e.display;t.cachedCharWidth=t.cachedTextHeight=t.cachedPaddingH=null,t.scrollbarsClipped=!1,e.setSize()}for(var Ho={3:"Pause",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",61:"=",91:"Mod",92:"Mod",93:"Mod",106:"*",107:"=",109:"-",110:".",111:"/",145:"ScrollLock",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",63232:"Up",63233:"Down",63234:"Left",63235:"Right",63272:"Delete",63273:"Home",63275:"End",63276:"PageUp",63277:"PageDown",63302:"Insert"},Fo=0;Fo<10;Fo++)Ho[Fo+48]=Ho[Fo+96]=String(Fo);for(var Po=65;Po<=90;Po++)Ho[Po]=String.fromCharCode(Po);for(var Eo=1;Eo<=12;Eo++)Ho[Eo+111]=Ho[Eo+63235]="F"+Eo;var Io={};function zo(e){var t,r,n,i,o=e.split(/-(?!$)/);e=o[o.length-1];for(var l=0;l<o.length-1;l++){var s=o[l];if(/^(cmd|meta|m)$/i.test(s))i=!0;else if(/^a(lt)?$/i.test(s))t=!0;else if(/^(c|ctrl|control)$/i.test(s))r=!0;else{if(!/^s(hift)?$/i.test(s))throw new Error("Unrecognized modifier name: "+s);n=!0}}return t&&(e="Alt-"+e),r&&(e="Ctrl-"+e),i&&(e="Cmd-"+e),n&&(e="Shift-"+e),e}function Ro(e){var t={};for(var r in e)if(e.hasOwnProperty(r)){var n=e[r];if(/^(name|fallthrough|(de|at)tach)$/.test(r))continue;if("..."==n){delete e[r];continue}for(var i=q(r.split(" "),zo),o=0;o<i.length;o++){var l=void 0,s=void 0;o==i.length-1?(s=i.join(" "),l=n):(s=i.slice(0,o+1).join(" "),l="...");var a=t[s];if(a){if(a!=l)throw new Error("Inconsistent bindings for "+s)}else t[s]=l}delete e[r]}for(var u in t)e[u]=t[u];return e}function Bo(e,t,r,n){var i=(t=Ko(t)).call?t.call(e,n):t[e];if(!1===i)return"nothing";if("..."===i)return"multi";if(null!=i&&r(i))return"handled";if(t.fallthrough){if("[object Array]"!=Object.prototype.toString.call(t.fallthrough))return Bo(e,t.fallthrough,r,n);for(var o=0;o<t.fallthrough.length;o++){var l=Bo(e,t.fallthrough[o],r,n);if(l)return l}}}function Go(e){var t="string"==typeof e?e:Ho[e.keyCode];return"Ctrl"==t||"Alt"==t||"Shift"==t||"Mod"==t}function Uo(e,t,r){var n=e;return t.altKey&&"Alt"!=n&&(e="Alt-"+e),(y?t.metaKey:t.ctrlKey)&&"Ctrl"!=n&&(e="Ctrl-"+e),(y?t.ctrlKey:t.metaKey)&&"Cmd"!=n&&(e="Cmd-"+e),!r&&t.shiftKey&&"Shift"!=n&&(e="Shift-"+e),e}function Vo(e,t){if(v&&34==e.keyCode&&e.char)return!1;var r=Ho[e.keyCode];return null!=r&&!e.altGraphKey&&(3==e.keyCode&&e.code&&(r=e.code),Uo(r,e,t))}function Ko(e){return"string"==typeof e?Io[e]:e}function jo(t,e){for(var r=t.doc.sel.ranges,n=[],i=0;i<r.length;i++){for(var o=e(r[i]);n.length&&tt(o.from,$(n).to)<=0;){var l=n.pop();if(tt(l.from,o.from)<0){o.from=l.from;break}}n.push(o)}_n(t,function(){for(var e=n.length-1;0<=e;e--)ao(t.doc,"",n[e].from,n[e].to,"+delete");Mn(t)})}function Xo(e,t,r){var n=oe(e.text,t+r,r);return n<0||n>e.text.length?null:n}function Yo(e,t,r){var n=Xo(e,t.ch,r);return null==n?null:new et(t.line,n,r<0?"after":"before")}function _o(e,t,r,n,i){if(e){var o=ce(r,t.doc.direction);if(o){var l,s=i<0?$(o):o[0],a=i<0==(1==s.level)?"after":"before";if(0<s.level||"rtl"==t.doc.direction){var u=Or(t,r);l=i<0?r.text.length-1:0;var c=Ar(t,u,l).top;l=le(function(e){return Ar(t,u,e).top==c},i<0==(1==s.level)?s.from:s.to-1,l),"before"==a&&(l=Xo(r,l,1))}else l=i<0?s.to:s.from;return new et(n,l,a)}}return new et(n,i<0?r.text.length:0,i<0?"before":"after")}Io.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharAfter",Backspace:"delCharBefore","Shift-Backspace":"delCharBefore",Tab:"defaultTab","Shift-Tab":"indentAuto",Enter:"newlineAndIndent",Insert:"toggleOverwrite",Esc:"singleSelection"},Io.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Up":"goLineUp","Ctrl-Down":"goLineDown","Ctrl-Left":"goGroupLeft","Ctrl-Right":"goGroupRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delGroupBefore","Ctrl-Delete":"delGroupAfter","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll","Ctrl-[":"indentLess","Ctrl-]":"indentMore","Ctrl-U":"undoSelection","Shift-Ctrl-U":"redoSelection","Alt-U":"redoSelection",fallthrough:"basic"},Io.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Alt-F":"goWordRight","Alt-B":"goWordLeft","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageDown","Shift-Ctrl-V":"goPageUp","Ctrl-D":"delCharAfter","Ctrl-H":"delCharBefore","Alt-D":"delWordAfter","Alt-Backspace":"delWordBefore","Ctrl-K":"killLine","Ctrl-T":"transposeChars","Ctrl-O":"openLine"},Io.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Home":"goDocStart","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goGroupLeft","Alt-Right":"goGroupRight","Cmd-Left":"goLineLeft","Cmd-Right":"goLineRight","Alt-Backspace":"delGroupBefore","Ctrl-Alt-Backspace":"delGroupAfter","Alt-Delete":"delGroupAfter","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll","Cmd-[":"indentLess","Cmd-]":"indentMore","Cmd-Backspace":"delWrappedLineLeft","Cmd-Delete":"delWrappedLineRight","Cmd-U":"undoSelection","Shift-Cmd-U":"redoSelection","Ctrl-Up":"goDocStart","Ctrl-Down":"goDocEnd",fallthrough:["basic","emacsy"]},Io["default"]=w?Io.macDefault:Io.pcDefault;var $o={selectAll:to,singleSelection:function(e){return e.setSelection(e.getCursor("anchor"),e.getCursor("head"),V)},killLine:function(r){return jo(r,function(e){if(e.empty()){var t=Xe(r.doc,e.head.line).text.length;return e.head.ch==t&&e.head.line<r.lastLine()?{from:e.head,to:et(e.head.line+1,0)}:{from:e.head,to:et(e.head.line,t)}}return{from:e.from(),to:e.to()}})},deleteLine:function(t){return jo(t,function(e){return{from:et(e.from().line,0),to:st(t.doc,et(e.to().line+1,0))}})},delLineLeft:function(e){return jo(e,function(e){return{from:et(e.from().line,0),to:e.from()}})},delWrappedLineLeft:function(r){return jo(r,function(e){var t=r.charCoords(e.head,"div").top+5;return{from:r.coordsChar({left:0,top:t},"div"),to:e.from()}})},delWrappedLineRight:function(n){return jo(n,function(e){var t=n.charCoords(e.head,"div").top+5,r=n.coordsChar({left:n.display.lineDiv.offsetWidth+100,top:t},"div");return{from:e.from(),to:r}})},undo:function(e){return e.undo()},redo:function(e){return e.redo()},undoSelection:function(e){return e.undoSelection()},redoSelection:function(e){return e.redoSelection()},goDocStart:function(e){return e.extendSelection(et(e.firstLine(),0))},goDocEnd:function(e){return e.extendSelection(et(e.lastLine()))},goLineStart:function(t){return t.extendSelectionsBy(function(e){return qo(t,e.head.line)},{origin:"+move",bias:1})},goLineStartSmart:function(t){return t.extendSelectionsBy(function(e){return Zo(t,e.head)},{origin:"+move",bias:1})},goLineEnd:function(t){return t.extendSelectionsBy(function(e){return function(e,t){var r=Xe(e.doc,t),n=function(e){for(var t;t=Pt(e);)e=t.find(1,!0).line;return e}(r);n!=r&&(t=qe(n));return _o(!0,e,r,t,-1)}(t,e.head.line)},{origin:"+move",bias:-1})},goLineRight:function(r){return r.extendSelectionsBy(function(e){var t=r.cursorCoords(e.head,"div").top+5;return r.coordsChar({left:r.display.lineDiv.offsetWidth+100,top:t},"div")},j)},goLineLeft:function(r){return r.extendSelectionsBy(function(e){var t=r.cursorCoords(e.head,"div").top+5;return r.coordsChar({left:0,top:t},"div")},j)},goLineLeftSmart:function(n){return n.extendSelectionsBy(function(e){var t=n.cursorCoords(e.head,"div").top+5,r=n.coordsChar({left:0,top:t},"div");return r.ch<n.getLine(r.line).search(/\S/)?Zo(n,e.head):r},j)},goLineUp:function(e){return e.moveV(-1,"line")},goLineDown:function(e){return e.moveV(1,"line")},goPageUp:function(e){return e.moveV(-1,"page")},goPageDown:function(e){return e.moveV(1,"page")},goCharLeft:function(e){return e.moveH(-1,"char")},goCharRight:function(e){return e.moveH(1,"char")},goColumnLeft:function(e){return e.moveH(-1,"column")},goColumnRight:function(e){return e.moveH(1,"column")},goWordLeft:function(e){return e.moveH(-1,"word")},goGroupRight:function(e){return e.moveH(1,"group")},goGroupLeft:function(e){return e.moveH(-1,"group")},goWordRight:function(e){return e.moveH(1,"word")},delCharBefore:function(e){return e.deleteH(-1,"char")},delCharAfter:function(e){return e.deleteH(1,"char")},delWordBefore:function(e){return e.deleteH(-1,"word")},delWordAfter:function(e){return e.deleteH(1,"word")},delGroupBefore:function(e){return e.deleteH(-1,"group")},delGroupAfter:function(e){return e.deleteH(1,"group")},indentAuto:function(e){return e.indentSelection("smart")},indentMore:function(e){return e.indentSelection("add")},indentLess:function(e){return e.indentSelection("subtract")},insertTab:function(e){return e.replaceSelection("\t")},insertSoftTab:function(e){for(var t=[],r=e.listSelections(),n=e.options.tabSize,i=0;i<r.length;i++){var o=r[i].from(),l=z(e.getLine(o.line),o.ch,n);t.push(_(n-l%n))}e.replaceSelections(t)},defaultTab:function(e){e.somethingSelected()?e.indentSelection("add"):e.execCommand("insertTab")},transposeChars:function(l){return _n(l,function(){for(var e=l.listSelections(),t=[],r=0;r<e.length;r++)if(e[r].empty()){var n=e[r].head,i=Xe(l.doc,n.line).text;if(i)if(n.ch==i.length&&(n=new et(n.line,n.ch-1)),0<n.ch)n=new et(n.line,n.ch+1),l.replaceRange(i.charAt(n.ch-1)+i.charAt(n.ch-2),et(n.line,n.ch-2),n,"+transpose");else if(n.line>l.doc.first){var o=Xe(l.doc,n.line-1).text;o&&(n=new et(n.line,1),l.replaceRange(i.charAt(0)+l.doc.lineSeparator()+o.charAt(o.length-1),et(n.line-1,o.length-1),n,"+transpose"))}t.push(new mi(n,n))}l.setSelections(t)})},newlineAndIndent:function(n){return _n(n,function(){for(var e=n.listSelections(),t=e.length-1;0<=t;t--)n.replaceRange(n.doc.lineSeparator(),e[t].anchor,e[t].head,"+input");e=n.listSelections();for(var r=0;r<e.length;r++)n.indentLine(e[r].from().line,null,!0);Mn(n)})},openLine:function(e){return e.replaceSelection("\n","start")},toggleOverwrite:function(e){return e.toggleOverwrite()}};function qo(e,t){var r=Xe(e.doc,t),n=zt(r);return n!=r&&(t=qe(n)),_o(!0,e,n,t,1)}function Zo(e,t){var r=qo(e,t.line),n=Xe(e.doc,r.line),i=ce(n,e.doc.direction);if(!i||0==i[0].level){var o=Math.max(0,n.text.search(/\S/)),l=t.line==r.line&&t.ch<=o&&t.ch;return et(r.line,l?0:o,r.sticky)}return r}function Qo(e,t,r){if("string"==typeof t&&!(t=$o[t]))return!1;e.display.input.ensurePolled();var n=e.display.shift,i=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),r&&(e.display.shift=!1),i=t(e)!=U}finally{e.display.shift=n,e.state.suppressEdits=!1}return i}var Jo=new R;function el(e,t,r,n){var i=e.state.keySeq;if(i){if(Go(t))return"handled";if(/\'$/.test(t)?e.state.keySeq=null:Jo.set(50,function(){e.state.keySeq==i&&(e.state.keySeq=null,e.display.input.reset())}),tl(e,i+" "+t,r,n))return!0}return tl(e,t,r,n)}function tl(e,t,r,n){var i=function(e,t,r){for(var n=0;n<e.state.keyMaps.length;n++){var i=Bo(t,e.state.keyMaps[n],r,e);if(i)return i}return e.options.extraKeys&&Bo(t,e.options.extraKeys,r,e)||Bo(t,e.options.keyMap,r,e)}(e,t,n);return"multi"==i&&(e.state.keySeq=t),"handled"==i&&lr(e,"keyHandled",e,t,r),"handled"!=i&&"multi"!=i||(we(r),mn(e)),!!i}function rl(t,e){var r=Vo(e,!0);return!!r&&(e.shiftKey&&!t.state.keySeq?el(t,"Shift-"+r,e,function(e){return Qo(t,e,!0)})||el(t,r,e,function(e){if("string"==typeof e?/^go[A-Z]/.test(e):e.motion)return Qo(t,e)}):el(t,r,e,function(e){return Qo(t,e)}))}var nl=null;function il(e){var t=this;if(t.curOp.focus=W(),!ve(t,e)){x&&C<11&&27==e.keyCode&&(e.returnValue=!1);var r=e.keyCode;t.display.shift=16==r||e.shiftKey;var n=rl(t,e);v&&(nl=n?r:null,!n&&88==r&&!Fe&&(w?e.metaKey:e.ctrlKey)&&t.replaceSelection("",null,"cut")),18!=r||/\bCodeMirror-crosshair\b/.test(t.display.lineDiv.className)||function(e){var t=e.display.lineDiv;function r(e){18!=e.keyCode&&e.altKey||(T(t,"CodeMirror-crosshair"),pe(document,"keyup",r),pe(document,"mouseover",r))}H(t,"CodeMirror-crosshair"),fe(document,"keyup",r),fe(document,"mouseover",r)}(t)}}function ol(e){16==e.keyCode&&(this.doc.sel.shift=!1),ve(this,e)}function ll(e){var t=this;if(!(br(t.display,e)||ve(t,e)||e.ctrlKey&&!e.altKey||w&&e.metaKey)){var r=e.keyCode,n=e.charCode;if(v&&r==nl)return nl=null,void we(e);if(!v||e.which&&!(e.which<10)||!rl(t,e)){var i,o=String.fromCharCode(null==n?r:n);if("\b"!=o)if(!el(i=t,"'"+o+"'",e,function(e){return Qo(i,e,!0)}))t.display.input.onKeyPress(e)}}}var sl,al,ul=function(e,t,r){this.time=e,this.pos=t,this.button=r};function cl(e){var t=this,r=t.display;if(!(ve(t,e)||r.activeTouch&&r.input.supportsTouch()))if(r.input.ensurePolled(),r.shift=e.shiftKey,br(r,e))b||(r.scroller.draggable=!1,setTimeout(function(){return r.scroller.draggable=!0},100));else if(!dl(t,e)){var n,i,o,l=on(t,e),s=ke(e),a=l?(n=l,i=s,o=+new Date,al&&al.compare(o,n,i)?(sl=al=null,"triple"):sl&&sl.compare(o,n,i)?(al=new ul(o,n,i),sl=null,"double"):(sl=new ul(o,n,i),al=null,"single")):"single";window.focus(),1==s&&t.state.selectingText&&t.state.selectingText(e),l&&function(r,e,n,t,i){var o="Click";"double"==t?o="Double"+o:"triple"==t&&(o="Triple"+o);return el(r,Uo(o=(1==e?"Left":2==e?"Middle":"Right")+o,i),i,function(e){if("string"==typeof e&&(e=$o[e]),!e)return!1;var t=!1;try{r.isReadOnly()&&(r.state.suppressEdits=!0),t=e(r,n)!=U}finally{r.state.suppressEdits=!1}return t})}(t,s,l,a,e)||(1==s?l?function(e,t,r,n){x?setTimeout(E(yn,e),0):e.curOp.focus=W();var i,o=function(e,t,r){var n=e.getOption("configureMouse"),i=n?n(e,t,r):{};if(null==i.unit){var o=d?r.shiftKey&&r.metaKey:r.altKey;i.unit=o?"rectangle":"single"==t?"char":"double"==t?"word":"line"}(null==i.extend||e.doc.extend)&&(i.extend=e.doc.extend||r.shiftKey);null==i.addNew&&(i.addNew=w?r.metaKey:r.ctrlKey);null==i.moveOnDrag&&(i.moveOnDrag=!(w?r.altKey:r.ctrlKey));return i}(e,r,n),l=e.doc.sel;e.options.dragDrop&&Ne&&!e.isReadOnly()&&"single"==r&&-1<(i=l.contains(t))&&(tt((i=l.ranges[i]).from(),t)<0||0<t.xRel)&&(0<tt(i.to(),t)||t.xRel<0)?function(t,r,n,i){var o=t.display,l=!1,s=$n(t,function(e){b&&(o.scroller.draggable=!1),t.state.draggingText=!1,pe(o.wrapper.ownerDocument,"mouseup",s),pe(o.wrapper.ownerDocument,"mousemove",a),pe(o.scroller,"dragstart",u),pe(o.scroller,"drop",s),l||(we(e),i.addNew||Ui(t.doc,n,null,null,i.extend),b||x&&9==C?setTimeout(function(){o.wrapper.ownerDocument.body.focus(),o.input.focus()},20):o.input.focus())}),a=function(e){l=l||10<=Math.abs(r.clientX-e.clientX)+Math.abs(r.clientY-e.clientY)},u=function(){return l=!0};b&&(o.scroller.draggable=!0);(t.state.draggingText=s).copy=!i.moveOnDrag,o.scroller.dragDrop&&o.scroller.dragDrop();fe(o.wrapper.ownerDocument,"mouseup",s),fe(o.wrapper.ownerDocument,"mousemove",a),fe(o.scroller,"dragstart",u),fe(o.scroller,"drop",s),bn(t),setTimeout(function(){return o.input.focus()},20)}(e,n,t,o):function(v,e,m,y){var l=v.display,b=v.doc;we(e);var w,x,C=b.sel,t=C.ranges;y.addNew&&!y.extend?(x=b.sel.contains(m),w=-1<x?t[x]:new mi(m,m)):(w=b.sel.primary(),x=b.sel.primIndex);if("rectangle"==y.unit)y.addNew||(w=new mi(m,m)),m=on(v,e,!0,!0),x=-1;else{var r=hl(v,m,y.unit);w=y.extend?Gi(w,r.anchor,r.head,y.extend):r}y.addNew?-1==x?(x=t.length,Yi(b,yi(v,t.concat([w]),x),{scroll:!1,origin:"*mouse"})):1<t.length&&t[x].empty()&&"char"==y.unit&&!y.extend?(Yi(b,yi(v,t.slice(0,x).concat(t.slice(x+1)),0),{scroll:!1,origin:"*mouse"}),C=b.sel):Ki(b,x,w,K):(Yi(b,new vi([w],x=0),K),C=b.sel);var S=m;function s(e){if(0!=tt(S,e))if(S=e,"rectangle"==y.unit){for(var t=[],r=v.options.tabSize,n=z(Xe(b,m.line).text,m.ch,r),i=z(Xe(b,e.line).text,e.ch,r),o=Math.min(n,i),l=Math.max(n,i),s=Math.min(m.line,e.line),a=Math.min(v.lastLine(),Math.max(m.line,e.line));s<=a;s++){var u=Xe(b,s).text,c=X(u,o,r);o==l?t.push(new mi(et(s,c),et(s,c))):u.length>c&&t.push(new mi(et(s,c),et(s,X(u,l,r))))}t.length||t.push(new mi(m,m)),Yi(b,yi(v,C.ranges.slice(0,x).concat(t),x),{origin:"*mouse",scroll:!1}),v.scrollIntoView(e)}else{var h,f=w,d=hl(v,e,y.unit),p=f.anchor;0<tt(d.anchor,p)?(h=d.head,p=ot(f.from(),d.anchor)):(h=d.anchor,p=it(f.to(),d.head));var g=C.ranges.slice(0);g[x]=function(e,t){var r=t.anchor,n=t.head,i=Xe(e.doc,r.line);if(0==tt(r,n)&&r.sticky==n.sticky)return t;var o=ce(i);if(!o)return t;var l=ae(o,r.ch,r.sticky),s=o[l];if(s.from!=r.ch&&s.to!=r.ch)return t;var a,u=l+(s.from==r.ch==(1!=s.level)?0:1);if(0==u||u==o.length)return t;if(n.line!=r.line)a=0<(n.line-r.line)*("ltr"==e.doc.direction?1:-1);else{var c=ae(o,n.ch,n.sticky),h=c-l||(n.ch-r.ch)*(1==s.level?-1:1);a=c==u-1||c==u?h<0:0<h}var f=o[u+(a?-1:0)],d=a==(1==f.level),p=d?f.from:f.to,g=d?"after":"before";return r.ch==p&&r.sticky==g?t:new mi(new et(r.line,p,g),n)}(v,new mi(st(b,p),h)),Yi(b,yi(v,g,x),K)}}var a=l.wrapper.getBoundingClientRect(),u=0;function n(e){v.state.selectingText=!1,u=1/0,e&&(we(e),l.input.focus()),pe(l.wrapper.ownerDocument,"mousemove",i),pe(l.wrapper.ownerDocument,"mouseup",o),b.history.lastSelOrigin=null}var i=$n(v,function(e){0!==e.buttons&&ke(e)?function e(t){var r=++u;var n=on(v,t,!0,"rectangle"==y.unit);if(!n)return;if(0!=tt(n,S)){v.curOp.focus=W(),s(n);var i=Ln(l,b);(n.line>=i.to||n.line<i.from)&&setTimeout($n(v,function(){u==r&&e(t)}),150)}else{var o=t.clientY<a.top?-20:t.clientY>a.bottom?20:0;o&&setTimeout($n(v,function(){u==r&&(l.scroller.scrollTop+=o,e(t))}),50)}}(e):n(e)}),o=$n(v,n);v.state.selectingText=o,fe(l.wrapper.ownerDocument,"mousemove",i),fe(l.wrapper.ownerDocument,"mouseup",o)}(e,n,t,o)}(t,l,a,e):Le(e)==r.scroller&&we(e):2==s?(l&&Ui(t.doc,l),setTimeout(function(){return r.input.focus()},20)):3==s&&(S?t.display.input.onContextMenu(e):bn(t)))}}function hl(e,t,r){if("char"==r)return new mi(t,t);if("word"==r)return e.findWordAt(t);if("line"==r)return new mi(et(t.line,0),st(e.doc,et(t.line+1,0)));var n=r(e,t);return new mi(n.from,n.to)}function fl(e,t,r,n){var i,o;if(t.touches)i=t.touches[0].clientX,o=t.touches[0].clientY;else try{i=t.clientX,o=t.clientY}catch(t){return!1}if(i>=Math.floor(e.display.gutters.getBoundingClientRect().right))return!1;n&&we(t);var l=e.display,s=l.lineDiv.getBoundingClientRect();if(o>s.bottom||!ye(e,r))return Ce(t);o-=s.top-l.viewOffset;for(var a=0;a<e.display.gutterSpecs.length;++a){var u=l.gutters.childNodes[a];if(u&&u.getBoundingClientRect().right>=i)return ge(e,r,e,Ze(e.doc,o),e.display.gutterSpecs[a].className,t),Ce(t)}}function dl(e,t){return fl(e,t,"gutterClick",!0)}function pl(e,t){br(e.display,t)||function(e,t){if(!ye(e,"gutterContextMenu"))return!1;return fl(e,t,"gutterContextMenu",!1)}(e,t)||ve(e,t,"contextmenu")||S||e.display.input.onContextMenu(t)}function gl(e){e.display.wrapper.className=e.display.wrapper.className.replace(/\s*cm-s-\S+/g,"")+e.options.theme.replace(/(^|\s)\s*/g," cm-s-"),Ir(e)}ul.prototype.compare=function(e,t,r){return this.time+400>e&&0==tt(t,this.pos)&&r==this.button};var vl={toString:function(){return"CodeMirror.Init"}},ml={},yl={};function bl(e,t,r){if(!t!=!(r&&r!=vl)){var n=e.display.dragFunctions,i=t?fe:pe;i(e.display.scroller,"dragstart",n.start),i(e.display.scroller,"dragenter",n.enter),i(e.display.scroller,"dragover",n.over),i(e.display.scroller,"dragleave",n.leave),i(e.display.scroller,"drop",n.drop)}}function wl(e){e.options.lineWrapping?(H(e.display.wrapper,"CodeMirror-wrap"),e.display.sizer.style.minWidth="",e.display.sizerWidth=null):(T(e.display.wrapper,"CodeMirror-wrap"),jt(e)),nn(e),sn(e),Ir(e),setTimeout(function(){return In(e)},100)}function xl(e,t){var r=this;if(!(this instanceof xl))return new xl(e,t);this.options=t=t?I(t):{},I(ml,t,!1);var n=t.value;"string"==typeof n?n=new ko(n,t.mode,null,t.lineSeparator,t.direction):t.mode&&(n.modeOption=t.mode),this.doc=n;var i=new xl.inputStyles[t.inputStyle](this),o=this.display=new function(e,t,r,n){var i=this;this.input=r,i.scrollbarFiller=A("div",null,"CodeMirror-scrollbar-filler"),i.scrollbarFiller.setAttribute("cm-not-content","true"),i.gutterFiller=A("div",null,"CodeMirror-gutter-filler"),i.gutterFiller.setAttribute("cm-not-content","true"),i.lineDiv=O("div",null,"CodeMirror-code"),i.selectionDiv=A("div",null,null,"position: relative; z-index: 1"),i.cursorDiv=A("div",null,"CodeMirror-cursors"),i.measure=A("div",null,"CodeMirror-measure"),i.lineMeasure=A("div",null,"CodeMirror-measure"),i.lineSpace=O("div",[i.measure,i.lineMeasure,i.selectionDiv,i.cursorDiv,i.lineDiv],null,"position: relative; outline: none");var o=O("div",[i.lineSpace],"CodeMirror-lines");i.mover=A("div",[o],null,"position: relative"),i.sizer=A("div",[i.mover],"CodeMirror-sizer"),i.sizerWidth=null,i.heightForcer=A("div",null,null,"position: absolute; height: "+G+"px; width: 1px;"),i.gutters=A("div",null,"CodeMirror-gutters"),i.lineGutter=null,i.scroller=A("div",[i.sizer,i.heightForcer,i.gutters],"CodeMirror-scroll"),i.scroller.setAttribute("tabIndex","-1"),i.wrapper=A("div",[i.scrollbarFiller,i.gutterFiller,i.scroller],"CodeMirror"),x&&C<8&&(i.gutters.style.zIndex=-1,i.scroller.style.paddingRight=0),b||g&&f||(i.scroller.draggable=!0),e&&(e.appendChild?e.appendChild(i.wrapper):e(i.wrapper)),i.viewFrom=i.viewTo=t.first,i.reportedViewFrom=i.reportedViewTo=t.first,i.view=[],i.renderedView=null,i.externalMeasured=null,i.viewOffset=0,i.lastWrapHeight=i.lastWrapWidth=0,i.updateLineNumbers=null,i.nativeBarWidth=i.barHeight=i.barWidth=0,i.scrollbarsClipped=!1,i.lineNumWidth=i.lineNumInnerWidth=i.lineNumChars=null,i.alignWidgets=!1,i.cachedCharWidth=i.cachedTextHeight=i.cachedPaddingH=null,i.maxLine=null,i.maxLineLength=0,i.maxLineChanged=!1,i.wheelDX=i.wheelDY=i.wheelStartX=i.wheelStartY=null,i.shift=!1,i.selForContextMenu=null,i.activeTouch=null,i.gutterSpecs=ai(n.gutters,n.lineNumbers),ui(i),r.init(i)}(e,n,i,t);for(var l in gl(o.wrapper.CodeMirror=this),t.lineWrapping&&(this.display.wrapper.className+=" CodeMirror-wrap"),Bn(this),this.state={keyMaps:[],overlays:[],modeGen:0,overwrite:!1,delayingBlurEvent:!1,focused:!1,suppressEdits:!1,pasteIncoming:-1,cutIncoming:-1,selectingText:!1,draggingText:!1,highlight:new R,keySeq:null,specialChars:null},t.autofocus&&!f&&o.input.focus(),x&&C<11&&setTimeout(function(){return r.display.input.reset(!0)},20),function(i){var o=i.display;fe(o.scroller,"mousedown",$n(i,cl)),fe(o.scroller,"dblclick",x&&C<11?$n(i,function(e){if(!ve(i,e)){var t=on(i,e);if(t&&!dl(i,e)&&!br(i.display,e)){we(e);var r=i.findWordAt(t);Ui(i.doc,r.anchor,r.head)}}}):function(e){return ve(i,e)||we(e)});fe(o.scroller,"contextmenu",function(e){return pl(i,e)});var r,n={end:0};function l(){o.activeTouch&&(r=setTimeout(function(){return o.activeTouch=null},1e3),(n=o.activeTouch).end=+new Date)}function s(e,t){if(null==t.left)return!0;var r=t.left-e.left,n=t.top-e.top;return 400<r*r+n*n}fe(o.scroller,"touchstart",function(e){if(!ve(i,e)&&!function(e){if(1!=e.touches.length)return!1;var t=e.touches[0];return t.radiusX<=1&&t.radiusY<=1}(e)&&!dl(i,e)){o.input.ensurePolled(),clearTimeout(r);var t=+new Date;o.activeTouch={start:t,moved:!1,prev:t-n.end<=300?n:null},1==e.touches.length&&(o.activeTouch.left=e.touches[0].pageX,o.activeTouch.top=e.touches[0].pageY)}}),fe(o.scroller,"touchmove",function(){o.activeTouch&&(o.activeTouch.moved=!0)}),fe(o.scroller,"touchend",function(e){var t=o.activeTouch;if(t&&!br(o,e)&&null!=t.left&&!t.moved&&new Date-t.start<300){var r,n=i.coordsChar(o.activeTouch,"page");r=!t.prev||s(t,t.prev)?new mi(n,n):!t.prev.prev||s(t,t.prev.prev)?i.findWordAt(n):new mi(et(n.line,0),st(i.doc,et(n.line+1,0))),i.setSelection(r.anchor,r.head),i.focus(),we(e)}l()}),fe(o.scroller,"touchcancel",l),fe(o.scroller,"scroll",function(){o.scroller.clientHeight&&(Dn(i,o.scroller.scrollTop),Hn(i,o.scroller.scrollLeft,!0),ge(i,"scroll",i))}),fe(o.scroller,"mousewheel",function(e){return gi(i,e)}),fe(o.scroller,"DOMMouseScroll",function(e){return gi(i,e)}),fe(o.wrapper,"scroll",function(){return o.wrapper.scrollTop=o.wrapper.scrollLeft=0}),o.dragFunctions={enter:function(e){ve(i,e)||Se(e)},over:function(e){ve(i,e)||(!function(e,t){var r=on(e,t);if(r){var n=document.createDocumentFragment();pn(e,r,n),e.display.dragCursor||(e.display.dragCursor=A("div",null,"CodeMirror-cursors CodeMirror-dragcursors"),e.display.lineSpace.insertBefore(e.display.dragCursor,e.display.cursorDiv)),N(e.display.dragCursor,n)}}(i,e),Se(e))},start:function(e){return function(e,t){if(x&&(!e.state.draggingText||+new Date-To<100))Se(t);else if(!ve(e,t)&&!br(e.display,t)&&(t.dataTransfer.setData("Text",e.getSelection()),t.dataTransfer.effectAllowed="copyMove",t.dataTransfer.setDragImage&&!a)){var r=A("img",null,null,"position: fixed; left: 0; top: 0;");r.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",v&&(r.width=r.height=1,e.display.wrapper.appendChild(r),r._top=r.offsetTop),t.dataTransfer.setDragImage(r,0,0),v&&r.parentNode.removeChild(r)}}(i,e)},drop:$n(i,Mo),leave:function(e){ve(i,e)||No(i)}};var e=o.input.getField();fe(e,"keyup",function(e){return ol.call(i,e)}),fe(e,"keydown",$n(i,il)),fe(e,"keypress",$n(i,ll)),fe(e,"focus",function(e){return wn(i,e)}),fe(e,"blur",function(e){return xn(i,e)})}(this),Do(),Un(this),this.curOp.forceUpdate=!0,Oi(this,n),t.autofocus&&!f||this.hasFocus()?setTimeout(E(wn,this),20):xn(this),yl)yl.hasOwnProperty(l)&&yl[l](r,t[l],vl);si(this),t.finishInit&&t.finishInit(this);for(var s=0;s<Cl.length;++s)Cl[s](r);Vn(this),b&&t.lineWrapping&&"optimizelegibility"==getComputedStyle(o.lineDiv).textRendering&&(o.lineDiv.style.textRendering="auto")}xl.defaults=ml,xl.optionHandlers=yl;var Cl=[];function Sl(e,t,r,n){var i,o=e.doc;null==r&&(r="add"),"smart"==r&&(o.mode.indent?i=dt(e,t).state:r="prev");var l=e.options.tabSize,s=Xe(o,t),a=z(s.text,null,l);s.stateAfter&&(s.stateAfter=null);var u,c=s.text.match(/^\s*/)[0];if(n||/\S/.test(s.text)){if("smart"==r&&((u=o.mode.indent(i,s.text.slice(c.length),s.text))==U||150<u)){if(!n)return;r="prev"}}else u=0,r="not";"prev"==r?u=t>o.first?z(Xe(o,t-1).text,null,l):0:"add"==r?u=a+e.options.indentUnit:"subtract"==r?u=a-e.options.indentUnit:"number"==typeof r&&(u=a+r),u=Math.max(0,u);var h="",f=0;if(e.options.indentWithTabs)for(var d=Math.floor(u/l);d;--d)f+=l,h+="\t";if(f<u&&(h+=_(u-f)),h!=c)return ao(o,h,et(t,0),et(t,c.length),"+input"),!(s.stateAfter=null);for(var p=0;p<o.sel.ranges.length;p++){var g=o.sel.ranges[p];if(g.head.line==t&&g.head.ch<c.length){var v=et(t,c.length);Ki(o,p,new mi(v,v));break}}}xl.defineInitHook=function(e){return Cl.push(e)};var Ll=null;function kl(e){Ll=e}function Tl(e,t,r,n,i){var o=e.doc;e.display.shift=!1,n||(n=o.sel);var l=+new Date-200,s="paste"==i||e.state.pasteIncoming>l,a=We(t),u=null;if(s&&1<n.ranges.length)if(Ll&&Ll.text.join("\n")==t){if(n.ranges.length%Ll.text.length==0){u=[];for(var c=0;c<Ll.text.length;c++)u.push(o.splitLines(Ll.text[c]))}}else a.length==n.ranges.length&&e.options.pasteLinesPerSelection&&(u=q(a,function(e){return[e]}));for(var h=e.curOp.updateInput,f=n.ranges.length-1;0<=f;f--){var d=n.ranges[f],p=d.from(),g=d.to();d.empty()&&(r&&0<r?p=et(p.line,p.ch-r):e.state.overwrite&&!s?g=et(g.line,Math.min(Xe(o,g.line).text.length,g.ch+$(a).length)):s&&Ll&&Ll.lineWise&&Ll.text.join("\n")==t&&(p=g=et(p.line,0)));var v={from:p,to:g,text:u?u[f%u.length]:a,origin:i||(s?"paste":e.state.cutIncoming>l?"cut":"+input")};no(e.doc,v),lr(e,"inputRead",e,v)}t&&!s&&Nl(e,t),Mn(e),e.curOp.updateInput<2&&(e.curOp.updateInput=h),e.curOp.typing=!0,e.state.pasteIncoming=e.state.cutIncoming=-1}function Ml(e,t){var r=e.clipboardData&&e.clipboardData.getData("Text");if(r)return e.preventDefault(),t.isReadOnly()||t.options.disableInput||_n(t,function(){return Tl(t,r,0,null,"paste")}),!0}function Nl(e,t){if(e.options.electricChars&&e.options.smartIndent)for(var r=e.doc.sel,n=r.ranges.length-1;0<=n;n--){var i=r.ranges[n];if(!(100<i.head.ch||n&&r.ranges[n-1].head.line==i.head.line)){var o=e.getModeAt(i.head),l=!1;if(o.electricChars){for(var s=0;s<o.electricChars.length;s++)if(-1<t.indexOf(o.electricChars.charAt(s))){l=Sl(e,i.head.line,"smart");break}}else o.electricInput&&o.electricInput.test(Xe(e.doc,i.head.line).text.slice(0,i.head.ch))&&(l=Sl(e,i.head.line,"smart"));l&&lr(e,"electricInput",e,i.head.line)}}}function Ol(e){for(var t=[],r=[],n=0;n<e.doc.sel.ranges.length;n++){var i=e.doc.sel.ranges[n].head.line,o={anchor:et(i,0),head:et(i+1,0)};r.push(o),t.push(e.getRange(o.anchor,o.head))}return{text:t,ranges:r}}function Al(e,t,r,n){e.setAttribute("autocorrect",r?"":"off"),e.setAttribute("autocapitalize",n?"":"off"),e.setAttribute("spellcheck",!!t)}function Dl(){var e=A("textarea",null,null,"position: absolute; bottom: -1em; padding: 0; width: 1px; height: 1em; outline: none"),t=A("div",[e],null,"overflow: hidden; position: relative; width: 3px; height: 0px;");return b?e.style.width="1000px":e.setAttribute("wrap","off"),c&&(e.style.border="1px solid black"),Al(e),t}function Wl(n,i,o,e,l){var t=i,r=o,s=Xe(n,i.line);function a(e){var t,r;if(null==(t=l?function(t,r,s,e){var a=ce(r,t.doc.direction);if(!a)return Yo(r,s,e);s.ch>=r.text.length?(s.ch=r.text.length,s.sticky="before"):s.ch<=0&&(s.ch=0,s.sticky="after");var n=ae(a,s.ch,s.sticky),i=a[n];if("ltr"==t.doc.direction&&i.level%2==0&&(0<e?i.to>s.ch:i.from<s.ch))return Yo(r,s,e);var o,u=function(e,t){return Xo(r,e instanceof et?e.ch:e,t)},l=function(e){return t.options.lineWrapping?(o=o||Or(t,r),$r(t,r,o,e)):{begin:0,end:r.text.length}},c=l("before"==s.sticky?u(s,-1):s.ch);if("rtl"==t.doc.direction||1==i.level){var h=1==i.level==e<0,f=u(s,h?1:-1);if(null!=f&&(h?f<=i.to&&f<=c.end:f>=i.from&&f>=c.begin)){var d=h?"before":"after";return new et(s.line,f,d)}}var p=function(e,t,r){for(var n=function(e,t){return t?new et(s.line,u(e,1),"before"):new et(s.line,e,"after")};0<=e&&e<a.length;e+=t){var i=a[e],o=0<t==(1!=i.level),l=o?r.begin:u(r.end,-1);if(i.from<=l&&l<i.to)return n(l,o);if(l=o?i.from:u(i.to,-1),r.begin<=l&&l<r.end)return n(l,o)}},g=p(n+e,e,c);if(g)return g;var v=0<e?c.end:u(c.begin,-1);return null==v||0<e&&v==r.text.length||!(g=p(0<e?0:a.length-1,e,l(v)))?null:g}(n.cm,s,i,o):Yo(s,i,o))){if(e||(r=i.line+o)<n.first||r>=n.first+n.size||(i=new et(r,i.ch,i.sticky),!(s=Xe(n,r))))return!1;i=_o(l,n.cm,s,i.line,o)}else i=t;return!0}if("char"==e)a();else if("column"==e)a(!0);else if("word"==e||"group"==e)for(var u=null,c="group"==e,h=n.cm&&n.cm.getHelper(i,"wordChars"),f=!0;!(o<0)||a(!f);f=!1){var d=s.text.charAt(i.ch)||"\n",p=te(d,h)?"w":c&&"\n"==d?"n":!c||/\s/.test(d)?null:"p";if(!c||f||p||(p="s"),u&&u!=p){o<0&&(o=1,a(),i.sticky="after");break}if(p&&(u=p),0<o&&!a(!f))break}var g=Ji(n,i,t,r,!0);return rt(t,g)&&(g.hitSide=!0),g}function Hl(e,t,r,n){var i,o,l=e.doc,s=t.left;if("page"==n){var a=Math.min(e.display.wrapper.clientHeight,window.innerHeight||document.documentElement.clientHeight),u=Math.max(a-.5*Qr(e.display),3);i=(0<r?t.bottom:t.top)+r*u}else"line"==n&&(i=0<r?t.bottom+3:t.top-3);for(;(o=Yr(e,s,i)).outside;){if(r<0?i<=0:i>=l.height){o.hitSide=!0;break}i+=5*r}return o}var Fl=function(e){this.cm=e,this.lastAnchorNode=this.lastAnchorOffset=this.lastFocusNode=this.lastFocusOffset=null,this.polling=new R,this.composing=null,this.gracePeriod=!1,this.readDOMTimeout=null};function Pl(e,t){var r=Nr(e,t.line);if(!r||r.hidden)return null;var n=Xe(e.doc,t.line),i=Tr(r,n,t.line),o=ce(n,e.doc.direction),l="left";o&&(l=ae(o,t.ch)%2?"right":"left");var s=Hr(i.map,t.ch,l);return s.offset="right"==s.collapse?s.end:s.start,s}function El(e,t){return t&&(e.bad=!0),e}function Il(e,t,r){var n;if(t==e.display.lineDiv){if(!(n=e.display.lineDiv.childNodes[r]))return El(e.clipPos(et(e.display.viewTo-1)),!0);t=null,r=0}else for(n=t;;n=n.parentNode){if(!n||n==e.display.lineDiv)return null;if(n.parentNode&&n.parentNode==e.display.lineDiv)break}for(var i=0;i<e.display.view.length;i++){var o=e.display.view[i];if(o.node==n)return zl(o,t,r)}}function zl(u,e,t){var r=u.text.firstChild,n=!1;if(!e||!D(r,e))return El(et(qe(u.line),0),!0);if(e==r&&(n=!0,e=r.childNodes[t],t=0,!e)){var i=u.rest?$(u.rest):u.line;return El(et(qe(i),i.text.length),n)}var o=3==e.nodeType?e:null,l=e;for(o||1!=e.childNodes.length||3!=e.firstChild.nodeType||(o=e.firstChild,t&&(t=o.nodeValue.length));l.parentNode!=r;)l=l.parentNode;var c=u.measure,h=c.maps;function s(e,t,r){for(var n=-1;n<(h?h.length:0);n++)for(var i=n<0?c.map:h[n],o=0;o<i.length;o+=3){var l=i[o+2];if(l==e||l==t){var s=qe(n<0?u.line:u.rest[n]),a=i[o]+r;return(r<0||l!=e)&&(a=i[o+(r?1:0)]),et(s,a)}}}var a=s(o,l,t);if(a)return El(a,n);for(var f=l.nextSibling,d=o?o.nodeValue.length-t:0;f;f=f.nextSibling){if(a=s(f,f.firstChild,0))return El(et(a.line,a.ch-d),n);d+=f.textContent.length}for(var p=l.previousSibling,g=t;p;p=p.previousSibling){if(a=s(p,p.firstChild,-1))return El(et(a.line,a.ch+g),n);g+=p.textContent.length}}Fl.prototype.init=function(e){var t=this,l=this,s=l.cm,a=l.div=e.lineDiv;function r(e){if(!ve(s,e)){if(s.somethingSelected())kl({lineWise:!1,text:s.getSelections()}),"cut"==e.type&&s.replaceSelection("",null,"cut");else{if(!s.options.lineWiseCopyCut)return;var t=Ol(s);kl({lineWise:!0,text:t.text}),"cut"==e.type&&s.operation(function(){s.setSelections(t.ranges,0,V),s.replaceSelection("",null,"cut")})}if(e.clipboardData){e.clipboardData.clearData();var r=Ll.text.join("\n");if(e.clipboardData.setData("Text",r),e.clipboardData.getData("Text")==r)return void e.preventDefault()}var n=Dl(),i=n.firstChild;s.display.lineSpace.insertBefore(n,s.display.lineSpace.firstChild),i.value=Ll.text.join("\n");var o=document.activeElement;P(i),setTimeout(function(){s.display.lineSpace.removeChild(n),o.focus(),o==a&&l.showPrimarySelection()},50)}}Al(a,s.options.spellcheck,s.options.autocorrect,s.options.autocapitalize),fe(a,"paste",function(e){ve(s,e)||Ml(e,s)||C<=11&&setTimeout($n(s,function(){return t.updateFromDOM()}),20)}),fe(a,"compositionstart",function(e){t.composing={data:e.data,done:!1}}),fe(a,"compositionupdate",function(e){t.composing||(t.composing={data:e.data,done:!1})}),fe(a,"compositionend",function(e){t.composing&&(e.data!=t.composing.data&&t.readFromDOMSoon(),t.composing.done=!0)}),fe(a,"touchstart",function(){return l.forceCompositionEnd()}),fe(a,"input",function(){t.composing||t.readFromDOMSoon()}),fe(a,"copy",r),fe(a,"cut",r)},Fl.prototype.prepareSelection=function(){var e=dn(this.cm,!1);return e.focus=this.cm.state.focused,e},Fl.prototype.showSelection=function(e,t){e&&this.cm.display.view.length&&((e.focus||t)&&this.showPrimarySelection(),this.showMultipleSelections(e))},Fl.prototype.getSelection=function(){return this.cm.display.wrapper.ownerDocument.getSelection()},Fl.prototype.showPrimarySelection=function(){var e=this.getSelection(),t=this.cm,r=t.doc.sel.primary(),n=r.from(),i=r.to();if(t.display.viewTo==t.display.viewFrom||n.line>=t.display.viewTo||i.line<t.display.viewFrom)e.removeAllRanges();else{var o=Il(t,e.anchorNode,e.anchorOffset),l=Il(t,e.focusNode,e.focusOffset);if(!o||o.bad||!l||l.bad||0!=tt(ot(o,l),n)||0!=tt(it(o,l),i)){var s=t.display.view,a=n.line>=t.display.viewFrom&&Pl(t,n)||{node:s[0].measure.map[2],offset:0},u=i.line<t.display.viewTo&&Pl(t,i);if(!u){var c=s[s.length-1].measure,h=c.maps?c.maps[c.maps.length-1]:c.map;u={node:h[h.length-1],offset:h[h.length-2]-h[h.length-3]}}if(a&&u){var f,d=e.rangeCount&&e.getRangeAt(0);try{f=k(a.node,a.offset,u.offset,u.node)}catch(e){}f&&(!g&&t.state.focused?(e.collapse(a.node,a.offset),f.collapsed||(e.removeAllRanges(),e.addRange(f))):(e.removeAllRanges(),e.addRange(f)),d&&null==e.anchorNode?e.addRange(d):g&&this.startGracePeriod()),this.rememberSelection()}else e.removeAllRanges()}}},Fl.prototype.startGracePeriod=function(){var e=this;clearTimeout(this.gracePeriod),this.gracePeriod=setTimeout(function(){e.gracePeriod=!1,e.selectionChanged()&&e.cm.operation(function(){return e.cm.curOp.selectionChanged=!0})},20)},Fl.prototype.showMultipleSelections=function(e){N(this.cm.display.cursorDiv,e.cursors),N(this.cm.display.selectionDiv,e.selection)},Fl.prototype.rememberSelection=function(){var e=this.getSelection();this.lastAnchorNode=e.anchorNode,this.lastAnchorOffset=e.anchorOffset,this.lastFocusNode=e.focusNode,this.lastFocusOffset=e.focusOffset},Fl.prototype.selectionInEditor=function(){var e=this.getSelection();if(!e.rangeCount)return!1;var t=e.getRangeAt(0).commonAncestorContainer;return D(this.div,t)},Fl.prototype.focus=function(){"nocursor"!=this.cm.options.readOnly&&(this.selectionInEditor()||this.showSelection(this.prepareSelection(),!0),this.div.focus())},Fl.prototype.blur=function(){this.div.blur()},Fl.prototype.getField=function(){return this.div},Fl.prototype.supportsTouch=function(){return!0},Fl.prototype.receivedFocus=function(){var t=this;this.selectionInEditor()?this.pollSelection():_n(this.cm,function(){return t.cm.curOp.selectionChanged=!0}),this.polling.set(this.cm.options.pollInterval,function e(){t.cm.state.focused&&(t.pollSelection(),t.polling.set(t.cm.options.pollInterval,e))})},Fl.prototype.selectionChanged=function(){var e=this.getSelection();return e.anchorNode!=this.lastAnchorNode||e.anchorOffset!=this.lastAnchorOffset||e.focusNode!=this.lastFocusNode||e.focusOffset!=this.lastFocusOffset},Fl.prototype.pollSelection=function(){if(null==this.readDOMTimeout&&!this.gracePeriod&&this.selectionChanged()){var e=this.getSelection(),t=this.cm;if(h&&l&&this.cm.display.gutterSpecs.length&&function(e){for(var t=e;t;t=t.parentNode)if(/CodeMirror-gutter-wrapper/.test(t.className))return!0;return!1}(e.anchorNode))return this.cm.triggerOnKeyDown({type:"keydown",keyCode:8,preventDefault:Math.abs}),this.blur(),void this.focus();if(!this.composing){this.rememberSelection();var r=Il(t,e.anchorNode,e.anchorOffset),n=Il(t,e.focusNode,e.focusOffset);r&&n&&_n(t,function(){Yi(t.doc,bi(r,n),V),(r.bad||n.bad)&&(t.curOp.selectionChanged=!0)})}}},Fl.prototype.pollContent=function(){null!=this.readDOMTimeout&&(clearTimeout(this.readDOMTimeout),this.readDOMTimeout=null);var e,t,r,n=this.cm,i=n.display,o=n.doc.sel.primary(),l=o.from(),s=o.to();if(0==l.ch&&l.line>n.firstLine()&&(l=et(l.line-1,Xe(n.doc,l.line-1).length)),s.ch==Xe(n.doc,s.line).text.length&&s.line<n.lastLine()&&(s=et(s.line+1,0)),l.line<i.viewFrom||s.line>i.viewTo-1)return!1;l.line==i.viewFrom||0==(e=ln(n,l.line))?(t=qe(i.view[0].line),r=i.view[0].node):(t=qe(i.view[e].line),r=i.view[e-1].node.nextSibling);var a,u,c=ln(n,s.line);if(c==i.view.length-1?(a=i.viewTo-1,u=i.lineDiv.lastChild):(a=qe(i.view[c+1].line)-1,u=i.view[c+1].node.previousSibling),!r)return!1;for(var h=n.doc.splitLines(function(a,e,t,u,c){var r="",h=!1,f=a.doc.lineSeparator(),d=!1;function p(){h&&(r+=f,d&&(r+=f),h=d=!1)}function g(e){e&&(p(),r+=e)}function v(e){if(1==e.nodeType){var t=e.getAttribute("cm-text");if(t)return void g(t);var r,n=e.getAttribute("cm-marker");if(n){var i=a.findMarks(et(u,0),et(c+1,0),(s=+n,function(e){return e.id==s}));return void(i.length&&(r=i[0].find(0))&&g(Ye(a.doc,r.from,r.to).join(f)))}if("false"==e.getAttribute("contenteditable"))return;var o=/^(pre|div|p|li|table|br)$/i.test(e.nodeName);if(!/^br$/i.test(e.nodeName)&&0==e.textContent.length)return;o&&p();for(var l=0;l<e.childNodes.length;l++)v(e.childNodes[l]);/^(pre|p)$/i.test(e.nodeName)&&(d=!0),o&&(h=!0)}else 3==e.nodeType&&g(e.nodeValue.replace(/\u200b/g,"").replace(/\u00a0/g," "));var s}for(;v(e),e!=t;)e=e.nextSibling,d=!1;return r}(n,r,u,t,a)),f=Ye(n.doc,et(t,0),et(a,Xe(n.doc,a).text.length));1<h.length&&1<f.length;)if($(h)==$(f))h.pop(),f.pop(),a--;else{if(h[0]!=f[0])break;h.shift(),f.shift(),t++}for(var d=0,p=0,g=h[0],v=f[0],m=Math.min(g.length,v.length);d<m&&g.charCodeAt(d)==v.charCodeAt(d);)++d;for(var y=$(h),b=$(f),w=Math.min(y.length-(1==h.length?d:0),b.length-(1==f.length?d:0));p<w&&y.charCodeAt(y.length-p-1)==b.charCodeAt(b.length-p-1);)++p;if(1==h.length&&1==f.length&&t==l.line)for(;d&&d>l.ch&&y.charCodeAt(y.length-p-1)==b.charCodeAt(b.length-p-1);)d--,p++;h[h.length-1]=y.slice(0,y.length-p).replace(/^\u200b+/,""),h[0]=h[0].slice(d).replace(/\u200b+$/,"");var x=et(t,d),C=et(a,f.length?$(f).length-p:0);return 1<h.length||h[0]||tt(x,C)?(ao(n.doc,h,x,C,"+input"),!0):void 0},Fl.prototype.ensurePolled=function(){this.forceCompositionEnd()},Fl.prototype.reset=function(){this.forceCompositionEnd()},Fl.prototype.forceCompositionEnd=function(){this.composing&&(clearTimeout(this.readDOMTimeout),this.composing=null,this.updateFromDOM(),this.div.blur(),this.div.focus())},Fl.prototype.readFromDOMSoon=function(){var e=this;null==this.readDOMTimeout&&(this.readDOMTimeout=setTimeout(function(){if(e.readDOMTimeout=null,e.composing){if(!e.composing.done)return;e.composing=null}e.updateFromDOM()},80))},Fl.prototype.updateFromDOM=function(){var e=this;!this.cm.isReadOnly()&&this.pollContent()||_n(this.cm,function(){return sn(e.cm)})},Fl.prototype.setUneditable=function(e){e.contentEditable="false"},Fl.prototype.onKeyPress=function(e){0==e.charCode||this.composing||(e.preventDefault(),this.cm.isReadOnly()||$n(this.cm,Tl)(this.cm,String.fromCharCode(null==e.charCode?e.keyCode:e.charCode),0))},Fl.prototype.readOnlyChanged=function(e){this.div.contentEditable=String("nocursor"!=e)},Fl.prototype.onContextMenu=function(){},Fl.prototype.resetPosition=function(){},Fl.prototype.needsContentAttribute=!0;var Rl,Bl,Gl,Ul=function(e){this.cm=e,this.prevInput="",this.pollingFast=!1,this.polling=new R,this.hasSelection=!1,this.composing=null};Ul.prototype.init=function(r){var e=this,n=this,i=this.cm;this.createField(r);var o=this.textarea;function t(e){if(!ve(i,e)){if(i.somethingSelected())kl({lineWise:!1,text:i.getSelections()});else{if(!i.options.lineWiseCopyCut)return;var t=Ol(i);kl({lineWise:!0,text:t.text}),"cut"==e.type?i.setSelections(t.ranges,null,V):(n.prevInput="",o.value=t.text.join("\n"),P(o))}"cut"==e.type&&(i.state.cutIncoming=+new Date)}}r.wrapper.insertBefore(this.wrapper,r.wrapper.firstChild),c&&(o.style.width="0px"),fe(o,"input",function(){x&&9<=C&&e.hasSelection&&(e.hasSelection=null),n.poll()}),fe(o,"paste",function(e){ve(i,e)||Ml(e,i)||(i.state.pasteIncoming=+new Date,n.fastPoll())}),fe(o,"cut",t),fe(o,"copy",t),fe(r.scroller,"paste",function(e){if(!br(r,e)&&!ve(i,e)){if(!o.dispatchEvent)return i.state.pasteIncoming=+new Date,void n.focus();var t=new Event("paste");t.clipboardData=e.clipboardData,o.dispatchEvent(t)}}),fe(r.lineSpace,"selectstart",function(e){br(r,e)||we(e)}),fe(o,"compositionstart",function(){var e=i.getCursor("from");n.composing&&n.composing.range.clear(),n.composing={start:e,range:i.markText(e,i.getCursor("to"),{className:"CodeMirror-composing"})}}),fe(o,"compositionend",function(){n.composing&&(n.poll(),n.composing.range.clear(),n.composing=null)})},Ul.prototype.createField=function(e){this.wrapper=Dl(),this.textarea=this.wrapper.firstChild},Ul.prototype.prepareSelection=function(){var e=this.cm,t=e.display,r=e.doc,n=dn(e);if(e.options.moveInputWithCursor){var i=Kr(e,r.sel.primary().head,"div"),o=t.wrapper.getBoundingClientRect(),l=t.lineDiv.getBoundingClientRect();n.teTop=Math.max(0,Math.min(t.wrapper.clientHeight-10,i.top+l.top-o.top)),n.teLeft=Math.max(0,Math.min(t.wrapper.clientWidth-10,i.left+l.left-o.left))}return n},Ul.prototype.showSelection=function(e){var t=this.cm.display;N(t.cursorDiv,e.cursors),N(t.selectionDiv,e.selection),null!=e.teTop&&(this.wrapper.style.top=e.teTop+"px",this.wrapper.style.left=e.teLeft+"px")},Ul.prototype.reset=function(e){if(!this.contextMenuPending&&!this.composing){var t=this.cm;if(t.somethingSelected()){this.prevInput="";var r=t.getSelection();this.textarea.value=r,t.state.focused&&P(this.textarea),x&&9<=C&&(this.hasSelection=r)}else e||(this.prevInput=this.textarea.value="",x&&9<=C&&(this.hasSelection=null))}},Ul.prototype.getField=function(){return this.textarea},Ul.prototype.supportsTouch=function(){return!1},Ul.prototype.focus=function(){if("nocursor"!=this.cm.options.readOnly&&(!f||W()!=this.textarea))try{this.textarea.focus()}catch(e){}},Ul.prototype.blur=function(){this.textarea.blur()},Ul.prototype.resetPosition=function(){this.wrapper.style.top=this.wrapper.style.left=0},Ul.prototype.receivedFocus=function(){this.slowPoll()},Ul.prototype.slowPoll=function(){var e=this;this.pollingFast||this.polling.set(this.cm.options.pollInterval,function(){e.poll(),e.cm.state.focused&&e.slowPoll()})},Ul.prototype.fastPoll=function(){var t=!1,r=this;r.pollingFast=!0,r.polling.set(20,function e(){r.poll()||t?(r.pollingFast=!1,r.slowPoll()):(t=!0,r.polling.set(60,e))})},Ul.prototype.poll=function(){var e=this,t=this.cm,r=this.textarea,n=this.prevInput;if(this.contextMenuPending||!t.state.focused||He(r)&&!n&&!this.composing||t.isReadOnly()||t.options.disableInput||t.state.keySeq)return!1;var i=r.value;if(i==n&&!t.somethingSelected())return!1;if(x&&9<=C&&this.hasSelection===i||w&&/[\uf700-\uf7ff]/.test(i))return t.display.input.reset(),!1;if(t.doc.sel==t.display.selForContextMenu){var o=i.charCodeAt(0);if(8203!=o||n||(n="​"),8666==o)return this.reset(),this.cm.execCommand("undo")}for(var l=0,s=Math.min(n.length,i.length);l<s&&n.charCodeAt(l)==i.charCodeAt(l);)++l;return _n(t,function(){Tl(t,i.slice(l),n.length-l,null,e.composing?"*compose":null),1e3<i.length||-1<i.indexOf("\n")?r.value=e.prevInput="":e.prevInput=i,e.composing&&(e.composing.range.clear(),e.composing.range=t.markText(e.composing.start,t.getCursor("to"),{className:"CodeMirror-composing"}))}),!0},Ul.prototype.ensurePolled=function(){this.pollingFast&&this.poll()&&(this.pollingFast=!1)},Ul.prototype.onKeyPress=function(){x&&9<=C&&(this.hasSelection=null),this.fastPoll()},Ul.prototype.onContextMenu=function(e){var r=this,n=r.cm,i=n.display,o=r.textarea;r.contextMenuPending&&r.contextMenuPending();var t=on(n,e),l=i.scroller.scrollTop;if(t&&!v){n.options.resetSelectionOnContextMenu&&-1==n.doc.sel.contains(t)&&$n(n,Yi)(n.doc,bi(t),V);var s,a=o.style.cssText,u=r.wrapper.style.cssText,c=r.wrapper.offsetParent.getBoundingClientRect();if(r.wrapper.style.cssText="position: static",o.style.cssText="position: absolute; width: 30px; height: 30px;\n      top: "+(e.clientY-c.top-5)+"px; left: "+(e.clientX-c.left-5)+"px;\n      z-index: 1000; background: "+(x?"rgba(255, 255, 255, .05)":"transparent")+";\n      outline: none; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);",b&&(s=window.scrollY),i.input.focus(),b&&window.scrollTo(null,s),i.input.reset(),n.somethingSelected()||(o.value=r.prevInput=" "),r.contextMenuPending=d,i.selForContextMenu=n.doc.sel,clearTimeout(i.detectingSelectAll),x&&9<=C&&f(),S){Se(e);var h=function(){pe(window,"mouseup",h),setTimeout(d,20)};fe(window,"mouseup",h)}else setTimeout(d,50)}function f(){if(null!=o.selectionStart){var e=n.somethingSelected(),t="​"+(e?o.value:"");o.value="⇚",o.value=t,r.prevInput=e?"":"​",o.selectionStart=1,o.selectionEnd=t.length,i.selForContextMenu=n.doc.sel}}function d(){if(r.contextMenuPending==d&&(r.contextMenuPending=!1,r.wrapper.style.cssText=u,o.style.cssText=a,x&&C<9&&i.scrollbars.setScrollTop(i.scroller.scrollTop=l),null!=o.selectionStart)){(!x||x&&C<9)&&f();var e=0,t=function(){i.selForContextMenu==n.doc.sel&&0==o.selectionStart&&0<o.selectionEnd&&"​"==r.prevInput?$n(n,to)(n):e++<10?i.detectingSelectAll=setTimeout(t,500):(i.selForContextMenu=null,i.input.reset())};i.detectingSelectAll=setTimeout(t,200)}}},Ul.prototype.readOnlyChanged=function(e){e||this.reset(),this.textarea.disabled="nocursor"==e},Ul.prototype.setUneditable=function(){},Ul.prototype.needsContentAttribute=!1,function(i){var o=i.optionHandlers;function e(e,t,n,r){i.defaults[e]=t,n&&(o[e]=r?function(e,t,r){r!=vl&&n(e,t,r)}:n)}i.defineOption=e,i.Init=vl,e("value","",function(e,t){return e.setValue(t)},!0),e("mode",null,function(e,t){e.doc.modeOption=t,Li(e)},!0),e("indentUnit",2,Li,!0),e("indentWithTabs",!1),e("smartIndent",!0),e("tabSize",4,function(e){ki(e),Ir(e),sn(e)},!0),e("lineSeparator",null,function(e,n){if(e.doc.lineSep=n){var i=[],o=e.doc.first;e.doc.iter(function(e){for(var t=0;;){var r=e.text.indexOf(n,t);if(-1==r)break;t=r+n.length,i.push(et(o,r))}o++});for(var t=i.length-1;0<=t;t--)ao(e.doc,n,i[t],et(i[t].line,i[t].ch+n.length))}}),e("specialChars",/[\u0000-\u001f\u007f-\u009f\u00ad\u061c\u200b-\u200f\u2028\u2029\ufeff\ufff9-\ufffc]/g,function(e,t,r){e.state.specialChars=new RegExp(t.source+(t.test("\t")?"":"|\t"),"g"),r!=vl&&e.refresh()}),e("specialCharPlaceholder",Zt,function(e){return e.refresh()},!0),e("electricChars",!0),e("inputStyle",f?"contenteditable":"textarea",function(){throw new Error("inputStyle can not (yet) be changed in a running editor")},!0),e("spellcheck",!1,function(e,t){return e.getInputField().spellcheck=t},!0),e("autocorrect",!1,function(e,t){return e.getInputField().autocorrect=t},!0),e("autocapitalize",!1,function(e,t){return e.getInputField().autocapitalize=t},!0),e("rtlMoveVisually",!p),e("wholeLineUpdateBefore",!0),e("theme","default",function(e){gl(e),ci(e)},!0),e("keyMap","default",function(e,t,r){var n=Ko(t),i=r!=vl&&Ko(r);i&&i.detach&&i.detach(e,n),n.attach&&n.attach(e,i||null)}),e("extraKeys",null),e("configureMouse",null),e("lineWrapping",!1,wl,!0),e("gutters",[],function(e,t){e.display.gutterSpecs=ai(t,e.options.lineNumbers),ci(e)},!0),e("fixedGutter",!0,function(e,t){e.display.gutters.style.left=t?tn(e.display)+"px":"0",e.refresh()},!0),e("coverGutterNextToScrollbar",!1,function(e){return In(e)},!0),e("scrollbarStyle","native",function(e){Bn(e),In(e),e.display.scrollbars.setScrollTop(e.doc.scrollTop),e.display.scrollbars.setScrollLeft(e.doc.scrollLeft)},!0),e("lineNumbers",!1,function(e,t){e.display.gutterSpecs=ai(e.options.gutters,t),ci(e)},!0),e("firstLineNumber",1,ci,!0),e("lineNumberFormatter",function(e){return e},ci,!0),e("showCursorWhenSelecting",!1,fn,!0),e("resetSelectionOnContextMenu",!0),e("lineWiseCopyCut",!0),e("pasteLinesPerSelection",!0),e("selectionsMayTouch",!1),e("readOnly",!1,function(e,t){"nocursor"==t&&(xn(e),e.display.input.blur()),e.display.input.readOnlyChanged(t)}),e("disableInput",!1,function(e,t){t||e.display.input.reset()},!0),e("dragDrop",!0,bl),e("allowDropFileTypes",null),e("cursorBlinkRate",530),e("cursorScrollMargin",0),e("cursorHeight",1,fn,!0),e("singleCursorHeightPerLine",!0,fn,!0),e("workTime",100),e("workDelay",100),e("flattenSpans",!0,ki,!0),e("addModeClass",!1,ki,!0),e("pollInterval",100),e("undoDepth",200,function(e,t){return e.doc.history.undoDepth=t}),e("historyEventDelay",1250),e("viewportMargin",10,function(e){return e.refresh()},!0),e("maxHighlightLength",1e4,ki,!0),e("moveInputWithCursor",!0,function(e,t){t||e.display.input.resetPosition()}),e("tabindex",null,function(e,t){return e.display.input.getField().tabIndex=t||""}),e("autofocus",null),e("direction","ltr",function(e,t){return e.doc.setDirection(t)},!0),e("phrases",null)}(xl),Bl=(Rl=xl).optionHandlers,Gl=Rl.helpers={},Rl.prototype={constructor:Rl,focus:function(){window.focus(),this.display.input.focus()},setOption:function(e,t){var r=this.options,n=r[e];r[e]==t&&"mode"!=e||(r[e]=t,Bl.hasOwnProperty(e)&&$n(this,Bl[e])(this,t,n),ge(this,"optionChange",this,e))},getOption:function(e){return this.options[e]},getDoc:function(){return this.doc},addKeyMap:function(e,t){this.state.keyMaps[t?"push":"unshift"](Ko(e))},removeKeyMap:function(e){for(var t=this.state.keyMaps,r=0;r<t.length;++r)if(t[r]==e||t[r].name==e)return t.splice(r,1),!0},addOverlay:qn(function(e,t){var r=e.token?e:Rl.getMode(this.options,e);if(r.startState)throw new Error("Overlays may not be stateful.");!function(e,t,r){for(var n=0,i=r(t);n<e.length&&r(e[n])<=i;)n++;e.splice(n,0,t)}(this.state.overlays,{mode:r,modeSpec:e,opaque:t&&t.opaque,priority:t&&t.priority||0},function(e){return e.priority}),this.state.modeGen++,sn(this)}),removeOverlay:qn(function(e){for(var t=this.state.overlays,r=0;r<t.length;++r){var n=t[r].modeSpec;if(n==e||"string"==typeof e&&n.name==e)return t.splice(r,1),this.state.modeGen++,void sn(this)}}),indentLine:qn(function(e,t,r){"string"!=typeof t&&"number"!=typeof t&&(t=null==t?this.options.smartIndent?"smart":"prev":t?"add":"subtract"),Qe(this.doc,e)&&Sl(this,e,t,r)}),indentSelection:qn(function(e){for(var t=this,r=this.doc.sel.ranges,n=-1,i=0;i<r.length;i++){var o=r[i];if(o.empty())o.head.line>n&&(Sl(t,o.head.line,e,!0),n=o.head.line,i==t.doc.sel.primIndex&&Mn(t));else{var l=o.from(),s=o.to(),a=Math.max(n,l.line);n=Math.min(t.lastLine(),s.line-(s.ch?0:1))+1;for(var u=a;u<n;++u)Sl(t,u,e);var c=t.doc.sel.ranges;0==l.ch&&r.length==c.length&&0<c[i].from().ch&&Ki(t.doc,i,new mi(l,c[i].to()),V)}}}),getTokenAt:function(e,t){return yt(this,e,t)},getLineTokens:function(e,t){return yt(this,et(e),t,!0)},getTokenTypeAt:function(e){e=st(this.doc,e);var t,r=ft(this,Xe(this.doc,e.line)),n=0,i=(r.length-1)/2,o=e.ch;if(0==o)t=r[2];else for(;;){var l=n+i>>1;if((l?r[2*l-1]:0)>=o)i=l;else{if(!(r[2*l+1]<o)){t=r[2*l+2];break}n=l+1}}var s=t?t.indexOf("overlay "):-1;return s<0?t:0==s?null:t.slice(0,s-1)},getModeAt:function(e){var t=this.doc.mode;return t.innerMode?Rl.innerMode(t,this.getTokenAt(e).state).mode:t},getHelper:function(e,t){return this.getHelpers(e,t)[0]},getHelpers:function(e,t){var r=[];if(!Gl.hasOwnProperty(t))return r;var n=Gl[t],i=this.getModeAt(e);if("string"==typeof i[t])n[i[t]]&&r.push(n[i[t]]);else if(i[t])for(var o=0;o<i[t].length;o++){var l=n[i[t][o]];l&&r.push(l)}else i.helperType&&n[i.helperType]?r.push(n[i.helperType]):n[i.name]&&r.push(n[i.name]);for(var s=0;s<n._global.length;s++){var a=n._global[s];a.pred(i,this)&&-1==B(r,a.val)&&r.push(a.val)}return r},getStateAfter:function(e,t){var r=this.doc;return dt(this,(e=lt(r,null==e?r.first+r.size-1:e))+1,t).state},cursorCoords:function(e,t){var r=this.doc.sel.primary();return Kr(this,null==e?r.head:"object"==typeof e?st(this.doc,e):e?r.from():r.to(),t||"page")},charCoords:function(e,t){return Vr(this,st(this.doc,e),t||"page")},coordsChar:function(e,t){return Yr(this,(e=Ur(this,e,t||"page")).left,e.top)},lineAtHeight:function(e,t){return e=Ur(this,{top:e,left:0},t||"page").top,Ze(this.doc,e+this.display.viewOffset)},heightAtLine:function(e,t,r){var n,i=!1;if("number"==typeof e){var o=this.doc.first+this.doc.size-1;e<this.doc.first?e=this.doc.first:o<e&&(e=o,i=!0),n=Xe(this.doc,e)}else n=e;return Gr(this,n,{top:0,left:0},t||"page",r||i).top+(i?this.doc.height-Vt(n):0)},defaultTextHeight:function(){return Qr(this.display)},defaultCharWidth:function(){return Jr(this.display)},getViewport:function(){return{from:this.display.viewFrom,to:this.display.viewTo}},addWidget:function(e,t,r,n,i){var o,l,s,a=this.display,u=(e=Kr(this,st(this.doc,e))).bottom,c=e.left;if(t.style.position="absolute",t.setAttribute("cm-ignore-events","true"),this.display.input.setUneditable(t),a.sizer.appendChild(t),"over"==n)u=e.top;else if("above"==n||"near"==n){var h=Math.max(a.wrapper.clientHeight,this.doc.height),f=Math.max(a.sizer.clientWidth,a.lineSpace.clientWidth);("above"==n||e.bottom+t.offsetHeight>h)&&e.top>t.offsetHeight?u=e.top-t.offsetHeight:e.bottom+t.offsetHeight<=h&&(u=e.bottom),c+t.offsetWidth>f&&(c=f-t.offsetWidth)}t.style.top=u+"px",t.style.left=t.style.right="","right"==i?(c=a.sizer.clientWidth-t.offsetWidth,t.style.right="0px"):("left"==i?c=0:"middle"==i&&(c=(a.sizer.clientWidth-t.offsetWidth)/2),t.style.left=c+"px"),r&&(o=this,l={left:c,top:u,right:c+t.offsetWidth,bottom:u+t.offsetHeight},null!=(s=kn(o,l)).scrollTop&&Dn(o,s.scrollTop),null!=s.scrollLeft&&Hn(o,s.scrollLeft))},triggerOnKeyDown:qn(il),triggerOnKeyPress:qn(ll),triggerOnKeyUp:ol,triggerOnMouseDown:qn(cl),execCommand:function(e){if($o.hasOwnProperty(e))return $o[e].call(null,this)},triggerElectric:qn(function(e){Nl(this,e)}),findPosH:function(e,t,r,n){var i=1;t<0&&(i=-1,t=-t);for(var o=st(this.doc,e),l=0;l<t&&!(o=Wl(this.doc,o,i,r,n)).hitSide;++l);return o},moveH:qn(function(t,r){var n=this;this.extendSelectionsBy(function(e){return n.display.shift||n.doc.extend||e.empty()?Wl(n.doc,e.head,t,r,n.options.rtlMoveVisually):t<0?e.from():e.to()},j)}),deleteH:qn(function(r,n){var e=this.doc.sel,i=this.doc;e.somethingSelected()?i.replaceSelection("",null,"+delete"):jo(this,function(e){var t=Wl(i,e.head,r,n,!1);return r<0?{from:t,to:e.head}:{from:e.head,to:t}})}),findPosV:function(e,t,r,n){var i=1,o=n;t<0&&(i=-1,t=-t);for(var l=st(this.doc,e),s=0;s<t;++s){var a=Kr(this,l,"div");if(null==o?o=a.left:a.left=o,(l=Hl(this,a,i,r)).hitSide)break}return l},moveV:qn(function(n,i){var o=this,l=this.doc,s=[],a=!this.display.shift&&!l.extend&&l.sel.somethingSelected();if(l.extendSelectionsBy(function(e){if(a)return n<0?e.from():e.to();var t=Kr(o,e.head,"div");null!=e.goalColumn&&(t.left=e.goalColumn),s.push(t.left);var r=Hl(o,t,n,i);return"page"==i&&e==l.sel.primary()&&Tn(o,Vr(o,r,"div").top-t.top),r},j),s.length)for(var e=0;e<l.sel.ranges.length;e++)l.sel.ranges[e].goalColumn=s[e]}),findWordAt:function(e){var t=Xe(this.doc,e.line).text,r=e.ch,n=e.ch;if(t){var i=this.getHelper(e,"wordChars");"before"!=e.sticky&&n!=t.length||!r?++n:--r;for(var o=t.charAt(r),l=te(o,i)?function(e){return te(e,i)}:/\s/.test(o)?function(e){return/\s/.test(e)}:function(e){return!/\s/.test(e)&&!te(e)};0<r&&l(t.charAt(r-1));)--r;for(;n<t.length&&l(t.charAt(n));)++n}return new mi(et(e.line,r),et(e.line,n))},toggleOverwrite:function(e){null!=e&&e==this.state.overwrite||((this.state.overwrite=!this.state.overwrite)?H(this.display.cursorDiv,"CodeMirror-overwrite"):T(this.display.cursorDiv,"CodeMirror-overwrite"),ge(this,"overwriteToggle",this,this.state.overwrite))},hasFocus:function(){return this.display.input.getField()==W()},isReadOnly:function(){return!(!this.options.readOnly&&!this.doc.cantEdit)},scrollTo:qn(function(e,t){Nn(this,e,t)}),getScrollInfo:function(){var e=this.display.scroller;return{left:e.scrollLeft,top:e.scrollTop,height:e.scrollHeight-Sr(this)-this.display.barHeight,width:e.scrollWidth-Sr(this)-this.display.barWidth,clientHeight:kr(this),clientWidth:Lr(this)}},scrollIntoView:qn(function(e,t){var r,n;null==e?(e={from:this.doc.sel.primary().head,to:null},null==t&&(t=this.options.cursorScrollMargin)):"number"==typeof e?e={from:et(e,0),to:null}:null==e.from&&(e={from:e,to:null}),e.to||(e.to=e.from),e.margin=t||0,null!=e.from.line?(n=e,On(r=this),r.curOp.scrollToPos=n):An(this,e.from,e.to,e.margin)}),setSize:qn(function(e,t){var r=this,n=function(e){return"number"==typeof e||/^\d+$/.test(String(e))?e+"px":e};null!=e&&(this.display.wrapper.style.width=n(e)),null!=t&&(this.display.wrapper.style.height=n(t)),this.options.lineWrapping&&Er(this);var i=this.display.viewFrom;this.doc.iter(i,this.display.viewTo,function(e){if(e.widgets)for(var t=0;t<e.widgets.length;t++)if(e.widgets[t].noHScroll){an(r,i,"widget");break}++i}),this.curOp.forceUpdate=!0,ge(this,"refresh",this)}),operation:function(e){return _n(this,e)},startOperation:function(){return Un(this)},endOperation:function(){return Vn(this)},refresh:qn(function(){var e=this.display.cachedTextHeight;sn(this),this.curOp.forceUpdate=!0,Ir(this),Nn(this,this.doc.scrollLeft,this.doc.scrollTop),ii(this.display),(null==e||.5<Math.abs(e-Qr(this.display)))&&nn(this),ge(this,"refresh",this)}),swapDoc:qn(function(e){var t=this.doc;return t.cm=null,this.state.selectingText&&this.state.selectingText(),Oi(this,e),Ir(this),this.display.input.reset(),Nn(this,e.scrollLeft,e.scrollTop),this.curOp.forceScroll=!0,lr(this,"swapDoc",this,t),t}),phrase:function(e){var t=this.options.phrases;return t&&Object.prototype.hasOwnProperty.call(t,e)?t[e]:e},getInputField:function(){return this.display.input.getField()},getWrapperElement:function(){return this.display.wrapper},getScrollerElement:function(){return this.display.scroller},getGutterElement:function(){return this.display.gutters}},be(Rl),Rl.registerHelper=function(e,t,r){Gl.hasOwnProperty(e)||(Gl[e]=Rl[e]={_global:[]}),Gl[e][t]=r},Rl.registerGlobalHelper=function(e,t,r,n){Rl.registerHelper(e,t,n),Gl[e]._global.push({pred:r,val:n})};var Vl,Kl="iter insert remove copy getEditor constructor".split(" ");for(var jl in ko.prototype)ko.prototype.hasOwnProperty(jl)&&B(Kl,jl)<0&&(xl.prototype[jl]=function(e){return function(){return e.apply(this.doc,arguments)}}(ko.prototype[jl]));return be(ko),xl.inputStyles={textarea:Ul,contenteditable:Fl},xl.defineMode=function(e){xl.defaults.mode||"null"==e||(xl.defaults.mode=e),function(e,t){2<arguments.length&&(t.dependencies=Array.prototype.slice.call(arguments,2)),Ee[e]=t}.apply(this,arguments)},xl.defineMIME=function(e,t){Ie[e]=t},xl.defineMode("null",function(){return{token:function(e){return e.skipToEnd()}}}),xl.defineMIME("text/plain","null"),xl.defineExtension=function(e,t){xl.prototype[e]=t},xl.defineDocExtension=function(e,t){ko.prototype[e]=t},xl.fromTextArea=function(t,e){if((e=e?I(e):{}).value=t.value,!e.tabindex&&t.tabIndex&&(e.tabindex=t.tabIndex),!e.placeholder&&t.placeholder&&(e.placeholder=t.placeholder),null==e.autofocus){var r=W();e.autofocus=r==t||null!=t.getAttribute("autofocus")&&r==document.body}function n(){t.value=s.getValue()}var i;if(t.form&&(fe(t.form,"submit",n),!e.leaveSubmitMethodAlone)){var o=t.form;i=o.submit;try{var l=o.submit=function(){n(),o.submit=i,o.submit(),o.submit=l}}catch(e){}}e.finishInit=function(e){e.save=n,e.getTextArea=function(){return t},e.toTextArea=function(){e.toTextArea=isNaN,n(),t.parentNode.removeChild(e.getWrapperElement()),t.style.display="",t.form&&(pe(t.form,"submit",n),"function"==typeof t.form.submit&&(t.form.submit=i))}},t.style.display="none";var s=xl(function(e){return t.parentNode.insertBefore(e,t.nextSibling)},e);return s},(Vl=xl).off=pe,Vl.on=fe,Vl.wheelEventPixels=pi,Vl.Doc=ko,Vl.splitLines=We,Vl.countColumn=z,Vl.findColumn=X,Vl.isWordChar=ee,Vl.Pass=U,Vl.signal=ge,Vl.Line=Xt,Vl.changeEnd=wi,Vl.scrollbarModel=Rn,Vl.Pos=et,Vl.cmpPos=tt,Vl.modes=Ee,Vl.mimeModes=Ie,Vl.resolveMode=ze,Vl.getMode=Re,Vl.modeExtensions=Be,Vl.extendMode=Ge,Vl.copyState=Ue,Vl.startState=Ke,Vl.innerMode=Ve,Vl.commands=$o,Vl.keyMap=Io,Vl.keyName=Vo,Vl.isModifierKey=Go,Vl.lookupKey=Bo,Vl.normalizeKeyMap=Ro,Vl.StringStream=je,Vl.SharedTextMarker=xo,Vl.TextMarker=bo,Vl.LineWidget=vo,Vl.e_preventDefault=we,Vl.e_stopPropagation=xe,Vl.e_stop=Se,Vl.addClass=H,Vl.contains=D,Vl.rmClass=T,Vl.keyNames=Ho,xl.version="5.48.4",xl});











//把JavaScript格式搞过来
!function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define(["../../lib/codemirror"],e):e(CodeMirror)}(function(Le){"use strict";Le.defineMode("javascript",function(e,l){var n,a,d=e.indentUnit,p=l.statementIndent,o=l.jsonld,c=l.json||o,u=l.typescript,f=l.wordCharacters||/[\w$\xa1-\uffff]/,s=function(){function e(e){return{type:e,style:"keyword"}}var t=e("keyword a"),r=e("keyword b"),n=e("keyword c"),a=e("keyword d"),i=e("operator"),o={type:"atom",style:"atom"};return{"if":e("if"),"while":t,"with":t,"else":r,"do":r,"try":r,"finally":r,"return":a,"break":a,"continue":a,"new":e("new"),"delete":n,"void":n,"throw":n,"debugger":e("debugger"),"var":e("var"),"const":e("var"),"let":e("var"),"function":e("function"),"catch":e("catch"),"for":e("for"),"switch":e("switch"),"case":e("case"),"default":e("default"),"in":i,"typeof":i,"instanceof":i,"true":o,"false":o,"null":o,"undefined":o,"NaN":o,"Infinity":o,"this":e("this"),"class":e("class"),"super":e("atom"),"yield":n,"export":e("export"),"import":e("import"),"extends":n,"await":n}}(),m=/[+\-*&%=<>!?|~^@]/,v=/^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)"/;function k(e,t,r){return n=e,a=r,t}function y(e,t){var a,r=e.next();if('"'==r||"'"==r)return t.tokenize=(a=r,function(e,t){var r,n=!1;if(o&&"@"==e.peek()&&e.match(v))return t.tokenize=y,k("jsonld-keyword","meta");for(;null!=(r=e.next())&&(r!=a||n);)n=!n&&"\\"==r;return n||(t.tokenize=y),k("string","string")}),t.tokenize(e,t);if("."==r&&e.match(/^\d[\d_]*(?:[eE][+\-]?[\d_]+)?/))return k("number","number");if("."==r&&e.match(".."))return k("spread","meta");if(/[\[\]{}\(\),;\:\.]/.test(r))return k(r);if("="==r&&e.eat(">"))return k("=>","operator");if("0"==r&&e.match(/^(?:x[\dA-Fa-f_]+|o[0-7_]+|b[01_]+)n?/))return k("number","number");if(/\d/.test(r))return e.match(/^[\d_]*(?:n|(?:\.[\d_]*)?(?:[eE][+\-]?[\d_]+)?)?/),k("number","number");if("/"==r)return e.eat("*")?(t.tokenize=w)(e,t):e.eat("/")?(e.skipToEnd(),k("comment","comment")):Ke(e,t,1)?(function(e){for(var t,r=!1,n=!1;null!=(t=e.next());){if(!r){if("/"==t&&!n)return;"["==t?n=!0:n&&"]"==t&&(n=!1)}r=!r&&"\\"==t}}(e),e.match(/^\b(([gimyus])(?![gimyus]*\2))+\b/),k("regexp","string-2")):(e.eat("="),k("operator","operator",e.current()));if("`"==r)return(t.tokenize=b)(e,t);if("#"==r)return e.skipToEnd(),k("error","error");if(m.test(r))return">"==r&&t.lexical&&">"==t.lexical.type||(e.eat("=")?"!"!=r&&"="!=r||e.eat("="):/[<>*+\-]/.test(r)&&(e.eat(r),">"==r&&e.eat(r))),k("operator","operator",e.current());if(f.test(r)){e.eatWhile(f);var n=e.current();if("."!=t.lastType){if(s.propertyIsEnumerable(n)){var i=s[n];return k(i.type,i.style,n)}if("async"==n&&e.match(/^(\s|\/\*.*?\*\/)*[\[\(\w]/,!1))return k("async","keyword",n)}return k("variable","variable",n)}}function w(e,t){for(var r,n=!1;r=e.next();){if("/"==r&&n){t.tokenize=y;break}n="*"==r}return k("comment","comment")}function b(e,t){for(var r,n=!1;null!=(r=e.next());){if(!n&&("`"==r||"$"==r&&e.eat("{"))){t.tokenize=y;break}n=!n&&"\\"==r}return k("quasi","string-2",e.current())}var x="([{}])";function i(e,t){t.fatArrowAt&&(t.fatArrowAt=null);var r=e.string.indexOf("=>",e.start);if(!(r<0)){if(u){var n=/:\s*(?:\w+(?:<[^>]*>|\[\])?|\{[^}]*\})\s*$/.exec(e.string.slice(e.start,r));n&&(r=n.index)}for(var a=0,i=!1,o=r-1;0<=o;--o){var c=e.string.charAt(o),s=x.indexOf(c);if(0<=s&&s<3){if(!a){++o;break}if(0==--a){"("==c&&(i=!0);break}}else if(3<=s&&s<6)++a;else if(f.test(c))i=!0;else if(/["'\/`]/.test(c))for(;;--o){if(0==o)return;if(e.string.charAt(o-1)==c&&"\\"!=e.string.charAt(o-2)){o--;break}}else if(i&&!a){++o;break}}i&&!a&&(t.fatArrowAt=o)}}var h={atom:!0,number:!0,variable:!0,string:!0,regexp:!0,"this":!0,"jsonld-keyword":!0};function g(e,t,r,n,a,i){this.indented=e,this.column=t,this.type=r,this.prev=a,this.info=i,null!=n&&(this.align=n)}function j(e,t){for(var r=e.localVars;r;r=r.next)if(r.name==t)return!0;for(var n=e.context;n;n=n.prev)for(r=n.vars;r;r=r.next)if(r.name==t)return!0}var M={state:null,column:null,marked:null,cc:null};function A(){for(var e=arguments.length-1;0<=e;e--)M.cc.push(arguments[e])}function V(){return A.apply(null,arguments),!0}function E(e,t){for(var r=t;r;r=r.next)if(r.name==e)return!0;return!1}function r(e){var t=M.state;if(M.marked="def",t.context)if("var"==t.lexical.info&&t.context&&t.context.block){var r=function e(t,r){{if(r){if(r.block){var n=e(t,r.prev);return n?n==r.prev?r:new I(n,r.vars,!0):null}return E(t,r.vars)?r:new I(r.prev,new T(t,r.vars),!1)}return null}}(e,t.context);if(null!=r)return void(t.context=r)}else if(!E(e,t.localVars))return void(t.localVars=new T(e,t.localVars));l.globalVars&&!E(e,t.globalVars)&&(t.globalVars=new T(e,t.globalVars))}function z(e){return"public"==e||"private"==e||"protected"==e||"abstract"==e||"readonly"==e}function I(e,t,r){this.prev=e,this.vars=t,this.block=r}function T(e,t){this.name=e,this.next=t}var t=new T("this",new T("arguments",null));function $(){M.state.context=new I(M.state.context,M.state.localVars,!1),M.state.localVars=t}function C(){M.state.context=new I(M.state.context,M.state.localVars,!0),M.state.localVars=null}function _(){M.state.localVars=M.state.context.vars,M.state.context=M.state.context.prev}function O(n,a){var e=function(){var e=M.state,t=e.indented;if("stat"==e.lexical.type)t=e.lexical.indented;else for(var r=e.lexical;r&&")"==r.type&&r.align;r=r.prev)t=r.indented;e.lexical=new g(t,M.stream.column(),n,null,e.lexical,a)};return e.lex=!0,e}function q(){var e=M.state;e.lexical.prev&&(")"==e.lexical.type&&(e.indented=e.lexical.indented),e.lexical=e.lexical.prev)}function P(r){return function e(t){return t==r?V():";"==r||"}"==t||")"==t||"]"==t?A():V(e)}}function S(e,t){return"var"==e?V(O("vardef",t),we,P(";"),q):"keyword a"==e?V(O("form"),F,S,q):"keyword b"==e?V(O("form"),S,q):"keyword d"==e?M.stream.match(/^\s*$/,!1)?V():V(O("stat"),W,P(";"),q):"debugger"==e?V(P(";")):"{"==e?V(O("}"),C,ie,q,_):";"==e?V():"if"==e?("else"==M.state.lexical.info&&M.state.cc[M.state.cc.length-1]==q&&M.state.cc.pop()(),V(O("form"),F,S,q,Me)):"function"==e?V(ze):"for"==e?V(O("form"),Ae,S,q):"class"==e||u&&"interface"==t?(M.marked="keyword",V(O("form","class"==e?e:t),_e,q)):"variable"==e?u&&"declare"==t?(M.marked="keyword",V(S)):u&&("module"==t||"enum"==t||"type"==t)&&M.stream.match(/^\s*\w/,!1)?(M.marked="keyword","enum"==t?V(Ge):"type"==t?V(Te,P("operator"),fe,P(";")):V(O("form"),be,P("{"),O("}"),ie,q,q)):u&&"namespace"==t?(M.marked="keyword",V(O("form"),U,S,q)):u&&"abstract"==t?(M.marked="keyword",V(S)):V(O("stat"),Y):"switch"==e?V(O("form"),F,P("{"),O("}","switch"),C,ie,q,q,_):"case"==e?V(U,P(":")):"default"==e?V(P(":")):"catch"==e?V(O("form"),$,N,S,q,_):"export"==e?V(O("stat"),Se,q):"import"==e?V(O("stat"),Ue,q):"async"==e?V(S):"@"==t?V(U,S):A(O("stat"),U,P(";"),q)}function N(e){if("("==e)return V($e,P(")"))}function U(e,t){return H(e,t,!1)}function B(e,t){return H(e,t,!0)}function F(e){return"("!=e?A():V(O(")"),U,P(")"),q)}function H(e,t,r){if(M.state.fatArrowAt==M.stream.start){var n=r?Q:L;if("("==e)return V($,O(")"),ne($e,")"),q,P("=>"),n,_);if("variable"==e)return A($,be,P("=>"),n,_)}var a,i=r?G:D;return h.hasOwnProperty(e)?V(i):"function"==e?V(ze,i):"class"==e||u&&"interface"==t?(M.marked="keyword",V(O("form"),Ce,q)):"keyword c"==e||"async"==e?V(r?B:U):"("==e?V(O(")"),W,P(")"),q,i):"operator"==e||"spread"==e?V(r?B:U):"["==e?V(O("]"),De,q,i):"{"==e?ae(ee,"}",null,i):"quasi"==e?A(J,i):"new"==e?V((a=r,function(e){return"."==e?V(a?X:R):"variable"==e&&u?V(ve,a?G:D):A(a?B:U)})):"import"==e?V(U):V()}function W(e){return e.match(/[;\}\)\],]/)?A():A(U)}function D(e,t){return","==e?V(U):G(e,t,!1)}function G(e,t,r){var n=0==r?D:G,a=0==r?U:B;return"=>"==e?V($,r?Q:L,_):"operator"==e?/\+\+|--/.test(t)||u&&"!"==t?V(n):u&&"<"==t&&M.stream.match(/^([^>]|<.*?>)*>\s*\(/,!1)?V(O(">"),ne(fe,">"),q,n):"?"==t?V(U,P(":"),a):V(a):"quasi"==e?A(J,n):";"!=e?"("==e?ae(B,")","call",n):"."==e?V(Z,n):"["==e?V(O("]"),W,P("]"),q,n):u&&"as"==t?(M.marked="keyword",V(fe,n)):"regexp"==e?(M.state.lastType=M.marked="operator",M.stream.backUp(M.stream.pos-M.stream.start-1),V(a)):void 0:void 0}function J(e,t){return"quasi"!=e?A():"${"!=t.slice(t.length-2)?V(J):V(U,K)}function K(e){if("}"==e)return M.marked="string-2",M.state.tokenize=b,V(J)}function L(e){return i(M.stream,M.state),A("{"==e?S:U)}function Q(e){return i(M.stream,M.state),A("{"==e?S:B)}function R(e,t){if("target"==t)return M.marked="keyword",V(D)}function X(e,t){if("target"==t)return M.marked="keyword",V(G)}function Y(e){return":"==e?V(q,S):A(D,P(";"),q)}function Z(e){if("variable"==e)return M.marked="property",V()}function ee(e,t){if("async"==e)return M.marked="property",V(ee);if("variable"==e||"keyword"==M.style){return M.marked="property","get"==t||"set"==t?V(te):(u&&M.state.fatArrowAt==M.stream.start&&(r=M.stream.match(/^\s*:\s*/,!1))&&(M.state.fatArrowAt=M.stream.pos+r[0].length),V(re));var r}else{if("number"==e||"string"==e)return M.marked=o?"property":M.style+" property",V(re);if("jsonld-keyword"==e)return V(re);if(u&&z(t))return M.marked="keyword",V(ee);if("["==e)return V(U,oe,P("]"),re);if("spread"==e)return V(B,re);if("*"==t)return M.marked="keyword",V(ee);if(":"==e)return A(re)}}function te(e){return"variable"!=e?A(re):(M.marked="property",V(ze))}function re(e){return":"==e?V(B):"("==e?A(ze):void 0}function ne(n,a,i){function o(e,t){if(i?-1<i.indexOf(e):","==e){var r=M.state.lexical;return"call"==r.info&&(r.pos=(r.pos||0)+1),V(function(e,t){return e==a||t==a?A():A(n)},o)}return e==a||t==a?V():i&&-1<i.indexOf(";")?A(n):V(P(a))}return function(e,t){return e==a||t==a?V():A(n,o)}}function ae(e,t,r){for(var n=3;n<arguments.length;n++)M.cc.push(arguments[n]);return V(O(t,r),ne(e,t),q)}function ie(e){return"}"==e?V():A(S,ie)}function oe(e,t){if(u){if(":"==e)return V(fe);if("?"==t)return V(oe)}}function ce(e,t){if(u&&(":"==e||"in"==t))return V(fe)}function se(e){if(u&&":"==e)return M.stream.match(/^\s*\w+\s+is\b/,!1)?V(U,ue,fe):V(fe)}function ue(e,t){if("is"==t)return M.marked="keyword",V()}function fe(e,t){return"keyof"==t||"typeof"==t||"infer"==t?(M.marked="keyword",V("typeof"==t?B:fe)):"variable"==e||"void"==t?(M.marked="type",V(me)):"|"==t||"&"==t?V(fe):"string"==e||"number"==e||"atom"==e?V(me):"["==e?V(O("]"),ne(fe,"]",","),q,me):"{"==e?V(O("}"),ne(de,"}",",;"),q,me):"("==e?V(ne(pe,")"),le,me):"<"==e?V(ne(fe,">"),fe):void 0}function le(e){if("=>"==e)return V(fe)}function de(e,t){return"variable"==e||"keyword"==M.style?(M.marked="property",V(de)):"?"==t||"number"==e||"string"==e?V(de):":"==e?V(fe):"["==e?V(P("variable"),ce,P("]"),de):"("==e?A(Ie,de):void 0}function pe(e,t){return"variable"==e&&M.stream.match(/^\s*[?:]/,!1)||"?"==t?V(pe):":"==e?V(fe):"spread"==e?V(pe):A(fe)}function me(e,t){return"<"==t?V(O(">"),ne(fe,">"),q,me):"|"==t||"."==e||"&"==t?V(fe):"["==e?V(fe,P("]"),me):"extends"==t||"implements"==t?(M.marked="keyword",V(fe)):"?"==t?V(fe,P(":"),fe):void 0}function ve(e,t){if("<"==t)return V(O(">"),ne(fe,">"),q,me)}function ke(){return A(fe,ye)}function ye(e,t){if("="==t)return V(fe)}function we(e,t){return"enum"==t?(M.marked="keyword",V(Ge)):A(be,oe,ge,je)}function be(e,t){return u&&z(t)?(M.marked="keyword",V(be)):"variable"==e?(r(t),V()):"spread"==e?V(be):"["==e?ae(he,"]"):"{"==e?ae(xe,"}"):void 0}function xe(e,t){return"variable"!=e||M.stream.match(/^\s*:/,!1)?("variable"==e&&(M.marked="property"),"spread"==e?V(be):"}"==e?A():"["==e?V(U,P("]"),P(":"),xe):V(P(":"),be,ge)):(r(t),V(ge))}function he(){return A(be,ge)}function ge(e,t){if("="==t)return V(B)}function je(e){if(","==e)return V(we)}function Me(e,t){if("keyword b"==e&&"else"==t)return V(O("form","else"),S,q)}function Ae(e,t){return"await"==t?V(Ae):"("==e?V(O(")"),Ve,q):void 0}function Ve(e){return"var"==e?V(we,Ee):"variable"==e?V(Ee):A(Ee)}function Ee(e,t){return")"==e?V():";"==e?V(Ee):"in"==t||"of"==t?(M.marked="keyword",V(U,Ee)):A(U,Ee)}function ze(e,t){return"*"==t?(M.marked="keyword",V(ze)):"variable"==e?(r(t),V(ze)):"("==e?V($,O(")"),ne($e,")"),q,se,S,_):u&&"<"==t?V(O(">"),ne(ke,">"),q,ze):void 0}function Ie(e,t){return"*"==t?(M.marked="keyword",V(Ie)):"variable"==e?(r(t),V(Ie)):"("==e?V($,O(")"),ne($e,")"),q,se,_):u&&"<"==t?V(O(">"),ne(ke,">"),q,Ie):void 0}function Te(e,t){return"keyword"==e||"variable"==e?(M.marked="type",V(Te)):"<"==t?V(O(">"),ne(ke,">"),q):void 0}function $e(e,t){return"@"==t&&V(U,$e),"spread"==e?V($e):u&&z(t)?(M.marked="keyword",V($e)):u&&"this"==e?V(oe,ge):A(be,oe,ge)}function Ce(e,t){return"variable"==e?_e(e,t):Oe(e,t)}function _e(e,t){if("variable"==e)return r(t),V(Oe)}function Oe(e,t){return"<"==t?V(O(">"),ne(ke,">"),q,Oe):"extends"==t||"implements"==t||u&&","==e?("implements"==t&&(M.marked="keyword"),V(u?fe:U,Oe)):"{"==e?V(O("}"),qe,q):void 0}function qe(e,t){return"async"==e||"variable"==e&&("static"==t||"get"==t||"set"==t||u&&z(t))&&M.stream.match(/^\s+[\w$\xa1-\uffff]/,!1)?(M.marked="keyword",V(qe)):"variable"==e||"keyword"==M.style?(M.marked="property",V(u?Pe:ze,qe)):"number"==e||"string"==e?V(u?Pe:ze,qe):"["==e?V(U,oe,P("]"),u?Pe:ze,qe):"*"==t?(M.marked="keyword",V(qe)):u&&"("==e?A(Ie,qe):";"==e||","==e?V(qe):"}"==e?V():"@"==t?V(U,qe):void 0}function Pe(e,t){if("?"==t)return V(Pe);if(":"==e)return V(fe,ge);if("="==t)return V(B);var r=M.state.lexical.prev;return A(r&&"interface"==r.info?Ie:ze)}function Se(e,t){return"*"==t?(M.marked="keyword",V(We,P(";"))):"default"==t?(M.marked="keyword",V(U,P(";"))):"{"==e?V(ne(Ne,"}"),We,P(";")):A(S)}function Ne(e,t){return"as"==t?(M.marked="keyword",V(P("variable"))):"variable"==e?A(B,Ne):void 0}function Ue(e){return"string"==e?V():"("==e?A(U):A(Be,Fe,We)}function Be(e,t){return"{"==e?ae(Be,"}"):("variable"==e&&r(t),"*"==t&&(M.marked="keyword"),V(He))}function Fe(e){if(","==e)return V(Be,Fe)}function He(e,t){if("as"==t)return M.marked="keyword",V(Be)}function We(e,t){if("from"==t)return M.marked="keyword",V(U)}function De(e){return"]"==e?V():A(ne(B,"]"))}function Ge(){return A(O("form"),be,P("{"),O("}"),ne(Je,"}"),q,q)}function Je(){return A(be,ge)}function Ke(e,t,r){return t.tokenize==y&&/^(?:operator|sof|keyword [bcd]|case|new|export|default|spread|[\[{}\(,;:]|=>)$/.test(t.lastType)||"quasi"==t.lastType&&/\{\s*$/.test(e.string.slice(0,e.pos-(r||0)))}return q.lex=_.lex=!0,{startState:function(e){var t={tokenize:y,lastType:"sof",cc:[],lexical:new g((e||0)-d,0,"block",!1),localVars:l.localVars,context:l.localVars&&new I(null,null,!1),indented:e||0};return l.globalVars&&"object"==typeof l.globalVars&&(t.globalVars=l.globalVars),t},token:function(e,t){if(e.sol()&&(t.lexical.hasOwnProperty("align")||(t.lexical.align=!1),t.indented=e.indentation(),i(e,t)),t.tokenize!=w&&e.eatSpace())return null;var r=t.tokenize(e,t);return"comment"==n?r:(t.lastType="operator"!=n||"++"!=a&&"--"!=a?n:"incdec",function(e,t,r,n,a){var i=e.cc;for(M.state=e,M.stream=a,M.marked=null,M.cc=i,M.style=t,e.lexical.hasOwnProperty("align")||(e.lexical.align=!0);;)if((i.length?i.pop():c?U:S)(r,n)){for(;i.length&&i[i.length-1].lex;)i.pop()();return M.marked?M.marked:"variable"==r&&j(e,n)?"variable-2":t}}(t,r,n,a,e))},indent:function(e,t){if(e.tokenize==w)return Le.Pass;if(e.tokenize!=y)return 0;var r,n=t&&t.charAt(0),a=e.lexical;if(!/^\s*else\b/.test(t))for(var i=e.cc.length-1;0<=i;--i){var o=e.cc[i];if(o==q)a=a.prev;else if(o!=Me)break}for(;("stat"==a.type||"form"==a.type)&&("}"==n||(r=e.cc[e.cc.length-1])&&(r==D||r==G)&&!/^[,\.=+\-*:?[\(]/.test(t));)a=a.prev;p&&")"==a.type&&"stat"==a.prev.type&&(a=a.prev);var c,s,u=a.type,f=n==u;return"vardef"==u?a.indented+("operator"==e.lastType||","==e.lastType?a.info.length+1:0):"form"==u&&"{"==n?a.indented:"form"==u?a.indented+d:"stat"==u?a.indented+(s=t,"operator"==(c=e).lastType||","==c.lastType||m.test(s.charAt(0))||/[,.]/.test(s.charAt(0))?p||d:0):"switch"!=a.info||f||0==l.doubleIndentSwitch?a.align?a.column+(f?0:1):a.indented+(f?0:d):a.indented+(/^(?:case|default)\b/.test(t)?d:2*d)},electricInput:/^\s*(?:case .*?:|default:|\{|\})$/,blockCommentStart:c?null:"/*",blockCommentEnd:c?null:"*/",blockCommentContinue:c?null:" * ",lineComment:c?null:"//",fold:"brace",closeBrackets:"()[]{}''\"\"``",helperType:c?"json":"javascript",jsonldMode:o,jsonMode:c,expressionAllowed:Ke,skipExpression:function(e){var t=e.cc[e.cc.length-1];t!=U&&t!=B||e.cc.pop()}}}),Le.registerHelper("wordChars","javascript",/[\w$]/),Le.defineMIME("text/javascript","javascript"),Le.defineMIME("text/ecmascript","javascript"),Le.defineMIME("application/javascript","javascript"),Le.defineMIME("application/x-javascript","javascript"),Le.defineMIME("application/ecmascript","javascript"),Le.defineMIME("application/json",{name:"javascript",json:!0}),Le.defineMIME("application/x-json",{name:"javascript",json:!0}),Le.defineMIME("application/ld+json",{name:"javascript",jsonld:!0}),Le.defineMIME("text/typescript",{name:"javascript",typescript:!0}),Le.defineMIME("application/typescript",{name:"javascript",typescript:!0})});












//把css搞过来
$("head").append("<style>.CodeMirror{font-family:monospace;height:300px;color:black;direction:ltr}.CodeMirror-lines{padding:4px 0}.CodeMirror pre.CodeMirror-line,.CodeMirror pre.CodeMirror-line-like{padding:0 4px}.CodeMirror-scrollbar-filler,.CodeMirror-gutter-filler{background-color:white}.CodeMirror-gutters{border-right:1px solid #ddd;background-color:#f7f7f7;white-space:nowrap}.CodeMirror-linenumber{padding:0 3px 0 5px;min-width:20px;text-align:right;color:#999;white-space:nowrap}.CodeMirror-guttermarker{color:black}.CodeMirror-guttermarker-subtle{color:#999}.CodeMirror-cursor{border-left:1px solid black;border-right:0;width:0}.CodeMirror div.CodeMirror-secondarycursor{border-left:1px solid silver}.cm-fat-cursor .CodeMirror-cursor{width:auto;border:0 !important;background:#7e7}.cm-fat-cursor div.CodeMirror-cursors{z-index:1}.cm-fat-cursor-mark{background-color:rgba(20,255,20,0.5);-webkit-animation:blink 1.06s steps(1) infinite;-moz-animation:blink 1.06s steps(1) infinite;animation:blink 1.06s steps(1) infinite}.cm-animate-fat-cursor{width:auto;border:0;-webkit-animation:blink 1.06s steps(1) infinite;-moz-animation:blink 1.06s steps(1) infinite;animation:blink 1.06s steps(1) infinite;background-color:#7e7}@-moz-keyframes blink{50%{background-color:transparent}}@-webkit-keyframes blink{50%{background-color:transparent}}@keyframes blink{50%{background-color:transparent}}.cm-tab{display:inline-block;text-decoration:inherit}.CodeMirror-rulers{position:absolute;left:0;right:0;top:-50px;bottom:0;overflow:hidden}.CodeMirror-ruler{border-left:1px solid #ccc;top:0;bottom:0;position:absolute}.cm-s-default .cm-header{color:blue}.cm-s-default .cm-quote{color:#090}.cm-negative{color:#d44}.cm-positive{color:#292}.cm-header,.cm-strong{font-weight:bold}.cm-em{font-style:italic}.cm-link{text-decoration:underline}.cm-strikethrough{text-decoration:line-through}.cm-s-default .cm-keyword{color:#708}.cm-s-default .cm-atom{color:#219}.cm-s-default .cm-number{color:#164}.cm-s-default .cm-def{color:#00f}.cm-s-default .cm-variable-2{color:#05a}.cm-s-default .cm-variable-3,.cm-s-default .cm-type{color:#085}.cm-s-default .cm-comment{color:#a50}.cm-s-default .cm-string{color:#a11}.cm-s-default .cm-string-2{color:#f50}.cm-s-default .cm-meta{color:#555}.cm-s-default .cm-qualifier{color:#555}.cm-s-default .cm-builtin{color:#30a}.cm-s-default .cm-bracket{color:#997}.cm-s-default .cm-tag{color:#170}.cm-s-default .cm-attribute{color:#00c}.cm-s-default .cm-hr{color:#999}.cm-s-default .cm-link{color:#00c}.cm-s-default .cm-error{color:red}.cm-invalidchar{color:red}.CodeMirror-composing{border-bottom:2px solid}div.CodeMirror span.CodeMirror-matchingbracket{color:#0b0}div.CodeMirror span.CodeMirror-nonmatchingbracket{color:#a22}.CodeMirror-matchingtag{background:rgba(255,150,0,.3)}.CodeMirror-activeline-background{background:#e8f2ff}.CodeMirror{position:relative;overflow:hidden;background:white}.CodeMirror-scroll{overflow:scroll !important;margin-bottom:-30px;margin-right:-30px;padding-bottom:30px;height:100%;outline:0;position:relative}.CodeMirror-sizer{position:relative;border-right:30px solid transparent}.CodeMirror-vscrollbar,.CodeMirror-hscrollbar,.CodeMirror-scrollbar-filler,.CodeMirror-gutter-filler{position:absolute;z-index:6;display:none}.CodeMirror-vscrollbar{right:0;top:0;overflow-x:hidden;overflow-y:scroll}.CodeMirror-hscrollbar{bottom:0;left:0;overflow-y:hidden;overflow-x:scroll}.CodeMirror-scrollbar-filler{right:0;bottom:0}.CodeMirror-gutter-filler{left:0;bottom:0}.CodeMirror-gutters{position:absolute;left:0;top:0;min-height:100%;z-index:3}.CodeMirror-gutter{white-space:normal;height:100%;display:inline-block;vertical-align:top;margin-bottom:-30px}.CodeMirror-gutter-wrapper{position:absolute;z-index:4;background:none !important;border:none !important}.CodeMirror-gutter-background{position:absolute;top:0;bottom:0;z-index:4}.CodeMirror-gutter-elt{position:absolute;cursor:default;z-index:4}.CodeMirror-gutter-wrapper ::selection{background-color:transparent}.CodeMirror-gutter-wrapper ::-moz-selection{background-color:transparent}.CodeMirror-lines{cursor:text;min-height:1px}.CodeMirror pre.CodeMirror-line,.CodeMirror pre.CodeMirror-line-like{-moz-border-radius:0;-webkit-border-radius:0;border-radius:0;border-width:0;background:transparent;font-family:inherit;font-size:inherit;margin:0;white-space:pre;word-wrap:normal;line-height:inherit;color:inherit;z-index:2;position:relative;overflow:visible;-webkit-tap-highlight-color:transparent;-webkit-font-variant-ligatures:contextual;font-variant-ligatures:contextual}.CodeMirror-wrap pre.CodeMirror-line,.CodeMirror-wrap pre.CodeMirror-line-like{word-wrap:break-word;white-space:pre-wrap;word-break:normal}.CodeMirror-linebackground{position:absolute;left:0;right:0;top:0;bottom:0;z-index:0}.CodeMirror-linewidget{position:relative;z-index:2;padding:.1px}.CodeMirror-rtl pre{direction:rtl}.CodeMirror-code{outline:0}.CodeMirror-scroll,.CodeMirror-sizer,.CodeMirror-gutter,.CodeMirror-gutters,.CodeMirror-linenumber{-moz-box-sizing:content-box;box-sizing:content-box}.CodeMirror-measure{position:absolute;width:100%;height:0;overflow:hidden;visibility:hidden}.CodeMirror-cursor{position:absolute;pointer-events:none}.CodeMirror-measure pre{position:static}div.CodeMirror-cursors{visibility:hidden;position:relative;z-index:3}div.CodeMirror-dragcursors{visibility:visible}.CodeMirror-focused div.CodeMirror-cursors{visibility:visible}.CodeMirror-selected{background:#d9d9d9}.CodeMirror-focused .CodeMirror-selected{background:#d7d4f0}.CodeMirror-crosshair{cursor:crosshair}.CodeMirror-line::selection,.CodeMirror-line>span::selection,.CodeMirror-line>span>span::selection{background:#d7d4f0}.CodeMirror-line::-moz-selection,.CodeMirror-line>span::-moz-selection,.CodeMirror-line>span>span::-moz-selection{background:#d7d4f0}.cm-searching{background-color:#ffa;background-color:rgba(255,255,0,.4)}.cm-force-border{padding-right:.1px}@media print{.CodeMirror div.CodeMirror-cursors{visibility:hidden}}.cm-tab-wrap-hack:after{content:''}span.CodeMirror-selectedtext{background:0}</style>");








