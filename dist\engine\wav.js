/*
录音
https://github.com/xiangyuecn/Recorder
src: engine/wav.js
*/
!function(t){var e="object"==typeof window&&!!window.document,n=(e?window:Object).Recorder,a=n.i18n;!function(d,t,b,e){"use strict";d.prototype.enc_wav={stable:!0,fast:!0,getTestMsg:function(){return b("gPSE::支持位数8位、16位（填在比特率里面），采样率取值无限制；此编码器仅在pcm数据前加了一个44字节的wav头，编码出来的16位wav文件去掉开头的44字节即可得到pcm（注：其他wav编码器可能不是44字节）")}};d.prototype.wav=function(t,e,n){var a=this.set;!function(t){var e=t.bitRate,n=8==e?8:16;e!=n&&d.CLog(b("wyw9::WAV Info: 不支持{1}位，已更新成{2}位",0,e,n),3);t.bitRate=n}(a);var r=t.length,i=a.sampleRate,o=a.bitRate,f=r*(o/8),w=d.wav_header(1,1,i,o,f),c=w.length,u=new Uint8Array(c+f);if(u.set(w),8==o)for(var v=0;v<r;v++){var s=128+(t[v]>>8);u[c++]=s}else(u=new Int16Array(u.buffer)).set(t,c/2);e(u.buffer,"audio/wav")},d.wav_header=function(t,e,n,a,r){var i=1==t?0:2,o=new ArrayBuffer(44+i),f=new DataView(o),w=0,c=function(t){for(var e=0;e<t.length;e++,w++)f.setUint8(w,t.charCodeAt(e))},u=function(t){f.setUint16(w,t,!0),w+=2},v=function(t){f.setUint32(w,t,!0),w+=4};return c("RIFF"),v(36+i+r),c("WAVE"),c("fmt "),v(16+i),u(t),u(e),v(n),v(n*(e*a/8)),u(e*a/8),u(a),1!=t&&u(0),c("data"),v(r),new Uint8Array(o)}}(n,0,a.$T)}();