!function(e){var t={};function a(n){if(t[n])return t[n].exports;var s=t[n]={i:n,l:!1,exports:{}};return e[n].call(s.exports,s,s.exports,a),s.l=!0,s.exports}a.m=e,a.c=t,a.d=function(e,t,n){a.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.t=function(e,t){if(1&t&&(e=a(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(a.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var s in e)a.d(n,s,function(t){return e[t]}.bind(null,s));return n},a.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(t,"a",t),t},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.p="",a(a.s=0)}([function(e,t,a){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var s=n(a(1));a(2),a(3),a(4);var r,i,o,l=window,_=document,c=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];l.reclog.apply(null,e)};l.recOpen=function(){r=null,i=null,o=null;var e=(0,s.default)({type:"mp3",sampleRate:16e3,bitRate:16,onProcess:function(e,t,a,n,s,r){_.querySelector(".recpowerx").style.width=t+"%",_.querySelector(".recpowert").innerText=f(a,1)+" / "+t,i.input(e[e.length-1],t,n)}});e.open((function(){r=e,i=s.default.WaveView({elem:".recwave"}),c("已打开录音，可以点击录制开始录音了",2)}),(function(e,t){c((t?"UserNotAllow，":"")+"打开录音失败："+e,1)}))},l.recClose=function(){r?(r.close(),c("已关闭")):c("未打开录音",1)},l.recStart=function(){r&&s.default.IsOpen()?(o=null,r.start(),c("已开始录音...")):c("未打开录音",1)},l.recPause=function(){r&&s.default.IsOpen()?r.pause():c("未打开录音",1)},l.recResume=function(){r&&s.default.IsOpen()?r.resume():c("未打开录音",1)},l.recStop=function(){r&&s.default.IsOpen()?r.stop((function(e,t){console.log(e,(l.URL||webkitURL).createObjectURL(e),"时长:"+t+"ms"),o=e,c("已录制mp3："+f(t)+"ms "+e.size+"字节，可以点击播放、上传了",2)}),(function(e){c("录音失败:"+e,1)})):c("未打开录音",1)},l.recPlay=function(){if(o){var e=("a"+Math.random()).replace(".","");c('播放中: <span class="'+e+'"></span>');var t=_.createElement("audio");t.controls=!0,_.querySelector("."+e).appendChild(t),t.src=(l.URL||webkitURL).createObjectURL(o),t.play(),setTimeout((function(){(l.URL||webkitURL).revokeObjectURL(t.src)}),5e3)}else c("请先录音，然后停止后再播放",1)},l.recUpload=function(){var e=o;if(e){var t="http://127.0.0.1:9528",a=function(e,t){return function(){4==e.readyState&&(200==e.status?c(t+'上传成功 <span style="color:#999">response: '+e.responseText+"</span>",2):(c(t+"没有完成上传，演示上传地址无需关注上传结果，只要浏览器控制台内Network面板内看到的请求数据结构是预期的就ok了。","#d8c1a0"),console.error(t+"上传失败",e.status,e.responseText)))}};c("开始上传到"+t+"，请稍候... （你可以先到源码 /assets/node-localServer 目录内执行 npm run start 来运行本地测试服务器）");var n=new l.FileReader;n.onloadend=function(){var s="";s+="mime="+encodeURIComponent(e.type),s+="&upfile_b64="+encodeURIComponent((/.+;\s*base64\s*,\s*(.+)$/i.exec(n.result)||[])[1]);var r=new XMLHttpRequest;r.open("POST",t+"/uploadBase64"),r.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),r.onreadystatechange=a(r,"上传方式一【Base64】"),r.send(s)},n.readAsDataURL(e);var s=new FormData;s.append("upfile",e,"recorder.mp3");var r=new XMLHttpRequest;r.open("POST",t+"/upload"),r.onreadystatechange=a(r,"上传方式二【FormData】"),r.send(s)}else c("请先录音，然后停止后再上传",1)},l.recLocalDown=function(){if(o){var e=("a"+Math.random()).replace(".","");l.recdown64.lastCls=e,c('点击 <span class="'+e+'"></span> 下载，或复制文本<button onclick="recdown64(\''+e+'\')">生成Base64文本</button><span class="'+e+'_b64"></span>');var t="recorder-"+Date.now()+".mp3",a=_.createElement("A");a.innerHTML="下载 "+t,a.href=(l.URL||webkitURL).createObjectURL(o),a.download=t,_.querySelector("."+e).appendChild(a),/mobile/i.test(navigator.userAgent)?alert("因移动端绝大部分国产浏览器未适配Blob Url的下载，所以本demo代码在移动端未调用downA.click()。请尝试点击日志中显示的下载链接下载"):a.click()}else c("请先录音，然后停止后再下载",1)},l.recdown64=function(e){var t=_.querySelector("."+e+"_b64");if(l.recdown64.lastCls==e){var a=new FileReader;a.onloadend=function(){t.innerHTML="<textarea></textarea>",t.querySelector("textarea").value=a.result},a.readAsDataURL(o)}else t.innerHTML='<span style="color:red">老的数据没有保存，只支持最新的一条</span>'};var f=function(e,t){var a=Math.floor(e/6e4),n=Math.floor(e/1e3)%60;return(t||a>0?(a<10?"0":"")+a+":":"")+(t||a>0||n>0?("0"+n).substr(-2)+"″":"")+("00"+e%1e3).substr(-3)}},function(e,t,a){var n,s,r;s="object"==typeof window&&!!window.document,function(e,t){"use strict";var a=function(){},n=function(e){return"number"==typeof e},s=function(e){return JSON.stringify(e)},r=function(e){return new P(e)},i=r.LM="2025-01-11 09:28",o="https://github.com/xiangyuecn/Recorder",l="Recorder",_=e[l];if(_&&_.LM==i)_.CLog(_.i18n.$T("K8zP::重复导入{1}",0,l),3);else{r.IsOpen=function(){var e=r.Stream;if(e){var t=S(e)[0];if(t){var a=t.readyState;return"live"==a||a==t.LIVE}}return!1},r.BufferSize=4096,r.Destroy=function(){for(var e in M(l+" Destroy"),g(),c)c[e]()};var c={};r.BindDestroy=function(e,t){c[e]=t},r.Support=function(){if(!t)return!1;var e=navigator.mediaDevices||{};return e.getUserMedia||(e=navigator).getUserMedia||(e.getUserMedia=e.webkitGetUserMedia||e.mozGetUserMedia||e.msGetUserMedia),!!e.getUserMedia&&(r.Scope=e,!!r.GetContext())},r.GetContext=function(e){if(!t)return null;var a=window.AudioContext;if(a||(a=window.webkitAudioContext),!a)return null;var n=r.Ctx,s=0;return n||(n=r.Ctx=new a,s=1,r.NewCtxs=r.NewCtxs||[],r.BindDestroy("Ctx",(function(){var e=r.Ctx;e&&e.close&&(f(e),r.Ctx=0);var t=r.NewCtxs;r.NewCtxs=[];for(var a=0;a<t.length;a++)f(t[a])}))),e&&n.close&&(s||(n._useC||f(n),n=new a),n._useC=1,r.NewCtxs.push(n)),n},r.CloseNewCtx=function(e){if(e&&e.close){f(e);for(var t=r.NewCtxs||[],a=t.length,n=0;n<t.length;n++)if(t[n]==e){t.splice(n,1);break}M(C("mSxV::剩{1}个GetContext未close",0,a+"-1="+t.length),t.length?3:0)}};var f=function(e){if(e&&e.close&&!e._isC&&(e._isC=1,"closed"!=e.state))try{e.close()}catch(e){M("ctx close err",1,e)}},u=r.ResumeCtx=function(e,t,a,n){var s=0,r=0,i=0,o=0,l="EventListener",_="ResumeCtx ",c=function(t,_){r&&f(),s||(s=1,t&&n(t,o),_&&a(o)),_&&(!e._LsSC&&e["add"+l]&&e["add"+l]("statechange",u),e._LsSC=1,i=1)},f=function(e){if(!e||!r){r=e?1:0;for(var t=["focus","mousedown","mouseup","touchstart","touchend"],a=0;a<t.length;a++)window[(e?"add":"remove")+l](t[a],u,!0)}},u=function(){var a=e.state,n=h(a);if(!s&&!t(n?++o:o))return c();n?(i&&M(_+"sc "+a,3),f(1),e.resume().then((function(){i&&M(_+"sc "+e.state),c(0,1)})).catch((function(t){M(_+"error",1,t),h(e.state)||c(t.message||"error")}))):"closed"==a?(i&&!e._isC&&M(_+"sc "+a,1),c("ctx closed")):c(0,1)};u()},h=r.CtxSpEnd=function(e){return"suspended"==e||"interrupted"==e},p=function(e){var t=e.state,a="ctx.state="+t;return h(t)&&(a+=C("nMIy::（注意：ctx不是running状态，rec.open和start至少要有一个在用户操作(触摸、点击等)时进行调用，否则将在rec.start时尝试进行ctx.resume，可能会产生兼容性问题(仅iOS)，请参阅文档中runningContext配置）")),a};r.ConnectEnableWebM=!0,r.ConnectEnableWorklet=!1;var b=function(e){var t=e.BufferSize||r.BufferSize,a=e.Stream,n=a._c,i=n.sampleRate,o={},_=S(a)[0],c=null,f="";if(_&&_.getSettings){var h=(c=_.getSettings()).sampleRate;h&&h!=i&&(f=C("eS8i::Stream的采样率{1}不等于{2}，将进行采样率转换（注意：音质不会变好甚至可能变差），主要在移动端未禁用回声消除时会产生此现象，浏览器有回声消除时可能只会返回16k采样率的音频数据，",0,h,i))}a._ts=c,M(f+"Stream TrackSet: "+s(c),f?3:0);var p,b,m,g=function(e){var t=a._m=n.createMediaStreamSource(a),s=n.destination,r="createMediaStreamDestination";n[r]&&(s=a._d=n[r]()),t.connect(e),e.connect(s)},w="",R=a._call,y=function(e,t){for(var a in R){if(t!=i){o.index=0;var n=(o=r.SampleData([e],t,i,o,{_sum:1})).data,s=o._sum}else{o={};for(var l=e.length,_=(n=new Int16Array(l),s=0,0);_<l;_++){var c=Math.max(-1,Math.min(1,e[_]));c=c<0?32768*c:32767*c,n[_]=c,s+=Math.abs(c)}}for(var f in R)R[f](n,s);return}},A=n.createScriptProcessor||n.createJavaScriptNode,T=C("ZGlf::。由于{1}内部1秒375次回调，在移动端可能会有性能问题导致回调丢失录音变短，PC端无影响，暂不建议开启{1}。",0,"audioWorklet"),x=function(){b=a.isWorklet=!1,v(a),M(C("7TU0::Connect采用老的{1}，",0,"ScriptProcessor")+E.get(C(r.ConnectEnableWorklet?"JwCL::但已设置{1}尝试启用{2}":"VGjB::可设置{1}尝试启用{2}",2),[l+".ConnectEnableWorklet=true","audioWorklet"])+w+T,3);var e=a._p=A.call(n,t,1,1);g(e),e.onaudioprocess=function(e){var t=e.inputBuffer.getChannelData(0);y(t,i)}},B=function(){p=a.isWebM=!1,d(a),b=a.isWorklet=!A||r.ConnectEnableWorklet;var e=window.AudioWorkletNode;if(b&&n.audioWorklet&&e){var s=function(){return b&&a._na},o=a._na=function(){""!==m&&(clearTimeout(m),m=setTimeout((function(){m=0,s()&&(M(C("MxX1::{1}未返回任何音频，恢复使用{2}",0,"audioWorklet","ScriptProcessor"),3),A&&x())}),500))},_=function(){if(s()){var r=a._n=new e(n,"RecProc",{processorOptions:{bufferSize:t}});g(r),r.port.onmessage=function(e){m&&(clearTimeout(m),m=""),s()?y(e.data.val,i):b||M(C("XUap::{1}多余回调",0,"audioWorklet"),3)},M(C("yOta::Connect采用{1}，设置{2}可恢复老式{3}",0,"audioWorklet",l+".ConnectEnableWorklet=false","ScriptProcessor")+w+T,3)}},c=function(){if(s())if(n.RecProc)_();else{var e,t,a=(t="class RecProc extends AudioWorkletProcessor{",t+="constructor "+(e=function(e){return e.toString().replace(/^function|DEL_/g,"").replace(/\$RA/g,"Recorder audioWorklet")})((function(e){DEL_super(e);var t=this,a=e.processorOptions.bufferSize;t.bufferSize=a,t.buffer=new Float32Array(2*a),t.pos=0,t.port.onmessage=function(e){e.data.kill&&(t.kill=!0,$C.log("$RA kill call"))},$C.log("$RA .ctor call",e)})),t+="process "+e((function(e,t,a){var n=this.bufferSize,s=this.buffer,r=this.pos;if((e=(e[0]||[])[0]||[]).length){s.set(e,r);var i=~~((r+=e.length)/n)*n;if(i){this.port.postMessage({val:s.slice(0,i)});var o=s.subarray(i,r);(s=new Float32Array(2*n)).set(o),r=o.length,this.buffer=s}this.pos=r}return!this.kill})),t=(t+='}try{registerProcessor("RecProc", RecProc)}catch(e){$C.error("Recorder audioWorklet Reg Error",e)}').replace(/\$C\./g,"console."),"data:text/javascript;base64,"+btoa(unescape(encodeURIComponent(t))));n.audioWorklet.addModule(a).then((function(e){s()&&(n.RecProc=1,_(),m&&o())})).catch((function(e){M("audioWorklet.addModule Error",1,e),s()&&x()}))}};u(n,(function(){return s()}),c,c)}else x()};!function(){var e=window.MediaRecorder;p=a.isWebM=r.ConnectEnableWebM;var n=e&&"ondataavailable"in e.prototype&&e.isTypeSupported("audio/webm; codecs=pcm");if(w=n?"":C("VwPd::（此浏览器不支持{1}）",0,"MediaRecorder.WebM.PCM"),p&&n){var s=function(){return p&&a._ra};a._ra=function(){""!==m&&(clearTimeout(m),m=setTimeout((function(){s()&&(M(C("vHnb::{1}未返回任何音频，降级使用{2}",0,"MediaRecorder","audioWorklet"),3),B())}),500))};var i=Object.assign({mimeType:"audio/webm; codecs=pcm"},r.ConnectWebMOptions),o=a._r=new e(a,i),_=a._rd={};o.ondataavailable=function(e){var t=new FileReader;t.onloadend=function(){if(s()){var e=k(new Uint8Array(t.result),_);if(!e)return;if(-1==e)return void B();m&&(clearTimeout(m),m=""),y(e,_.webmSR)}else p||M(C("O9P7::{1}多余回调",0,"MediaRecorder"),3)},t.readAsArrayBuffer(e.data)};try{o.start(~~(t/48)),M(C("LMEm::Connect采用{1}，设置{2}可恢复使用{3}或老式{4}",0,"MediaRecorder.WebM.PCM",l+".ConnectEnableWebM=false","audioWorklet","ScriptProcessor"))}catch(e){M("mr start err",1,e),B()}}else B()}()},m=function(e){e._na&&e._na(),e._ra&&e._ra()},v=function(e){e._na=null,e._n&&(e._n.port.postMessage({kill:!0}),e._n.disconnect(),e._n=null)},d=function(e){if(e._ra=null,e._r){try{e._r.stop()}catch(e){M("mr stop err",1,e)}e._r=null}},g=function(e){var t=(e=e||r)==r,a=e.Stream;a&&(a._m&&(a._m.disconnect(),a._m=null),!a._RC&&a._c&&r.CloseNewCtx(a._c),a._RC=null,a._c=null,a._d&&(w(a._d.stream),a._d=null),a._p&&(a._p.disconnect(),a._p.onaudioprocess=a._p=null),v(a),d(a),t&&w(a)),e.Stream=0},w=r.StopS_=function(e){for(var t=S(e),a=0;a<t.length;a++){var n=t[a];n.stop&&n.stop()}e.stop&&e.stop()},S=function(e){var t=0,a=0,n=[];e.getAudioTracks&&(t=e.getAudioTracks(),a=e.getVideoTracks()),t||(t=e.audioTracks,a=e.videoTracks);for(var s=0,r=t?t.length:0;s<r;s++)n.push(t[s]);for(s=0,r=a?a.length:0;s<r;s++)n.push(a[s]);return n};r.SampleData=function(e,t,a,n,s){var i="SampleData";n||(n={});var o,l=n.index||0,_=n.offset||0,c=n.raisePrev||0,f=n.filter;f&&f.fn&&(f.sr&&f.sr!=t||f.srn&&f.srn!=a)&&(f=null,M(C("d48C::{1}的filter采样率变了，重设滤波",0,i),3)),f||(f=a<=t?{fn:(o=a>3*t/4?0:a/2*3/4)?r.IIRFilter(!0,t,o):0}:{fn:(o=t>3*a/4?0:t/2*3/4)?r.IIRFilter(!0,a,o):0}),f.sr=t,f.srn=a;var u=f.fn,h=n.frameNext||[];s||(s={});var p=s.frameSize||1;s.frameType&&(p="mp3"==s.frameType?1152:1);var b=s._sum,m=0,v=e.length;l>v+1&&M(C("tlbC::{1}似乎传入了未重置chunk {2}",0,i,l+">"+v),3);for(var d=0,g=l;g<v;g++)d+=e[g].length;var w=t/a;if(w>1)d=Math.max(0,d-Math.floor(_)),d=Math.floor(d/w);else if(w<1){var S=1/w;d=Math.floor(d*S)}d+=h.length;var R=new Int16Array(d),y=0;for(g=0;g<h.length;g++)R[y]=h[g],y++;for(;l<v;l++){var k=e[l],A=k instanceof Float32Array,T=(g=_,k.length),x=u&&u.Embed,B=0,E=0,L=0,P=0;if(w<1){for(var I=y+g,H=c,O=0;O<T;O++){var N=k[O];A&&(N=(N=Math.max(-1,Math.min(1,N)))<0?32768*N:32767*N);var D=Math.floor(I);I+=S;for(var V=Math.floor(I),F=(N-H)/(V-D),j=1;D<V;D++,j++){var X=Math.floor(H+j*F);x?(L=X,P=x.b0*L+x.b1*x.x1+x.b0*x.x2-x.a1*x.y1-x.a2*x.y2,x.x2=x.x1,x.x1=L,x.y2=x.y1,x.y1=P,X=P):X=u?u(X):X,X>32767?X=32767:X<-32768&&(X=-32768),b&&(m+=Math.abs(X)),R[D]=X,y++}H=c=N,g+=S}_=g%1}else{O=0;for(var Y=0;O<T;O++,Y++)if(Y<T&&(N=k[Y],A&&(N=(N=Math.max(-1,Math.min(1,N)))<0?32768*N:32767*N),x?(L=N,P=x.b0*L+x.b1*x.x1+x.b0*x.x2-x.a1*x.y1-x.a2*x.y2,x.x2=x.x1,x.x1=L,x.y2=x.y1,x.y1=P):P=u?u(N):N),B=E,E=P,0!=Y){var z=Math.floor(g);if(O==z){var q=B+((Math.ceil(g)<T?E:B)-B)*(g-z);q>32767?q=32767:q<-32768&&(q=-32768),b&&(m+=Math.abs(q)),R[y]=q,y++,g+=w}}else O--;_=Math.max(0,g-T)}}w<1&&y+1==d&&(d--,R=new Int16Array(R.buffer.slice(0,2*d))),y-1!=d&&y!=d&&M(i+" idx:"+y+" != size:"+d,3),h=null;var U=d%p;if(U>0){var G=2*(d-U);h=new Int16Array(R.buffer.slice(G)),R=new Int16Array(R.buffer.slice(0,G))}var W={index:l,offset:_,raisePrev:c,filter:f,frameNext:h,sampleRate:a,data:R};return b&&(W._sum=m),W},r.IIRFilter=function(e,t,a){var n=2*Math.PI*a/t,s=Math.sin(n),r=Math.cos(n),i=s/2,o=1+i,l=-2*r/o,_=(1-i)/o;if(e)var c=(1-r)/2/o,f=(1-r)/o;else c=(1+r)/2/o,f=-(1+r)/o;var u=0,h=0,p=0,b=0,m=0,v=function(e){return p=c*e+f*u+c*h-l*b-_*m,h=u,u=e,m=b,b=p,p};return v.Embed={x1:0,x2:0,y1:0,y2:0,b0:c,b1:f,a1:l,a2:_},v},r.PowerLevel=function(e,t){var a=e/t||0;return a<1251?Math.round(a/1250*10):Math.round(Math.min(100,Math.max(0,100*(1+Math.log(a/1e4)/Math.log(10)))))},r.PowerDBFS=function(e){var t=Math.max(.1,e||0);return t=Math.min(t,32767),t=20*Math.log(t/32767)/Math.log(10),Math.max(-100,Math.round(t))},r.CLog=function(e,t){if("object"==typeof console){var a=new Date,s=("0"+a.getMinutes()).substr(-2)+":"+("0"+a.getSeconds()).substr(-2)+"."+("00"+a.getMilliseconds()).substr(-3),i=this&&this.envIn&&this.envCheck&&this.id,o=["["+s+" "+l+(i?":"+i:"")+"]"+e],_=arguments,c=r.CLog,f=2,u=c.log||console.log;for(n(t)?u=1==t?c.error||console.error:3==t?c.warn||console.warn:u:f=1;f<_.length;f++)o.push(_[f]);R?u&&u("[IsLoser]"+o[0],o.length>1?o:""):u.apply(console,o)}};var M=function(){r.CLog.apply(this,arguments)},R=!0;try{R=!console.log.apply}catch(e){}var y=0;r.Sync={O:9,C:9},r.prototype=P.prototype={CLog:M,_streamStore:function(){return this.set.sourceStream?this:r},_streamGet:function(){return this._streamStore().Stream},_streamCtx:function(){var e=this._streamGet();return e&&e._c},open:function(e,n){var l=this,_=l.set,c=l._streamStore(),f=0;e=e||a;var u=function(e,t){t=!!t,l.CLog(C("5tWi::录音open失败：")+e+",isUserNotAllow:"+t,1),f&&r.CloseNewCtx(f),n&&n(e,t)};l._streamTag="getUserMedia";var h=function(){l.CLog("open ok, id:"+l.id+" stream:"+l._streamTag),e(),l._SO=0},m=c.Sync,v=++m.O,d=m.C;if(l._O=l._O_=v,l._SO=l._S,t){var w=l.envCheck({envName:"H5",canProcess:!0});if(w)u(C("A5bm::不能录音：")+w);else{var S,M=function(){(S=_.runningContext)||(S=f=r.GetContext(!0))};if(_.sourceStream){if(l._streamTag="set.sourceStream",!r.GetContext())return void u(C("1iU7::不支持此浏览器从流中获取录音"));M(),g(c);var R=l.Stream=_.sourceStream;R._c=S,R._RC=_.runningContext,R._call={};try{b(c)}catch(e){return g(c),void u(C("BTW2::从流中打开录音失败：")+e.message)}h()}else{var y=function(e,t){try{window.top.a}catch(e){return void u(C("Nclz::无权录音(跨域，请尝试给iframe添加麦克风访问策略，如{1})",0,'allow="camera;microphone"'))}k(1,e)&&(/Found/i.test(e)?u(t+C("jBa9::，无可用麦克风")):u(t))},k=function(e,t){if(/Permission|Allow/i.test(t))e&&u(C("gyO5::用户拒绝了录音权限"),!0);else{if(!1!==window.isSecureContext)return 1;e&&u(C("oWNo::浏览器禁止不安全页面录音，可开启https解决"))}};if(r.IsOpen())h();else if(r.Support()){M();var A,T,x=function(e){setTimeout((function(){e._call={};var t=r.Stream;t&&(g(),e._call=t._call),r.Stream=e,e._c=S,e._RC=_.runningContext,function(){if(d!=m.C||!l._O){var e=C("dFm8::open被取消");return v==m.O?l.close():e=C("VtJO::open被中断"),u(e),!0}}()||(r.IsOpen()?(t&&l.CLog(C("upb8::发现同时多次调用open"),1),b(c),h()):u(C("Q1GA::录音功能无效：无音频流")))}),100)},B=function(e){var t=e.name||e.message||e.code+":"+e,a="";1==E&&k(0,t)&&(a=C("KxE2::，将尝试禁用回声消除后重试"));var n=C("xEQR::请求录音权限错误"),s=C("bDOG::无法录音：");l.CLog(n+a+"|"+e,a||T?3:1,e),a?(A=t,T=e,L(1)):T?(l.CLog(n+"|"+T,1,T),y(A,s+T)):y(t,s+e)},E=0,L=function(e){E++;var t="audioTrackSet",a="echoCancellation",n="noiseSuppression",c=JSON.parse(s(_[t]||!0));l.CLog("open... "+E+" "+t+":"+s(c)),e&&("object"!=typeof c&&(c={}),c.autoGainControl=!1,c[a]=!1,c[n]=!1),c.sampleRate&&l.CLog(C("IjL3::注意：已配置{1}参数，可能会出现浏览器不能正确选用麦克风、移动端无法启用回声消除等现象",0,t+".sampleRate"),3);var f={audio:c,video:_.videoTrackSet||!1};try{var u=r.Scope.getUserMedia(f,x,B)}catch(e){l.CLog("getUserMedia",3,e),f={audio:!0,video:!1},u=r.Scope.getUserMedia(f,x,B)}l.CLog("getUserMedia("+s(f)+") "+p(S)+C("RiWe::，未配置 {1} 时浏览器可能会自动启用回声消除，移动端未禁用回声消除时可能会降低系统播放音量（关闭录音后可恢复）和仅提供16k采样率的音频流（不需要回声消除时可明确配置成禁用来获得48k高音质的流），请参阅文档中{2}配置",0,"audioTrackSet:{echoCancellation,noiseSuppression,autoGainControl}",t)+"("+o+") LM:"+i+" UA:"+navigator.userAgent),u&&u.then&&u.then(x).catch(B)};L()}else y("",C("COxc::此浏览器不支持录音"))}}}else u(C.G("NonBrowser-1",["open"])+C("EMJq::，可尝试使用RecordApp解决方案")+"("+o+"/tree/master/app-support-sample)")},close:function(e){e=e||a;var t=this._streamStore();this._stop();var n=" stream:"+this._streamTag,s=t.Sync;if(this._O=0,this._O_!=s.O)return this.CLog(C("hWVz::close被忽略（因为同时open了多个rec，只有最后一个会真正close）")+n,3),void e();s.C++,g(t),this.CLog("close,"+n),e()},mock:function(e,t){return this._stop(),this.isMock=1,this.mockEnvInfo=null,this.buffers=[e],this.recSize=e.length,this._setSrcSR(t),this._streamTag="mock",this},_setSrcSR:function(e){var t=this.set,a=t.sampleRate;a>e?t.sampleRate=e:a=0,this.srcSampleRate=e,this.CLog("srcSampleRate: "+e+" set.sampleRate: "+t.sampleRate+(a?" "+C("UHvm::忽略")+": "+a:""),a?3:0)},envCheck:function(e){var t,a=this.set,n="CPU_BE";if(t||r[n]||"function"!=typeof Int8Array||new Int8Array(new Int32Array([1]).buffer)[0]||(L(n),t=C("Essp::不支持{1}架构",0,n)),!t){var s=a.type,i=this[s+"_envCheck"];a.takeoffEncodeChunk&&(i?e.canProcess||(t=C("7uMV::{1}环境不支持实时处理",0,e.envName)):t=C("2XBl::{1}类型不支持设置takeoffEncodeChunk",0,s)+(this[s]?"":C("LG7e::(未加载编码器)"))),!t&&i&&(t=this[s+"_envCheck"](e,a))}return t||""},envStart:function(e,t){var a=this.set;if(this.isMock=e?1:0,this.mockEnvInfo=e,this.buffers=[],this.recSize=0,e&&(this._streamTag="env$"+e.envName),this.state=1,this.envInLast=0,this.envInFirst=0,this.envInFix=0,this.envInFixTs=[],this._setSrcSR(t),this.engineCtx=0,this[a.type+"_start"]){var n=this.engineCtx=this[a.type+"_start"](a);n&&(n.pcmDatas=[],n.pcmSize=0)}},envResume:function(){this.envInFixTs=[]},envIn:function(e,t){var a=this,n=a.set,s=a.engineCtx;if(1==a.state){var i=a.srcSampleRate,o=e.length,l=r.PowerLevel(t,o),_=a.buffers,c=_.length;_.push(e);var f=_,u=c,h=Date.now(),p=Math.round(o/i*1e3);a.envInLast=h,1==a.buffers.length&&(a.envInFirst=h-p);var b=a.envInFixTs;b.splice(0,0,{t:h,d:p});for(var m=h,v=0,d=0;d<b.length;d++){var g=b[d];if(h-g.t>3e3){b.length=d;break}m=g.t,v+=g.d}var w=b[1],S=h-m;if(S-v>S/3&&(w&&S>1e3||b.length>=6)){var M=h-w.t-p;if(M>p/5){var R=!n.disableEnvInFix;if(a.CLog("["+h+"]"+E.get(C(R?"4Kfd::补偿{1}ms":"bM5i::未补偿{1}ms",1),[M]),3),a.envInFix+=M,R){var y=new Int16Array(M*i/1e3);o+=y.length,_.push(y)}}}var k=a.recSize,A=o,T=k+A;if(a.recSize=T,s){var x=r.SampleData(_,i,n.sampleRate,s.chunkInfo);s.chunkInfo=x,T=(k=s.pcmSize)+(A=x.data.length),s.pcmSize=T,_=s.pcmDatas,c=_.length,_.push(x.data),i=x.sampleRate}var B=Math.round(T/i*1e3),L=_.length,P=f.length,I=function(){for(var e=H?0:-A,t=null==_[0],r=c;r<L;r++){var i=_[r];null==i?t=1:(e+=i.length,s&&i.length&&a[n.type+"_encode"](s,i))}if(t&&s)for(r=u,f[0]&&(r=0);r<P;r++)f[r]=null;t&&(e=H?A:0,_[0]=null),s?s.pcmSize+=e:a.recSize+=e},H=0;try{H=!0===(H=n.onProcess(_,l,B,i,c,I))}catch(e){console.error("rec.set.onProcess"+C("gFUF::回调出错是不允许的，需保证不会抛异常"),e)}var O=Date.now()-h;if(O>10&&a.envInFirst-h>1e3&&a.CLog("rec.set.onProcess"+C("2ghS::低性能，耗时{1}ms",0,O),3),H){var N=0;for(d=c;d<L;d++)null==_[d]?N=1:_[d]=new Int16Array(0);N?a.CLog(C("ufqH::未进入异步前不能清除buffers"),3):s?s.pcmSize-=A:a.recSize-=A}else I()}else a.state||a.CLog("envIn at state=0",3)},start:function(){var e=this,t=1;if(e.set.sourceStream?e.Stream||(t=0):r.IsOpen()||(t=0),t){var a=e._streamCtx();if(e.CLog(C("kLDN::start 开始录音，")+p(a)+" stream:"+e._streamTag),e._stop(),e.envStart(null,a.sampleRate),e.state=3,e._SO&&e._SO+1!=e._S)e.CLog(C("Bp2y::start被中断"),3);else{e._SO=0;var n=function(){3==e.state&&(e.state=1,e.resume())},s="AudioContext resume: ";e._streamGet()._call[e.id]=function(){e.CLog(s+a.state+"|stream ok"),n()},u(a,(function(t){return t&&e.CLog(s+"wait..."),3==e.state}),(function(t){t&&e.CLog(s+a.state),n()}),(function(t){e.CLog(s+a.state+C("upkE::，可能无法录音：")+t,1),n()}))}}else e.CLog(C("6WmN::start失败：未open"),1)},pause:function(){var e=this._streamGet();this.state&&(this.state=2,this.CLog("pause"),e&&delete e._call[this.id])},resume:function(){var e=this,t=e._streamGet(),a="resume(wait ctx)";if(3==e.state)e.CLog(a);else if(e.state){e.state=1,e.CLog("resume"),e.envResume(),t&&(t._call[e.id]=function(t,a){1==e.state&&e.envIn(t,a)},m(t));var n=e._streamCtx();n&&u(n,(function(t){return t&&e.CLog(a+"..."),1==e.state}),(function(s){s&&e.CLog(a+n.state),m(t)}),(function(t){e.CLog(a+n.state+"[err]"+t,1)}))}},_stop:function(e){var t=this.set;this.isMock||this._S++,this.state&&(this.pause(),this.state=0),!e&&this[t.type+"_stop"]&&(this[t.type+"_stop"](this.engineCtx),this.engineCtx=0)},stop:function(e,t,a){var n,s=this,_=s.set,c=s.envInLast-s.envInFirst,f=c&&s.buffers.length;s.CLog(C("Xq4s::stop 和start时差:")+(c?c+"ms "+C("3CQP::补偿:")+s.envInFix+"ms envIn:"+f+" fps:"+(f/c*1e3).toFixed(1):"-")+" stream:"+s._streamTag+" ("+o+") LM:"+i);var u=function(){s._stop(),a&&s.close()},h=function(e){s.CLog(C("u8JG::结束录音失败：")+e,1),t&&t(e),u()},p=function(t,a,i){var o="DefaultDataType",c=s.dataType||r[o]||"blob",f="dataType="+c,p=t instanceof ArrayBuffer,b=0,m=p?t.byteLength:t.size;if("arraybuffer"==c?p||(b=1):"blob"==c?"function"!=typeof Blob?b=C.G("NonBrowser-1",[f])+C("1skY::，请设置{1}",0,l+"."+o+'="arraybuffer"'):(p&&(t=new Blob([t],{type:a})),t instanceof Blob||(b=1),a=t.type||a):b=C.G("NotSupport-1",[f]),s.CLog(C("Wv7l::结束录音 编码花{1}ms 音频时长{2}ms 文件大小{3}b",0,Date.now()-n,i,m)+" "+f+","+a),b)h(1!=b?b:C("Vkbd::{1}编码器返回的不是{2}",0,_.type,c)+", "+f);else{if(_.takeoffEncodeChunk)s.CLog(C("QWnr::启用takeoffEncodeChunk后stop返回的blob长度为0不提供音频数据"),3);else if(m<Math.max(50,i/5))return void h(C("Sz2H::生成的{1}无效",0,_.type));e&&e(t,i,a),u()}};if(!s.isMock){var b=3==s.state;if(!s.state||b)return void h(C("wf9t::未开始录音")+(b?C("Dl2c::，开始录音前无用户交互导致AudioContext未运行"):""))}s._stop(!0);var m=s.recSize;if(m)if(s[_.type]){if(s.isMock){var v=s.envCheck(s.mockEnvInfo||{envName:"mock",canProcess:!1});if(v)return void h(C("AxOH::录音错误：")+v)}var d=s.engineCtx;if(s[_.type+"_complete"]&&d){var g=Math.round(d.pcmSize/_.sampleRate*1e3);return n=Date.now(),void s[_.type+"_complete"](d,(function(e,t){p(e,t,g)}),h)}if(n=Date.now(),s.buffers[0]){var w=r.SampleData(s.buffers,s.srcSampleRate,_.sampleRate);_.sampleRate=w.sampleRate;var S=w.data;g=Math.round(S.length/_.sampleRate*1e3),s.CLog(C("CxeT::采样:{1} 花:{2}ms",0,m+"->"+S.length,Date.now()-n)),setTimeout((function(){n=Date.now(),s[_.type](S,(function(e,t){p(e,t,g)}),(function(e){h(e)}))}))}else h(C("xkKd::音频buffers被释放"))}else h(C("xGuI::未加载{1}编码器，请尝试到{2}的src/engine内找到{1}的编码器并加载",0,_.type,l));else h(C("Ltz3::未采集到录音"))}};var k=function(e,t){t.pos||(t.pos=[0],t.tracks={},t.bytes=[]);var a=t.tracks,n=[t.pos[0]],s=function(){t.pos[0]=n[0]},r=t.bytes.length,i=new Uint8Array(r+e.length);if(i.set(t.bytes),i.set(e,r),t.bytes=i,!t._ht){if(x(i,n),B(i,n),!A(x(i,n),[24,83,128,103]))return;for(x(i,n);n[0]<i.length;){var o=x(i,n),l=B(i,n),_=[0],c=0;if(!l)return;if(A(o,[22,84,174,107])){for(;_[0]<l.length;){var f=x(l,_),u=B(l,_),h=[0],p={channels:0,sampleRate:0};if(A(f,[174]))for(;h[0]<u.length;){var b=x(u,h),m=B(u,h),v=[0];if(A(b,[215])){var d=T(m);p.number=d,a[d]=p}else if(A(b,[131]))1==(d=T(m))?p.type="video":2==d?(p.type="audio",c||(t.track0=p),p.idx=c++):p.type="Type-"+d;else if(A(b,[134])){for(var g="",w=0;w<m.length;w++)g+=String.fromCharCode(m[w]);p.codec=g}else if(A(b,[225]))for(;v[0]<m.length;){var S=x(m,v),R=B(m,v);if(A(S,[181])){d=0;var y=new Uint8Array(R.reverse()).buffer;4==R.length?d=new Float32Array(y)[0]:8==R.length?d=new Float64Array(y)[0]:M("WebM Track !Float",1,R),p.sampleRate=Math.round(d)}else A(S,[98,100])?p.bitDepth=T(R):A(S,[159])&&(p.channels=T(R))}}}t._ht=1,M("WebM Tracks",a),s();break}}}var k=t.track0;if(k){var E=k.sampleRate;if(t.webmSR=E,16==k.bitDepth&&/FLOAT/i.test(k.codec)&&(k.bitDepth=32,M("WebM 16->32 bit",3)),E<8e3||32!=k.bitDepth||k.channels<1||!/(\b|_)PCM\b/i.test(k.codec))return t.bytes=[],t.bad||M("WebM Track Unexpected",3,t),t.bad=1,-1;for(var C=[],L=0;n[0]<i.length&&(f=x(i,n),u=B(i,n));){if(A(f,[163])){var P=15&u[0];if(!(p=a[P]))return M("WebM !Track"+P,1,a),-1;if(0===p.idx){var I=new Uint8Array(u.length-4);for(w=4;w<u.length;w++)I[w-4]=u[w];C.push(I),L+=I.length}}s()}if(L){var H=new Uint8Array(i.length-t.pos[0]);H.set(i.subarray(t.pos[0])),t.bytes=H,t.pos[0]=0,I=new Uint8Array(L),w=0;for(var O=0;w<C.length;w++)I.set(C[w],O),O+=C[w].length;if(y=new Float32Array(I.buffer),k.channels>1){var N=[];for(w=0;w<y.length;)N.push(y[w]),w+=k.channels;y=new Float32Array(N)}return y}}},A=function(e,t){if(!e||e.length!=t.length)return!1;if(1==e.length)return e[0]==t[0];for(var a=0;a<e.length;a++)if(e[a]!=t[a])return!1;return!0},T=function(e){for(var t="",a=0;a<e.length;a++){var n=e[a];t+=(n<16?"0":"")+n.toString(16)}return parseInt(t,16)||0},x=function(e,t,a){var n=t[0];if(!(n>=e.length)){var s=("0000000"+e[n].toString(2)).substr(-8),r=/^(0*1)(\d*)$/.exec(s);if(r){var i=r[1].length,o=[];if(!(n+i>e.length)){for(var l=0;l<i;l++)o[l]=e[n],n++;return a&&(o[0]=parseInt(r[2]||"0",2)),t[0]=n,o}}}},B=function(e,t){var a=x(e,t,1);if(a){var n=T(a),s=t[0],r=[];if(n<2147483647){if(s+n>e.length)return;for(var i=0;i<n;i++)r[i]=e[s],s++}return t[0]=s,r}},E=r.i18n={lang:"zh-CN",alias:{"zh-CN":"zh","en-US":"en"},locales:{},data:{},put:function(e,t){var a=l+".i18n.put: ",n=e.overwrite;n=null==n||n;var s=e.lang;if(!(s=E.alias[s]||s))throw new Error(a+"set.lang?");var r=E.locales[s];r||(r={},E.locales[s]=r);for(var i,o=/^([\w\-]+):/,_=0;_<t.length;_++){var c=t[_];if(i=o.exec(c)){var f=i[1];c=c.substr(f.length+1),!n&&r[f]||(r[f]=c)}else M(a+"'key:'? "+c,3,e)}},get:function(){return E.v_G.apply(null,arguments)},v_G:function(e,t,a){t=t||[],a=a||E.lang,a=E.alias[a]||a;var n=E.locales[a],s=n&&n[e]||"";return s||"zh"==a?(E.lastLang=a,"=Empty"==s?"":s.replace(/\{(\d+)(\!?)\}/g,(function(a,n,r){return a=t[(n=+n||0)-1],(n<1||n>t.length)&&(a="{?}",M("i18n["+e+"] no {"+n+"}: "+s,3)),r?"":a}))):"en"==a?E.v_G(e,t,"zh"):E.v_G(e,t,"en")},$T:function(){return E.v_T.apply(null,arguments)},v_T:function(){for(var e,t=arguments,a="",s=[],r=0,i=l+".i18n.$T:",o=/^([\w\-]*):/,_=0;_<t.length;_++){var c=t[_];if(0==_){if(!(a=(e=o.exec(c))&&e[1]))throw new Error(i+"0 'key:'?");c=c.substr(a.length+1)}if(-1===r)s.push(c);else{if(r)throw new Error(i+" bad args");if(0===c)r=-1;else if(n(c)){if(c<1)throw new Error(i+" bad args");r=c}else{var f=1==_?"en":_?"":"zh";if((e=o.exec(c))&&(f=e[1]||f,c=c.substr(e[1].length+1)),!e||!f)throw new Error(i+_+" 'lang:'?");E.put({lang:f,overwrite:!1},[a+":"+c])}}}return a?r>0?a:E.v_G(a,s):""}},C=E.$T;C.G=E.get,C("NonBrowser-1::非浏览器环境，不支持{1}",1),C("IllegalArgs-1::参数错误：{1}",1),C("NeedImport-2::调用{1}需要先导入{2}",2),C("NotSupport-1::不支持：{1}",1),r.TrafficImgUrl="//ia.51.la/go1?id=20469973&pvFlag=1";var L=r.Traffic=function(e){if(t){e=e?"/"+l+"/Report/"+e:"";var a=r.TrafficImgUrl;if(a){var n=r.Traffic,s=/^(https?:..[^\/#]*\/?)[^#]*/i.exec(location.href)||[],i=s[1]||"http://file/",o=(s[0]||i)+e;0==a.indexOf("//")&&(a=/^https:/i.test(o)?"https:"+a:"http:"+a),e&&(a=a+"&cu="+encodeURIComponent(i+e)),n[o]||(n[o]=1,(new Image).src=a,M("Traffic Analysis Image: "+(e||l+".TrafficImgUrl="+r.TrafficImgUrl)))}}};_&&(M(C("8HO5::覆盖导入{1}",0,l),1),_.Destroy()),e[l]=r}function P(e){this.id=++y,L();var t={type:"mp3",onProcess:a};for(var s in e)t[s]=e[s];this.set=t;var r=t.bitRate,i=t.sampleRate;(r&&!n(r)||i&&!n(i))&&this.CLog(C.G("IllegalArgs-1",[C("VtS4::{1}和{2}必须是数值",0,"sampleRate","bitRate")]),1,e),t.bitRate=+r||16,t.sampleRate=+i||16e3,this.state=0,this._S=9,this.Sync={O:9,C:9}}}(r=s?window:Object,s),void 0===(n=function(){return r.Recorder}.call(t,a,t,e))||(e.exports=n),e.exports&&(e.exports=r.Recorder)},function(e,t){var a,n,s;a="object"==typeof window&&!!window.document,n=(a?window:Object).Recorder,s=n.i18n,function(e,t,a,n){"use strict";var s="48000, 44100, 32000, 24000, 22050, 16000, 12000, 11025, 8000",r="8, 16, 24, 32, 40, 48, 56, 64, 80, 96, 112, 128, 144, 160, 192, 224, 256, 320";e.prototype.enc_mp3={stable:!0,takeEC:"full",getTestMsg:function(){return a("Zm7L::采样率范围：{1}；比特率范围：{2}（不同比特率支持的采样率范围不同，小于32kbps时采样率需小于32000）",0,s,r)}};var i,o=function(t){var n=t.bitRate,i=t.sampleRate,o=i;if(-1==(" "+r+",").indexOf(" "+n+",")&&e.CLog(a("eGB9::{1}不在mp3支持的取值范围：{2}",0,"bitRate="+n,r),3),-1==(" "+s+",").indexOf(" "+i+",")){for(var l=s.split(", "),_=[],c=0;c<l.length;c++)_.push({v:+l[c],s:Math.abs(l[c]-i)});_.sort((function(e,t){return e.s-t.s})),o=_[0].v,t.sampleRate=o,e.CLog(a("zLTa::sampleRate已更新为{1}，因为{2}不在mp3支持的取值范围：{3}",0,o,i,s),3)}},l=function(){return a.G("NeedImport-2",["mp3.js","src/engine/mp3-engine.js"])},_=n&&"function"==typeof Worker;e.prototype.mp3=function(t,a,n){var s=this.set,r=t.length;if(e.lamejs){if(_){var i=this.mp3_start(s);if(i){if(i.isW)return this.mp3_encode(i,t),void this.mp3_complete(i,a,n,1);this.mp3_stop(i)}}o(s);var c=new e.lamejs.Mp3Encoder(1,s.sampleRate,s.bitRate),f=new Int8Array(5e5),p=0,b=0,m=0,v=function(){try{if(b<r)var e=c.encodeBuffer(t.subarray(b,b+57600));else m=1,e=c.flush()}catch(e){if(console.error(e),!m)try{c.flush()}catch(e){console.error(e)}return void n("MP3 Encoder: "+e.message)}var i=e.length;if(i>0){if(p+i>f.length){var o=new Int8Array(f.length+Math.max(5e5,i));o.set(f.subarray(0,p)),f=o}f.set(e,p),p+=i}if(b<r)b+=57600,setTimeout(v);else{var l=[f.buffer.slice(0,p)],_=u.fn(l,p,r,s.sampleRate);h(_,s),a(l[0]||new ArrayBuffer(0),"audio/mp3")}};v()}else n(l())},e.BindDestroy("mp3Worker",(function(){i&&(e.CLog("mp3Worker Destroy"),i.terminate(),i=null)})),e.prototype.mp3_envCheck=function(t,n){var s="";return n.takeoffEncodeChunk&&(f()||(s=a("yhUs::当前浏览器版本太低，无法实时处理"))),s||e.lamejs||(s=l()),s},e.prototype.mp3_start=function(e){return f(e)};var c={id:0},f=function(t,n){var s,r=function(e){var t=e.data,a=s.wkScope.wk_ctxs,n=s.wkScope.wk_lame,r=s.wkScope.wk_mp3TrimFix,i=a[t.id];if("init"==t.action)a[t.id]={sampleRate:t.sampleRate,bitRate:t.bitRate,takeoff:t.takeoff,pcmSize:0,memory:new Int8Array(5e5),mOffset:0,encObj:new n.Mp3Encoder(1,t.sampleRate,t.bitRate)};else if(!i)return;var o=function(e){var t=e.length;if(i.mOffset+t>i.memory.length){var a=new Int8Array(i.memory.length+Math.max(5e5,t));a.set(i.memory.subarray(0,i.mOffset)),i.memory=a}i.memory.set(e,i.mOffset),i.mOffset+=t};switch(t.action){case"stop":if(!i.isCp)try{i.encObj.flush()}catch(e){console.error(e)}i.encObj=null,delete a[t.id];break;case"encode":if(i.isCp)break;i.pcmSize+=t.pcm.length;try{var l=i.encObj.encodeBuffer(t.pcm)}catch(e){i.err=e,console.error(e)}l&&l.length>0&&(i.takeoff?p.onmessage({action:"takeoff",id:t.id,chunk:l}):o(l));break;case"complete":i.isCp=1;try{l=i.encObj.flush()}catch(e){i.err=e,console.error(e)}if(l&&l.length>0&&(i.takeoff?p.onmessage({action:"takeoff",id:t.id,chunk:l}):o(l)),i.err){p.onmessage({action:t.action,id:t.id,err:"MP3 Encoder: "+i.err.message});break}var _=[i.memory.buffer.slice(0,i.mOffset)],c=r.fn(_,i.mOffset,i.pcmSize,i.sampleRate);p.onmessage({action:t.action,id:t.id,blob:_[0]||new ArrayBuffer(0),meta:c})}},l=function(e){p.onmessage=function(t){var a=t;e&&(a=t.data);var n=c[a.id];n&&("takeoff"==a.action?n.set.takeoffEncodeChunk(new Uint8Array(a.chunk.buffer)):(n.call&&n.call(a),n.call=null))}},h=function(){var e={worker:p,set:t};return t?(e.id=++c.id,c[e.id]=e,o(t),p.postMessage({action:"init",id:e.id,sampleRate:t.sampleRate,bitRate:t.bitRate,takeoff:!!t.takeoffEncodeChunk,x:new Int16Array(5)})):p.postMessage({x:new Int16Array(5)}),e},p=i;if(n||!_)return e.CLog(a("k9PT::当前环境不支持Web Worker，mp3实时编码器运行在主线程中"),3),p={postMessage:function(e){r({data:e})}},s={wkScope:{wk_ctxs:{},wk_lame:e.lamejs,wk_mp3TrimFix:u}},l(),h();try{if(!p){var b=(r+"").replace(/[\w\$]+\.onmessage/g,"self.postMessage"),m=");wk_lame();self.onmessage="+(b=b.replace(/[\w\$]+\.wkScope/g,"wkScope"));m+=";var wkScope={ wk_ctxs:{},wk_lame:wk_lame",m+=",wk_mp3TrimFix:{rm:"+u.rm+",fn:"+u.fn+"} }";var v=e.lamejs.toString(),d=(window.URL||webkitURL).createObjectURL(new Blob(["var wk_lame=(",v,m],{type:"text/javascript"}));p=new Worker(d),setTimeout((function(){(window.URL||webkitURL).revokeObjectURL(d)}),1e4),l(1)}var g=h();return g.isW=1,i=p,g}catch(e){return p&&p.terminate(),console.error(e),f(t,1)}};e.prototype.mp3_stop=function(t){if(t&&t.worker){t.worker.postMessage({action:"stop",id:t.id}),t.worker=null,delete c[t.id];var n=-1;for(var s in c)n++;n&&e.CLog(a("fT6M::mp3 worker剩{1}个未stop",0,n),3)}},e.prototype.mp3_encode=function(e,t){e&&e.worker&&e.worker.postMessage({action:"encode",id:e.id,pcm:t})},e.prototype.mp3_complete=function(e,t,n,s){var r=this;e&&e.worker?(e.call=function(a){s&&r.mp3_stop(e),a.err?n(a.err):(h(a.meta,e.set),t(a.blob,"audio/mp3"))},e.worker.postMessage({action:"complete",id:e.id})):n(a("mPxH::mp3编码器未start"))},e.mp3ReadMeta=function(e,t){var a="undefined"!=typeof window&&window.parseInt||"undefined"!=typeof self&&self.parseInt||parseInt,n=new Uint8Array(e[0]||[]);if(n.length<4)return null;var s=function(e,t){return("0000000"+((t||n)[e]||0).toString(2)).substr(-8)},r=s(0)+s(1),i=s(2)+s(3);if(!/^1{11}/.test(r))return null;var o={"00":2.5,10:2,11:1}[r.substr(11,2)],l={"01":3}[r.substr(13,2)],_={1:[44100,48e3,32e3],2:[22050,24e3,16e3],2.5:[11025,12e3,8e3]}[o];_&&(_=_[a(i.substr(4,2),2)]);var c=[[0,8,16,24,32,40,48,56,64,80,96,112,128,144,160],[0,32,40,48,56,64,80,96,112,128,160,192,224,256,320]][1==o?1:0][a(i.substr(0,4),2)];if(!(o&&l&&c&&_))return null;for(var f=Math.round(8*t/c),u=1==l?384:2==l||1==o?1152:576,h=u/_*1e3,p=Math.floor(u*c/8/_*1e3),b=0,m=0,v=0;v<e.length;v++){var d=e[v];if((m+=d.byteLength)>=p+3){var g=new Uint8Array(d);b="1"==s(d.byteLength-(m-(p+3)+1),g).charAt(6);break}}return b&&p++,{version:o,layer:l,sampleRate:_,bitRate:c,duration:f,size:t,hasPadding:b,frameSize:p,frameDurationFloat:h}};var u={rm:e.mp3ReadMeta,fn:function(e,t,a,n){var s=this.rm(e,t);if(!s)return{size:t,err:"mp3 unknown format"};var r=Math.round(a/n*1e3),i=Math.floor((s.duration-r)/s.frameDurationFloat);if(i>0){var o=i*s.frameSize-(s.hasPadding?1:0);t-=o;for(var l=0,_=[],c=0;c<e.length;c++){var f=e[c];if(o<=0)break;o>=f.byteLength?(o-=f.byteLength,_.push(f),e.splice(c,1),c--):(e[c]=f.slice(o),l=f,o=0)}if(!this.rm(e,t)){for(l&&(e[0]=l),c=0;c<_.length;c++)e.splice(c,0,_[c]);s.err="mp3 fix error: 已还原，错误原因不明"}var u=s.trimFix={};u.remove=i,u.removeDuration=Math.round(i*s.frameDurationFloat),u.duration=Math.round(8*t/s.bitRate)}return s}},h=function(t,n){var s="MP3 Info: ";(t.sampleRate&&t.sampleRate!=n.sampleRate||t.bitRate&&t.bitRate!=n.bitRate)&&(e.CLog(s+a("uY9i::和设置的不匹配{1}，已更新成{2}",0,"set:"+n.bitRate+"kbps "+n.sampleRate+"hz","set:"+t.bitRate+"kbps "+t.sampleRate+"hz"),3,n),n.sampleRate=t.sampleRate,n.bitRate=t.bitRate);var r=t.trimFix;r?(s+=a("iMSm::Fix移除{1}帧",0,r.remove)+" "+r.removeDuration+"ms -> "+r.duration+"ms",r.remove>2&&(t.err=(t.err?t.err+", ":"")+a("b9zm::移除帧数过多"))):s+=(t.duration||"-")+"ms",t.err?e.CLog(s,t.size?1:0,t.err,t):e.CLog(s,t)}}(n,0,s.$T,a)},function(e,t){!function(e){"use strict";function t(){var e=function(e){return Math.log(e)/Math.log(10)},a=function(e){throw new Error("abort("+e+")")};function n(e){return new Int8Array(e)}function s(e){return new Int16Array(e)}function r(e){return new Int32Array(e)}function i(e){return new Float32Array(e)}function o(e){return new Float64Array(e)}function l(e){if(1==e.length)return i(e[0]);var t=e[0];e=e.slice(1);for(var a=[],n=0;n<t;n++)a.push(l(e));return a}function _(e){if(1==e.length)return r(e[0]);var t=e[0];e=e.slice(1);for(var a=[],n=0;n<t;n++)a.push(_(e));return a}function c(e){if(1==e.length)return s(e[0]);var t=e[0];e=e.slice(1);for(var a=[],n=0;n<t;n++)a.push(c(e));return a}function f(e){if(1==e.length)return new Array(e[0]);var t=e[0];e=e.slice(1);for(var a=[],n=0;n<t;n++)a.push(f(e));return a}var u={fill:function(e,t,a,n){if(2==arguments.length)for(var s=0;s<e.length;s++)e[s]=arguments[1];else for(s=t;s<a;s++)e[s]=n}},h={arraycopy:function(e,t,a,n,s){for(var r=t+s;t<r;)a[n++]=e[t++]}},p={};function b(e){this.ordinal=e}p.SQRT2=1.4142135623730951,p.FAST_LOG10=function(t){return e(t)},p.FAST_LOG10_X=function(t,a){return e(t)*a},b.short_block_allowed=new b(0),b.short_block_coupled=new b(1),b.short_block_dispensed=new b(2),b.short_block_forced=new b(3);var m={};function v(e){this.ordinal=e}function d(e){var t=e;this.ordinal=function(){return t}}function g(){this.getLameShortVersion=function(){return"3.98.4"}}function w(){var e=null;function t(e){this.bits=0|e}this.qupvt=null,this.setModules=function(t){this.qupvt=t,e=t};var n=[[0,0],[0,0],[0,0],[0,0],[0,0],[0,1],[1,1],[1,1],[1,2],[2,2],[2,3],[2,3],[3,4],[3,4],[3,4],[4,5],[4,5],[4,6],[5,6],[5,6],[5,7],[6,7],[6,7]];function s(e,t,a,n,s,r){var i=.5946/t;for(e>>=1;0!=e--;)s[r++]=i>a[n++]?0:1,s[r++]=i>a[n++]?0:1}function i(t,a,n,s,r,i){var o=(t>>=1)%2;for(t>>=1;0!=t--;){var l,_,c,f,u,h,p,b;l=n[s++]*a,_=n[s++]*a,u=0|l,c=n[s++]*a,h=0|_,f=n[s++]*a,p=0|c,l+=e.adj43[u],b=0|f,_+=e.adj43[h],r[i++]=0|l,c+=e.adj43[p],r[i++]=0|_,f+=e.adj43[b],r[i++]=0|c,r[i++]=0|f}0!=o&&(u=0|(l=n[s++]*a),h=0|(_=n[s++]*a),l+=e.adj43[u],_+=e.adj43[h],r[i++]=0|l,r[i++]=0|_)}var o=[1,2,5,7,7,10,10,13,13,13,13,13,13,13,13];function l(e,t,n,s){var r=function(e,t,a){var n=0,s=0;do{var r=e[t++],i=e[t++];n<r&&(n=r),s<i&&(s=i)}while(t<a);return n<s&&(n=s),n}(e,t,n);switch(r){case 0:return r;case 1:return function(e,t,a,n){var s=0,r=T.ht[1].hlen;do{var i=2*e[t+0]+e[t+1];t+=2,s+=r[i]}while(t<a);return n.bits+=s,1}(e,t,n,s);case 2:case 3:return function(e,t,a,n,s){var r,i,o=0,l=T.ht[n].xlen;i=2==n?T.table23:T.table56;do{var _=e[t+0]*l+e[t+1];t+=2,o+=i[_]}while(t<a);return r=65535&o,(o>>=16)>r&&(o=r,n++),s.bits+=o,n}(e,t,n,o[r-1],s);case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 12:case 13:case 14:case 15:return function(e,t,a,n,s){var r=0,i=0,o=0,l=T.ht[n].xlen,_=T.ht[n].hlen,c=T.ht[n+1].hlen,f=T.ht[n+2].hlen;do{var u=e[t+0]*l+e[t+1];t+=2,r+=_[u],i+=c[u],o+=f[u]}while(t<a);var h=n;return r>i&&(r=i,h++),r>o&&(r=o,h=n+2),s.bits+=r,h}(e,t,n,o[r-1],s);default:var i,l;for(r>O.IXMAX_VAL&&a(),r-=15,i=24;i<32&&!(T.ht[i].linmax>=r);i++);for(l=i-8;l<24&&!(T.ht[l].linmax>=r);l++);return function(e,t,a,n,s,r){var i,o=65536*T.ht[n].xlen+T.ht[s].xlen,l=0;do{var _=e[t++],c=e[t++];0!=_&&(_>14&&(_=15,l+=o),_*=16),0!=c&&(c>14&&(c=15,l+=o),_+=c),l+=T.largetbl[_]}while(t<a);return i=65535&l,(l>>=16)>i&&(l=i,n=s),r.bits+=l,n}(e,t,n,l,i,s)}}function _(e,a,n,s,r,i,o,_){for(var c=a.big_values,f=2;f<Y.SBMAX_l+1;f++){var u=e.scalefac_band.l[f];if(u>=c)break;var h=r[f-2]+a.count1bits;if(n.part2_3_length<=h)break;var p=new t(h),b=l(s,u,c,p);h=p.bits,n.part2_3_length<=h||(n.assign(a),n.part2_3_length=h,n.region0_count=i[f-2],n.region1_count=f-2-i[f-2],n.table_select[0]=o[f-2],n.table_select[1]=_[f-2],n.table_select[2]=b)}}this.noquant_count_bits=function(e,n,s){var r=n.l3_enc,i=Math.min(576,n.max_nonzero_coeff+2>>1<<1);for(null!=s&&(s.sfb_count1=0);i>1&&0==(r[i-1]|r[i-2]);i-=2);n.count1=i;for(var o=0,_=0;i>3;i-=4){var c;if((2147483647&(r[i-1]|r[i-2]|r[i-3]|r[i-4]))>1)break;c=2*(2*(2*r[i-4]+r[i-3])+r[i-2])+r[i-1],o+=T.t32l[c],_+=T.t33l[c]}var f=o;if(n.count1table_select=0,o>_&&(f=_,n.count1table_select=1),n.count1bits=f,n.big_values=i,0==i)return f;if(n.block_type==Y.SHORT_TYPE)(o=3*e.scalefac_band.s[3])>n.big_values&&(o=n.big_values),_=n.big_values;else if(n.block_type==Y.NORM_TYPE){if(o=n.region0_count=e.bv_scf[i-2],_=n.region1_count=e.bv_scf[i-1],_=e.scalefac_band.l[o+_+2],o=e.scalefac_band.l[o+1],_<i){var u=new t(f);n.table_select[2]=l(r,_,i,u),f=u.bits}}else n.region0_count=7,n.region1_count=Y.SBMAX_l-1-7-1,(o=e.scalefac_band.l[8])>(_=i)&&(o=_);if(o=Math.min(o,i),_=Math.min(_,i),0<o&&(u=new t(f),n.table_select[0]=l(r,0,o,u),f=u.bits),o<_&&(u=new t(f),n.table_select[1]=l(r,o,_,u),f=u.bits),2==e.use_best_huffman&&a(),null!=s&&n.block_type==Y.NORM_TYPE){for(var h=0;e.scalefac_band.l[h]<n.big_values;)h++;s.sfb_count1=h}return f},this.count_bits=function(t,n,r,o){var l=r.l3_enc,_=O.IXMAX_VAL/e.IPOW20(r.global_gain);return r.xrpow_max>_?O.LARGE_BITS:(function(t,n,r,o,l){var _,c,f,h=0,p=0,b=0,m=0,v=n,d=0,g=v,w=0,S=t,M=0;for(f=null!=l&&o.global_gain==l.global_gain,c=o.block_type==Y.SHORT_TYPE?38:21,_=0;_<=c;_++){var R=-1;if((f||o.block_type==Y.NORM_TYPE)&&(R=o.global_gain-(o.scalefac[_]+(0!=o.preflag?e.pretab[_]:0)<<o.scalefac_scale+1)-8*o.subblock_gain[o.window[_]]),f&&l.step[_]==R)0!=p&&(i(p,r,S,M,g,w),p=0),0!=b&&a();else{var y,k=o.width[_];if(h+o.width[_]>o.max_nonzero_coeff&&(y=o.max_nonzero_coeff-h+1,u.fill(n,o.max_nonzero_coeff,576,0),(k=y)<0&&(k=0),_=c+1),0==p&&0==b&&(g=v,w=d,S=t,M=m),null!=l&&l.sfb_count1>0&&_>=l.sfb_count1&&l.step[_]>0&&R>=l.step[_]?(0!=p&&(i(p,r,S,M,g,w),p=0,g=v,w=d,S=t,M=m),b+=k):(0!=b&&(s(b,r,S,M,g,w),b=0,g=v,w=d,S=t,M=m),p+=k),k<=0){0!=b&&a(),0!=p&&a();break}}_<=c&&(d+=o.width[_],m+=o.width[_],h+=o.width[_])}0!=p&&(i(p,r,S,M,g,w),p=0),0!=b&&a()}(n,l,e.IPOW20(r.global_gain),r,o),0!=(2&t.substep_shaping)&&a(),this.noquant_count_bits(t,r,o))},this.best_huffman_divide=function(e,a){var n=new D,s=a.l3_enc,i=r(23),o=r(23),c=r(23),f=r(23);if(a.block_type!=Y.SHORT_TYPE||1!=e.mode_gr){n.assign(a),a.block_type==Y.NORM_TYPE&&(function(e,a,n,s,r,i,o){for(var _=a.big_values,c=0;c<=22;c++)s[c]=O.LARGE_BITS;for(c=0;c<16;c++){var f=e.scalefac_band.l[c+1];if(f>=_)break;var u=0,h=new t(u),p=l(n,0,f,h);u=h.bits;for(var b=0;b<8;b++){var m=e.scalefac_band.l[c+b+2];if(m>=_)break;var v=u,d=l(n,f,m,h=new t(v));v=h.bits,s[c+b]>v&&(s[c+b]=v,r[c+b]=c,i[c+b]=p,o[c+b]=d)}}}(e,a,s,i,o,c,f),_(e,n,a,s,i,o,c,f));var u=n.big_values;if(!(0==u||(s[u-2]|s[u-1])>1||(u=a.count1+2)>576)){n.assign(a),n.count1=u;for(var h=0,p=0;u>n.big_values;u-=4){var b=2*(2*(2*s[u-4]+s[u-3])+s[u-2])+s[u-1];h+=T.t32l[b],p+=T.t33l[b]}if(n.big_values=u,n.count1table_select=0,h>p&&(h=p,n.count1table_select=1),n.count1bits=h,n.block_type==Y.NORM_TYPE)_(e,n,a,s,i,o,c,f);else{if(n.part2_3_length=h,(h=e.scalefac_band.l[8])>u&&(h=u),h>0){var m=new t(n.part2_3_length);n.table_select[0]=l(s,0,h,m),n.part2_3_length=m.bits}u>h&&(m=new t(n.part2_3_length),n.table_select[1]=l(s,h,u,m),n.part2_3_length=m.bits),a.part2_3_length>n.part2_3_length&&a.assign(n)}}}};var c=[1,1,1,1,8,2,2,2,4,4,4,8,8,8,16,16],f=[1,2,4,8,1,2,4,8,2,4,8,2,4,8,4,8],h=[0,0,0,0,3,1,1,1,2,2,2,3,3,3,4,4],p=[0,1,2,3,0,1,2,3,1,2,3,1,2,3,2,3];w.slen1_tab=h,w.slen2_tab=p,this.best_scalefac_store=function(t,a,n,s){var r,i,o,l,_=s.tt[a][n],u=0;for(o=0,r=0;r<_.sfbmax;r++){var b=_.width[r];for(o+=b,l=-b;l<0&&0==_.l3_enc[l+o];l++);0==l&&(_.scalefac[r]=u=-2)}if(0==_.scalefac_scale&&0==_.preflag){var m=0;for(r=0;r<_.sfbmax;r++)_.scalefac[r]>0&&(m|=_.scalefac[r]);if(0==(1&m)&&0!=m){for(r=0;r<_.sfbmax;r++)_.scalefac[r]>0&&(_.scalefac[r]>>=1);_.scalefac_scale=u=1}}if(0==_.preflag&&_.block_type!=Y.SHORT_TYPE&&2==t.mode_gr){for(r=11;r<Y.SBPSY_l&&!(_.scalefac[r]<e.pretab[r]&&-2!=_.scalefac[r]);r++);if(r==Y.SBPSY_l){for(r=11;r<Y.SBPSY_l;r++)_.scalefac[r]>0&&(_.scalefac[r]-=e.pretab[r]);_.preflag=u=1}}for(i=0;i<4;i++)s.scfsi[n][i]=0;for(2==t.mode_gr&&1==a&&s.tt[0][n].block_type!=Y.SHORT_TYPE&&s.tt[1][n].block_type!=Y.SHORT_TYPE&&(function(e,t){for(var a,n=t.tt[1][e],s=t.tt[0][e],r=0;r<T.scfsi_band.length-1;r++){for(a=T.scfsi_band[r];a<T.scfsi_band[r+1]&&!(s.scalefac[a]!=n.scalefac[a]&&n.scalefac[a]>=0);a++);if(a==T.scfsi_band[r+1]){for(a=T.scfsi_band[r];a<T.scfsi_band[r+1];a++)n.scalefac[a]=-1;t.scfsi[e][r]=1}}var i=0,o=0;for(a=0;a<11;a++)-1!=n.scalefac[a]&&(o++,i<n.scalefac[a]&&(i=n.scalefac[a]));for(var l=0,_=0;a<Y.SBPSY_l;a++)-1!=n.scalefac[a]&&(_++,l<n.scalefac[a]&&(l=n.scalefac[a]));for(r=0;r<16;r++)if(i<c[r]&&l<f[r]){var u=h[r]*o+p[r]*_;n.part2_length>u&&(n.part2_length=u,n.scalefac_compress=r)}}(n,s),u=0),r=0;r<_.sfbmax;r++)-2==_.scalefac[r]&&(_.scalefac[r]=0);0!=u&&(2==t.mode_gr?this.scale_bitcount(_):this.scale_bitcount_lsf(t,_))};var b=[0,18,36,54,54,36,54,72,54,72,90,72,90,108,108,126],m=[0,18,36,54,51,35,53,71,52,70,88,69,87,105,104,122],v=[0,10,20,30,33,21,31,41,32,42,52,43,53,63,64,74];this.scale_bitcount=function(t){var a,n,s,r=0,i=0,o=t.scalefac;if(t.block_type==Y.SHORT_TYPE)s=b,0!=t.mixed_block_flag&&(s=m);else if(s=v,0==t.preflag){for(n=11;n<Y.SBPSY_l&&!(o[n]<e.pretab[n]);n++);if(n==Y.SBPSY_l)for(t.preflag=1,n=11;n<Y.SBPSY_l;n++)o[n]-=e.pretab[n]}for(n=0;n<t.sfbdivide;n++)r<o[n]&&(r=o[n]);for(;n<t.sfbmax;n++)i<o[n]&&(i=o[n]);for(t.part2_length=O.LARGE_BITS,a=0;a<16;a++)r<c[a]&&i<f[a]&&t.part2_length>s[a]&&(t.part2_length=s[a],t.scalefac_compress=a);return t.part2_length==O.LARGE_BITS};var d=[[15,15,7,7],[15,15,7,0],[7,3,0,0],[15,31,31,0],[7,7,7,0],[3,3,0,0]];this.scale_bitcount_lsf=function(t,a){var n,s,i,o,l,_,c,f,u=r(4),h=a.scalefac;for(n=0!=a.preflag?2:0,c=0;c<4;c++)u[c]=0;if(a.block_type==Y.SHORT_TYPE){s=1;var p=e.nr_of_sfb_block[n][s];for(f=0,i=0;i<4;i++)for(o=p[i]/3,c=0;c<o;c++,f++)for(l=0;l<3;l++)h[3*f+l]>u[i]&&(u[i]=h[3*f+l])}else for(s=0,p=e.nr_of_sfb_block[n][s],f=0,i=0;i<4;i++)for(o=p[i],c=0;c<o;c++,f++)h[f]>u[i]&&(u[i]=h[f]);for(_=!1,i=0;i<4;i++)u[i]>d[n][i]&&(_=!0);if(!_){var b,m,v,w;for(a.sfb_partition_table=e.nr_of_sfb_block[n][s],i=0;i<4;i++)a.slen[i]=g[u[i]];switch(b=a.slen[0],m=a.slen[1],v=a.slen[2],w=a.slen[3],n){case 0:a.scalefac_compress=(5*b+m<<4)+(v<<2)+w;break;case 1:a.scalefac_compress=400+(5*b+m<<2)+v;break;case 2:a.scalefac_compress=500+3*b+m}}if(!_)for(a.part2_length=0,i=0;i<4;i++)a.part2_length+=a.slen[i]*a.sfb_partition_table[i];return _};var g=[0,1,2,2,3,3,3,3,4,4,4,4,4,4,4,4];this.huffman_init=function(e){for(var t=2;t<=576;t+=2){for(var a,s=0;e.scalefac_band.l[++s]<t;);for(a=n[s][0];e.scalefac_band.l[a+1]>t;)a--;for(a<0&&(a=n[s][0]),e.bv_scf[t-2]=a,a=n[s][1];e.scalefac_band.l[a+e.bv_scf[t-2]+2]>t;)a--;a<0&&(a=n[s][1]),e.bv_scf[t-1]=a}}}function S(){}function M(){function e(e,t,a,n,s,r,i,o,l,_,c,f,u,h){this.quant_comp=t,this.quant_comp_s=a,this.safejoint=n,this.nsmsfix=s,this.st_lrm=r,this.st_s=i,this.nsbass=o,this.scale=l,this.masking_adj=_,this.ath_lower=c,this.ath_curve=f,this.interch=u,this.sfscale=h}var t;function n(e,t,n){a()}this.setModules=function(e){t=e};var s=[new e(8,9,9,0,0,6.6,145,0,.95,0,-30,11,.0012,1),new e(16,9,9,0,0,6.6,145,0,.95,0,-25,11,.001,1),new e(24,9,9,0,0,6.6,145,0,.95,0,-20,11,.001,1),new e(32,9,9,0,0,6.6,145,0,.95,0,-15,11,.001,1),new e(40,9,9,0,0,6.6,145,0,.95,0,-10,11,9e-4,1),new e(48,9,9,0,0,6.6,145,0,.95,0,-10,11,9e-4,1),new e(56,9,9,0,0,6.6,145,0,.95,0,-6,11,8e-4,1),new e(64,9,9,0,0,6.6,145,0,.95,0,-2,11,8e-4,1),new e(80,9,9,0,0,6.6,145,0,.95,0,0,8,7e-4,1),new e(96,9,9,0,2.5,6.6,145,0,.95,0,1,5.5,6e-4,1),new e(112,9,9,0,2.25,6.6,145,0,.95,0,2,4.5,5e-4,1),new e(128,9,9,0,1.95,6.4,140,0,.95,0,3,4,2e-4,1),new e(160,9,9,1,1.79,6,135,0,.95,-2,5,3.5,0,1),new e(192,9,9,1,1.49,5.6,125,0,.97,-4,7,3,0,0),new e(224,9,9,1,1.25,5.2,125,0,.98,-6,9,2,0,0),new e(256,9,9,1,.97,5.2,125,0,1,-8,10,1,0,0),new e(320,9,9,1,.9,5.2,125,0,1,-10,12,0,0,0)];function r(e,a,n){var r=a,i=t.nearestBitrateFullIndex(a);if(e.VBR=v.vbr_abr,e.VBR_mean_bitrate_kbps=r,e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,320),e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,8),e.brate=e.VBR_mean_bitrate_kbps,e.VBR_mean_bitrate_kbps>320&&(e.disable_reservoir=!0),s[i].safejoint>0&&(e.exp_nspsytune=2|e.exp_nspsytune),s[i].sfscale>0&&(e.internal_flags.noise_shaping=2),Math.abs(s[i].nsbass)>0){var o=int(4*s[i].nsbass);o<0&&(o+=64),e.exp_nspsytune=e.exp_nspsytune|o<<2}return 0!=n?e.quant_comp=s[i].quant_comp:Math.abs(e.quant_comp- -1)>0||(e.quant_comp=s[i].quant_comp),0!=n?e.quant_comp_short=s[i].quant_comp_s:Math.abs(e.quant_comp_short- -1)>0||(e.quant_comp_short=s[i].quant_comp_s),0!=n?e.msfix=s[i].nsmsfix:Math.abs(e.msfix- -1)>0||(e.msfix=s[i].nsmsfix),0!=n?e.internal_flags.nsPsy.attackthre=s[i].st_lrm:Math.abs(e.internal_flags.nsPsy.attackthre- -1)>0||(e.internal_flags.nsPsy.attackthre=s[i].st_lrm),0!=n?e.internal_flags.nsPsy.attackthre_s=s[i].st_s:Math.abs(e.internal_flags.nsPsy.attackthre_s- -1)>0||(e.internal_flags.nsPsy.attackthre_s=s[i].st_s),0!=n?e.scale=s[i].scale:Math.abs(e.scale- -1)>0||(e.scale=s[i].scale),0!=n?e.maskingadjust=s[i].masking_adj:Math.abs(e.maskingadjust-0)>0||(e.maskingadjust=s[i].masking_adj),s[i].masking_adj>0?0!=n?e.maskingadjust_short=.9*s[i].masking_adj:Math.abs(e.maskingadjust_short-0)>0||(e.maskingadjust_short=.9*s[i].masking_adj):0!=n?e.maskingadjust_short=1.1*s[i].masking_adj:Math.abs(e.maskingadjust_short-0)>0||(e.maskingadjust_short=1.1*s[i].masking_adj),0!=n?e.ATHlower=-s[i].ath_lower/10:Math.abs(10*-e.ATHlower-0)>0||(e.ATHlower=-s[i].ath_lower/10),0!=n?e.ATHcurve=s[i].ath_curve:Math.abs(e.ATHcurve- -1)>0||(e.ATHcurve=s[i].ath_curve),0!=n?e.interChRatio=s[i].interch:Math.abs(e.interChRatio- -1)>0||(e.interChRatio=s[i].interch),a}this.apply_preset=function(e,t,a){switch(t){case Q.R3MIX:t=Q.V3,e.VBR=v.vbr_mtrh;break;case Q.MEDIUM:t=Q.V4,e.VBR=v.vbr_rh;break;case Q.MEDIUM_FAST:t=Q.V4,e.VBR=v.vbr_mtrh;break;case Q.STANDARD:t=Q.V2,e.VBR=v.vbr_rh;break;case Q.STANDARD_FAST:t=Q.V2,e.VBR=v.vbr_mtrh;break;case Q.EXTREME:t=Q.V0,e.VBR=v.vbr_rh;break;case Q.EXTREME_FAST:t=Q.V0,e.VBR=v.vbr_mtrh;break;case Q.INSANE:return t=320,e.preset=t,r(e,t,a),e.VBR=v.vbr_off,t}switch(e.preset=t,t){case Q.V9:case Q.V8:case Q.V7:case Q.V6:case Q.V5:case Q.V4:case Q.V3:case Q.V2:case Q.V1:case Q.V0:return n(),t}return 8<=t&&t<=320?r(e,t,a):(e.preset=0,t)}}function R(){var e;this.setModules=function(t){e=t},this.ResvFrameBegin=function(t,n){var s,r=t.internal_flags,i=r.l3_side,o=e.getframebits(t);n.bits=(o-8*r.sideinfo_len)/r.mode_gr;var l=2048*r.mode_gr-8;t.brate>320?a():(s=11520,t.strict_ISO&&a()),r.ResvMax=s-o,r.ResvMax>l&&(r.ResvMax=l),(r.ResvMax<0||t.disable_reservoir)&&(r.ResvMax=0);var _=n.bits*r.mode_gr+Math.min(r.ResvSize,r.ResvMax);return _>s&&(_=s),i.resvDrain_pre=0,null!=r.pinfo&&a(),_},this.ResvMaxBits=function(e,t,a,n){var s,r=e.internal_flags,i=r.ResvSize,o=r.ResvMax;0!=n&&(i+=t),0!=(1&r.substep_shaping)&&(o*=.9),a.bits=t,10*i>9*o?(s=i-9*o/10,a.bits+=s,r.substep_shaping|=128):(s=0,r.substep_shaping&=127,e.disable_reservoir||0!=(1&r.substep_shaping)||(a.bits-=.1*t));var l=i<6*r.ResvMax/10?i:6*r.ResvMax/10;return(l-=s)<0&&(l=0),l},this.ResvAdjust=function(e,t){e.ResvSize-=t.part2_3_length+t.part2_length},this.ResvFrameEnd=function(e,t){var a,n=e.l3_side;e.ResvSize+=t*e.mode_gr;var s=0;n.resvDrain_post=0,n.resvDrain_pre=0,0!=(a=e.ResvSize%8)&&(s+=a),(a=e.ResvSize-s-e.ResvMax)>0&&(s+=a);var r=Math.min(8*n.main_data_begin,s)/8;n.resvDrain_pre+=8*r,s-=8*r,e.ResvSize-=8*r,n.main_data_begin-=r,n.resvDrain_post+=s,e.ResvSize-=s}}function y(){this.setModules=function(e,t,a){};var e=[0,49345,49537,320,49921,960,640,49729,50689,1728,1920,51009,1280,50625,50305,1088,52225,3264,3456,52545,3840,53185,52865,3648,2560,51905,52097,2880,51457,2496,2176,51265,55297,6336,6528,55617,6912,56257,55937,6720,7680,57025,57217,8e3,56577,7616,7296,56385,5120,54465,54657,5440,55041,6080,5760,54849,53761,4800,4992,54081,4352,53697,53377,4160,61441,12480,12672,61761,13056,62401,62081,12864,13824,63169,63361,14144,62721,13760,13440,62529,15360,64705,64897,15680,65281,16320,16e3,65089,64001,15040,15232,64321,14592,63937,63617,14400,10240,59585,59777,10560,60161,11200,10880,59969,60929,11968,12160,61249,11520,60865,60545,11328,58369,9408,9600,58689,9984,59329,59009,9792,8704,58049,58241,9024,57601,8640,8320,57409,40961,24768,24960,41281,25344,41921,41601,25152,26112,42689,42881,26432,42241,26048,25728,42049,27648,44225,44417,27968,44801,28608,28288,44609,43521,27328,27520,43841,26880,43457,43137,26688,30720,47297,47489,31040,47873,31680,31360,47681,48641,32448,32640,48961,32e3,48577,48257,31808,46081,29888,30080,46401,30464,47041,46721,30272,29184,45761,45953,29504,45313,29120,28800,45121,20480,37057,37249,20800,37633,21440,21120,37441,38401,22208,22400,38721,21760,38337,38017,21568,39937,23744,23936,40257,24320,40897,40577,24128,23040,39617,39809,23360,39169,22976,22656,38977,34817,18624,18816,35137,19200,35777,35457,19008,19968,36545,36737,20288,36097,19904,19584,35905,17408,33985,34177,17728,34561,18368,18048,34369,33281,17088,17280,33601,16640,33217,32897,16448];function t(t,a){return a=a>>8^e[255&(a^t)]}this.updateMusicCRC=function(e,a,n,s){for(var r=0;r<s;++r)e[0]=t(a[n+r],e[0])}}function k(){var e=this,t=null,s=null;this.setModules=function(e,a,n,r){t=n,s=r};var i=null,o=0,l=0,_=0;function c(e){h.arraycopy(e.header[e.w_ptr].buf,0,i,l,e.sideinfo_len),l+=e.sideinfo_len,o+=8*e.sideinfo_len,e.w_ptr=e.w_ptr+1&W.MAX_HEADER_BUF-1}function f(e,t,a){for(;a>0;){var n;0==_&&(_=8,l++,e.header[e.w_ptr].write_timing==o&&c(e),i[l]=0),a-=n=Math.min(a,_),_-=n,i[l]|=t>>a<<_,o+=n}}function p(e,a){var n,s=e.internal_flags;if(a>=8&&(f(s,76,8),a-=8),a>=8&&(f(s,65,8),a-=8),a>=8&&(f(s,77,8),a-=8),a>=8&&(f(s,69,8),a-=8),a>=32){var r=t.getLameShortVersion();if(a>=32)for(n=0;n<r.length&&a>=8;++n)a-=8,f(s,r.charCodeAt(n),8)}for(;a>=1;a-=1)f(s,s.ancillary_flag,1),s.ancillary_flag^=e.disable_reservoir?0:1}function b(e,t,a){for(var n=e.header[e.h_ptr].ptr;a>0;){var s=Math.min(a,8-(7&n));a-=s,e.header[e.h_ptr].buf[n>>3]|=t>>a<<8-(7&n)-s,n+=s}e.header[e.h_ptr].ptr=n}function m(e,t){var a,n=T.ht[t.count1table_select+32],s=0,r=t.big_values,i=t.big_values;for(a=(t.count1-t.big_values)/4;a>0;--a){var o=0,l=0;0!=t.l3_enc[r+0]&&(l+=8,t.xr[i+0]<0&&o++),0!=t.l3_enc[r+1]&&(l+=4,o*=2,t.xr[i+1]<0&&o++),0!=t.l3_enc[r+2]&&(l+=2,o*=2,t.xr[i+2]<0&&o++),0!=t.l3_enc[r+3]&&(l++,o*=2,t.xr[i+3]<0&&o++),r+=4,i+=4,f(e,o+n.table[l],n.hlen[l]),s+=n.hlen[l]}return s}function v(e,t,a,n,s){var r=T.ht[t],i=0;if(0==t)return i;for(var o=a;o<n;o+=2){var l=0,_=0,c=r.xlen,u=r.xlen,h=0,p=s.l3_enc[o],b=s.l3_enc[o+1];0!=p&&(s.xr[o]<0&&h++,l--),t>15&&(p>14&&(h|=p-15<<1,_=c,p=15),b>14&&(h<<=c,h|=b-15,_+=c,b=15),u=16),0!=b&&(h<<=1,s.xr[o+1]<0&&h++,l--),p=p*u+b,_-=l,l+=r.hlen[p],f(e,r.table[p],l),f(e,h,_),i+=l+_}return i}function d(e,t){var a=3*e.scalefac_band.s[3];a>t.big_values&&(a=t.big_values);var n=v(e,t.table_select[0],0,a,t);return n+=v(e,t.table_select[1],a,t.big_values,t)}function g(e,t){var a,n,s,r;a=t.big_values;var i=t.region0_count+1;return s=e.scalefac_band.l[i],i+=t.region1_count+1,s>a&&(s=a),(r=e.scalefac_band.l[i])>a&&(r=a),n=v(e,t.table_select[0],0,s,t),n+=v(e,t.table_select[1],s,r,t),n+=v(e,t.table_select[2],r,a,t)}function S(){this.total=0}function M(t,n){var s,r,i,_=t.internal_flags;return _.w_ptr,-1==(i=_.h_ptr-1)&&(i=W.MAX_HEADER_BUF-1),s=_.header[i].write_timing-o,n.total=s,s>=0&&a(),s+=r=e.getframebits(t),n.total+=r,n.total%8!=0?n.total=1+n.total/8:n.total=n.total/8,n.total+=l+1,s}this.getframebits=function(e){var t,a=e.internal_flags;return t=0!=a.bitrate_index?T.bitrate_table[e.version][a.bitrate_index]:e.brate,8*(0|72e3*(e.version+1)*t/e.out_samplerate+a.padding)},this.flush_bitstream=function(e){var t,n,s=e.internal_flags,r=s.h_ptr-1;-1==r&&(r=W.MAX_HEADER_BUF-1),t=s.l3_side,(n=M(e,new S))<0||(p(e,n),s.ResvSize=0,t.main_data_begin=0,s.findReplayGain&&a(),s.findPeakSample&&a())},this.format_bitstream=function(e){var t,n=e.internal_flags;t=n.l3_side;var s=this.getframebits(e);p(e,t.resvDrain_pre),function(e,t){var n,s,r,i=e.internal_flags;if(n=i.l3_side,i.header[i.h_ptr].ptr=0,u.fill(i.header[i.h_ptr].buf,0,i.sideinfo_len,0),e.out_samplerate<16e3?b(i,4094,12):b(i,4095,12),b(i,e.version,1),b(i,1,2),b(i,e.error_protection?0:1,1),b(i,i.bitrate_index,4),b(i,i.samplerate_index,2),b(i,i.padding,1),b(i,e.extension,1),b(i,e.mode.ordinal(),2),b(i,i.mode_ext,2),b(i,e.copyright,1),b(i,e.original,1),b(i,e.emphasis,2),e.error_protection&&b(i,0,16),1==e.version){for(b(i,n.main_data_begin,9),2==i.channels_out?b(i,n.private_bits,3):b(i,n.private_bits,5),r=0;r<i.channels_out;r++){var o;for(o=0;o<4;o++)b(i,n.scfsi[r][o],1)}for(s=0;s<2;s++)for(r=0;r<i.channels_out;r++)b(i,(l=n.tt[s][r]).part2_3_length+l.part2_length,12),b(i,l.big_values/2,9),b(i,l.global_gain,8),b(i,l.scalefac_compress,4),l.block_type!=Y.NORM_TYPE?(b(i,1,1),b(i,l.block_type,2),b(i,l.mixed_block_flag,1),14==l.table_select[0]&&(l.table_select[0]=16),b(i,l.table_select[0],5),14==l.table_select[1]&&(l.table_select[1]=16),b(i,l.table_select[1],5),b(i,l.subblock_gain[0],3),b(i,l.subblock_gain[1],3),b(i,l.subblock_gain[2],3)):(b(i,0,1),14==l.table_select[0]&&(l.table_select[0]=16),b(i,l.table_select[0],5),14==l.table_select[1]&&(l.table_select[1]=16),b(i,l.table_select[1],5),14==l.table_select[2]&&(l.table_select[2]=16),b(i,l.table_select[2],5),b(i,l.region0_count,4),b(i,l.region1_count,3)),b(i,l.preflag,1),b(i,l.scalefac_scale,1),b(i,l.count1table_select,1)}else for(b(i,n.main_data_begin,8),b(i,n.private_bits,i.channels_out),s=0,r=0;r<i.channels_out;r++){var l;b(i,(l=n.tt[s][r]).part2_3_length+l.part2_length,12),b(i,l.big_values/2,9),b(i,l.global_gain,8),b(i,l.scalefac_compress,9),l.block_type!=Y.NORM_TYPE?(b(i,1,1),b(i,l.block_type,2),b(i,l.mixed_block_flag,1),14==l.table_select[0]&&(l.table_select[0]=16),b(i,l.table_select[0],5),14==l.table_select[1]&&(l.table_select[1]=16),b(i,l.table_select[1],5),b(i,l.subblock_gain[0],3),b(i,l.subblock_gain[1],3),b(i,l.subblock_gain[2],3)):(b(i,0,1),14==l.table_select[0]&&(l.table_select[0]=16),b(i,l.table_select[0],5),14==l.table_select[1]&&(l.table_select[1]=16),b(i,l.table_select[1],5),14==l.table_select[2]&&(l.table_select[2]=16),b(i,l.table_select[2],5),b(i,l.region0_count,4),b(i,l.region1_count,3)),b(i,l.scalefac_scale,1),b(i,l.count1table_select,1)}e.error_protection&&a();var _=i.h_ptr;i.h_ptr=_+1&W.MAX_HEADER_BUF-1,i.header[i.h_ptr].write_timing=i.header[_].write_timing+t,i.h_ptr,i.w_ptr}(e,s);var r=8*n.sideinfo_len;if(r+=function(e){var t,a,n,s,r=0,i=e.internal_flags,o=i.l3_side;if(1==e.version)for(t=0;t<2;t++)for(a=0;a<i.channels_out;a++){var l=o.tt[t][a],_=w.slen1_tab[l.scalefac_compress],c=w.slen2_tab[l.scalefac_compress];for(s=0,n=0;n<l.sfbdivide;n++)-1!=l.scalefac[n]&&(f(i,l.scalefac[n],_),s+=_);for(;n<l.sfbmax;n++)-1!=l.scalefac[n]&&(f(i,l.scalefac[n],c),s+=c);l.block_type==Y.SHORT_TYPE?s+=d(i,l):s+=g(i,l),r+=s+=m(i,l)}else for(t=0,a=0;a<i.channels_out;a++){var u,h,p=0;if(s=0,n=0,h=0,(l=o.tt[t][a]).block_type==Y.SHORT_TYPE){for(;h<4;h++){var b=l.sfb_partition_table[h]/3,v=l.slen[h];for(u=0;u<b;u++,n++)f(i,Math.max(l.scalefac[3*n+0],0),v),f(i,Math.max(l.scalefac[3*n+1],0),v),f(i,Math.max(l.scalefac[3*n+2],0),v),p+=3*v}s+=d(i,l)}else{for(;h<4;h++)for(b=l.sfb_partition_table[h],v=l.slen[h],u=0;u<b;u++,n++)f(i,Math.max(l.scalefac[n],0),v),p+=v;s+=g(i,l)}r+=p+(s+=m(i,l))}return r}(e),p(e,t.resvDrain_post),r+=t.resvDrain_post,t.main_data_begin+=(s-r)/8,M(e,new S),n.ResvSize,8*t.main_data_begin!=n.ResvSize&&(n.ResvSize=8*t.main_data_begin),o>1e9){var i;for(i=0;i<W.MAX_HEADER_BUF;++i)n.header[i].write_timing-=o;o=0}return 0},this.copy_buffer=function(e,t,n,o,c){var f=l+1;if(f<=0)return 0;if(0!=o&&f>o)return-1;if(h.arraycopy(i,0,t,n,f),l=-1,_=0,0!=c){var u=r(1);u[0]=e.nMusicCRC,s.updateMusicCRC(u,t,n,f),e.nMusicCRC=u[0],f>0&&(e.VBR_seek_table.nBytesWritten+=f),e.decode_on_the_fly&&a()}return f},this.init_bit_stream_w=function(e){i=n(Q.LAME_MAXMP3BUFFER),e.h_ptr=e.w_ptr=0,e.header[e.h_ptr].write_timing=0,l=-1,_=0,o=0}}function A(e,t,a,n){this.xlen=e,this.linmax=t,this.table=a,this.hlen=n}m.MAX_VALUE=34028235e31,v.vbr_off=new v(0),v.vbr_mt=new v(1),v.vbr_rh=new v(2),v.vbr_abr=new v(3),v.vbr_mtrh=new v(4),v.vbr_default=v.vbr_mtrh,d.STEREO=new d(0),d.JOINT_STEREO=new d(1),d.DUAL_CHANNEL=new d(2),d.MONO=new d(3),d.NOT_SET=new d(4),S.STEPS_per_dB=100,S.MAX_dB=120,S.GAIN_NOT_ENOUGH_SAMPLES=-24601,S.GAIN_ANALYSIS_ERROR=0,S.GAIN_ANALYSIS_OK=1,S.INIT_GAIN_ANALYSIS_ERROR=0,S.INIT_GAIN_ANALYSIS_OK=1,S.YULE_ORDER=10,S.MAX_ORDER=S.YULE_ORDER,S.MAX_SAMP_FREQ=48e3,S.RMS_WINDOW_TIME_NUMERATOR=1,S.RMS_WINDOW_TIME_DENOMINATOR=20,S.MAX_SAMPLES_PER_WINDOW=S.MAX_SAMP_FREQ*S.RMS_WINDOW_TIME_NUMERATOR/S.RMS_WINDOW_TIME_DENOMINATOR+1,y.NUMTOCENTRIES=100,y.MAXFRAMESIZE=2880,k.EQ=function(e,t){return Math.abs(e)>Math.abs(t)?Math.abs(e-t)<=1e-6*Math.abs(e):Math.abs(e-t)<=1e-6*Math.abs(t)},k.NEQ=function(e,t){return!k.EQ(e,t)};var T={};function x(e){this.bits=e}function B(){this.over_noise=0,this.tot_noise=0,this.max_noise=0,this.over_count=0,this.over_SSD=0,this.bits=0}function E(){this.setModules=function(e,t){}}function C(){this.useAdjust=0,this.aaSensitivityP=0,this.adjust=0,this.adjustLimit=0,this.decay=0,this.floor=0,this.l=i(Y.SBMAX_l),this.s=i(Y.SBMAX_s),this.psfb21=i(Y.PSFB21),this.psfb12=i(Y.PSFB12),this.cb_l=i(Y.CBANDS),this.cb_s=i(Y.CBANDS),this.eql_w=i(Y.BLKSIZE/2)}function L(){this.class_id=0,this.num_samples=0,this.num_channels=0,this.in_samplerate=0,this.out_samplerate=0,this.scale=0,this.scale_left=0,this.scale_right=0,this.analysis=!1,this.bWriteVbrTag=!1,this.decode_only=!1,this.quality=0,this.mode=d.STEREO,this.force_ms=!1,this.free_format=!1,this.findReplayGain=!1,this.decode_on_the_fly=!1,this.write_id3tag_automatic=!1,this.brate=0,this.compression_ratio=0,this.copyright=0,this.original=0,this.extension=0,this.emphasis=0,this.error_protection=0,this.strict_ISO=!1,this.disable_reservoir=!1,this.quant_comp=0,this.quant_comp_short=0,this.experimentalY=!1,this.experimentalZ=0,this.exp_nspsytune=0,this.preset=0,this.VBR=null,this.VBR_q_frac=0,this.VBR_q=0,this.VBR_mean_bitrate_kbps=0,this.VBR_min_bitrate_kbps=0,this.VBR_max_bitrate_kbps=0,this.VBR_hard_min=0,this.lowpassfreq=0,this.highpassfreq=0,this.lowpasswidth=0,this.highpasswidth=0,this.maskingadjust=0,this.maskingadjust_short=0,this.ATHonly=!1,this.ATHshort=!1,this.noATH=!1,this.ATHtype=0,this.ATHcurve=0,this.ATHlower=0,this.athaa_type=0,this.athaa_loudapprox=0,this.athaa_sensitivity=0,this.short_blocks=null,this.useTemporal=!1,this.interChRatio=0,this.msfix=0,this.tune=!1,this.tune_value_a=0,this.version=0,this.encoder_delay=0,this.encoder_padding=0,this.framesize=0,this.frameNum=0,this.lame_allocated_gfp=0,this.internal_flags=null}function P(e){var t=e;this.quantize=t,this.iteration_loop=function(e,t,n,s){var o=e.internal_flags,l=i(V.SFBMAX),_=i(576),c=r(2),f=0,u=o.l3_side,h=new x(f);this.quantize.rv.ResvFrameBegin(e,h),f=h.bits;for(var p=0;p<o.mode_gr;p++){this.quantize.qupvt.on_pe(e,t,c,f,p,p),o.mode_ext==Y.MPG_MD_MS_LR&&a();for(var b=0;b<o.channels_out;b++){var m,v,d=u.tt[p][b];d.block_type!=Y.SHORT_TYPE?(m=0,v=o.PSY.mask_adjust-m):(m=0,v=o.PSY.mask_adjust_short-m),o.masking_lower=Math.pow(10,.1*v),this.quantize.init_outer_loop(o,d),this.quantize.init_xrpow(o,d,_)&&(this.quantize.qupvt.calc_xmin(e,s[p][b],d,l),this.quantize.outer_loop(e,d,l,_,b,c[b])),this.quantize.iteration_finish_one(o,p,b)}}this.quantize.rv.ResvFrameEnd(o,f)}}function I(){}function H(e,t,a,n){this.l=r(1+Y.SBMAX_l),this.s=r(1+Y.SBMAX_s),this.psfb21=r(1+Y.PSFB21),this.psfb12=r(1+Y.PSFB12);var s=this.l,i=this.s;4==arguments.length&&(this.arrL=arguments[0],this.arrS=arguments[1],this.arr21=arguments[2],this.arr12=arguments[3],h.arraycopy(this.arrL,0,s,0,Math.min(this.arrL.length,this.l.length)),h.arraycopy(this.arrS,0,i,0,Math.min(this.arrS.length,this.s.length)),h.arraycopy(this.arr21,0,this.psfb21,0,Math.min(this.arr21.length,this.psfb21.length)),h.arraycopy(this.arr12,0,this.psfb12,0,Math.min(this.arr12.length,this.psfb12.length)))}function O(){var t=null,n=null,s=null;this.setModules=function(e,a,r){t=e,n=a,s=r},this.IPOW20=function(e){return u[e]};var o=O.IXMAX_VAL+2,l=O.Q_MAX,_=O.Q_MAX2;O.LARGE_BITS,this.nr_of_sfb_block=[[[6,5,5,5],[9,9,9,9],[6,9,9,9]],[[6,5,7,3],[9,9,12,6],[6,9,12,6]],[[11,10,0,0],[18,18,0,0],[15,18,0,0]],[[7,7,7,0],[12,12,12,0],[6,15,12,0]],[[6,6,6,3],[12,9,9,6],[6,12,9,6]],[[8,8,5,0],[15,12,9,0],[6,18,9,0]]];var c=[0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,2,2,3,3,3,2,0];this.pretab=c,this.sfBandIndex=[new H([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,24,32,42,56,74,100,132,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new H([0,6,12,18,24,30,36,44,54,66,80,96,114,136,162,194,232,278,332,394,464,540,576],[0,4,8,12,18,26,36,48,62,80,104,136,180,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new H([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,26,36,48,62,80,104,134,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new H([0,4,8,12,16,20,24,30,36,44,52,62,74,90,110,134,162,196,238,288,342,418,576],[0,4,8,12,16,22,30,40,52,66,84,106,136,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new H([0,4,8,12,16,20,24,30,36,42,50,60,72,88,106,128,156,190,230,276,330,384,576],[0,4,8,12,16,22,28,38,50,64,80,100,126,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new H([0,4,8,12,16,20,24,30,36,44,54,66,82,102,126,156,194,240,296,364,448,550,576],[0,4,8,12,16,22,30,42,58,78,104,138,180,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new H([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,26,36,48,62,80,104,134,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new H([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,26,36,48,62,80,104,134,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new H([0,12,24,36,48,60,72,88,108,132,160,192,232,280,336,400,476,566,568,570,572,574,576],[0,8,16,24,36,52,72,96,124,160,162,164,166,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0])];var f=i(l+_+1),u=i(l),h=i(o),b=i(o);function d(e,t){var a=s.ATHformula(t,e);return a-=100,a=Math.pow(10,a/10+e.ATHlower)}function g(e){this.s=e}this.adj43=b,this.iteration_init=function(n){var s,r=n.internal_flags,i=r.l3_side;if(0==r.iteration_init_init){for(r.iteration_init_init=1,i.main_data_begin=0,function(t){for(var n=t.internal_flags.ATH.l,s=t.internal_flags.ATH.psfb21,r=t.internal_flags.ATH.s,i=t.internal_flags.ATH.psfb12,o=t.internal_flags,l=t.out_samplerate,_=0;_<Y.SBMAX_l;_++){var c=o.scalefac_band.l[_],f=o.scalefac_band.l[_+1];n[_]=m.MAX_VALUE;for(var u=c;u<f;u++){var h=d(t,u*l/1152);n[_]=Math.min(n[_],h)}}for(_=0;_<Y.PSFB21;_++)for(c=o.scalefac_band.psfb21[_],f=o.scalefac_band.psfb21[_+1],s[_]=m.MAX_VALUE,u=c;u<f;u++)h=d(t,u*l/1152),s[_]=Math.min(s[_],h);for(_=0;_<Y.SBMAX_s;_++){for(c=o.scalefac_band.s[_],f=o.scalefac_band.s[_+1],r[_]=m.MAX_VALUE,u=c;u<f;u++)h=d(t,u*l/384),r[_]=Math.min(r[_],h);r[_]*=o.scalefac_band.s[_+1]-o.scalefac_band.s[_]}for(_=0;_<Y.PSFB12;_++){for(c=o.scalefac_band.psfb12[_],f=o.scalefac_band.psfb12[_+1],i[_]=m.MAX_VALUE,u=c;u<f;u++)h=d(t,u*l/384),i[_]=Math.min(i[_],h);i[_]*=o.scalefac_band.s[13]-o.scalefac_band.s[12]}t.noATH&&a(),o.ATH.floor=10*e(d(t,-1))}(n),h[0]=0,s=1;s<o;s++)h[s]=Math.pow(s,4/3);for(s=0;s<o-1;s++)b[s]=s+1-Math.pow(.5*(h[s]+h[s+1]),.75);for(b[s]=.5,s=0;s<l;s++)u[s]=Math.pow(2,-.1875*(s-210));for(s=0;s<=l+_;s++)f[s]=Math.pow(2,.25*(s-210-_));var c,p,v,g;for(t.huffman_init(r),(s=n.exp_nspsytune>>2&63)>=32&&(s-=64),c=Math.pow(10,s/4/10),(s=n.exp_nspsytune>>8&63)>=32&&(s-=64),p=Math.pow(10,s/4/10),(s=n.exp_nspsytune>>14&63)>=32&&(s-=64),v=Math.pow(10,s/4/10),(s=n.exp_nspsytune>>20&63)>=32&&(s-=64),g=v*Math.pow(10,s/4/10),s=0;s<Y.SBMAX_l;s++)w=s<=6?c:s<=13?p:s<=20?v:g,r.nsPsy.longfact[s]=w;for(s=0;s<Y.SBMAX_s;s++){var w;w=s<=5?c:s<=10?p:s<=11?v:g,r.nsPsy.shortfact[s]=w}}},this.on_pe=function(e,t,s,i,o,l){var _,c,f=e.internal_flags,u=0,h=r(2),p=new x(u),b=n.ResvMaxBits(e,i,p,l),m=(u=p.bits)+b;for(m>W.MAX_BITS_PER_GRANULE&&(m=W.MAX_BITS_PER_GRANULE),_=0,c=0;c<f.channels_out;++c)s[c]=Math.min(W.MAX_BITS_PER_CHANNEL,u/f.channels_out),h[c]=0|s[c]*t[o][c]/700-s[c],h[c]>3*i/4&&(h[c]=3*i/4),h[c]<0&&(h[c]=0),h[c]+s[c]>W.MAX_BITS_PER_CHANNEL&&(h[c]=Math.max(0,W.MAX_BITS_PER_CHANNEL-s[c])),_+=h[c];if(_>b)for(c=0;c<f.channels_out;++c)h[c]=b*h[c]/_;for(c=0;c<f.channels_out;++c)s[c]+=h[c],b-=h[c];for(_=0,c=0;c<f.channels_out;++c)_+=s[c];return _>W.MAX_BITS_PER_GRANULE&&a(),m},this.athAdjust=function(e,t,a){var n=90.30873362,s=p.FAST_LOG10_X(t,10),r=e*e,i=0;return s-=a,r>1e-20&&(i=1+p.FAST_LOG10_X(r,10/n)),i<0&&(i=0),s*=i,s+=a+n-94.82444863,Math.pow(10,.1*s)},this.calc_xmin=function(e,t,n,s){var r,i=0,o=e.internal_flags,l=0,_=0,c=o.ATH,f=n.xr,u=e.VBR==v.vbr_mtrh?1:0,h=o.masking_lower;for(e.VBR!=v.vbr_mtrh&&e.VBR!=v.vbr_mt||(h=1),r=0;r<n.psy_lmax;r++){M=(S=e.VBR==v.vbr_rh||e.VBR==v.vbr_mtrh?athAdjust(c.adjust,c.l[r],c.floor):c.adjust*c.l[r])/(d=n.width[r]),R=2220446049250313e-31,x=d>>1,T=0;do{T+=B=f[l]*f[l],R+=B<M?B:M,T+=E=f[++l]*f[l],R+=E<M?E:M,l++}while(--x>0);T>S&&_++,r==Y.SBPSY_l&&a(),0!=u&&(S=R),e.ATHonly||(y=t.en.l[r])>0&&(A=T*t.thm.l[r]*h/y,0!=u&&(A*=o.nsPsy.longfact[r]),S<A&&(S=A)),s[i++]=0!=u?S:S*o.nsPsy.longfact[r]}var p=575;if(n.block_type!=Y.SHORT_TYPE)for(var b=576;0!=b--&&k.EQ(f[b],0);)p=b;n.max_nonzero_coeff=p;for(var m=n.sfb_smin;r<n.psymax;m++,r+=3){var d,g,w;for(w=e.VBR==v.vbr_rh||e.VBR==v.vbr_mtrh?athAdjust(c.adjust,c.s[m],c.floor):c.adjust*c.s[m],d=n.width[r],g=0;g<3;g++){var S,M,R,y,A,T=0,x=d>>1;M=w/d,R=2220446049250313e-31;do{var B,E;T+=B=f[l]*f[l],R+=B<M?B:M,T+=E=f[++l]*f[l],R+=E<M?E:M,l++}while(--x>0);T>w&&_++,m==Y.SBPSY_s&&a(),S=0!=u?R:w,e.ATHonly||e.ATHshort||(y=t.en.s[m][g])>0&&(A=T*t.thm.s[m][g]*h/y,0!=u&&(A*=o.nsPsy.shortfact[m]),S<A&&(S=A)),s[i++]=0!=u?S:S*o.nsPsy.shortfact[m]}e.useTemporal&&(s[i-3]>s[i-3+1]&&(s[i-3+1]+=(s[i-3]-s[i-3+1])*o.decay),s[i-3+1]>s[i-3+2]&&(s[i-3+2]+=(s[i-3+1]-s[i-3+2])*o.decay))}return _},this.calc_noise_core=function(e,t,a,n){var s=0,r=t.s,o=e.l3_enc;if(r>e.count1)for(;0!=a--;)_=e.xr[r],r++,s+=_*_,_=e.xr[r],r++,s+=_*_;else if(r>e.big_values){var l=i(2);for(l[0]=0,l[1]=n;0!=a--;)_=Math.abs(e.xr[r])-l[o[r]],r++,s+=_*_,_=Math.abs(e.xr[r])-l[o[r]],r++,s+=_*_}else for(;0!=a--;){var _;_=Math.abs(e.xr[r])-h[o[r]]*n,r++,s+=_*_,_=Math.abs(e.xr[r])-h[o[r]]*n,r++,s+=_*_}return t.s=r,s},this.calc_noise=function(e,t,a,n,s){var r,i,o=0,l=0,_=0,u=0,h=0,b=-20,m=0,v=e.scalefac,d=0;for(n.over_SSD=0,r=0;r<e.psymax;r++){var w,S=e.global_gain-(v[d++]+(0!=e.preflag?c[r]:0)<<e.scalefac_scale+1)-8*e.subblock_gain[e.window[r]],M=0;if(null!=s&&s.step[r]==S)M=s.noise[r],m+=e.width[r],a[o++]=M/t[l++],M=s.noise_log[r];else{var R,y=f[S+O.Q_MAX2];i=e.width[r]>>1,m+e.width[r]>e.max_nonzero_coeff&&(i=(R=e.max_nonzero_coeff-m+1)>0?R>>1:0);var k=new g(m);M=this.calc_noise_core(e,k,i,y),m=k.s,null!=s&&(s.step[r]=S,s.noise[r]=M),M=a[o++]=M/t[l++],M=p.FAST_LOG10(Math.max(M,1e-20)),null!=s&&(s.noise_log[r]=M)}null!=s&&(s.global_gain=e.global_gain),h+=M,M>0&&(w=Math.max(0|10*M+.5,1),n.over_SSD+=w*w,_++,u+=M),b=Math.max(b,M)}return n.over_count=_,n.tot_noise=h,n.over_noise=u,n.max_noise=b,_}}function N(){this.global_gain=0,this.sfb_count1=0,this.step=r(39),this.noise=i(39),this.noise_log=i(39)}function D(){this.xr=i(576),this.l3_enc=r(576),this.scalefac=r(V.SFBMAX),this.xrpow_max=0,this.part2_3_length=0,this.big_values=0,this.count1=0,this.global_gain=0,this.scalefac_compress=0,this.block_type=0,this.mixed_block_flag=0,this.table_select=r(3),this.subblock_gain=r(4),this.region0_count=0,this.region1_count=0,this.preflag=0,this.scalefac_scale=0,this.count1table_select=0,this.part2_length=0,this.sfb_lmax=0,this.sfb_smin=0,this.psy_lmax=0,this.sfbmax=0,this.psymax=0,this.sfbdivide=0,this.width=r(V.SFBMAX),this.window=r(V.SFBMAX),this.count1bits=0,this.sfb_partition_table=null,this.slen=r(4),this.max_nonzero_coeff=0;var e=this;function t(e){return new Int32Array(e)}this.assign=function(a){var n;e.xr=(n=a.xr,new Float32Array(n)),e.l3_enc=t(a.l3_enc),e.scalefac=t(a.scalefac),e.xrpow_max=a.xrpow_max,e.part2_3_length=a.part2_3_length,e.big_values=a.big_values,e.count1=a.count1,e.global_gain=a.global_gain,e.scalefac_compress=a.scalefac_compress,e.block_type=a.block_type,e.mixed_block_flag=a.mixed_block_flag,e.table_select=t(a.table_select),e.subblock_gain=t(a.subblock_gain),e.region0_count=a.region0_count,e.region1_count=a.region1_count,e.preflag=a.preflag,e.scalefac_scale=a.scalefac_scale,e.count1table_select=a.count1table_select,e.part2_length=a.part2_length,e.sfb_lmax=a.sfb_lmax,e.sfb_smin=a.sfb_smin,e.psy_lmax=a.psy_lmax,e.sfbmax=a.sfbmax,e.psymax=a.psymax,e.sfbdivide=a.sfbdivide,e.width=t(a.width),e.window=t(a.window),e.count1bits=a.count1bits,e.sfb_partition_table=a.sfb_partition_table.slice(0),e.slen=t(a.slen),e.max_nonzero_coeff=a.max_nonzero_coeff}}T.t1HB=[1,1,1,0],T.t2HB=[1,2,1,3,1,1,3,2,0],T.t3HB=[3,2,1,1,1,1,3,2,0],T.t5HB=[1,2,6,5,3,1,4,4,7,5,7,1,6,1,1,0],T.t6HB=[7,3,5,1,6,2,3,2,5,4,4,1,3,3,2,0],T.t7HB=[1,2,10,19,16,10,3,3,7,10,5,3,11,4,13,17,8,4,12,11,18,15,11,2,7,6,9,14,3,1,6,4,5,3,2,0],T.t8HB=[3,4,6,18,12,5,5,1,2,16,9,3,7,3,5,14,7,3,19,17,15,13,10,4,13,5,8,11,5,1,12,4,4,1,1,0],T.t9HB=[7,5,9,14,15,7,6,4,5,5,6,7,7,6,8,8,8,5,15,6,9,10,5,1,11,7,9,6,4,1,14,4,6,2,6,0],T.t10HB=[1,2,10,23,35,30,12,17,3,3,8,12,18,21,12,7,11,9,15,21,32,40,19,6,14,13,22,34,46,23,18,7,20,19,33,47,27,22,9,3,31,22,41,26,21,20,5,3,14,13,10,11,16,6,5,1,9,8,7,8,4,4,2,0],T.t11HB=[3,4,10,24,34,33,21,15,5,3,4,10,32,17,11,10,11,7,13,18,30,31,20,5,25,11,19,59,27,18,12,5,35,33,31,58,30,16,7,5,28,26,32,19,17,15,8,14,14,12,9,13,14,9,4,1,11,4,6,6,6,3,2,0],T.t12HB=[9,6,16,33,41,39,38,26,7,5,6,9,23,16,26,11,17,7,11,14,21,30,10,7,17,10,15,12,18,28,14,5,32,13,22,19,18,16,9,5,40,17,31,29,17,13,4,2,27,12,11,15,10,7,4,1,27,12,8,12,6,3,1,0],T.t13HB=[1,5,14,21,34,51,46,71,42,52,68,52,67,44,43,19,3,4,12,19,31,26,44,33,31,24,32,24,31,35,22,14,15,13,23,36,59,49,77,65,29,40,30,40,27,33,42,16,22,20,37,61,56,79,73,64,43,76,56,37,26,31,25,14,35,16,60,57,97,75,114,91,54,73,55,41,48,53,23,24,58,27,50,96,76,70,93,84,77,58,79,29,74,49,41,17,47,45,78,74,115,94,90,79,69,83,71,50,59,38,36,15,72,34,56,95,92,85,91,90,86,73,77,65,51,44,43,42,43,20,30,44,55,78,72,87,78,61,46,54,37,30,20,16,53,25,41,37,44,59,54,81,66,76,57,54,37,18,39,11,35,33,31,57,42,82,72,80,47,58,55,21,22,26,38,22,53,25,23,38,70,60,51,36,55,26,34,23,27,14,9,7,34,32,28,39,49,75,30,52,48,40,52,28,18,17,9,5,45,21,34,64,56,50,49,45,31,19,12,15,10,7,6,3,48,23,20,39,36,35,53,21,16,23,13,10,6,1,4,2,16,15,17,27,25,20,29,11,17,12,16,8,1,1,0,1],T.t15HB=[7,12,18,53,47,76,124,108,89,123,108,119,107,81,122,63,13,5,16,27,46,36,61,51,42,70,52,83,65,41,59,36,19,17,15,24,41,34,59,48,40,64,50,78,62,80,56,33,29,28,25,43,39,63,55,93,76,59,93,72,54,75,50,29,52,22,42,40,67,57,95,79,72,57,89,69,49,66,46,27,77,37,35,66,58,52,91,74,62,48,79,63,90,62,40,38,125,32,60,56,50,92,78,65,55,87,71,51,73,51,70,30,109,53,49,94,88,75,66,122,91,73,56,42,64,44,21,25,90,43,41,77,73,63,56,92,77,66,47,67,48,53,36,20,71,34,67,60,58,49,88,76,67,106,71,54,38,39,23,15,109,53,51,47,90,82,58,57,48,72,57,41,23,27,62,9,86,42,40,37,70,64,52,43,70,55,42,25,29,18,11,11,118,68,30,55,50,46,74,65,49,39,24,16,22,13,14,7,91,44,39,38,34,63,52,45,31,52,28,19,14,8,9,3,123,60,58,53,47,43,32,22,37,24,17,12,15,10,2,1,71,37,34,30,28,20,17,26,21,16,10,6,8,6,2,0],T.t16HB=[1,5,14,44,74,63,110,93,172,149,138,242,225,195,376,17,3,4,12,20,35,62,53,47,83,75,68,119,201,107,207,9,15,13,23,38,67,58,103,90,161,72,127,117,110,209,206,16,45,21,39,69,64,114,99,87,158,140,252,212,199,387,365,26,75,36,68,65,115,101,179,164,155,264,246,226,395,382,362,9,66,30,59,56,102,185,173,265,142,253,232,400,388,378,445,16,111,54,52,100,184,178,160,133,257,244,228,217,385,366,715,10,98,48,91,88,165,157,148,261,248,407,397,372,380,889,884,8,85,84,81,159,156,143,260,249,427,401,392,383,727,713,708,7,154,76,73,141,131,256,245,426,406,394,384,735,359,710,352,11,139,129,67,125,247,233,229,219,393,743,737,720,885,882,439,4,243,120,118,115,227,223,396,746,742,736,721,712,706,223,436,6,202,224,222,218,216,389,386,381,364,888,443,707,440,437,1728,4,747,211,210,208,370,379,734,723,714,1735,883,877,876,3459,865,2,377,369,102,187,726,722,358,711,709,866,1734,871,3458,870,434,0,12,10,7,11,10,17,11,9,13,12,10,7,5,3,1,3],T.t24HB=[15,13,46,80,146,262,248,434,426,669,653,649,621,517,1032,88,14,12,21,38,71,130,122,216,209,198,327,345,319,297,279,42,47,22,41,74,68,128,120,221,207,194,182,340,315,295,541,18,81,39,75,70,134,125,116,220,204,190,178,325,311,293,271,16,147,72,69,135,127,118,112,210,200,188,352,323,306,285,540,14,263,66,129,126,119,114,214,202,192,180,341,317,301,281,262,12,249,123,121,117,113,215,206,195,185,347,330,308,291,272,520,10,435,115,111,109,211,203,196,187,353,332,313,298,283,531,381,17,427,212,208,205,201,193,186,177,169,320,303,286,268,514,377,16,335,199,197,191,189,181,174,333,321,305,289,275,521,379,371,11,668,184,183,179,175,344,331,314,304,290,277,530,383,373,366,10,652,346,171,168,164,318,309,299,287,276,263,513,375,368,362,6,648,322,316,312,307,302,292,284,269,261,512,376,370,364,359,4,620,300,296,294,288,282,273,266,515,380,374,369,365,361,357,2,1033,280,278,274,267,264,259,382,378,372,367,363,360,358,356,0,43,20,19,17,15,13,11,9,7,6,4,7,5,3,1,3],T.t32HB=[1,10,8,20,12,20,16,32,14,12,24,0,28,16,24,16],T.t33HB=[15,28,26,48,22,40,36,64,14,24,20,32,12,16,8,0],T.t1l=[1,4,3,5],T.t2l=[1,4,7,4,5,7,6,7,8],T.t3l=[2,3,7,4,4,7,6,7,8],T.t5l=[1,4,7,8,4,5,8,9,7,8,9,10,8,8,9,10],T.t6l=[3,4,6,8,4,4,6,7,5,6,7,8,7,7,8,9],T.t7l=[1,4,7,9,9,10,4,6,8,9,9,10,7,7,9,10,10,11,8,9,10,11,11,11,8,9,10,11,11,12,9,10,11,12,12,12],T.t8l=[2,4,7,9,9,10,4,4,6,10,10,10,7,6,8,10,10,11,9,10,10,11,11,12,9,9,10,11,12,12,10,10,11,11,13,13],T.t9l=[3,4,6,7,9,10,4,5,6,7,8,10,5,6,7,8,9,10,7,7,8,9,9,10,8,8,9,9,10,11,9,9,10,10,11,11],T.t10l=[1,4,7,9,10,10,10,11,4,6,8,9,10,11,10,10,7,8,9,10,11,12,11,11,8,9,10,11,12,12,11,12,9,10,11,12,12,12,12,12,10,11,12,12,13,13,12,13,9,10,11,12,12,12,13,13,10,10,11,12,12,13,13,13],T.t11l=[2,4,6,8,9,10,9,10,4,5,6,8,10,10,9,10,6,7,8,9,10,11,10,10,8,8,9,11,10,12,10,11,9,10,10,11,11,12,11,12,9,10,11,12,12,13,12,13,9,9,9,10,11,12,12,12,9,9,10,11,12,12,12,12],T.t12l=[4,4,6,8,9,10,10,10,4,5,6,7,9,9,10,10,6,6,7,8,9,10,9,10,7,7,8,8,9,10,10,10,8,8,9,9,10,10,10,11,9,9,10,10,10,11,10,11,9,9,9,10,10,11,11,12,10,10,10,11,11,11,11,12],T.t13l=[1,5,7,8,9,10,10,11,10,11,12,12,13,13,14,14,4,6,8,9,10,10,11,11,11,11,12,12,13,14,14,14,7,8,9,10,11,11,12,12,11,12,12,13,13,14,15,15,8,9,10,11,11,12,12,12,12,13,13,13,13,14,15,15,9,9,11,11,12,12,13,13,12,13,13,14,14,15,15,16,10,10,11,12,12,12,13,13,13,13,14,13,15,15,16,16,10,11,12,12,13,13,13,13,13,14,14,14,15,15,16,16,11,11,12,13,13,13,14,14,14,14,15,15,15,16,18,18,10,10,11,12,12,13,13,14,14,14,14,15,15,16,17,17,11,11,12,12,13,13,13,15,14,15,15,16,16,16,18,17,11,12,12,13,13,14,14,15,14,15,16,15,16,17,18,19,12,12,12,13,14,14,14,14,15,15,15,16,17,17,17,18,12,13,13,14,14,15,14,15,16,16,17,17,17,18,18,18,13,13,14,15,15,15,16,16,16,16,16,17,18,17,18,18,14,14,14,15,15,15,17,16,16,19,17,17,17,19,18,18,13,14,15,16,16,16,17,16,17,17,18,18,21,20,21,18],T.t15l=[3,5,6,8,8,9,10,10,10,11,11,12,12,12,13,14,5,5,7,8,9,9,10,10,10,11,11,12,12,12,13,13,6,7,7,8,9,9,10,10,10,11,11,12,12,13,13,13,7,8,8,9,9,10,10,11,11,11,12,12,12,13,13,13,8,8,9,9,10,10,11,11,11,11,12,12,12,13,13,13,9,9,9,10,10,10,11,11,11,11,12,12,13,13,13,14,10,9,10,10,10,11,11,11,11,12,12,12,13,13,14,14,10,10,10,11,11,11,11,12,12,12,12,12,13,13,13,14,10,10,10,11,11,11,11,12,12,12,12,13,13,14,14,14,10,10,11,11,11,11,12,12,12,13,13,13,13,14,14,14,11,11,11,11,12,12,12,12,12,13,13,13,13,14,15,14,11,11,11,11,12,12,12,12,13,13,13,13,14,14,14,15,12,12,11,12,12,12,13,13,13,13,13,13,14,14,15,15,12,12,12,12,12,13,13,13,13,14,14,14,14,14,15,15,13,13,13,13,13,13,13,13,14,14,14,14,15,15,14,15,13,13,13,13,13,13,13,14,14,14,14,14,15,15,15,15],T.t16_5l=[1,5,7,9,10,10,11,11,12,12,12,13,13,13,14,11,4,6,8,9,10,11,11,11,12,12,12,13,14,13,14,11,7,8,9,10,11,11,12,12,13,12,13,13,13,14,14,12,9,9,10,11,11,12,12,12,13,13,14,14,14,15,15,13,10,10,11,11,12,12,13,13,13,14,14,14,15,15,15,12,10,10,11,11,12,13,13,14,13,14,14,15,15,15,16,13,11,11,11,12,13,13,13,13,14,14,14,14,15,15,16,13,11,11,12,12,13,13,13,14,14,15,15,15,15,17,17,13,11,12,12,13,13,13,14,14,15,15,15,15,16,16,16,13,12,12,12,13,13,14,14,15,15,15,15,16,15,16,15,14,12,13,12,13,14,14,14,14,15,16,16,16,17,17,16,13,13,13,13,13,14,14,15,16,16,16,16,16,16,15,16,14,13,14,14,14,14,15,15,15,15,17,16,16,16,16,18,14,15,14,14,14,15,15,16,16,16,18,17,17,17,19,17,14,14,15,13,14,16,16,15,16,16,17,18,17,19,17,16,14,11,11,11,12,12,13,13,13,14,14,14,14,14,14,14,12],T.t16l=[1,5,7,9,10,10,11,11,12,12,12,13,13,13,14,10,4,6,8,9,10,11,11,11,12,12,12,13,14,13,14,10,7,8,9,10,11,11,12,12,13,12,13,13,13,14,14,11,9,9,10,11,11,12,12,12,13,13,14,14,14,15,15,12,10,10,11,11,12,12,13,13,13,14,14,14,15,15,15,11,10,10,11,11,12,13,13,14,13,14,14,15,15,15,16,12,11,11,11,12,13,13,13,13,14,14,14,14,15,15,16,12,11,11,12,12,13,13,13,14,14,15,15,15,15,17,17,12,11,12,12,13,13,13,14,14,15,15,15,15,16,16,16,12,12,12,12,13,13,14,14,15,15,15,15,16,15,16,15,13,12,13,12,13,14,14,14,14,15,16,16,16,17,17,16,12,13,13,13,13,14,14,15,16,16,16,16,16,16,15,16,13,13,14,14,14,14,15,15,15,15,17,16,16,16,16,18,13,15,14,14,14,15,15,16,16,16,18,17,17,17,19,17,13,14,15,13,14,16,16,15,16,16,17,18,17,19,17,16,13,10,10,10,11,11,12,12,12,13,13,13,13,13,13,13,10],T.t24l=[4,5,7,8,9,10,10,11,11,12,12,12,12,12,13,10,5,6,7,8,9,10,10,11,11,11,12,12,12,12,12,10,7,7,8,9,9,10,10,11,11,11,11,12,12,12,13,9,8,8,9,9,10,10,10,11,11,11,11,12,12,12,12,9,9,9,9,10,10,10,10,11,11,11,12,12,12,12,13,9,10,9,10,10,10,10,11,11,11,11,12,12,12,12,12,9,10,10,10,10,10,11,11,11,11,12,12,12,12,12,13,9,11,10,10,10,11,11,11,11,12,12,12,12,12,13,13,10,11,11,11,11,11,11,11,11,11,12,12,12,12,13,13,10,11,11,11,11,11,11,11,12,12,12,12,12,13,13,13,10,12,11,11,11,11,12,12,12,12,12,12,13,13,13,13,10,12,12,11,11,11,12,12,12,12,12,12,13,13,13,13,10,12,12,12,12,12,12,12,12,12,12,13,13,13,13,13,10,12,12,12,12,12,12,12,12,13,13,13,13,13,13,13,10,13,12,12,12,12,12,12,13,13,13,13,13,13,13,13,10,9,9,9,9,9,9,9,9,9,9,9,10,10,10,10,6],T.t32l=[1,5,5,7,5,8,7,9,5,7,7,9,7,9,9,10],T.t33l=[4,5,5,6,5,6,6,7,5,6,6,7,6,7,7,8],T.ht=[new A(0,0,null,null),new A(2,0,T.t1HB,T.t1l),new A(3,0,T.t2HB,T.t2l),new A(3,0,T.t3HB,T.t3l),new A(0,0,null,null),new A(4,0,T.t5HB,T.t5l),new A(4,0,T.t6HB,T.t6l),new A(6,0,T.t7HB,T.t7l),new A(6,0,T.t8HB,T.t8l),new A(6,0,T.t9HB,T.t9l),new A(8,0,T.t10HB,T.t10l),new A(8,0,T.t11HB,T.t11l),new A(8,0,T.t12HB,T.t12l),new A(16,0,T.t13HB,T.t13l),new A(0,0,null,T.t16_5l),new A(16,0,T.t15HB,T.t15l),new A(1,1,T.t16HB,T.t16l),new A(2,3,T.t16HB,T.t16l),new A(3,7,T.t16HB,T.t16l),new A(4,15,T.t16HB,T.t16l),new A(6,63,T.t16HB,T.t16l),new A(8,255,T.t16HB,T.t16l),new A(10,1023,T.t16HB,T.t16l),new A(13,8191,T.t16HB,T.t16l),new A(4,15,T.t24HB,T.t24l),new A(5,31,T.t24HB,T.t24l),new A(6,63,T.t24HB,T.t24l),new A(7,127,T.t24HB,T.t24l),new A(8,255,T.t24HB,T.t24l),new A(9,511,T.t24HB,T.t24l),new A(11,2047,T.t24HB,T.t24l),new A(13,8191,T.t24HB,T.t24l),new A(0,0,T.t32HB,T.t32l),new A(0,0,T.t33HB,T.t33l)],T.largetbl=[65540,327685,458759,589832,655369,655370,720906,720907,786443,786444,786444,851980,851980,851980,917517,655370,262149,393222,524295,589832,655369,720906,720906,720907,786443,786443,786444,851980,917516,851980,917516,655370,458759,524295,589832,655369,720905,720906,786442,786443,851979,786443,851979,851980,851980,917516,917517,720905,589832,589832,655369,720905,720906,786442,786442,786443,851979,851979,917515,917516,917516,983052,983052,786441,655369,655369,720905,720906,786442,786442,851978,851979,851979,917515,917516,917516,983052,983052,983053,720905,655370,655369,720906,720906,786442,851978,851979,917515,851979,917515,917516,983052,983052,983052,1048588,786441,720906,720906,720906,786442,851978,851979,851979,851979,917515,917516,917516,917516,983052,983052,1048589,786441,720907,720906,786442,786442,851979,851979,851979,917515,917516,983052,983052,983052,983052,1114125,1114125,786442,720907,786443,786443,851979,851979,851979,917515,917515,983051,983052,983052,983052,1048588,1048589,1048589,786442,786443,786443,786443,851979,851979,917515,917515,983052,983052,983052,983052,1048588,983053,1048589,983053,851978,786444,851979,786443,851979,917515,917516,917516,917516,983052,1048588,1048588,1048589,1114125,1114125,1048589,786442,851980,851980,851979,851979,917515,917516,983052,1048588,1048588,1048588,1048588,1048589,1048589,983053,1048589,851978,851980,917516,917516,917516,917516,983052,983052,983052,983052,1114124,1048589,1048589,1048589,1048589,1179661,851978,983052,917516,917516,917516,983052,983052,1048588,1048588,1048589,1179661,1114125,1114125,1114125,1245197,1114125,851978,917517,983052,851980,917516,1048588,1048588,983052,1048589,1048589,1114125,1179661,1114125,1245197,1114125,1048589,851978,655369,655369,655369,720905,720905,786441,786441,786441,851977,851977,851977,851978,851978,851978,851978,655366],T.table23=[65538,262147,458759,262148,327684,458759,393222,458759,524296],T.table56=[65539,262148,458758,524296,262148,327684,524294,589831,458757,524294,589831,655368,524295,524295,589832,655369],T.bitrate_table=[[0,8,16,24,32,40,48,56,64,80,96,112,128,144,160,-1],[0,32,40,48,56,64,80,96,112,128,160,192,224,256,320,-1],[0,8,16,24,32,40,48,56,64,-1,-1,-1,-1,-1,-1,-1]],T.samplerate_table=[[22050,24e3,16e3,-1],[44100,48e3,32e3,-1],[11025,12e3,8e3,-1]],T.scfsi_band=[0,6,11,16,21],O.Q_MAX=257,O.Q_MAX2=116,O.LARGE_BITS=1e5,O.IXMAX_VAL=8206;var V={};function F(){var e,t;this.rv=null,this.qupvt=null;var n,s=new E;function r(e){this.ordinal=e}function o(e){for(var t=0;t<e.sfbmax;t++)if(e.scalefac[t]+e.subblock_gain[e.window[t]]==0)return!1;return!0}function l(e,t,n,s,r){var i;switch(e){default:case 9:t.over_count>0?(i=n.over_SSD<=t.over_SSD,n.over_SSD==t.over_SSD&&(i=n.bits<t.bits)):i=n.max_noise<0&&10*n.max_noise+n.bits<=10*t.max_noise+t.bits;break;case 0:i=n.over_count<t.over_count||n.over_count==t.over_count&&n.over_noise<t.over_noise||n.over_count==t.over_count&&k.EQ(n.over_noise,t.over_noise)&&n.tot_noise<t.tot_noise;break;case 8:a();case 1:i=n.max_noise<t.max_noise;break;case 2:i=n.tot_noise<t.tot_noise;break;case 3:i=n.tot_noise<t.tot_noise&&n.max_noise<t.max_noise;break;case 4:i=n.max_noise<=0&&t.max_noise>.2||n.max_noise<=0&&t.max_noise<0&&t.max_noise>n.max_noise-.2&&n.tot_noise<t.tot_noise||n.max_noise<=0&&t.max_noise>0&&t.max_noise>n.max_noise-.2&&n.tot_noise<t.tot_noise+t.over_noise||n.max_noise>0&&t.max_noise>-.05&&t.max_noise>n.max_noise-.1&&n.tot_noise+n.over_noise<t.tot_noise+t.over_noise||n.max_noise>0&&t.max_noise>-.1&&t.max_noise>n.max_noise-.15&&n.tot_noise+n.over_noise+n.over_noise<t.tot_noise+t.over_noise+t.over_noise;break;case 5:i=n.over_noise<t.over_noise||k.EQ(n.over_noise,t.over_noise)&&n.tot_noise<t.tot_noise;break;case 6:i=n.over_noise<t.over_noise||k.EQ(n.over_noise,t.over_noise)&&(n.max_noise<t.max_noise||k.EQ(n.max_noise,t.max_noise)&&n.tot_noise<=t.tot_noise);break;case 7:i=n.over_count<t.over_count||n.over_noise<t.over_noise}return 0==t.over_count&&(i=i&&n.bits<t.bits),i}function _(e,s,r,i,l){var _=e.internal_flags;!function(e,t,n,s,r){var i,o=e.internal_flags;i=0==t.scalefac_scale?1.2968395546510096:1.6817928305074292;for(var l=0,_=0;_<t.sfbmax;_++)l<n[_]&&(l=n[_]);var c=o.noise_shaping_amp;switch(3==c&&a(),c){case 2:break;case 1:l>1?l=Math.pow(l,.5):l*=.95;break;case 0:default:l>1?l=1:l*=.95}var f=0;for(_=0;_<t.sfbmax;_++){var u,h=t.width[_];if(f+=h,!(n[_]<l)){for(0!=(2&o.substep_shaping)&&a(),t.scalefac[_]++,u=-h;u<0;u++)s[f+u]*=i,s[f+u]>t.xrpow_max&&(t.xrpow_max=s[f+u]);if(2==o.noise_shaping_amp)return}}}(e,s,r,i);var c=o(s);return!(c||(c=2==_.mode_gr?n.scale_bitcount(s):n.scale_bitcount_lsf(_,s))&&(_.noise_shaping>1&&(u.fill(_.pseudohalf,0),0==s.scalefac_scale?(function(e,a){for(var n=0,s=0;s<e.sfbmax;s++){var r=e.width[s],i=e.scalefac[s];if(0!=e.preflag&&(i+=t.pretab[s]),n+=r,0!=(1&i)){i++;for(var o=-r;o<0;o++)a[n+o]*=1.2968395546510096,a[n+o]>e.xrpow_max&&(e.xrpow_max=a[n+o])}e.scalefac[s]=i>>1}e.preflag=0,e.scalefac_scale=1}(s,i),c=!1):s.block_type==Y.SHORT_TYPE&&_.subblock_gain>0&&(c=function(e,a,n){var s,r=a.scalefac;for(s=0;s<a.sfb_lmax;s++)if(r[s]>=16)return!0;for(var i=0;i<3;i++){var o=0,l=0;for(s=a.sfb_lmax+i;s<a.sfbdivide;s+=3)o<r[s]&&(o=r[s]);for(;s<a.sfbmax;s+=3)l<r[s]&&(l=r[s]);if(!(o<16&&l<8)){if(a.subblock_gain[i]>=7)return!0;a.subblock_gain[i]++;var _=e.scalefac_band.l[a.sfb_lmax];for(s=a.sfb_lmax+i;s<a.sfbmax;s+=3){var c=a.width[s],f=r[s];if((f-=4>>a.scalefac_scale)>=0)r[s]=f,_+=3*c;else{r[s]=0;var u=210+(f<<a.scalefac_scale+1);p=t.IPOW20(u),_+=c*(i+1);for(var h=-c;h<0;h++)n[_+h]*=p,n[_+h]>a.xrpow_max&&(a.xrpow_max=n[_+h]);_+=c*(3-i-1)}}var p=t.IPOW20(202);for(_+=a.width[s]*(i+1),h=-a.width[s];h<0;h++)n[_+h]*=p,n[_+h]>a.xrpow_max&&(a.xrpow_max=n[_+h])}}return!1}(_,s,i)||o(s))),c||(c=2==_.mode_gr?n.scale_bitcount(s):n.scale_bitcount_lsf(_,s)),c))}this.setModules=function(a,r,i,o){e=r,this.rv=r,t=i,this.qupvt=i,n=o,s.setModules(t,n)},this.init_xrpow=function(e,t,a){var n=0,s=0|t.max_nonzero_coeff;if(t.xrpow_max=0,u.fill(a,s,576,0),(n=function(e,t,a,n){n=0;for(var s=0;s<=a;++s){var r=Math.abs(e.xr[s]);n+=r,t[s]=Math.sqrt(r*Math.sqrt(r)),t[s]>e.xrpow_max&&(e.xrpow_max=t[s])}return n}(t,a,s,n))>1e-20){var r=0;0!=(2&e.substep_shaping)&&(r=1);for(var i=0;i<t.psymax;i++)e.pseudohalf[i]=r;return!0}return u.fill(t.l3_enc,0,576,0),!1},this.init_outer_loop=function(e,n){n.part2_3_length=0,n.big_values=0,n.count1=0,n.global_gain=210,n.scalefac_compress=0,n.table_select[0]=0,n.table_select[1]=0,n.table_select[2]=0,n.subblock_gain[0]=0,n.subblock_gain[1]=0,n.subblock_gain[2]=0,n.subblock_gain[3]=0,n.region0_count=0,n.region1_count=0,n.preflag=0,n.scalefac_scale=0,n.count1table_select=0,n.part2_length=0,n.sfb_lmax=Y.SBPSY_l,n.sfb_smin=Y.SBPSY_s,n.psy_lmax=e.sfb21_extra?Y.SBMAX_l:Y.SBPSY_l,n.psymax=n.psy_lmax,n.sfbmax=n.sfb_lmax,n.sfbdivide=11;for(var s=0;s<Y.SBMAX_l;s++)n.width[s]=e.scalefac_band.l[s+1]-e.scalefac_band.l[s],n.window[s]=3;if(n.block_type==Y.SHORT_TYPE){var r=i(576);n.sfb_smin=0,n.sfb_lmax=0,0!=n.mixed_block_flag&&a(),n.psymax=n.sfb_lmax+3*((e.sfb21_extra?Y.SBMAX_s:Y.SBPSY_s)-n.sfb_smin),n.sfbmax=n.sfb_lmax+3*(Y.SBPSY_s-n.sfb_smin),n.sfbdivide=n.sfbmax-18,n.psy_lmax=n.sfb_lmax;var o=e.scalefac_band.l[n.sfb_lmax];for(h.arraycopy(n.xr,0,r,0,576),s=n.sfb_smin;s<Y.SBMAX_s;s++)for(var l=e.scalefac_band.s[s],_=e.scalefac_band.s[s+1],c=0;c<3;c++)for(var f=l;f<_;f++)n.xr[o++]=r[3*f+c];var p=n.sfb_lmax;for(s=n.sfb_smin;s<Y.SBMAX_s;s++)n.width[p]=n.width[p+1]=n.width[p+2]=e.scalefac_band.s[s+1]-e.scalefac_band.s[s],n.window[p]=0,n.window[p+1]=1,n.window[p+2]=2,p+=3}n.count1bits=0,n.sfb_partition_table=t.nr_of_sfb_block[0][0],n.slen[0]=0,n.slen[1]=0,n.slen[2]=0,n.slen[3]=0,n.max_nonzero_coeff=575,u.fill(n.scalefac,0),function(e,a){var n=e.ATH,s=a.xr;if(a.block_type!=Y.SHORT_TYPE)for(var r=!1,i=Y.PSFB21-1;i>=0&&!r;i--){var o=e.scalefac_band.psfb21[i],l=e.scalefac_band.psfb21[i+1],_=t.athAdjust(n.adjust,n.psfb21[i],n.floor);e.nsPsy.longfact[21]>1e-12&&(_*=e.nsPsy.longfact[21]);for(var c=l-1;c>=o;c--){if(!(Math.abs(s[c])<_)){r=!0;break}s[c]=0}}else for(var f=0;f<3;f++)for(r=!1,i=Y.PSFB12-1;i>=0&&!r;i--){l=(o=3*e.scalefac_band.s[12]+(e.scalefac_band.s[13]-e.scalefac_band.s[12])*f+(e.scalefac_band.psfb12[i]-e.scalefac_band.psfb12[0]))+(e.scalefac_band.psfb12[i+1]-e.scalefac_band.psfb12[i]);var u=t.athAdjust(n.adjust,n.psfb12[i],n.floor);for(e.nsPsy.shortfact[12]>1e-12&&(u*=e.nsPsy.shortfact[12]),c=l-1;c>=o;c--){if(!(Math.abs(s[c])<u)){r=!0;break}s[c]=0}}}(e,n)},r.BINSEARCH_NONE=new r(0),r.BINSEARCH_UP=new r(1),r.BINSEARCH_DOWN=new r(2),this.outer_loop=function(e,s,o,c,f,u){var p=e.internal_flags,b=new D,m=i(576),d=i(V.SFBMAX),g=new B,w=new N,S=9999999,M=!1;if(function(e,t,s,i,o){var l,_=e.CurrentStep[i],c=!1,f=e.OldValue[i],u=r.BINSEARCH_NONE;for(t.global_gain=f,s-=t.part2_length;;){var h;if(l=n.count_bits(e,o,t,null),1==_||l==s)break;l>s?(u==r.BINSEARCH_DOWN&&(c=!0),c&&(_/=2),u=r.BINSEARCH_UP,h=_):(u==r.BINSEARCH_UP&&(c=!0),c&&(_/=2),u=r.BINSEARCH_DOWN,h=-_),t.global_gain+=h,t.global_gain<0&&a(),t.global_gain>255&&a()}for(;l>s&&t.global_gain<255;)t.global_gain++,l=n.count_bits(e,o,t,null);e.CurrentStep[i]=f-t.global_gain>=4?4:2,e.OldValue[i]=t.global_gain,t.part2_3_length=l}(p,s,u,f,c),0==p.noise_shaping)return 100;t.calc_noise(s,o,d,g,w),g.bits=s.part2_3_length,b.assign(s);var R=0;for(h.arraycopy(c,0,m,0,576);!M;){do{var y,k=new B,A=255;if(y=0!=(2&p.substep_shaping)?20:3,p.sfb21_extra&&a(),!_(e,b,d,c))break;0!=b.scalefac_scale&&(A=254);var T=u-b.part2_length;if(T<=0)break;for(;(b.part2_3_length=n.count_bits(p,c,b,w))>T&&b.global_gain<=A;)b.global_gain++;if(b.global_gain>A)break;if(0==g.over_count){for(;(b.part2_3_length=n.count_bits(p,c,b,w))>S&&b.global_gain<=A;)b.global_gain++;if(b.global_gain>A)break}if(t.calc_noise(b,o,d,k,w),k.bits=b.part2_3_length,0!=(l(s.block_type!=Y.SHORT_TYPE?e.quant_comp:e.quant_comp_short,g,k)?1:0))S=s.part2_3_length,g=k,s.assign(b),R=0,h.arraycopy(c,0,m,0,576);else if(0==p.full_outer_loop){if(++R>y&&0==g.over_count)break;p.noise_shaping_amp,p.noise_shaping_amp}}while(b.global_gain+b.scalefac_scale<255);3==p.noise_shaping_amp?a():M=!0}return e.VBR==v.vbr_rh||e.VBR==v.vbr_mtrh?h.arraycopy(m,0,c,0,576):0!=(1&p.substep_shaping)&&a(),g.over_count},this.iteration_finish_one=function(t,a,s){var r=t.l3_side,i=r.tt[a][s];n.best_scalefac_store(t,a,s,r),1==t.use_best_huffman&&n.best_huffman_divide(t,i),e.ResvAdjust(t,i)}}function j(){var e=[-.1482523854003001,32.308141959636465,296.40344946382766,883.1344870032432,11113.947376231741,1057.2713659324597,305.7402417275812,30.825928907280012,3.8533188138216365,59.42900443849514,709.5899960123345,5281.91112291017,-5829.66483675846,-817.6293103748613,-76.91656988279972,-4.594269939176596,.9063471690191471,.1960342806591213,-.15466694054279598,34.324387823855965,301.8067566458425,817.599602898885,11573.795901679885,1181.2520595540152,321.59731579894424,31.232021761053772,3.7107095756221318,53.650946155329365,684.167428119626,5224.56624370173,-6366.391851890084,-908.9766368219582,-89.83068876699639,-5.411397422890401,.8206787908286602,.3901806440322567,-.16070888947830023,36.147034243915876,304.11815768187864,732.7429163887613,11989.60988270091,1300.012278487897,335.28490093152146,31.48816102859945,3.373875931311736,47.232241542899175,652.7371796173471,5132.414255594984,-6909.087078780055,-1001.9990371107289,-103.62185754286375,-6.104916304710272,.7416505462720353,.5805693545089249,-.16636367662261495,37.751650073343995,303.01103387567713,627.9747488785183,12358.763425278165,1412.2779918482834,346.7496836825721,31.598286663170416,3.1598635433980946,40.57878626349686,616.1671130880391,5007.833007176154,-7454.040671756168,-1095.7960341867115,-118.24411666465777,-6.818469345853504,.6681786379192989,.7653668647301797,-.1716176790982088,39.11551877123304,298.3413246578966,503.5259106886539,12679.589408408976,1516.5821921214542,355.9850766329023,31.395241710249053,2.9164211881972335,33.79716964664243,574.8943997801362,4853.234992253242,-7997.57021486075,-1189.7624067269965,-133.6444792601766,-7.7202770609839915,.5993769336819237,.9427934736519954,-.17645823955292173,40.21879108166477,289.9982036694474,359.3226160751053,12950.259102786438,1612.1013903507662,362.85067106591504,31.045922092242872,2.822222032597987,26.988862316190684,529.8996541764288,4671.371946949588,-8535.899136645805,-1282.5898586244496,-149.58553632943463,-8.643494270763135,.5345111359507916,1.111140466039205,-.36174739330527045,41.04429910497807,277.5463268268618,195.6386023135583,13169.43812144731,1697.6433561479398,367.40983966190305,30.557037410382826,2.531473372857427,20.070154905927314,481.50208566532336,4464.970341588308,-9065.36882077239,-1373.62841526722,-166.1660487028118,-9.58289321133207,.4729647758913199,1.268786568327291,-.36970682634889585,41.393213350082036,261.2935935556502,12.935476055240873,13336.131683328815,1772.508612059496,369.76534388639965,29.751323653701338,2.4023193045459172,13.304795348228817,430.5615775526625,4237.0568611071185,-9581.931701634761,-1461.6913552409758,-183.12733958476446,-10.718010163869403,.41421356237309503,1.414213562373095,-.37677560326535325,41.619486213528496,241.05423794991074,-187.94665032361226,13450.063605744153,1836.153896465782,369.4908799925761,29.001847876923147,2.0714759319987186,6.779591200894186,377.7767837205709,3990.386575512536,-10081.709459700915,-1545.947424837898,-200.3762958015653,-11.864482073055006,.3578057213145241,1.546020906725474,-.3829366947518991,41.1516456456653,216.47684307105183,-406.1569483347166,13511.136535077321,1887.8076599260432,367.3025214564151,28.136213436723654,1.913880671464418,.3829366947518991,323.85365704338597,3728.1472257487526,-10561.233882199509,-1625.2025997821418,-217.62525175416,-13.015432208941645,.3033466836073424,1.66293922460509,-.5822628872992417,40.35639251440489,188.20071124269245,-640.2706748618148,13519.21490106562,1927.6022433578062,362.8197642637487,26.968821921868447,1.7463817695935329,-5.62650678237171,269.3016715297017,3453.386536448852,-11016.145278780888,-1698.6569643425091,-234.7658734267683,-14.16351421663124,.2504869601913055,1.76384252869671,-.5887180101749253,39.23429103868072,155.76096234403798,-889.2492977967378,13475.470561874661,1955.0535223723712,356.4450994756727,25.894952980042156,1.5695032905781554,-11.181939564328772,214.80884394039484,3169.1640829158237,-11443.321309975563,-1765.1588461316153,-251.68908574481912,-15.49755935939164,.198912367379658,1.847759065022573,-.7912582233652842,37.39369355329111,119.699486012458,-1151.0956593239027,13380.446257078214,1970.3952110853447,348.01959814116185,24.731487364283044,1.3850130831637748,-16.421408865300393,161.05030052864092,2878.3322807850063,-11838.991423510031,-1823.985884688674,-268.2854986386903,-16.81724543849939,.1483359875383474,1.913880671464418,-.7960642926861912,35.2322109610459,80.01928065061526,-1424.0212633405113,13235.794061869668,1973.804052543835,337.9908651258184,23.289159354463873,1.3934255946442087,-21.099669467133474,108.48348407242611,2583.700758091299,-12199.726194855148,-1874.2780658979746,-284.2467154529415,-18.11369784385905,.09849140335716425,1.961570560806461,-.998795456205172,32.56307803611191,36.958364584370486,-1706.075448829146,13043.287458812016,1965.3831106103316,326.43182772364605,22.175018750622293,1.198638339011324,-25.371248002043963,57.53505923036915,2288.41886619975,-12522.674544337233,-1914.8400385312243,-299.26241273417224,-19.37805630698734,.04912684976946725,1.990369453344394,.035780907*p.SQRT2*.5/2384e-9,.017876148*p.SQRT2*.5/2384e-9,.003134727*p.SQRT2*.5/2384e-9,.002457142*p.SQRT2*.5/2384e-9,971317e-9*p.SQRT2*.5/2384e-9,218868e-9*p.SQRT2*.5/2384e-9,101566e-9*p.SQRT2*.5/2384e-9,13828e-9*p.SQRT2*.5/2384e-9,12804.797818791945,1945.5515939597317,313.4244966442953,20.801593959731544,1995.1556208053692,9.000838926174497,-29.20218120805369],t=[[2382191739347913e-28,6423305872147834e-28,9400849094049688e-28,1122435026096556e-27,1183840321267481e-27,1122435026096556e-27,940084909404969e-27,6423305872147839e-28,2382191739347918e-28,5456116108943412e-27,4878985199565852e-27,4240448995017367e-27,3559909094758252e-27,2858043359288075e-27,2156177623817898e-27,1475637723558783e-27,8371015190102974e-28,2599706096327376e-28,-5456116108943412e-27,-4878985199565852e-27,-4240448995017367e-27,-3559909094758252e-27,-2858043359288076e-27,-2156177623817898e-27,-1475637723558783e-27,-8371015190102975e-28,-2599706096327376e-28,-2382191739347923e-28,-6423305872147843e-28,-9400849094049696e-28,-1122435026096556e-27,-1183840321267481e-27,-1122435026096556e-27,-9400849094049694e-28,-642330587214784e-27,-2382191739347918e-28],[2382191739347913e-28,6423305872147834e-28,9400849094049688e-28,1122435026096556e-27,1183840321267481e-27,1122435026096556e-27,9400849094049688e-28,6423305872147841e-28,2382191739347918e-28,5456116108943413e-27,4878985199565852e-27,4240448995017367e-27,3559909094758253e-27,2858043359288075e-27,2156177623817898e-27,1475637723558782e-27,8371015190102975e-28,2599706096327376e-28,-5461314069809755e-27,-4921085770524055e-27,-4343405037091838e-27,-3732668368707687e-27,-3093523840190885e-27,-2430835727329465e-27,-1734679010007751e-27,-974825365660928e-27,-2797435120168326e-28,0,0,0,0,0,0,-2283748241799531e-28,-4037858874020686e-28,-2146547464825323e-28],[.1316524975873958,.414213562373095,.7673269879789602,1.091308501069271,1.303225372841206,1.56968557711749,1.920982126971166,2.414213562373094,3.171594802363212,4.510708503662055,7.595754112725146,22.90376554843115,.984807753012208,.6427876096865394,.3420201433256688,.9396926207859084,-.1736481776669303,-.7660444431189779,.8660254037844387,.5,-.5144957554275265,-.4717319685649723,-.3133774542039019,-.1819131996109812,-.09457419252642064,-.04096558288530405,-.01419856857247115,-.003699974673760037,.8574929257125442,.8817419973177052,.9496286491027329,.9833145924917901,.9955178160675857,.9991605581781475,.999899195244447,.9999931550702802],[0,0,0,0,0,0,2283748241799531e-28,4037858874020686e-28,2146547464825323e-28,5461314069809755e-27,4921085770524055e-27,4343405037091838e-27,3732668368707687e-27,3093523840190885e-27,2430835727329466e-27,1734679010007751e-27,974825365660928e-27,2797435120168326e-28,-5456116108943413e-27,-4878985199565852e-27,-4240448995017367e-27,-3559909094758253e-27,-2858043359288075e-27,-2156177623817898e-27,-1475637723558782e-27,-8371015190102975e-28,-2599706096327376e-28,-2382191739347913e-28,-6423305872147834e-28,-9400849094049688e-28,-1122435026096556e-27,-1183840321267481e-27,-1122435026096556e-27,-9400849094049688e-28,-6423305872147841e-28,-2382191739347918e-28]],n=t[Y.SHORT_TYPE],s=t[Y.SHORT_TYPE],r=t[Y.SHORT_TYPE],o=t[Y.SHORT_TYPE],l=[0,1,16,17,8,9,24,25,4,5,20,21,12,13,28,29,2,3,18,19,10,11,26,27,6,7,22,23,14,15,30,31];function _(t,a,n){for(var s,r,i,o=10,l=a+238-14-286,_=-15;_<0;_++){var c,f,u;c=e[o+-10],f=t[l+-224]*c,u=t[a+224]*c,c=e[o+-9],f+=t[l+-160]*c,u+=t[a+160]*c,c=e[o+-8],f+=t[l+-96]*c,u+=t[a+96]*c,c=e[o+-7],f+=t[l+-32]*c,u+=t[a+32]*c,c=e[o+-6],f+=t[l+32]*c,u+=t[a+-32]*c,c=e[o+-5],f+=t[l+96]*c,u+=t[a+-96]*c,c=e[o+-4],f+=t[l+160]*c,u+=t[a+-160]*c,c=e[o+-3],f+=t[l+224]*c,u+=t[a+-224]*c,c=e[o+-2],f+=t[a+-256]*c,u-=t[l+256]*c,c=e[o+-1],f+=t[a+-192]*c,u-=t[l+192]*c,c=e[o+0],f+=t[a+-128]*c,u-=t[l+128]*c,c=e[o+1],f+=t[a+-64]*c,u-=t[l+64]*c,c=e[o+2],f+=t[a+0]*c,u-=t[l+0]*c,c=e[o+3],f+=t[a+64]*c,u-=t[l+-64]*c,c=e[o+4],f+=t[a+128]*c,u-=t[l+-128]*c,c=e[o+5],f+=t[a+192]*c,c=(u-=t[l+-192]*c)-(f*=e[o+6]),n[30+2*_]=u+f,n[31+2*_]=e[o+7]*c,o+=18,a--,l++}u=t[a+-16]*e[o+-10],f=t[a+-32]*e[o+-2],u+=(t[a+-48]-t[a+16])*e[o+-9],f+=t[a+-96]*e[o+-1],u+=(t[a+-80]+t[a+48])*e[o+-8],f+=t[a+-160]*e[o+0],u+=(t[a+-112]-t[a+80])*e[o+-7],f+=t[a+-224]*e[o+1],u+=(t[a+-144]+t[a+112])*e[o+-6],f-=t[a+32]*e[o+2],u+=(t[a+-176]-t[a+144])*e[o+-5],f-=t[a+96]*e[o+3],u+=(t[a+-208]+t[a+176])*e[o+-4],f-=t[a+160]*e[o+4],u+=(t[a+-240]-t[a+208])*e[o+-3],s=(f-=t[a+224])-u,r=f+u,u=n[14],f=n[15]-u,n[31]=r+u,n[30]=s+f,n[15]=s-f,n[14]=r-u,i=n[28]-n[0],n[0]+=n[28],n[28]=i*e[o+-36+7],i=n[29]-n[1],n[1]+=n[29],n[29]=i*e[o+-36+7],i=n[26]-n[2],n[2]+=n[26],n[26]=i*e[o+-72+7],i=n[27]-n[3],n[3]+=n[27],n[27]=i*e[o+-72+7],i=n[24]-n[4],n[4]+=n[24],n[24]=i*e[o+-108+7],i=n[25]-n[5],n[5]+=n[25],n[25]=i*e[o+-108+7],i=n[22]-n[6],n[6]+=n[22],n[22]=i*p.SQRT2,i=n[23]-n[7],n[7]+=n[23],n[23]=i*p.SQRT2-n[7],n[7]-=n[6],n[22]-=n[7],n[23]-=n[22],i=n[6],n[6]=n[31]-i,n[31]=n[31]+i,i=n[7],n[7]=n[30]-i,n[30]=n[30]+i,i=n[22],n[22]=n[15]-i,n[15]=n[15]+i,i=n[23],n[23]=n[14]-i,n[14]=n[14]+i,i=n[20]-n[8],n[8]+=n[20],n[20]=i*e[o+-180+7],i=n[21]-n[9],n[9]+=n[21],n[21]=i*e[o+-180+7],i=n[18]-n[10],n[10]+=n[18],n[18]=i*e[o+-216+7],i=n[19]-n[11],n[11]+=n[19],n[19]=i*e[o+-216+7],i=n[16]-n[12],n[12]+=n[16],n[16]=i*e[o+-252+7],i=n[17]-n[13],n[13]+=n[17],n[17]=i*e[o+-252+7],i=-n[20]+n[24],n[20]+=n[24],n[24]=i*e[o+-216+7],i=-n[21]+n[25],n[21]+=n[25],n[25]=i*e[o+-216+7],i=n[4]-n[8],n[4]+=n[8],n[8]=i*e[o+-216+7],i=n[5]-n[9],n[5]+=n[9],n[9]=i*e[o+-216+7],i=n[0]-n[12],n[0]+=n[12],n[12]=i*e[o+-72+7],i=n[1]-n[13],n[1]+=n[13],n[13]=i*e[o+-72+7],i=n[16]-n[28],n[16]+=n[28],n[28]=i*e[o+-72+7],i=-n[17]+n[29],n[17]+=n[29],n[29]=i*e[o+-72+7],i=p.SQRT2*(n[2]-n[10]),n[2]+=n[10],n[10]=i,i=p.SQRT2*(n[3]-n[11]),n[3]+=n[11],n[11]=i,i=p.SQRT2*(-n[18]+n[26]),n[18]+=n[26],n[26]=i-n[18],i=p.SQRT2*(-n[19]+n[27]),n[19]+=n[27],n[27]=i-n[19],i=n[2],n[19]-=n[3],n[3]-=i,n[2]=n[31]-i,n[31]+=i,i=n[3],n[11]-=n[19],n[18]-=i,n[3]=n[30]-i,n[30]+=i,i=n[18],n[27]-=n[11],n[19]-=i,n[18]=n[15]-i,n[15]+=i,i=n[19],n[10]-=i,n[19]=n[14]-i,n[14]+=i,i=n[10],n[11]-=i,n[10]=n[23]-i,n[23]+=i,i=n[11],n[26]-=i,n[11]=n[22]-i,n[22]+=i,i=n[26],n[27]-=i,n[26]=n[7]-i,n[7]+=i,i=n[27],n[27]=n[6]-i,n[6]+=i,i=p.SQRT2*(n[0]-n[4]),n[0]+=n[4],n[4]=i,i=p.SQRT2*(n[1]-n[5]),n[1]+=n[5],n[5]=i,i=p.SQRT2*(n[16]-n[20]),n[16]+=n[20],n[20]=i,i=p.SQRT2*(n[17]-n[21]),n[17]+=n[21],n[21]=i,i=-p.SQRT2*(n[8]-n[12]),n[8]+=n[12],n[12]=i-n[8],i=-p.SQRT2*(n[9]-n[13]),n[9]+=n[13],n[13]=i-n[9],i=-p.SQRT2*(n[25]-n[29]),n[25]+=n[29],n[29]=i-n[25],i=-p.SQRT2*(n[24]+n[28]),n[24]-=n[28],n[28]=i-n[24],i=n[24]-n[16],n[24]=i,i=n[20]-i,n[20]=i,i=n[28]-i,n[28]=i,i=n[25]-n[17],n[25]=i,i=n[21]-i,n[21]=i,i=n[29]-i,n[29]=i,i=n[17]-n[1],n[17]=i,i=n[9]-i,n[9]=i,i=n[25]-i,n[25]=i,i=n[5]-i,n[5]=i,i=n[21]-i,n[21]=i,i=n[13]-i,n[13]=i,i=n[29]-i,n[29]=i,i=n[1]-n[0],n[1]=i,i=n[16]-i,n[16]=i,i=n[17]-i,n[17]=i,i=n[8]-i,n[8]=i,i=n[9]-i,n[9]=i,i=n[24]-i,n[24]=i,i=n[25]-i,n[25]=i,i=n[4]-i,n[4]=i,i=n[5]-i,n[5]=i,i=n[20]-i,n[20]=i,i=n[21]-i,n[21]=i,i=n[12]-i,n[12]=i,i=n[13]-i,n[13]=i,i=n[28]-i,n[28]=i,i=n[29]-i,n[29]=i,i=n[0],n[0]+=n[31],n[31]-=i,i=n[1],n[1]+=n[30],n[30]-=i,i=n[16],n[16]+=n[15],n[15]-=i,i=n[17],n[17]+=n[14],n[14]-=i,i=n[8],n[8]+=n[23],n[23]-=i,i=n[9],n[9]+=n[22],n[22]-=i,i=n[24],n[24]+=n[7],n[7]-=i,i=n[25],n[25]+=n[6],n[6]-=i,i=n[4],n[4]+=n[27],n[27]-=i,i=n[5],n[5]+=n[26],n[26]-=i,i=n[20],n[20]+=n[11],n[11]-=i,i=n[21],n[21]+=n[10],n[10]-=i,i=n[12],n[12]+=n[19],n[19]-=i,i=n[13],n[13]+=n[18],n[18]-=i,i=n[28],n[28]+=n[3],n[3]-=i,i=n[29],n[29]+=n[2],n[2]-=i}function c(e,a){for(var n=0;n<3;n++){var s,r,i,o,l,_;r=(o=e[a+6]*t[Y.SHORT_TYPE][0]-e[a+15])+(s=e[a+0]*t[Y.SHORT_TYPE][2]-e[a+9]),i=o-s,l=(o=e[a+15]*t[Y.SHORT_TYPE][0]+e[a+6])+(s=e[a+9]*t[Y.SHORT_TYPE][2]+e[a+0]),_=-o+s,s=2069978111953089e-26*(e[a+3]*t[Y.SHORT_TYPE][1]-e[a+12]),o=2069978111953089e-26*(e[a+12]*t[Y.SHORT_TYPE][1]+e[a+3]),e[a+0]=190752519173728e-25*r+s,e[a+15]=190752519173728e-25*-l+o,i=.8660254037844387*i*1907525191737281e-26,l=.5*l*1907525191737281e-26+o,e[a+3]=i-l,e[a+6]=i+l,r=.5*r*1907525191737281e-26-s,_=.8660254037844387*_*1907525191737281e-26,e[a+9]=r+_,e[a+12]=r-_,a++}}this.mdct_sub48=function(e,f,p){for(var b,m,v,d,g,w,S,M,R,y,k,A,T,x,B,E,C,L,P,I,H,O=f,N=286,D=0;D<e.channels_out;D++){for(var V=0;V<e.mode_gr;V++){for(var F,j=e.l3_side.tt[V][D],X=j.xr,z=0,q=e.sb_sample[D][1-V],U=0,G=0;G<9;G++)for(_(O,N,q[U]),_(O,N+32,q[U+1]),U+=2,N+=64,F=1;F<32;F+=2)q[U-1][F]*=-1;for(F=0;F<32;F++,z+=18){var W=j.block_type,K=e.sb_sample[D][V],Z=e.sb_sample[D][1-V];if(0!=j.mixed_block_flag&&F<2&&(W=0),e.amp_filter[F]<1e-12)u.fill(X,z+0,z+18,0);else if(e.amp_filter[F]<1&&a(),W==Y.SHORT_TYPE){for(G=-3;G<0;G++){var Q=t[Y.SHORT_TYPE][G+3];X[z+3*G+9]=K[9+G][l[F]]*Q-K[8-G][l[F]],X[z+3*G+18]=K[14-G][l[F]]*Q+K[15+G][l[F]],X[z+3*G+10]=K[15+G][l[F]]*Q-K[14-G][l[F]],X[z+3*G+19]=Z[2-G][l[F]]*Q+Z[3+G][l[F]],X[z+3*G+11]=Z[3+G][l[F]]*Q-Z[2-G][l[F]],X[z+3*G+20]=Z[8-G][l[F]]*Q+Z[9+G][l[F]]}c(X,z)}else{var $=i(18);for(G=-9;G<0;G++){var J,ee;J=t[W][G+27]*Z[G+9][l[F]]+t[W][G+36]*Z[8-G][l[F]],ee=t[W][G+9]*K[G+9][l[F]]-t[W][G+18]*K[8-G][l[F]],$[G+9]=J-ee*n[3+G+9],$[G+18]=J*n[3+G+9]+ee}b=X,m=z,d=void 0,g=void 0,w=void 0,S=void 0,M=void 0,R=void 0,y=void 0,k=void 0,A=void 0,T=void 0,x=void 0,B=void 0,E=void 0,C=void 0,L=void 0,P=void 0,I=void 0,H=void 0,w=(v=$)[17]-v[9],M=v[15]-v[11],R=v[14]-v[12],y=v[0]+v[8],k=v[1]+v[7],A=v[2]+v[6],T=v[3]+v[5],b[m+17]=y+A-T-(k-v[4]),g=(y+A-T)*s[19]+(k-v[4]),d=(w-M-R)*s[18],b[m+5]=d+g,b[m+6]=d-g,S=(v[16]-v[10])*s[18],k=k*s[19]+v[4],d=w*s[12]+S+M*s[13]+R*s[14],g=-y*s[16]+k-A*s[17]+T*s[15],b[m+1]=d+g,b[m+2]=d-g,d=w*s[13]-S-M*s[14]+R*s[12],g=-y*s[17]+k-A*s[15]+T*s[16],b[m+9]=d+g,b[m+10]=d-g,d=w*s[14]-S+M*s[12]-R*s[13],g=y*s[15]-k+A*s[16]-T*s[17],b[m+13]=d+g,b[m+14]=d-g,x=v[8]-v[0],E=v[6]-v[2],C=v[5]-v[3],L=v[17]+v[9],P=v[16]+v[10],I=v[15]+v[11],H=v[14]+v[12],b[m+0]=L+I+H+(P+v[13]),d=(L+I+H)*s[19]-(P+v[13]),g=(x-E+C)*s[18],b[m+11]=d+g,b[m+12]=d-g,B=(v[7]-v[1])*s[18],P=v[13]-P*s[19],d=L*s[15]-P+I*s[16]+H*s[17],g=x*s[14]+B+E*s[12]+C*s[13],b[m+3]=d+g,b[m+4]=d-g,d=-L*s[17]+P-I*s[15]-H*s[16],g=x*s[13]+B-E*s[14]-C*s[12],b[m+7]=d+g,b[m+8]=d-g,d=-L*s[16]+P-I*s[17]-H*s[15],g=x*s[12]-B+E*s[13]-C*s[14],b[m+15]=d+g,b[m+16]=d-g}if(W!=Y.SHORT_TYPE&&0!=F)for(G=7;G>=0;--G){var te,ae;te=X[z+G]*r[20+G]+X[z+-1-G]*o[28+G],ae=X[z+G]*o[28+G]-X[z+-1-G]*r[20+G],X[z+-1-G]=te,X[z+G]=ae}}}if(O=p,N=286,1==e.mode_gr)for(var ne=0;ne<18;ne++)h.arraycopy(e.sb_sample[D][1][ne],0,e.sb_sample[D][0][ne],0,32)}}}function X(){this.thm=new U,this.en=new U}function Y(){Y.FFTOFFSET;var e=Y.MPG_MD_MS_LR,t=null;this.psy=null;var n=null,s=null;this.setModules=function(e,a,r,i){t=e,this.psy=a,n=a,s=i};var o=new j;this.lame_encode_mp3_frame=function(_,c,u,h,p,b){var m,g=f([2,2]);g[0][0]=new X,g[0][1]=new X,g[1][0]=new X,g[1][1]=new X;var w,S=f([2,2]);S[0][0]=new X,S[0][1]=new X,S[1][0]=new X,S[1][1]=new X;var M,R,y,k=[null,null],A=_.internal_flags,T=l([2,4]),x=[[0,0],[0,0]],B=[[0,0],[0,0]];if(k[0]=c,k[1]=u,0==A.lame_encode_frame_init&&function(e,t){var a,n,s=e.internal_flags;if(0==s.lame_encode_frame_init){var r,l,_=i(2014),c=i(2014);for(s.lame_encode_frame_init=1,r=0,l=0;r<286+576*(1+s.mode_gr);++r)r<576*s.mode_gr?(_[r]=0,2==s.channels_out&&(c[r]=0)):(_[r]=t[0][l],2==s.channels_out&&(c[r]=t[1][l]),++l);for(n=0;n<s.mode_gr;n++)for(a=0;a<s.channels_out;a++)s.l3_side.tt[n][a].block_type=Y.SHORT_TYPE;o.mdct_sub48(s,_,c)}}(_,k),A.padding=0,(A.slot_lag-=A.frac_SpF)<0&&(A.slot_lag+=_.out_samplerate,A.padding=1),0!=A.psymodel){var E,C=[null,null],L=0,P=r(2);for(y=0;y<A.mode_gr;y++){for(R=0;R<A.channels_out;R++)C[R]=k[R],L=576+576*y-Y.FFTOFFSET;if(_.VBR==v.vbr_mtrh||_.VBR==v.vbr_mt?a():E=n.L3psycho_anal_ns(_,C,L,y,g,S,x[y],B[y],T[y],P),0!=E)return-4;for(_.mode==d.JOINT_STEREO&&a(),R=0;R<A.channels_out;R++){var I=A.l3_side.tt[y][R];I.block_type=P[R],I.mixed_block_flag=0}}}else a();if(function(e){var t,n;if(0!=e.ATH.useAdjust)if(n=e.loudness_sq[0][0],t=e.loudness_sq[1][0],2==e.channels_out?a():(n+=n,t+=t),2==e.mode_gr&&(n=Math.max(n,t)),n*=.5,(n*=e.ATH.aaSensitivityP)>.03125)e.ATH.adjust>=1?e.ATH.adjust=1:e.ATH.adjust<e.ATH.adjustLimit&&(e.ATH.adjust=e.ATH.adjustLimit),e.ATH.adjustLimit=1;else{var s=31.98*n+625e-6;e.ATH.adjust>=s?(e.ATH.adjust*=.075*s+.925,e.ATH.adjust<s&&(e.ATH.adjust=s)):e.ATH.adjustLimit>=s?e.ATH.adjust=s:e.ATH.adjust<e.ATH.adjustLimit&&(e.ATH.adjust=e.ATH.adjustLimit),e.ATH.adjustLimit=s}else e.ATH.adjust=1}(A),o.mdct_sub48(A,k[0],k[1]),A.mode_ext=Y.MPG_MD_LR_LR,_.force_ms?A.mode_ext=Y.MPG_MD_MS_LR:_.mode==d.JOINT_STEREO&&a(),A.mode_ext==e?(w=S,M=B):(w=g,M=x),_.analysis&&null!=A.pinfo&&a(),_.VBR==v.vbr_off||_.VBR==v.vbr_abr){var H,O;for(H=0;H<18;H++)A.nsPsy.pefirbuf[H]=A.nsPsy.pefirbuf[H+1];for(O=0,y=0;y<A.mode_gr;y++)for(R=0;R<A.channels_out;R++)O+=M[y][R];for(A.nsPsy.pefirbuf[18]=O,O=A.nsPsy.pefirbuf[9],H=0;H<9;H++)O+=(A.nsPsy.pefirbuf[H]+A.nsPsy.pefirbuf[18-H])*Y.fircoef[H];for(O=3350*A.mode_gr*A.channels_out/O,y=0;y<A.mode_gr;y++)for(R=0;R<A.channels_out;R++)M[y][R]*=O}return A.iteration_loop.iteration_loop(_,M,[.5,.5],w),t.format_bitstream(_),m=t.copy_buffer(A,h,p,b,1),_.bWriteVbrTag&&s.addVbrFrame(_),_.analysis&&null!=A.pinfo&&a(),function(e){var t,n;for(e.bitrate_stereoMode_Hist[e.bitrate_index][4]++,e.bitrate_stereoMode_Hist[15][4]++,2==e.channels_out&&a(),t=0;t<e.mode_gr;++t)for(n=0;n<e.channels_out;++n){var s=0|e.l3_side.tt[t][n].block_type;0!=e.l3_side.tt[t][n].mixed_block_flag&&(s=4),e.bitrate_blockType_Hist[e.bitrate_index][s]++,e.bitrate_blockType_Hist[e.bitrate_index][5]++,e.bitrate_blockType_Hist[15][s]++,e.bitrate_blockType_Hist[15][5]++}}(A),m}}function z(){this.sum=0,this.seen=0,this.want=0,this.pos=0,this.size=0,this.bag=null,this.nVbrNumFrames=0,this.nBytesWritten=0,this.TotalFrameSize=0}function q(){this.tt=[[null,null],[null,null]],this.main_data_begin=0,this.private_bits=0,this.resvDrain_pre=0,this.resvDrain_post=0,this.scfsi=[r(4),r(4)];for(var e=0;e<2;e++)for(var t=0;t<2;t++)this.tt[e][t]=new D}function U(){this.l=i(Y.SBMAX_l),this.s=l([Y.SBMAX_s,3]);var e=this;this.assign=function(t){h.arraycopy(t.l,0,e.l,0,Y.SBMAX_l);for(var a=0;a<Y.SBMAX_s;a++)for(var n=0;n<3;n++)e.s[a][n]=t.s[a][n]}}function G(){this.last_en_subshort=l([4,9]),this.lastAttacks=r(4),this.pefirbuf=i(19),this.longfact=i(Y.SBMAX_l),this.shortfact=i(Y.SBMAX_s),this.attackthre=0,this.attackthre_s=0}function W(){function e(){this.write_timing=0,this.ptr=0,this.buf=n(40)}this.Class_ID=0,this.lame_encode_frame_init=0,this.iteration_init_init=0,this.fill_buffer_resample_init=0,this.mfbuf=l([2,W.MFSIZE]),this.mode_gr=0,this.channels_in=0,this.channels_out=0,this.resample_ratio=0,this.mf_samples_to_encode=0,this.mf_size=0,this.VBR_min_bitrate=0,this.VBR_max_bitrate=0,this.bitrate_index=0,this.samplerate_index=0,this.mode_ext=0,this.lowpass1=0,this.lowpass2=0,this.highpass1=0,this.highpass2=0,this.noise_shaping=0,this.noise_shaping_amp=0,this.substep_shaping=0,this.psymodel=0,this.noise_shaping_stop=0,this.subblock_gain=0,this.use_best_huffman=0,this.full_outer_loop=0,this.l3_side=new q,this.ms_ratio=i(2),this.padding=0,this.frac_SpF=0,this.slot_lag=0,this.tag_spec=null,this.nMusicCRC=0,this.OldValue=r(2),this.CurrentStep=r(2),this.masking_lower=0,this.bv_scf=r(576),this.pseudohalf=r(V.SFBMAX),this.sfb21_extra=!1,this.inbuf_old=new Array(2),this.blackfilt=new Array(2*W.BPC+1),this.itime=o(2),this.sideinfo_len=0,this.sb_sample=l([2,2,18,Y.SBLIMIT]),this.amp_filter=i(32),this.header=new Array(W.MAX_HEADER_BUF),this.h_ptr=0,this.w_ptr=0,this.ancillary_flag=0,this.ResvSize=0,this.ResvMax=0,this.scalefac_band=new H,this.minval_l=i(Y.CBANDS),this.minval_s=i(Y.CBANDS),this.nb_1=l([4,Y.CBANDS]),this.nb_2=l([4,Y.CBANDS]),this.nb_s1=l([4,Y.CBANDS]),this.nb_s2=l([4,Y.CBANDS]),this.s3_ss=null,this.s3_ll=null,this.decay=0,this.thm=new Array(4),this.en=new Array(4),this.tot_ener=i(4),this.loudness_sq=l([2,2]),this.loudness_sq_save=i(2),this.mld_l=i(Y.SBMAX_l),this.mld_s=i(Y.SBMAX_s),this.bm_l=r(Y.SBMAX_l),this.bo_l=r(Y.SBMAX_l),this.bm_s=r(Y.SBMAX_s),this.bo_s=r(Y.SBMAX_s),this.npart_l=0,this.npart_s=0,this.s3ind=_([Y.CBANDS,2]),this.s3ind_s=_([Y.CBANDS,2]),this.numlines_s=r(Y.CBANDS),this.numlines_l=r(Y.CBANDS),this.rnumlines_l=i(Y.CBANDS),this.mld_cb_l=i(Y.CBANDS),this.mld_cb_s=i(Y.CBANDS),this.numlines_s_num1=0,this.numlines_l_num1=0,this.pe=i(4),this.ms_ratio_s_old=0,this.ms_ratio_l_old=0,this.ms_ener_ratio_old=0,this.blocktype_old=r(2),this.nsPsy=new G,this.VBR_seek_table=new z,this.ATH=null,this.PSY=null,this.nogap_total=0,this.nogap_current=0,this.decode_on_the_fly=!0,this.findReplayGain=!0,this.findPeakSample=!0,this.PeakSample=0,this.RadioGain=0,this.AudiophileGain=0,this.rgdata=null,this.noclipGainChange=0,this.noclipScale=0,this.bitrate_stereoMode_Hist=_([16,5]),this.bitrate_blockType_Hist=_([16,6]),this.pinfo=null,this.hip=null,this.in_buffer_nsamples=0,this.in_buffer_0=null,this.in_buffer_1=null,this.iteration_loop=null;for(var t=0;t<this.en.length;t++)this.en[t]=new U;for(t=0;t<this.thm.length;t++)this.thm[t]=new U;for(t=0;t<this.header.length;t++)this.header[t]=new e}function K(){var e=i(Y.BLKSIZE),t=i(Y.BLKSIZE_s/2),a=[.9238795325112867,.3826834323650898,.9951847266721969,.0980171403295606,.9996988186962042,.02454122852291229,.9999811752826011,.006135884649154475];function n(e,t,n){var s,r,i,o=0,l=t+(n<<=1);s=4;do{var _,c,f,u,h,b,m;m=s>>1,b=(h=s<<1)+(u=s),s=h<<1,i=(r=t)+m;do{R=e[r+0]-e[r+u],M=e[r+0]+e[r+u],T=e[r+h]-e[r+b],k=e[r+h]+e[r+b],e[r+h]=M-k,e[r+0]=M+k,e[r+b]=R-T,e[r+u]=R+T,R=e[i+0]-e[i+u],M=e[i+0]+e[i+u],T=p.SQRT2*e[i+b],k=p.SQRT2*e[i+h],e[i+h]=M-k,e[i+0]=M+k,e[i+b]=R-T,e[i+u]=R+T,i+=s,r+=s}while(r<l);for(c=a[o+0],_=a[o+1],f=1;f<m;f++){var v,d;v=1-2*_*_,d=2*_*c,r=t+f,i=t+u-f;do{var g,w,S,M,R,y,k,A,T,x;w=d*e[r+u]-v*e[i+u],g=v*e[r+u]+d*e[i+u],R=e[r+0]-g,M=e[r+0]+g,y=e[i+0]-w,S=e[i+0]+w,w=d*e[r+b]-v*e[i+b],g=v*e[r+b]+d*e[i+b],T=e[r+h]-g,k=e[r+h]+g,x=e[i+h]-w,A=e[i+h]+w,w=_*k-c*x,g=c*k+_*x,e[r+h]=M-g,e[r+0]=M+g,e[i+b]=y-w,e[i+u]=y+w,w=c*A-_*T,g=_*A+c*T,e[i+h]=S-g,e[i+0]=S+g,e[r+b]=R-w,e[r+u]=R+w,i+=s,r+=s}while(r<l);c=(v=c)*a[o+0]-_*a[o+1],_=v*a[o+1]+_*a[o+0]}o+=2}while(s<n)}var s=[0,128,64,192,32,160,96,224,16,144,80,208,48,176,112,240,8,136,72,200,40,168,104,232,24,152,88,216,56,184,120,248,4,132,68,196,36,164,100,228,20,148,84,212,52,180,116,244,12,140,76,204,44,172,108,236,28,156,92,220,60,188,124,252,2,130,66,194,34,162,98,226,18,146,82,210,50,178,114,242,10,138,74,202,42,170,106,234,26,154,90,218,58,186,122,250,6,134,70,198,38,166,102,230,22,150,86,214,54,182,118,246,14,142,78,206,46,174,110,238,30,158,94,222,62,190,126,254];this.fft_short=function(e,a,r,i,o){for(var l=0;l<3;l++){var _=Y.BLKSIZE_s/2,c=65535&192*(l+1),f=Y.BLKSIZE_s/8-1;do{var u,h,p,b,m,v=255&s[f<<2];h=(u=t[v]*i[r][o+v+c])-(m=t[127-v]*i[r][o+v+c+128]),u+=m,b=(p=t[v+64]*i[r][o+v+c+64])-(m=t[63-v]*i[r][o+v+c+192]),p+=m,_-=4,a[l][_+0]=u+p,a[l][_+2]=u-p,a[l][_+1]=h+b,a[l][_+3]=h-b,h=(u=t[v+1]*i[r][o+v+c+1])-(m=t[126-v]*i[r][o+v+c+129]),u+=m,b=(p=t[v+65]*i[r][o+v+c+65])-(m=t[62-v]*i[r][o+v+c+193]),p+=m,a[l][_+Y.BLKSIZE_s/2+0]=u+p,a[l][_+Y.BLKSIZE_s/2+2]=u-p,a[l][_+Y.BLKSIZE_s/2+1]=h+b,a[l][_+Y.BLKSIZE_s/2+3]=h-b}while(--f>=0);n(a[l],_,Y.BLKSIZE_s/2)}},this.fft_long=function(t,a,r,i,o){var l=Y.BLKSIZE/8-1,_=Y.BLKSIZE/2;do{var c,f,u,h,p,b=255&s[l];f=(c=e[b]*i[r][o+b])-(p=e[b+512]*i[r][o+b+512]),c+=p,h=(u=e[b+256]*i[r][o+b+256])-(p=e[b+768]*i[r][o+b+768]),u+=p,a[0+(_-=4)]=c+u,a[_+2]=c-u,a[_+1]=f+h,a[_+3]=f-h,f=(c=e[b+1]*i[r][o+b+1])-(p=e[b+513]*i[r][o+b+513]),c+=p,h=(u=e[b+257]*i[r][o+b+257])-(p=e[b+769]*i[r][o+b+769]),u+=p,a[_+Y.BLKSIZE/2+0]=c+u,a[_+Y.BLKSIZE/2+2]=c-u,a[_+Y.BLKSIZE/2+1]=f+h,a[_+Y.BLKSIZE/2+3]=f-h}while(--l>=0);n(a,_,Y.BLKSIZE/2)},this.init_fft=function(a){for(var n=0;n<Y.BLKSIZE;n++)e[n]=.42-.5*Math.cos(2*Math.PI*(n+.5)/Y.BLKSIZE)+.08*Math.cos(4*Math.PI*(n+.5)/Y.BLKSIZE);for(n=0;n<Y.BLKSIZE_s/2;n++)t[n]=.5*(1-Math.cos(2*Math.PI*(n+.5)/Y.BLKSIZE_s))}}function Z(){var e=new K,t=2.302585092994046,n=1/217621504/(Y.BLKSIZE/2);function s(t,s,r,i,o,l,_,c,f,u,h){var p=t.internal_flags;f<2?(e.fft_long(p,i[o],f,u,h),e.fft_short(p,l[_],f,u,h)):2==f&&a(),s[0]=i[o+0][0],s[0]*=s[0];for(var b=Y.BLKSIZE/2-1;b>=0;--b){var m=i[o+0][Y.BLKSIZE/2-b],v=i[o+0][Y.BLKSIZE/2+b];s[Y.BLKSIZE/2-b]=.5*(m*m+v*v)}for(var d=2;d>=0;--d)for(r[d][0]=l[_+0][d][0],r[d][0]*=r[d][0],b=Y.BLKSIZE_s/2-1;b>=0;--b)m=l[_+0][d][Y.BLKSIZE_s/2-b],v=l[_+0][d][Y.BLKSIZE_s/2+b],r[d][Y.BLKSIZE_s/2-b]=.5*(m*m+v*v);var g=0;for(b=11;b<Y.HBLKSIZE;b++)g+=s[b];p.tot_ener[f]=g,t.analysis&&a(),2==t.athaa_loudapprox&&f<2&&(p.loudness_sq[c][f]=p.loudness_sq_save[f],p.loudness_sq_save[f]=function(e,t){for(var a=0,s=0;s<Y.BLKSIZE/2;++s)a+=e[s]*t.ATH.eql_w[s];return a*=n}(s,p))}var o,_,c,f=[1,.79433,.63096,.63096,.63096,.63096,.63096,.25119,.11749],h=[3.3246*3.3246,3.23837*3.23837,9.9500500969,9.0247369744,8.1854926609,7.0440875649,2.46209*2.46209,2.284*2.284,4.4892710641,1.96552*1.96552,1.82335*1.82335,1.69146*1.69146,2.4621061921,2.1508568964,1.37074*1.37074,1.31036*1.31036,1.5691069696,1.4555939904,1.16203*1.16203,1.2715945225,1.09428*1.09428,1.0659*1.0659,1.0779838276,1.0382591025,1],g=[1.7782755904,1.35879*1.35879,1.38454*1.38454,1.39497*1.39497,1.40548*1.40548,1.3537*1.3537,1.6999465924,1.22321*1.22321,1.3169398564,1],w=[5.5396212496,2.29259*2.29259,4.9868695969,2.12675*2.12675,2.02545*2.02545,1.87894*1.87894,1.74303*1.74303,1.61695*1.61695,2.2499700001,1.39148*1.39148,1.29083*1.29083,1.19746*1.19746,1.2339655056,1.0779838276];function S(e,t,a,n,s,r){var i,l,f;if(t>e){if(!(t<e*_))return e+t;i=t/e}else{if(e>=t*_)return e+t;i=e/t}if(e+=t,n+3<=6){if(i>=o)return e;var u=0|p.FAST_LOG10_X(i,16);return e*g[u]}return u=0|p.FAST_LOG10_X(i,16),t=0!=r?s.ATH.cb_s[a]*s.ATH.adjust:s.ATH.cb_l[a]*s.ATH.adjust,e<c*t?e>t?(l=1,u<=13&&(l=w[u]),f=p.FAST_LOG10_X(e/t,10/15),e*((h[u]-l)*f+l)):u>13?e:e*w[u]:e*h[u]}function M(e,t,a,n,s){var r,i,o=0,l=0;for(r=i=0;r<Y.SBMAX_s;++i,++r){for(var _=e.bo_s[r],c=e.npart_s,f=_<c?_:c;i<f;)o+=t[i],l+=a[i],i++;if(e.en[n].s[r][s]=o,e.thm[n].s[r][s]=l,i>=c){++r;break}var u=e.PSY.bo_s_weight[r],h=1-u;o=u*t[i],l=u*a[i],e.en[n].s[r][s]+=o,e.thm[n].s[r][s]+=l,o=h*t[i],l=h*a[i]}for(;r<Y.SBMAX_s;++r)e.en[n].s[r][s]=0,e.thm[n].s[r][s]=0}function R(e,t,a,n){var s,r,i=0,o=0;for(s=r=0;s<Y.SBMAX_l;++r,++s){for(var l=e.bo_l[s],_=e.npart_l,c=l<_?l:_;r<c;)i+=t[r],o+=a[r],r++;if(e.en[n].l[s]=i,e.thm[n].l[s]=o,r>=_){++s;break}var f=e.PSY.bo_l_weight[s],u=1-f;i=f*t[r],o=f*a[r],e.en[n].l[s]+=i,e.thm[n].l[s]+=o,i=u*t[r],o=u*a[r]}for(;s<Y.SBMAX_l;++s)e.en[n].l[s]=0,e.thm[n].l[s]=0}function y(e,t,a,n,s,r){var i,o,l=e.internal_flags;for(o=i=0;o<l.npart_s;++o){for(var _=0,c=0,f=l.numlines_s[o],u=0;u<f;++u,++i){var h=t[r][i];_+=h,c<h&&(c=h)}a[o]=_}for(i=o=0;o<l.npart_s;o++){var p=l.s3ind_s[o][0],b=l.s3_ss[i++]*a[p];for(++p;p<=l.s3ind_s[o][1];)b+=l.s3_ss[i]*a[p],++i,++p;var m=2*l.nb_s1[s][o];if(n[o]=Math.min(b,m),l.blocktype_old[1&s]==Y.SHORT_TYPE){m=16*l.nb_s2[s][o];var v=n[o];n[o]=Math.min(m,v)}l.nb_s2[s][o]=l.nb_s1[s][o],l.nb_s1[s][o]=b}for(;o<=Y.CBANDS;++o)a[o]=0,n[o]=0}function k(e,t,a){return a>=1?e:a<=0?t:t>0?Math.pow(e/t,a)*t:0}var A=[11.8,13.6,17.2,32,46.5,51.3,57.5,67.1,71.5,84.6,97.6,130];function T(e,a){for(var n=309.07,s=0;s<Y.SBMAX_s-1;s++)for(var r=0;r<3;r++){var i=e.thm.s[s][r];if(i>0){var o=i*a,l=e.en.s[s][r];l>o&&(n+=l>1e10*o?A[s]*(10*t):A[s]*p.FAST_LOG10(l/o))}}return n}var x=[6.8,5.8,5.8,6.4,6.5,9.9,12.1,14.4,15,18.9,21.6,26.9,34.2,40.2,46.8,56.5,60.7,73.9,85.7,93.4,126.1];function B(e,a){for(var n=281.0575,s=0;s<Y.SBMAX_l-1;s++){var r=e.thm.l[s];if(r>0){var i=r*a,o=e.en.l[s];o>i&&(n+=o>1e10*i?x[s]*(10*t):x[s]*p.FAST_LOG10(o/i))}}return n}function E(e,t,a,n,s){var r,i;for(r=i=0;r<e.npart_l;++r){var o,l=0,_=0;for(o=0;o<e.numlines_l[r];++o,++i){var c=t[i];l+=c,_<c&&(_=c)}a[r]=l,n[r]=_,s[r]=l*e.rnumlines_l[r]}}function C(e,t,a,n){var s=f.length-1,r=0,i=a[r]+a[r+1];for(i>0?((o=t[r])<t[r+1]&&(o=t[r+1]),(l=0|(i=20*(2*o-i)/(i*(e.numlines_l[r]+e.numlines_l[r+1]-1))))>s&&(l=s),n[r]=l):n[r]=0,r=1;r<e.npart_l-1;r++){var o,l;(i=a[r-1]+a[r]+a[r+1])>0?((o=t[r-1])<t[r]&&(o=t[r]),o<t[r+1]&&(o=t[r+1]),(l=0|(i=20*(3*o-i)/(i*(e.numlines_l[r-1]+e.numlines_l[r]+e.numlines_l[r+1]-1))))>s&&(l=s),n[r]=l):n[r]=0}(i=a[r-1]+a[r])>0?((o=t[r-1])<t[r]&&(o=t[r]),(l=0|(i=20*(2*o-i)/(i*(e.numlines_l[r-1]+e.numlines_l[r]-1))))>s&&(l=s),n[r]=l):n[r]=0}var L=[-1730326e-23,-.01703172,-1349528e-23,.0418072,-673278e-22,-.0876324,-30835e-21,.1863476,-1104424e-22,-.627638];function P(e){return e<0&&(e=0),e*=.001,13*Math.atan(.76*e)+3.5*Math.atan(e*e/56.25)}function I(e,t,a,n,s,o,l,_,c,f,u,h){var p,b=i(Y.CBANDS+1),m=_/(h>15?1152:384),v=r(Y.HBLKSIZE);_/=c;var d=0,g=0;for(p=0;p<Y.CBANDS;p++){var w;for(B=P(_*d),b[p]=_*d,w=d;P(_*w)-B<.34&&w<=c/2;w++);for(e[p]=w-d,g=p+1;d<w;)v[d++]=p;if(d>c/2){d=c/2,++p;break}}b[p]=_*d;for(var S=0;S<h;S++){var M,R,y,k,A;y=f[S],k=f[S+1],(M=0|Math.floor(.5+u*(y-.5)))<0&&(M=0),(R=0|Math.floor(.5+u*(k-.5)))>c/2&&(R=c/2),a[S]=(v[M]+v[R])/2,t[S]=v[R];var T=m*k;l[S]=(T-b[t[S]])/(b[t[S]+1]-b[t[S]]),l[S]<0?l[S]=0:l[S]>1&&(l[S]=1),A=P(_*f[S]*u),A=Math.min(A,15.5)/15.5,o[S]=Math.pow(10,1.25*(1-Math.cos(Math.PI*A))-2.5)}d=0;for(var x=0;x<g;x++){var B,E,C=e[x];B=P(_*d),E=P(_*(d+C-1)),n[x]=.5*(B+E),B=P(_*(d-.5)),E=P(_*(d+C-.5)),s[x]=E-B,d+=C}return g}function H(e,t,n,s,r,o){var _,c,f,u,h,p,b=l([Y.CBANDS,Y.CBANDS]),m=0;if(o)for(var v=0;v<t;v++)for(_=0;_<t;_++){var d=(c=n[v]-n[_],f=void 0,u=void 0,h=void 0,p=void 0,f=c,u=(f*=f>=0?3:1.5)>=.5&&f<=2.5?8*((p=f-.5)*p-2*p):0,((h=15.811389+7.5*(f+=.474)-17.5*Math.sqrt(1+f*f))<=-60?0:(f=Math.exp(.2302585093*(u+h)),f/=.6609193))*s[_]);b[v][_]=d*r[v]}else a();for(v=0;v<t;v++){for(_=0;_<t&&!(b[v][_]>0);_++);for(e[v][0]=_,_=t-1;_>0&&!(b[v][_]>0);_--);e[v][1]=_,m+=e[v][1]-e[v][0]+1}var g=i(m),w=0;for(v=0;v<t;v++)for(_=e[v][0];_<=e[v][1];_++)g[w++]=b[v][_];return g}function O(e){var t=P(e);return t=Math.min(t,15.5)/15.5,Math.pow(10,1.25*(1-Math.cos(Math.PI*t))-2.5)}function N(e,t){return e<-.3&&(e=3410),e/=1e3,e=Math.max(.1,e),3.64*Math.pow(e,-.8)-6.8*Math.exp(-.6*Math.pow(e-3.4,2))+6*Math.exp(-.15*Math.pow(e-8.7,2))+.001*(.6+.04*t)*Math.pow(e,4)}this.L3psycho_anal_ns=function(e,t,n,o,_,c,h,p,m,g){var w,A,x,P,I,H,O,N,D,V=e.internal_flags,F=l([2,Y.BLKSIZE]),j=l([2,3,Y.BLKSIZE_s]),X=i(Y.CBANDS+1),z=i(Y.CBANDS+1),q=i(Y.CBANDS+2),U=r(2),G=r(2),W=l([2,576]),K=r(Y.CBANDS+2),Z=r(Y.CBANDS+2);for(u.fill(Z,0),w=V.channels_out,e.mode==d.JOINT_STEREO&&(w=4),D=e.VBR==v.vbr_off?0==V.ResvMax?0:V.ResvSize/V.ResvMax*.5:e.VBR==v.vbr_rh||e.VBR==v.vbr_mtrh||e.VBR==v.vbr_mt?.6:1,A=0;A<V.channels_out;A++){var Q=t[A],$=n+576-350-21+192;for(P=0;P<576;P++){var J,ee;for(J=Q[$+P+10],ee=0,I=0;I<9;I+=2)J+=L[I]*(Q[$+P+I]+Q[$+P+21-I]),ee+=L[I+1]*(Q[$+P+I+1]+Q[$+P+21-I-1]);W[A][P]=J+ee}_[o][A].en.assign(V.en[A]),_[o][A].thm.assign(V.thm[A]),w>2&&a()}for(A=0;A<w;A++){var te,ae=i(12),ne=[0,0,0,0],se=i(12),re=1,ie=i(Y.CBANDS),oe=i(Y.CBANDS),le=[0,0,0,0],_e=i(Y.HBLKSIZE),ce=l([3,Y.HBLKSIZE_s]);for(P=0;P<3;P++)ae[P]=V.nsPsy.last_en_subshort[A][P+6],se[P]=ae[P]/V.nsPsy.last_en_subshort[A][P+4],ne[0]+=ae[P];2==A&&a();var fe=W[1&A],ue=0;for(P=0;P<9;P++){for(var he=ue+64,pe=1;ue<he;ue++)pe<Math.abs(fe[ue])&&(pe=Math.abs(fe[ue]));V.nsPsy.last_en_subshort[A][P]=ae[P+3]=pe,ne[1+P/3]+=pe,pe>ae[P+3-2]?pe/=ae[P+3-2]:pe=ae[P+3-2]>10*pe?ae[P+3-2]/(10*pe):0,se[P+3]=pe}for(e.analysis&&a(),te=3==A?V.nsPsy.attackthre_s:V.nsPsy.attackthre,P=0;P<12;P++)0==le[P/3]&&se[P]>te&&(le[P/3]=P%3+1);for(P=1;P<4;P++)(ne[P-1]>ne[P]?ne[P-1]/ne[P]:ne[P]/ne[P-1])<1.7&&(le[P]=0,1==P&&(le[0]=0));for(0!=le[0]&&0!=V.nsPsy.lastAttacks[A]&&(le[0]=0),3!=V.nsPsy.lastAttacks[A]&&le[0]+le[1]+le[2]+le[3]==0||(re=0,0!=le[1]&&0!=le[0]&&(le[1]=0),0!=le[2]&&0!=le[1]&&(le[2]=0),0!=le[3]&&0!=le[2]&&(le[3]=0)),A<2?G[A]=re:a(),m[A]=V.tot_ener[A],s(e,_e,ce,F,1&A,j,1&A,o,A,t,n),E(V,_e,X,ie,oe),C(V,ie,oe,K),N=0;N<3;N++){var be,me;for(y(e,ce,z,q,A,N),M(V,z,q,A,N),O=0;O<Y.SBMAX_s;O++){if(me=V.thm[A].s[O][N],me*=.8,le[N]>=2||1==le[N+1]){var ve=0!=N?N-1:2;pe=k(V.thm[A].s[O][ve],me,.6*D),me=Math.min(me,pe)}1==le[N]?(ve=0!=N?N-1:2,pe=k(V.thm[A].s[O][ve],me,.3*D),me=Math.min(me,pe)):(0!=N&&3==le[N-1]||0==N&&3==V.nsPsy.lastAttacks[A])&&(ve=2!=N?N+1:0,pe=k(V.thm[A].s[O][ve],me,.3*D),me=Math.min(me,pe)),be=ae[3*N+3]+ae[3*N+4]+ae[3*N+5],6*ae[3*N+5]<be&&(me*=.5,6*ae[3*N+4]<be&&(me*=.5)),V.thm[A].s[O][N]=me}}for(V.nsPsy.lastAttacks[A]=le[2],H=0,x=0;x<V.npart_l;x++){for(var de=V.s3ind[x][0],ge=X[de]*f[K[de]],we=V.s3_ll[H++]*ge;++de<=V.s3ind[x][1];)ge=X[de]*f[K[de]],we=S(we,V.s3_ll[H++]*ge,de,de-x,V,0);we*=.158489319246111,V.blocktype_old[1&A]==Y.SHORT_TYPE?q[x]=we:q[x]=k(Math.min(we,Math.min(2*V.nb_1[A][x],16*V.nb_2[A][x])),we,D),V.nb_2[A][x]=V.nb_1[A][x],V.nb_1[A][x]=we}for(;x<=Y.CBANDS;++x)X[x]=0,q[x]=0;R(V,X,q,A)}for(e.mode!=d.STEREO&&e.mode!=d.JOINT_STEREO||a(),e.mode==d.JOINT_STEREO&&a(),function(e,t,a,n){var s=e.internal_flags;e.short_blocks!=b.short_block_coupled||0!=t[0]&&0!=t[1]||(t[0]=t[1]=0);for(var r=0;r<s.channels_out;r++)n[r]=Y.NORM_TYPE,e.short_blocks==b.short_block_dispensed&&(t[r]=1),e.short_blocks==b.short_block_forced&&(t[r]=0),0!=t[r]?s.blocktype_old[r]==Y.SHORT_TYPE&&(n[r]=Y.STOP_TYPE):(n[r]=Y.SHORT_TYPE,s.blocktype_old[r]==Y.NORM_TYPE&&(s.blocktype_old[r]=Y.START_TYPE),s.blocktype_old[r]==Y.STOP_TYPE&&(s.blocktype_old[r]=Y.SHORT_TYPE)),a[r]=s.blocktype_old[r],s.blocktype_old[r]=n[r]}(e,G,g,U),A=0;A<w;A++){var Se,Me,Re,ye=0;A>1?a():(Se=h,ye=0,Me=g[A],Re=_[o][A]),Me==Y.SHORT_TYPE?Se[ye+A]=T(Re,V.masking_lower):Se[ye+A]=B(Re,V.masking_lower),e.analysis&&(V.pinfo.pe[o][A]=Se[ye+A])}return 0},this.psymodel_init=function(a){var n,s,r=a.internal_flags,l=!0,f=13,u=0,h=0,p=-8.25,b=-4.5,d=i(Y.CBANDS),g=i(Y.CBANDS),w=i(Y.CBANDS),S=a.out_samplerate;switch(a.experimentalZ){default:case 0:l=!0;break;case 1:l=a.VBR!=v.vbr_mtrh&&a.VBR!=v.vbr_mt;break;case 2:l=!1;break;case 3:f=8,u=-1.75,h=-.0125,p=-8.25,b=-2.25}for(r.ms_ener_ratio_old=.25,r.blocktype_old[0]=r.blocktype_old[1]=Y.NORM_TYPE,n=0;n<4;++n){for(var M=0;M<Y.CBANDS;++M)r.nb_1[n][M]=1e20,r.nb_2[n][M]=1e20,r.nb_s1[n][M]=r.nb_s2[n][M]=1;for(var R=0;R<Y.SBMAX_l;R++)r.en[n].l[R]=1e20,r.thm[n].l[R]=1e20;for(M=0;M<3;++M){for(R=0;R<Y.SBMAX_s;R++)r.en[n].s[R][M]=1e20,r.thm[n].s[R][M]=1e20;r.nsPsy.lastAttacks[n]=0}for(M=0;M<9;M++)r.nsPsy.last_en_subshort[n][M]=10}for(r.loudness_sq_save[0]=r.loudness_sq_save[1]=0,r.npart_l=I(r.numlines_l,r.bo_l,r.bm_l,d,g,r.mld_l,r.PSY.bo_l_weight,S,Y.BLKSIZE,r.scalefac_band.l,Y.BLKSIZE/1152,Y.SBMAX_l),n=0;n<r.npart_l;n++){var y=u;d[n]>=f&&(y=h*(d[n]-f)/(24-f)+u*(24-d[n])/(24-f)),w[n]=Math.pow(10,y/10),r.numlines_l[n]>0?r.rnumlines_l[n]=1/r.numlines_l[n]:r.rnumlines_l[n]=0}for(r.s3_ll=H(r.s3ind,r.npart_l,d,g,w,l),M=0,n=0;n<r.npart_l;n++){T=m.MAX_VALUE;for(var k=0;k<r.numlines_l[n];k++,M++){var A=S*M/(1e3*Y.BLKSIZE);x=this.ATHformula(1e3*A,a)-20,x=Math.pow(10,.1*x),T>(x*=r.numlines_l[n])&&(T=x)}r.ATH.cb_l[n]=T,(T=20*d[n]/10-20)>6&&(T=100),T<-15&&(T=-15),T-=8,r.minval_l[n]=Math.pow(10,T/10)*r.numlines_l[n]}for(r.npart_s=I(r.numlines_s,r.bo_s,r.bm_s,d,g,r.mld_s,r.PSY.bo_s_weight,S,Y.BLKSIZE_s,r.scalefac_band.s,Y.BLKSIZE_s/384,Y.SBMAX_s),M=0,n=0;n<r.npart_s;n++){var T;for(y=p,d[n]>=f&&(y=b*(d[n]-f)/(24-f)+p*(24-d[n])/(24-f)),w[n]=Math.pow(10,y/10),T=m.MAX_VALUE,k=0;k<r.numlines_s[n];k++,M++){var x;A=S*M/(1e3*Y.BLKSIZE_s),x=this.ATHformula(1e3*A,a)-20,x=Math.pow(10,.1*x),T>(x*=r.numlines_s[n])&&(T=x)}r.ATH.cb_s[n]=T,T=7*d[n]/12-7,d[n]>12&&(T*=1+3.1*Math.log(1+T)),d[n]<12&&(T*=1+2.3*Math.log(1-T)),T<-15&&(T=-15),T-=8,r.minval_s[n]=Math.pow(10,T/10)*r.numlines_s[n]}r.s3_ss=H(r.s3ind_s,r.npart_s,d,g,w,l),o=Math.pow(10,9/16),_=Math.pow(10,1.5),c=Math.pow(10,1.5),e.init_fft(r),r.decay=Math.exp(-1*t/(.01*S/192)),s=3.5,0!=(2&a.exp_nspsytune)&&(s=1),Math.abs(a.msfix)>0&&(s=a.msfix),a.msfix=s;for(var B=0;B<r.npart_l;B++)r.s3ind[B][1]>r.npart_l-1&&(r.s3ind[B][1]=r.npart_l-1);var E=576*r.mode_gr/S;if(r.ATH.decay=Math.pow(10,-1.2*E),r.ATH.adjust=.01,r.ATH.adjustLimit=1,-1!=a.ATHtype){var C=a.out_samplerate/Y.BLKSIZE,L=0;for(A=0,n=0;n<Y.BLKSIZE/2;++n)A+=C,r.ATH.eql_w[n]=1/Math.pow(10,this.ATHformula(A,a)/10),L+=r.ATH.eql_w[n];for(L=1/L,n=Y.BLKSIZE/2;--n>=0;)r.ATH.eql_w[n]*=L}for(B=M=0;B<r.npart_s;++B)for(n=0;n<r.numlines_s[B];++n)++M;for(B=M=0;B<r.npart_l;++B)for(n=0;n<r.numlines_l[B];++n)++M;for(M=0,n=0;n<r.npart_l;n++)A=S*(M+r.numlines_l[n]/2)/(1*Y.BLKSIZE),r.mld_cb_l[n]=O(A),M+=r.numlines_l[n];for(;n<Y.CBANDS;++n)r.mld_cb_l[n]=1;for(M=0,n=0;n<r.npart_s;n++)A=S*(M+r.numlines_s[n]/2)/(1*Y.BLKSIZE_s),r.mld_cb_s[n]=O(A),M+=r.numlines_s[n];for(;n<Y.CBANDS;++n)r.mld_cb_s[n]=1;return 0},this.ATHformula=function(e,t){var a;switch(t.ATHtype){case 0:a=N(e,9);break;case 1:a=N(e,-1);break;case 2:a=N(e,0);break;case 3:a=N(e,1)+6;break;case 4:a=N(e,t.ATHcurve);break;default:a=N(e,0)}return a}}function Q(){var e,t,n,s,r,o=this;Q.V9=410,Q.V8=420,Q.V7=430,Q.V6=440,Q.V5=450,Q.V4=460,Q.V3=470,Q.V2=480,Q.V1=490,Q.V0=500,Q.R3MIX=1e3,Q.STANDARD=1001,Q.EXTREME=1002,Q.INSANE=1003,Q.STANDARD_FAST=1004,Q.EXTREME_FAST=1005,Q.MEDIUM=1006,Q.MEDIUM_FAST=1007,Q.LAME_MAXMP3BUFFER=147456;var l,f,u=new Z;function h(){this.mask_adjust=0,this.mask_adjust_short=0,this.bo_l_weight=i(Y.SBMAX_l),this.bo_s_weight=i(Y.SBMAX_s)}function p(){this.lowerlimit=0}function m(e,t){this.lowpass=t}function g(e,t){var a=[new m(8,2e3),new m(16,3700),new m(24,3900),new m(32,5500),new m(40,7e3),new m(48,7500),new m(56,1e4),new m(64,11e3),new m(80,13500),new m(96,15100),new m(112,15600),new m(128,17e3),new m(160,17500),new m(192,18600),new m(224,19400),new m(256,19700),new m(320,20500)],n=o.nearestBitrateFullIndex(t);e.lowerlimit=a[n].lowpass}function w(e){var t=Y.BLKSIZE+e.framesize-Y.FFTOFFSET;return t=Math.max(t,512+e.framesize-32)}function M(e,t,a,n,s,r){var i=o.enc.lame_encode_mp3_frame(e,t,a,n,s,r);return e.frameNum++,i}function R(){this.n_in=0,this.n_out=0}function y(e,t,n,s,r,i){var o=e.internal_flags;if(o.resample_ratio<.9999||o.resample_ratio>1.0001)a();else{i.n_out=Math.min(e.framesize,r),i.n_in=i.n_out;for(var l=0;l<i.n_out;++l)t[0][o.mf_size+l]=n[0][s+l],2==o.channels_out&&(t[1][o.mf_size+l]=n[1][s+l])}}this.enc=new Y,this.setModules=function(a,i,o,_,c,h,p,b,m){e=a,t=i,n=o,s=_,r=c,l=h,f=b,this.enc.setModules(t,u,s,l)},this.lame_init=function(){var e=new L;return 0!=function(e){var t;return e.class_id=4294479419,t=e.internal_flags=new W,e.mode=d.NOT_SET,e.original=1,e.in_samplerate=44100,e.num_channels=2,e.num_samples=-1,e.bWriteVbrTag=!0,e.quality=-1,e.short_blocks=null,t.subblock_gain=-1,e.lowpassfreq=0,e.highpassfreq=0,e.lowpasswidth=-1,e.highpasswidth=-1,e.VBR=v.vbr_off,e.VBR_q=4,e.ATHcurve=-1,e.VBR_mean_bitrate_kbps=128,e.VBR_min_bitrate_kbps=0,e.VBR_max_bitrate_kbps=0,e.VBR_hard_min=0,t.VBR_min_bitrate=1,t.VBR_max_bitrate=13,e.quant_comp=-1,e.quant_comp_short=-1,e.msfix=-1,t.resample_ratio=1,t.OldValue[0]=180,t.OldValue[1]=180,t.CurrentStep[0]=4,t.CurrentStep[1]=4,t.masking_lower=1,t.nsPsy.attackthre=-1,t.nsPsy.attackthre_s=-1,e.scale=-1,e.athaa_type=-1,e.ATHtype=-1,e.athaa_loudapprox=-1,e.athaa_sensitivity=0,e.useTemporal=null,e.interChRatio=-1,t.mf_samples_to_encode=Y.ENCDELAY+Y.POSTDELAY,e.encoder_padding=0,t.mf_size=Y.ENCDELAY-Y.MDCTDELAY,e.findReplayGain=!1,e.decode_on_the_fly=!1,t.decode_on_the_fly=!1,t.findReplayGain=!1,t.findPeakSample=!1,t.RadioGain=0,t.AudiophileGain=0,t.noclipGainChange=0,t.noclipScale=-1,e.preset=0,e.write_id3tag_automatic=!0,0}(e)?null:(e.lame_allocated_gfp=1,e)},this.nearestBitrateFullIndex=function(e){var t=[8,16,24,32,40,48,56,64,80,96,112,128,160,192,224,256,320],a=0,n=0,s=0,r=0;r=t[16],s=16,n=t[16],a=16;for(var i=0;i<16;i++)if(Math.max(e,t[i+1])!=e){r=t[i+1],s=i+1,n=t[i],a=i;break}return r-e>e-n?a:s},this.lame_init_params=function(e){var i=e.internal_flags;if(i.Class_ID=0,null==i.ATH&&(i.ATH=new C),null==i.PSY&&(i.PSY=new h),null==i.rgdata&&(i.rgdata=new I),i.channels_in=e.num_channels,1==i.channels_in&&(e.mode=d.MONO),i.channels_out=e.mode==d.MONO?1:2,i.mode_ext=Y.MPG_MD_MS_LR,e.mode==d.MONO&&(e.force_ms=!1),e.VBR==v.vbr_off&&128!=e.VBR_mean_bitrate_kbps&&0==e.brate&&(e.brate=e.VBR_mean_bitrate_kbps),e.VBR==v.vbr_off||e.VBR==v.vbr_mtrh||e.VBR==v.vbr_mt||(e.free_format=!1),e.VBR==v.vbr_off&&0==e.brate&&a(),e.VBR==v.vbr_off&&e.compression_ratio>0&&a(),0!=e.out_samplerate&&(e.out_samplerate<16e3?(e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,8),e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,64)):e.out_samplerate<32e3?(e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,8),e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,160)):(e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,32),e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,320))),0==e.lowpassfreq){var o=16e3;switch(e.VBR){case v.vbr_off:g(c=new p,e.brate),o=c.lowerlimit;break;case v.vbr_abr:var c;g(c=new p,e.VBR_mean_bitrate_kbps),o=c.lowerlimit;break;case v.vbr_rh:a();default:a()}e.mode!=d.MONO||e.VBR!=v.vbr_off&&e.VBR!=v.vbr_abr||(o*=1.5),e.lowpassfreq=0|o}switch(0==e.out_samplerate&&a(),e.lowpassfreq=Math.min(20500,e.lowpassfreq),e.lowpassfreq=Math.min(e.out_samplerate/2,e.lowpassfreq),e.VBR==v.vbr_off&&(e.compression_ratio=16*e.out_samplerate*i.channels_out/(1e3*e.brate)),e.VBR==v.vbr_abr&&a(),e.bWriteVbrTag||(e.findReplayGain=!1,e.decode_on_the_fly=!1,i.findPeakSample=!1),i.findReplayGain=e.findReplayGain,i.decode_on_the_fly=e.decode_on_the_fly,i.decode_on_the_fly&&(i.findPeakSample=!0),i.findReplayGain&&a(),i.decode_on_the_fly&&!e.decode_only&&a(),i.mode_gr=e.out_samplerate<=24e3?1:2,e.framesize=576*i.mode_gr,e.encoder_delay=Y.ENCDELAY,i.resample_ratio=e.in_samplerate/e.out_samplerate,e.VBR){case v.vbr_mt:case v.vbr_rh:case v.vbr_mtrh:e.compression_ratio=[5.7,6.5,7.3,8.2,10,11.9,13,14,15,16.5][e.VBR_q];break;case v.vbr_abr:e.compression_ratio=16*e.out_samplerate*i.channels_out/(1e3*e.VBR_mean_bitrate_kbps);break;default:e.compression_ratio=16*e.out_samplerate*i.channels_out/(1e3*e.brate)}e.mode==d.NOT_SET&&(e.mode=d.JOINT_STEREO),e.highpassfreq>0?a():(i.highpass1=0,i.highpass2=0),e.lowpassfreq>0?(i.lowpass2=2*e.lowpassfreq,e.lowpasswidth>=0?a():i.lowpass1=2*e.lowpassfreq,i.lowpass1/=e.out_samplerate,i.lowpass2/=e.out_samplerate):a(),function(e){var t,n=e.internal_flags,s=32;if(n.lowpass1>0){for(var r=999,i=0;i<=31;i++)(_=i/31)>=n.lowpass2&&(s=Math.min(s,i)),n.lowpass1<_&&_<n.lowpass2&&(r=Math.min(r,i));n.lowpass1=999==r?(s-.75)/31:(r-.75)/31,n.lowpass2=s/31}for(n.highpass2>0&&a(),n.highpass2>0&&a(),i=0;i<32;i++){var o,l,_=i/31;n.highpass2>n.highpass1?a():o=1,l=n.lowpass2>n.lowpass1?(t=(_-n.lowpass1)/(n.lowpass2-n.lowpass1+1e-20))>1?0:t<=0?1:Math.cos(Math.PI/2*t):1,n.amp_filter[i]=o*l}}(e),i.samplerate_index=function(e,t){switch(e){case 44100:return t.version=1,0;case 48e3:return t.version=1,1;case 32e3:return t.version=1,2;case 22050:return t.version=0,0;case 24e3:return t.version=0,1;case 16e3:return t.version=0,2;case 11025:return t.version=0,0;case 12e3:return t.version=0,1;case 8e3:return t.version=0,2;default:return t.version=0,-1}}(e.out_samplerate,e),i.samplerate_index<0&&a(),e.VBR==v.vbr_off?e.free_format?i.bitrate_index=0:(e.brate=function(e,t,a){a<16e3&&(t=2);for(var n=T.bitrate_table[t][1],s=2;s<=14;s++)T.bitrate_table[t][s]>0&&Math.abs(T.bitrate_table[t][s]-e)<Math.abs(n-e)&&(n=T.bitrate_table[t][s]);return n}(e.brate,e.version,e.out_samplerate),i.bitrate_index=function(e,t,a){a<16e3&&(t=2);for(var n=0;n<=14;n++)if(T.bitrate_table[t][n]>0&&T.bitrate_table[t][n]==e)return n;return-1}(e.brate,e.version,e.out_samplerate),i.bitrate_index<=0&&a()):i.bitrate_index=1,e.analysis&&(e.bWriteVbrTag=!1),null!=i.pinfo&&(e.bWriteVbrTag=!1),t.init_bit_stream_w(i);for(var m,w=i.samplerate_index+3*e.version+6*(e.out_samplerate<16e3?1:0),S=0;S<Y.SBMAX_l+1;S++)i.scalefac_band.l[S]=s.sfBandIndex[w].l[S];for(S=0;S<Y.PSFB21+1;S++){var M=(i.scalefac_band.l[22]-i.scalefac_band.l[21])/Y.PSFB21,R=i.scalefac_band.l[21]+S*M;i.scalefac_band.psfb21[S]=R}for(i.scalefac_band.psfb21[Y.PSFB21]=576,S=0;S<Y.SBMAX_s+1;S++)i.scalefac_band.s[S]=s.sfBandIndex[w].s[S];for(S=0;S<Y.PSFB12+1;S++)M=(i.scalefac_band.s[13]-i.scalefac_band.s[12])/Y.PSFB12,R=i.scalefac_band.s[12]+S*M,i.scalefac_band.psfb12[S]=R;for(i.scalefac_band.psfb12[Y.PSFB12]=192,1==e.version?i.sideinfo_len=1==i.channels_out?21:36:i.sideinfo_len=1==i.channels_out?13:21,e.error_protection&&(i.sideinfo_len+=2),function(e){var t=e.internal_flags;e.frameNum=0,e.write_id3tag_automatic&&f.id3tag_write_v2(e),t.bitrate_stereoMode_Hist=_([16,5]),t.bitrate_blockType_Hist=_([16,6]),t.PeakSample=0,e.bWriteVbrTag&&l.InitVbrTag(e)}(e),i.Class_ID=4294479419,m=0;m<19;m++)i.nsPsy.pefirbuf[m]=700*i.mode_gr*i.channels_out;switch(-1==e.ATHtype&&(e.ATHtype=4),e.VBR){case v.vbr_mt:e.VBR=v.vbr_mtrh;case v.vbr_mtrh:null==e.useTemporal&&(e.useTemporal=!1),n.apply_preset(e,500-10*e.VBR_q,0),e.quality<0&&(e.quality=LAME_DEFAULT_QUALITY),e.quality<5&&(e.quality=0),e.quality>5&&(e.quality=5),i.PSY.mask_adjust=e.maskingadjust,i.PSY.mask_adjust_short=e.maskingadjust_short,e.experimentalY?i.sfb21_extra=!1:i.sfb21_extra=e.out_samplerate>44e3,i.iteration_loop=new VBRNewIterationLoop(r);break;case v.vbr_rh:n.apply_preset(e,500-10*e.VBR_q,0),i.PSY.mask_adjust=e.maskingadjust,i.PSY.mask_adjust_short=e.maskingadjust_short,e.experimentalY?i.sfb21_extra=!1:i.sfb21_extra=e.out_samplerate>44e3,e.quality>6&&(e.quality=6),e.quality<0&&(e.quality=LAME_DEFAULT_QUALITY),i.iteration_loop=new VBROldIterationLoop(r);break;default:var y;i.sfb21_extra=!1,e.quality<0&&(e.quality=LAME_DEFAULT_QUALITY),(y=e.VBR)==v.vbr_off&&(e.VBR_mean_bitrate_kbps=e.brate),n.apply_preset(e,e.VBR_mean_bitrate_kbps,0),e.VBR=y,i.PSY.mask_adjust=e.maskingadjust,i.PSY.mask_adjust_short=e.maskingadjust_short,y==v.vbr_off?i.iteration_loop=new P(r):a()}return e.VBR!=v.vbr_off&&a(),e.tune&&a(),function(e){var t=e.internal_flags;switch(e.quality){default:case 9:t.psymodel=0,t.noise_shaping=0,t.noise_shaping_amp=0,t.noise_shaping_stop=0,t.use_best_huffman=0,t.full_outer_loop=0;break;case 8:e.quality=7;case 7:t.psymodel=1,t.noise_shaping=0,t.noise_shaping_amp=0,t.noise_shaping_stop=0,t.use_best_huffman=0,t.full_outer_loop=0;break;case 6:case 5:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),t.noise_shaping_amp=0,t.noise_shaping_stop=0,-1==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=0,t.full_outer_loop=0;break;case 4:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),t.noise_shaping_amp=0,t.noise_shaping_stop=0,-1==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=1,t.full_outer_loop=0;break;case 3:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),t.noise_shaping_amp=1,t.noise_shaping_stop=1,-1==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=1,t.full_outer_loop=0;break;case 2:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),0==t.substep_shaping&&(t.substep_shaping=2),t.noise_shaping_amp=1,t.noise_shaping_stop=1,-1==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=1,t.full_outer_loop=0;break;case 1:case 0:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),0==t.substep_shaping&&(t.substep_shaping=2),t.noise_shaping_amp=2,t.noise_shaping_stop=1,-1==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=1,t.full_outer_loop=0}}(e),e.athaa_type<0?i.ATH.useAdjust=3:i.ATH.useAdjust=e.athaa_type,i.ATH.aaSensitivityP=Math.pow(10,e.athaa_sensitivity/-10),null==e.short_blocks&&(e.short_blocks=b.short_block_allowed),e.short_blocks!=b.short_block_allowed||e.mode!=d.JOINT_STEREO&&e.mode!=d.STEREO||(e.short_blocks=b.short_block_coupled),e.quant_comp<0&&(e.quant_comp=1),e.quant_comp_short<0&&(e.quant_comp_short=0),e.msfix<0&&(e.msfix=0),e.exp_nspsytune=1|e.exp_nspsytune,e.internal_flags.nsPsy.attackthre<0&&(e.internal_flags.nsPsy.attackthre=Z.NSATTACKTHRE),e.internal_flags.nsPsy.attackthre_s<0&&(e.internal_flags.nsPsy.attackthre_s=Z.NSATTACKTHRE_S),e.scale<0&&(e.scale=1),e.ATHtype<0&&(e.ATHtype=4),e.ATHcurve<0&&(e.ATHcurve=4),e.athaa_loudapprox<0&&(e.athaa_loudapprox=2),e.interChRatio<0&&(e.interChRatio=0),null==e.useTemporal&&(e.useTemporal=!0),i.slot_lag=i.frac_SpF=0,e.VBR==v.vbr_off&&(i.slot_lag=i.frac_SpF=72e3*(e.version+1)*e.brate%e.out_samplerate|0),s.iteration_init(e),u.psymodel_init(e),0},this.lame_encode_flush=function(e,n,s,r){var i,o,l,_,f=e.internal_flags,u=c([2,1152]),h=0,p=f.mf_samples_to_encode-Y.POSTDELAY,b=w(e);if(f.mf_samples_to_encode<1)return 0;for(i=0,e.in_samplerate!=e.out_samplerate&&a(),(l=e.framesize-p%e.framesize)<576&&(l+=e.framesize),e.encoder_padding=l,_=(p+l)/e.framesize;_>0&&h>=0;){var m=b-f.mf_size,v=e.frameNum;m*=e.in_samplerate,(m/=e.out_samplerate)>1152&&(m=1152),m<1&&(m=1),o=r-i,0==r&&(o=0),s+=h=this.lame_encode_buffer(e,u[0],u[1],m,n,s,o),i+=h,_-=v!=e.frameNum?1:0}return f.mf_samples_to_encode=0,h<0?h:(o=r-i,0==r&&(o=0),t.flush_bitstream(e),(h=t.copy_buffer(f,n,s,o,1))<0?h:(s+=h,o=r-(i+=h),0==r&&(o=0),e.write_id3tag_automatic&&a(),i))},this.lame_encode_buffer=function(n,s,r,o,l,_,c){var f=n.internal_flags,u=[null,null];if(4294479419!=f.Class_ID)return-3;if(0==o)return 0;!function(e,t){(null==e.in_buffer_0||e.in_buffer_nsamples<t)&&(e.in_buffer_0=i(t),e.in_buffer_1=i(t),e.in_buffer_nsamples=t)}(f,o),u[0]=f.in_buffer_0,u[1]=f.in_buffer_1;for(var h=0;h<o;h++)u[0][h]=s[h],f.channels_in>1&&(u[1][h]=r[h]);return function(n,s,r,i,o,l,_){var c,f,u,h,p,b=n.internal_flags,m=0,v=[null,null],d=[null,null];if(4294479419!=b.Class_ID)return-3;if(0==i)return 0;if((p=t.copy_buffer(b,o,l,_,0))<0)return p;if(l+=p,m+=p,d[0]=s,d[1]=r,k.NEQ(n.scale,0)&&k.NEQ(n.scale,1))for(f=0;f<i;++f)d[0][f]*=n.scale,2==b.channels_out&&(d[1][f]*=n.scale);if(k.NEQ(n.scale_left,0)&&k.NEQ(n.scale_left,1))for(f=0;f<i;++f)d[0][f]*=n.scale_left;if(k.NEQ(n.scale_right,0)&&k.NEQ(n.scale_right,1))for(f=0;f<i;++f)d[1][f]*=n.scale_right;2==n.num_channels&&1==b.channels_out&&a(),h=w(n),v[0]=b.mfbuf[0],v[1]=b.mfbuf[1];for(var g=0;i>0;){var A,T,x=[null,null];x[0]=d[0],x[1]=d[1];var B=new R;if(y(n,v,x,g,i,B),A=B.n_in,T=B.n_out,b.findReplayGain&&!b.decode_on_the_fly&&e.AnalyzeSamples(b.rgdata,v[0],b.mf_size,v[1],b.mf_size,T,b.channels_out)==S.GAIN_ANALYSIS_ERROR)return-6;if(i-=A,g+=A,b.channels_out,b.mf_size+=T,b.mf_samples_to_encode<1&&a(),b.mf_samples_to_encode+=T,b.mf_size>=h){var E=_-m;if(0==_&&(E=0),(c=M(n,v[0],v[1],o,l,E))<0)return c;for(l+=c,m+=c,b.mf_size-=n.framesize,b.mf_samples_to_encode-=n.framesize,u=0;u<b.channels_out;u++)for(f=0;f<b.mf_size;f++)v[u][f]=v[u][f+n.framesize]}}return m}(n,u[0],u[1],o,l,_,c)}}function $(){this.setModules=function(e,t){}}function J(){this.setModules=function(e,t,a){}}function ee(){}function te(){this.setModules=function(e,t){}}V.SFBMAX=3*Y.SBMAX_s,Y.ENCDELAY=576,Y.POSTDELAY=1152,Y.MDCTDELAY=48,Y.FFTOFFSET=224+Y.MDCTDELAY,Y.DECDELAY=528,Y.SBLIMIT=32,Y.CBANDS=64,Y.SBPSY_l=21,Y.SBPSY_s=12,Y.SBMAX_l=22,Y.SBMAX_s=13,Y.PSFB21=6,Y.PSFB12=6,Y.BLKSIZE=1024,Y.HBLKSIZE=Y.BLKSIZE/2+1,Y.BLKSIZE_s=256,Y.HBLKSIZE_s=Y.BLKSIZE_s/2+1,Y.NORM_TYPE=0,Y.START_TYPE=1,Y.SHORT_TYPE=2,Y.STOP_TYPE=3,Y.MPG_MD_LR_LR=0,Y.MPG_MD_LR_I=1,Y.MPG_MD_MS_LR=2,Y.MPG_MD_MS_I=3,Y.fircoef=[-.1039435,-.1892065,5*-.0432472,-.155915,3898045e-23,.0467745*5,.50455,.756825,.187098*5],W.MFSIZE=3456+Y.ENCDELAY-Y.MDCTDELAY,W.MAX_HEADER_BUF=256,W.MAX_BITS_PER_CHANNEL=4095,W.MAX_BITS_PER_GRANULE=7680,W.BPC=320,V.SFBMAX=3*Y.SBMAX_s,t.Mp3Encoder=function(e,t,s){1!=e&&a("fix cc: only supports mono");var r=new Q,i=new $,o=new S,l=new k,_=new M,c=new O,f=new F,u=new y,h=new g,p=new te,b=new R,m=new w,v=new J,A=new ee;r.setModules(o,l,_,c,f,u,h,p,A),l.setModules(o,A,h,u),p.setModules(l,h),_.setModules(r),f.setModules(l,b,c,m),c.setModules(m,b,r.enc.psy),b.setModules(l),m.setModules(c),u.setModules(r,l,h),i.setModules(v,A),v.setModules(h,p,_);var T=r.lame_init();T.num_channels=e,T.in_samplerate=t,T.out_samplerate=t,T.brate=s,T.mode=d.STEREO,T.quality=3,T.bWriteVbrTag=!1,T.disable_reservoir=!0,T.write_id3tag_automatic=!1,r.lame_init_params(T);var x=1152,B=0|1.25*x+7200,E=n(B);this.encodeBuffer=function(t,a){1==e&&(a=t),t.length>x&&(x=t.length,E=n(B=0|1.25*x+7200));var s=r.lame_encode_buffer(T,t,a,t.length,E,0,B);return new Int8Array(E.subarray(0,s))},this.flush=function(){var e=r.lame_encode_flush(T,E,0,B);return new Int8Array(E.subarray(0,e))}}}t(),e.lamejs=t}(("object"==typeof window&&window.document?window:Object).Recorder)},function(e,t){var a,n,s;a="object"==typeof window&&!!window.document,n=(a?window:Object).Recorder,s=n.i18n,function(e,t,a,n){"use strict";var s=function(e){return new r(e)},r=function(e){var t={scale:2,speed:9,phase:21.8,fps:20,keep:!0,lineWidth:3,linear1:[0,"rgba(150,96,238,1)",.2,"rgba(170,79,249,1)",1,"rgba(53,199,253,1)"],linear2:[0,"rgba(209,130,255,0.6)",1,"rgba(53,199,255,0.6)"],linearBg:[0,"rgba(255,255,255,0.2)",1,"rgba(54,197,252,0.2)"]};for(var s in e)t[s]=e[s];if(this.set=e=t,e.compatibleCanvas)var r=this.canvas=e.compatibleCanvas;else{if(!n)throw new Error(a.G("NonBrowser-1",["WaveView"]));var i=e.elem;i&&("string"==typeof i?i=document.querySelector(i):i.length&&(i=i[0])),i&&(e.width=i.offsetWidth,e.height=i.offsetHeight);var o=this.elem=document.createElement("div");o.style.fontSize=0,o.innerHTML='<canvas style="width:100%;height:100%;"/>',r=this.canvas=o.querySelector("canvas"),i&&(i.innerHTML="",i.appendChild(o))}var l=e.scale,_=e.width*l,c=e.height*l;if(!_||!c)throw new Error(a.G("IllegalArgs-1",["WaveView width=0 height=0"]));r.width=_,r.height=c;var f=this.ctx=r.getContext("2d");this.linear1=this.genLinear(f,_,e.linear1),this.linear2=this.genLinear(f,_,e.linear2),this.linearBg=this.genLinear(f,c,e.linearBg,!0),this._phase=0};r.prototype=s.prototype={genLinear:function(e,t,a,n){for(var s=e.createLinearGradient(0,0,n?0:t,n?t:0),r=0;r<a.length;)s.addColorStop(a[r++],a[r++]);return s},genPath:function(e,t,a){for(var n=[],s=this.set,r=s.scale,i=s.width*r,o=s.height*r/2,l=0;l<=i;l+=r){var _=(1+Math.cos(Math.PI+l/i*2*Math.PI))/2*o*t*Math.sin(2*Math.PI*(l/i)*e+a)+o;n.push(_)}return n},input:function(e,t,a){this.sampleRate=a,this.pcmData=e,this.pcmPos=0,this.inputTime=Date.now(),this.schedule()},schedule:function(){var t=this,a=t.set,n=Math.floor(1e3/a.fps);t.timer||(t.timer=setInterval((function(){t.schedule()}),n));var s=Date.now();if(!(s-(t.drawTime||0)<n)){t.drawTime=s;for(var r=t.sampleRate/a.fps,i=t.pcmData,o=t.pcmPos,l=Math.max(0,Math.min(r,i.length-o)),_=0,c=0;c<l;c++,o++)_+=Math.abs(i[o]);t.pcmPos=o,!l&&a.keep||t.draw(e.PowerLevel(_,l)),!l&&s-t.inputTime>1300&&(clearInterval(t.timer),t.timer=0)}},draw:function(e){var t=this.set,a=this.ctx,n=t.scale,s=t.width*n,r=t.height*n,i=t.speed/t.fps,o=this._phase-=i,l=o+i*t.phase,_=e/100,c=this.genPath(2,_,o),f=this.genPath(1.8,_,l);a.clearRect(0,0,s,r),a.beginPath();for(var u=0,h=0;h<=s;u++,h+=n)0==h?a.moveTo(h,c[u]):a.lineTo(h,c[u]);for(u--,h=s-1;h>=0;u--,h-=n)a.lineTo(h,f[u]);a.closePath(),a.fillStyle=this.linearBg,a.fill(),this.drawPath(f,this.linear2),this.drawPath(c,this.linear1)},drawPath:function(e,t){var a=this.set,n=this.ctx,s=a.scale,r=a.width*s;n.beginPath();for(var i=0,o=0;o<=r;i++,o+=s)0==o?n.moveTo(o,e[i]):n.lineTo(o,e[i]);n.lineWidth=a.lineWidth*s,n.strokeStyle=t,n.stroke()}},e.WaveView=s}(n,0,s.$T,a)}]);