<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
<link rel="shortcut icon" type="image/png" href="icon.png">

<title>iframe测试加载器</title>
</head>

<body style="background:#fff">

<div class="main">
	<div class="box"></div>
	<div class="reclog"></div>
</div>
<script>
function reclog(s,color){
	var now=new Date();
	var t=("0"+now.getHours()).substr(-2)
		+":"+("0"+now.getMinutes()).substr(-2)
		+":"+("0"+now.getSeconds()).substr(-2);
	var div=document.createElement("div");
	var elem=document.querySelector(".reclog");
	elem.insertBefore(div,elem.firstChild);
	div.innerHTML='<div style="color:'+(!color?"":color==1?"red":color==2?"#0b1":color)+'">['+t+']'+s+'</div>';
};
window.onerror=function(message, url, lineNo, columnNo, error){
	//https://www.cnblogs.com/xianyulaodi/p/6201829.html
	reclog('<span style="color:red">【Uncaught Error】'+message+'<pre>'+"at:"+lineNo+":"+columnNo+" url:"+url+"\n"+(error&&error.stack||"不能获得错误堆栈")+'</pre></span>');
};

reclog("IFrame测试，顶层页面URL: "+location.href.replace(/./g,function(a){return "&#"+a.charCodeAt(0)+";"})+'');
reclog('提示：跨域时未设置iframe相应策略H5录音权限永远是拒绝的（<span class="allowSetTips"></span>）<button onclick="setAllow(1)">重新打开网页并设置H5策略</button> <button onclick="setAllow()">清除H5策略</button>');
</script>

<script>
var viewIframe=function(url,allow){
	document.querySelector(".box").innerHTML='<iframe src="'+url+'" '+(allow?'allow="camera;microphone"':'')+' class="iframe" style="width:98%;height:85vh; border:4px solid #0B1"></iframe>';
	document.querySelector(".allowSetTips").innerHTML='<span style="color:'+(allow?"#0b1":"#f00")+'">iframe'+(allow?"已设置":"未设置")+' allow="camera;microphone"</span>';
};

var iframeUrl=decodeURIComponent((/[?&#]iframeUrl=((https?(:|%3A)|\/)[^&#]+)/i.exec(location.href)||[])[1]||"");
console.log("iframeUrl: "+iframeUrl);
if(!iframeUrl){
	reclog('提供的iframeUrl参数无效',1);	
}else{
	if(iframeUrl[0]=="/"){
		if(/gitee/.test(location.href)){
			iframeUrl="https://xiangyuecn.github.io/Recorder"+iframeUrl;
		}else{
			iframeUrl="https://xiangyuecn.gitee.io/recorder"+iframeUrl;
		}
	}
	viewIframe(iframeUrl,true);
}

var setAllow=function(set){
	if(set){
		iframeUrl=prompt("iframe地址（可以跨域）",iframeUrl)||iframeUrl;
	};
	
	viewIframe(iframeUrl,set);
};
</script>

<script>
if(/mobile/i.test(navigator.userAgent)){
	//移动端加载控制台组件
	var elem=document.createElement("script");
	elem.setAttribute("type","text/javascript");
	elem.setAttribute("src","ztest-vconsole.js");
	document.body.appendChild(elem);
	elem.onload=function(){
		new VConsole();
	};
};
</script>
</body>
</html>