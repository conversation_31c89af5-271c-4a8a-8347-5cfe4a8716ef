<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="BYZ-38-t0r">
    <device id="retina6_1" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Main View-->
        <scene sceneID="tne-QT-ifu">
            <objects>
                <viewController id="BYZ-38-t0r" customClass="MainView" customModule="recorder" customModuleProvider="target" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="J6Y-SV-BAx"/>
                        <viewControllerLayoutGuide type="bottom" id="7v9-m6-cJz"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="8bC-Xf-vdC">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="jgm-hi-xg7">
                                <rect key="frame" x="0.0" y="44" width="414" height="80"/>
                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="80" id="ctb-1v-xbW"/>
                                </constraints>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                            </textView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="v6B-l9-iVV">
                                <rect key="frame" x="0.0" y="132" width="414" height="730"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="jgm-hi-xg7" firstAttribute="leading" secondItem="8bC-Xf-vdC" secondAttribute="leading" id="239-rt-CJJ"/>
                            <constraint firstItem="v6B-l9-iVV" firstAttribute="bottom" secondItem="7v9-m6-cJz" secondAttribute="top" id="4bF-sI-PfP"/>
                            <constraint firstItem="jgm-hi-xg7" firstAttribute="leading" secondItem="v6B-l9-iVV" secondAttribute="leading" id="Y2N-PR-Fzg"/>
                            <constraint firstAttribute="trailing" secondItem="jgm-hi-xg7" secondAttribute="trailing" id="f2T-sE-nDR"/>
                            <constraint firstItem="jgm-hi-xg7" firstAttribute="top" secondItem="J6Y-SV-BAx" secondAttribute="bottom" id="mhu-Yl-DfU"/>
                            <constraint firstItem="jgm-hi-xg7" firstAttribute="trailing" secondItem="v6B-l9-iVV" secondAttribute="trailing" id="snd-iE-ekH"/>
                            <constraint firstItem="v6B-l9-iVV" firstAttribute="top" secondItem="jgm-hi-xg7" secondAttribute="bottom" constant="8" symbolic="YES" id="xoE-Nr-SRY"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="logs" destination="jgm-hi-xg7" id="3ce-r2-I0S"/>
                        <outlet property="webViewBox" destination="v6B-l9-iVV" id="jwE-un-Day"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="dkx-z0-nzr" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="37.681159420289859" y="18.75"/>
        </scene>
    </scenes>
</document>
