/*
录音
https://github.com/xiangyuecn/Recorder
src: engine/pcm.js
*/
!function(e){var t="object"==typeof window&&!!window.document,r=(t?window:Object).Recorder,n=r.i18n;!function(p,e,a,t){"use strict";p.prototype.enc_pcm={stable:!0,fast:!0,getTestMsg:function(){return a("fWsN::pcm为未封装的原始音频数据，pcm音频文件无法直接播放，可用Recorder.pcm2wav()转码成wav播放；支持位数8位、16位（填在比特率里面），采样率取值无限制")}};var f=function(e){var t=e.bitRate,r=8==t?8:16;t!=r&&p.CLog(a("uMUJ::PCM Info: 不支持{1}位，已更新成{2}位",0,t,r),3),e.bitRate=r};p.prototype.pcm=function(e,t,r){var n=this.set;f(n);var o=m(e,n.bitRate);t(o.buffer,"audio/pcm")};var m=function(e,t){if(8==t)for(var r=e.length,n=new Uint8Array(r),o=0;o<r;o++){var a=128+(e[o]>>8);n[o]=a}else{e=new Int16Array(e);var n=new Uint8Array(e.buffer)}return n};p.pcm2wav=function(e,f,m){e.blob||(e={blob:e});var t=e.blob,c=e.sampleRate||16e3,i=e.bitRate||16;if(e.sampleRate&&e.bitRate||p.CLog(a("KmRz::pcm2wav必须提供sampleRate和bitRate"),3),p.prototype.wav){var r=function(e,t){var r;if(8==i){var n=new Uint8Array(e);r=new Int16Array(n.length);for(var o=0;o<n.length;o++)r[o]=n[o]-128<<8}else r=new Int16Array(e);var a=p({type:"wav",sampleRate:c,bitRate:i});t&&(a.dataType="arraybuffer"),a.mock(r,c).stop(function(e,t,r){f(e,t,r)},m)};if(t instanceof ArrayBuffer)r(t,1);else{var n=new FileReader;n.onloadend=function(){r(n.result)},n.readAsArrayBuffer(t)}}else m(a.G("NeedImport-2",["pcm2wav","src/engine/wav.js"]))},p.prototype.pcm_envCheck=function(e,t){return""},p.prototype.pcm_start=function(e){return f(e),{set:e,memory:new Uint8Array(5e5),mOffset:0}};p.prototype.pcm_stop=function(e){e&&e.memory&&(e.memory=null)},p.prototype.pcm_encode=function(e,t){if(e&&e.memory){var r=e.set,n=m(t,r.bitRate);r.takeoffEncodeChunk?r.takeoffEncodeChunk(n):function(e,t){var r=t.length;if(e.mOffset+r>e.memory.length){var n=new Uint8Array(e.memory.length+Math.max(5e5,r));n.set(e.memory.subarray(0,e.mOffset)),e.memory=n}e.memory.set(t,e.mOffset),e.mOffset+=r}(e,n)}},p.prototype.pcm_complete=function(e,t,r,n){if(e&&e.memory){n&&this.pcm_stop(e);var o=e.memory.buffer.slice(0,e.mOffset);t(o,"audio/pcm")}else r(a("sDkA::pcm编码器未start"))}}(r,0,n.$T)}();