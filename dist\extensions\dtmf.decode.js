/*
录音
https://github.com/xiangyuecn/Recorder
src: extensions/dtmf.decode.js
*/
!function(e){var o="object"==typeof window&&!!window.document,r=(o?window:Object).Recorder,t=r.i18n;!function(P,e,Q,o){"use strict";P.DTMF_Decode=function(e,o,r){r||(r={});var t=r.lastIs||"",a=null==r.lastCheckCount?99:r.lastCheckCount,n=r.prevIs||"",s=r.totalLen||0,l=r.pcm,c=r.checkFactor||0,u=r.debug,d=[];if(!P.LibFFT)throw new Error(Q.G("NeedImport-2",["DTMF_Decode","src/extensions/lib.fft.js"]));var h=256,v=P.LibFFT(h),i=c||3,f=s,g=o/4e3,b=Math.floor(e.length/g);s+=b;var p=0;l&&l.length>h&&(b+=p=64*(i+1),f-=p);var F=new Int16Array(b);p&&F.set(l.subarray(l.length-p));for(var k=0;k<e.length;p++,k+=g)F[p]=e[Math.round(k)];e=F;for(var w=(o=4e3)/h,M=0;M+h<=e.length;M+=64){for(var F=e.subarray(M,M+h),y=v.transform(F),m=[],C=0,I=0,D=0,T=0,x=0,L=0,j=0,A=0,_=0;_<y.length;_++){var B=y[_],E=Math.log(B);m.push(E);var G=(_+1)*w;20<E&&(C<B&&G<1050?(C=B,I=E,D=G,T=_):x<B&&1050<G&&(x=B,L=E,j=G,A=_))}var N=-1,O=-1;if(600<D&&j<1700&&Math.abs(I-L)<2.5){for(var R=1,$=I,q=0,_=T;_<A;_++){var G=m[_];G&&G<$&&($=G,q=_)}for(var z=.5*(I-$),H=I,_=T;R&&_<q;_++){var G=m[_];G<=H?H=G:z<G-H&&(R=0)}for(var J=$,_=q;R&&_<A;_++){var G=m[_];J<=G?J=G:z<J-G&&(R=0)}R&&(N=V(D,S[0],w),O=V(j,S[1],w))}var K="";0<=N&&0<=O?(K=U[N][O],u&&console.log(K,Math.round((f+M)/o*1e3),I.toFixed(2),L.toFixed(2),Math.abs(I-L).toFixed(2)),t?t.key==K?a++:(K="",a=t.old+a):(n&&n.old2&&n.key==K&&f+M-n.start<100*o/1e3&&(a=(t=n).old2+1,u&&console.warn("接续了开叉的信号"+a)),t||(3<=a?(t={key:K,old:a,old2:a,start:f+M,pcms:[],use:0},a=1):(K="",a=0)))):t&&(t.old2=a,a=t.old+a),K?(u&&t.pcms.push(F),i<=a&&!t.use&&(t.use=1,d.push({key:K,time:Math.round(t.start/o*1e3)})),t.use&&(u&&console.log(K+"有效按键",t),t.old=0,t.old2=0,a=0)):(t&&(u&&console.log(t),n=t),t="",a++)}return{keys:d,lastIs:t,lastCheckCount:a,prevIs:n,totalLen:s,pcm:e,checkFactor:c,debug:u}};var S=[[697,770,852,941],[1209,1336,1477,1633]],U=[["1","2","3","A"],["4","5","6","B"],["7","8","9","C"],["*","0","#","D"]],V=function(e,o,r){for(var t=-1,a=1e3,n=0;n<o.length;n++){var s=Math.abs(o[n]-e);s<a&&(a=s)<2*r&&(t=n)}return t}}(r,0,t.$T)}();