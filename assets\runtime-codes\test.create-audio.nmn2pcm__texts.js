/********预定义简谱*********/
(function(){
var Export=window["test.create-audio.nmn2pcm__texts"]=[];


/** 思君黯然，完整 **/
var txt1="",txt2="";
txt1+=/*"6,3 6,7 1',3' 7,1'|6,3 6,7 1',3' 7,1'|"*/"3'- 2',1' 7,1'|2'---|";
txt2+=/*"0---|0---|"*/"0---|0---|";

txt1+="1'- 7,6 5,7|6---|5---|6- 7,1' 7,6|";
txt2+="0---|0---|0---|0---|";

txt1+="5---|4- 5,6 5,6|3---|6- 7,1' 7,6|";
txt2+="0---|0---|0---|0---|";

txt1+="7---|5- 7,6 6,5|6-- 1',2'|3'* 3'_ 3',2' 2',1'|";
txt2+="0---|0---|0---|0---|";

txt1+="2'-- 2',3'|4'* 4'_ 4',3' 2',4'|3'-- 1',2'|3'* 3'_ 3',4' 5',3'|";
txt2+="0---|0---|0---|0---|";

txt1+="2'-- 1',7|1'* 7_ 6,1' 7,5|6---|0 1',7 1'* 7_|";
txt2+="0---|0---|R[0.3] 0 3,2 3 5|6---|";

txt1+="6,1' 7,5|6------- 0 0|";
txt2+="0 0 0 3,2|3 5 6--- 0 0 R|";

Export.push({
	name:"思君黯然 - 天龙八部插曲"
	,url:"http://www.ktvc8.com/article/article_959050_1.html"
	,meterDuration:550
	,timbre:2
	,texts:[txt1, txt2]
});


/** 沧海一声笑 **/
var txt1="6*,5_ 3,2 1-|3*,2_ 1,6. 5.-|5.*,6._ 5.*,6._ 1*,2_ 3,5|6*,5_ 3__,2__ 1_ 2-|";
txt1+="6_ 6__,5__ 3,2 1-|3*,2_ 1,6. 5.-|5.*,6._ 5.*,6._ 1*,2_ 3,5|6*,5_ 3__,2__ 1_ 2-|";
txt1+="6_ 6__,5__ 3,2 1-|3*,2_ 1,6. 5.-|5.*,6._ 5.*,6._ 1*,2_ 3,5|6*,5_ 3__,2__ 1_ 2-|";
txt1+="6_ 6__,5__ 3,2 1-|3*,2_ 1,6. 5.-|5.*,6._ 5.*,6._ 1*,2_ 3,5|6*,5_ 3,2 1-|";

Export.push({
	name:"沧海一声笑 - 笑傲江湖主题曲"
	,url:"https://www.qinyipu.com/jianpu/liuxinggequ/2771.html"
	,meterDuration:600
	,timbre:3
	,texts:[txt1]
});


/** 小猪佩奇，片头 **/
var txt1="",txt2="",txt3="";
//(1)
txt1+="A(2) 0 0|0 0|0,5,0,5 0,5,0,5|0,5,0,5 0,5,0,5|";
txt2+="5',3'_,1'_ 2',5|5,7,2',4' 3',1'|0,3,0,3 0,3,0,3|0,4,0,4 0,4,0,4|";
txt3+="A(2)[0.1] 1.,5.,3.,5. 7..,5.,2.,5.|7..,5.,2.,5. 1.,5.,3.,5.|1.,5.. 1.,5..|2.,5.. 2.,5..|";
//(5)
txt1+="0 0|0 0|0 0 0|0 0|";
txt2+="5',3'_,1'_ 2',5|5,7,2',4' 3',1'|1',2',4',4' 5',6',1',1' 2',4',4',2'|5',3'_,1'_ 2',5'|";
txt3+="1.,5.,3.,5. 7..,5.,2.,5.|7..,5.,2.,5. 1.,5.,3.,5.|1.,5.,3.,5. 1.,5.,3.,5. 7..,5.,2.,5.|1.,5.,3.,5. 7..,5.,2.,5.|";
//(9)
txt1+="0 0|0 0|0 0|0 3',0|";
txt2+="5',7',2',4' 3',1'|1',2',4',4' 5',6',1',1'|5',3'_,1'_ 2',5'|5',7',2',4' 1',0|";
txt3+="7..,5.,2.,5. 1.,5.,3.,5.|1.,5.,3.,5. 1.,5.,3.,5.|1.,5.,3.,5. 7..,5.,2.,5.|7..,5.,2.,5. 1.,0|";
//(13)
txt1+="0 0|0 0|0 0|0 0|0 0 0 0|";
txt2+="5' 3',1'|2'|5--|5,7 2',4'|3'_ 1'-- 0_|";
txt3+="0 0|0 0|0 0|0 0|0 0 0 0|";

Export.push({
	name:"小猪佩奇 - 片头曲"
	,url:"https://www.qinyipu.com/jianpu/liuxinggequ/31175.html"
	,meterDuration:550
	,timbre:4
	,texts:[txt1, txt2, txt3]
});


/** 葫芦娃，完整 **/
var txt1="";
txt1+="1 1 3-|1_ 1 3*-|6 6* 5_ 6|5_ 1 3*-|";
txt1+="1'_ 6 5_ 6-|5_ 1 2*-|7*- 5_ 3|5---|";
txt1+="1' 6*,6_ 5,5 6,6|0_ 5 1_ 3-|1' 6*,6_ 5,5 6,6|0_ 5 1_ 2-|";
txt1+="3*- 1_ 6.|1---|3_ 5 6*-|3_ 5 6*-|1'*- 7_ 5|6---|";
txt1+="6 6,5 6,7 1'|7,5 5,3 5-|4 4,3 4,6 5,2|3---|";
txt1+="6 6,5 6,7 1'|7,1' 2',3' 2'-|2'* 2'_ 1' 2'|3',3' 2',2'_ 3'**  |";

Export.push({
	name:"葫芦娃 - 葫芦兄弟主题曲"
	,url:"https://www.qinyipu.com/jianpu/jianpudaquan/218255.html"
	,meterDuration:450
	,timbre:2.5
	,texts:[txt1]
});


/** 云宫迅音，完整 **/
var txt1="";
txt1+="6.,6. 3.,5. 1,6. 3.,5.|6.,6. 3.,5. 1,6. 3.,5.|6.,6. 3.,5. 1,6. 3.,5.|";
txt1+="6.,6. 3.,5. 1,6. 3.,5.|6.,6. 4.,5. 1,6. 4.,5.|6.,6. 4.,5. 1,6. 4.,5.|6.,6. 4.,5. 1,6. 4.,5.|";
txt1+="6.,6. 4.,5. 1,6. 4.,5.|6.,6. 4.,5. 1,3 6,1'|6 3 0,3 6,1'|6 0 0,3 6,1'|";
txt1+="2' 0 0,4 6,1'|2' 6 1' 2'|4' 1' 2' 4'|3'---|3- 6* 7_|";
txt1+="1' 6 4'* 2'_|3'-------|3- 6* 7_|1' 6 4'* 3'_|2'-------|";
txt1+="0 3' 2'* 1'_|3'---|0 2' 1'* 7_|2'---|0 4' 3' 2'|5'-- 4'|";
txt1+="3'-- 2'|3'---|3- 6* 7_|1' 6 4'* 2'_|3'-------|3- 6* 7_|";
txt1+="1' 6 4'* 3'_|2'-------|3- 6* 7_|1' 6 4' 3',2'|5'-------|";
txt1+="3- 6* 7_|1' 6 4' 3' 2'|5'-------|0 3' 2'* 1'_|3'---|";
txt1+="0 2' 1'* 7_|2'---|0 4' 3' 2'|5'-- 4'|3'-- 2'|3'---|";
txt1+="3- 6* 7_|1' 6 4'* 2'_|3'-------|3- 6* 7_|1' 6 4'* 3'_|2'-------|";

Export.push({
	name:"云宫迅音 - 西游记片头曲"
	,url:"https://www.qinyipu.com/jianpu/jianpudaquan/177628.html"
	,meterDuration:400
	,timbre:3
	,texts:[txt1]
});


/** 梁祝，完整 **/
var txt1="";
txt1+="0,5 3,2|1*- 2_ 7.,6.|5.*- 7_ 6,7|5*,6_ 4,3 2,3,4,3 5*,3_|";
txt1+="2,3,5,2 3,4,3,2 1* 5_|7.,2 6.,1 5.* 6__,1__|5.---|3 5*,6_ 1'*,2'_ 6_,1'_,5|";
txt1+="5*,1'_ 6',5',3',5' 2'-|2',2'_,3'_ 2'__ 7,6 5*,6_ 1',2'|3,1' 6,5,6,1' 5-|3',3'_,5'_ 7,2' 6__,1'__ 5*|";
txt1+="3*,5__,3_ 5*,6__,7,2' 6* 7__,6__ 5__,6__|1'*,2'_ 5',3' 2',3'_,2'_ 1',6_,5_|3 1' 6*,1'__,6,5 3,5,6,1'|5* 3__,5__ 2,3,2,1 7.,6.|";
txt1+="5* 3__,5__ 2,3,2,1 7.,6.|5* 3__,5__ 2,3,2,1 7.,6.|5. 6.,7. 2,7 6,5|2- 0,2' 7,6|";
txt1+="5- 0,7 6,4|3*,6_ 4,3 2_ 2' 7_|6*,2'_ 7,6 5_ 5' 4'_|3'*,5'_ 2',3' 6'-|";
txt1+="3',2',3' 5',3',5' 6',5',6' 7',6',7'|2'- 2' 3'|5'---|5'- 5'*,1'_ 6',5',3',5'|";
txt1+="2'- 3'-|3- 2*,3_ 5,6,1',2'|3',3'_,5'_ 7,2' 6__,1'__ 5*|3*,5__,3_ 5*,6_,7,2' 6* 7__,6__ 5__,6__|";
txt1+="1'*,2'_ 5',3' 2',3'_,2'_ 1',6_,5_|3 1' 6*,1'_,6,5 3,5,6,1'|5- 5',5'_,3'_ 2',3',5',2'|3'*,4'_ 3',2' 1'*,2'_,3',5' 2',1',7,6|";
txt1+="5* 6__,1'__ 5,6,4,3 2,1,2,3|5* 6__,1'__ 5,6,4,3 2,1,2,3|5 2,1 5.-|5' 6',1' 1'* 2'__,7__|";

Export.push({
	name:"梁祝 - 梁山伯与祝英台"
	,url:"http://www.ktvc8.com/article/article_659958_1.html"
	,meterDuration:650
	,timbre:2
	,texts:[txt1]
});


/** 世上只有妈妈好，完整 **/
var txt1="";
txt1+="6* 5_ 3 5|1' 6,5 6-|3 5,6 5 3,2|1,6. 5,3 2-|2* 3_ 5 5,6|";
txt1+="3 2 1-|5* 3_ 2,1 6.,1|5.---|";

Export.push({
	name:"世上只有妈妈好"
	,url:"https://www.qinyipu.com/jianpu/ertonggequ/2298.html"
	,meterDuration:600
	,timbre:2
	,waveType:"triangle"
	,texts:[txt1]
});


/** 一闪一闪亮晶晶，完整 **/
var txt1="";
txt1+="1 1 5 5|6 6 5-|4 4 3 3|2 2 1-|";
txt1+="5 5 4 4|3 3 2-|5 5 4 4|3 3 2-|";
txt1+="1 1 5 5|6 6 5-|4 4 3 3|2 2 1-|";

Export.push({
	name:"一闪一闪亮晶晶"
	,url:"https://www.qinyipu.com/jianpu/ertonggequ/2308.html"
	,meterDuration:400
	,timbre:5
	,waveType:"triangle"
	,texts:[txt1]
});


/** 两只老虎，完整 **/
var txt1="";
txt1+="1,2 3,1|1,2 3,1|3,4 5|3,4 5|5,6 5,4|";
txt1+="3 1|5,6 5,4|3 1|2 5.|1 0|2 5.|1 0|";

Export.push({
	name:"两只老虎"
	,url:"http://www.qupu123.com/shaoer/sizi/liangzhilaohu.html"
	,meterDuration:550
	,timbre:2
	,texts:[txt1]
});


/** 春天在哪里，完整 **/
var txt1="";
txt1+="3,3 3,1|5. 5.,0|3,3 3,1|3 0|5,5 3,1|";
txt1+="5.,5. 5.|6.,7. 1,3|2 0|3,3 3,1|5. 5.,0|";
txt1+="3,3 3,1|3 0|5,6 5,6|5,4 3,1|5.,0 3,0|2,1 0|";
txt1+="4_,4_,4 4,5|6,6 6,0|2_,2_,2 2,2|5-|1_,1_,1 1,2|";
txt1+="3,3 3,0|5._,5._,5. 5.,5.|2-|5,6 5,6|5,4 3,1|";
txt1+="2 5.|1,3 0|5,6 5,6|5,4 3,1|5.,0 3,0|2,1 0|";

Export.push({
	name:"春天在哪里"
	,url:"https://www.qinyipu.com/jianpu/ertonggequ/2611.html"
	,meterDuration:500
	,timbre:4
	,waveType:"triangle"
	,texts:[txt1]
});


/** 让我们荡起双桨，完整 **/
var txt1="";
txt1+="6._ 1,2|";
txt1+="3* 5_|3,1 2|6.-|0,1 2,3|5* 5_|";
txt1+="6 2|3-|0 3,5|6-|5* 6_|";
txt1+="1',7_,6_ 5,6|3 1,2|3* 5_|1 6.|1,2 3,6|";
txt1+="5---|3- 6* 6_|5,4 3 2-|3* 5_ 6.,1 2|";
txt1+="0 1,2 3 5,5|6 1' 7,6 5,3|6---|";

Export.push({
	name:"让我们荡起双桨"
	,url:"https://www.qinyipu.com/jianpu/liuxinggequ/2569.html"
	,meterDuration:600
	,timbre:2.5
	,waveType:"triangle"
	,texts:[txt1]
});


/** 铃儿响叮当，完整 **/
var txt1="";
txt1+="3,3 3 3,3 3|3,5 1*,2_ 3-|4,4 4,4 4,3 3,3|5,5 4,2 1 1'|";
txt1+="5.,3 2,1 5.* 5.__,5.__|5.,3 2,1 6.* 6._|6.,4 3,2 7.* 5._|";
txt1+="5,5 4,2 3 1,5.|5.,3 2,1 5.* 5._|5.,3 2,1 6.* 6._|";
txt1+="5.,4 3,2 5,5 5,5|6,5 4,2 1* 0__|3,3 3 3,3 3|";
txt1+="3,5 1*,2_ 3-|4,4 4*,4_ 4,3 3,3_,3_|5,5 4,2 1* 0_|";

Export.push({
	name:"铃儿响叮当 - Jingle Bells - 圣诞歌"
	,url:"https://puduoduo123.com/21671.html"
	,meterDuration:550
	,timbre:7
	,waveType:"triangle"
	,texts:[txt1]
});


/** 下雨的时候，完整 **/
var txt1="";
txt1+="0,5. 3,2 4,3|5.----- 5.,6. 7._ 1 3.----|";
txt1+="5._ 3,2 4_ 3 6.*--- 6.,7. 6.,3.|4.- 0,3. 4._|3 2*- 3_ 4|";
txt1+="5*---- 5,5 6,6|3* 6._ 6*,5_ 4*,3_|4- 0,6. 7.,1|4---|";
txt1+="0_ 0,5. 3,2 4_ 3 1*--|   0,5 6,7|1'*--   |1'_ 7,3|6-----|";
txt1+="0,6 5,1|4-- 6._ 3-- 4_ 3|6._ 2*--|0_ 0,3 6,7|1'-- |";
txt1+="   0,1' 7*,1'_ 7*,3_|6----- 0,6 5,3|1---|0_ 0,5. 3,2 4_ 3|";
txt1+="1*--|0_ 0,5. 3,2 4,3|5.----- 5.,6. 7._ 1 3.----|";
txt1+="0_ 3,2 4_ 3 6.*--|  0,5. 3,2 4,3|5----- 5,5 6,6|";
txt1+="3- 6,1' 7,3|6* 1'_ 7,6 5,1|5- 0,6 7,1'|4'---|   0,5 3',2' 4'_ 3'|";
txt1+="1'*---  |0_ 0,5. 3,2 4,3|5-------|";

Export.push({
	name:"下雨的时候 - SomeTimes When It Rains"
	,url:"https://www.cangqiang.com.cn/d/40266.html"
	,meterDuration:650
	,timbre:5
	,texts:[txt1]
});


/** 此情可待，开头一段 **/
var txt1="",txt2="",txt3="",txt4="";
//(1)
txt1+="0 0,5' 5',4' 3'_|2'- 2'_ 2',3' 4'_|3'* 1'_ 1' 2' 3'|2' 1' 7_ 6,5|";
txt2+="R[0.15] 1,5 1'--|7.,5 7--|6.,3 6 3 6.|4. 1 5.* 5._|";
txt3+="0---|0---|0---|0---|";
txt4+="0---|0---|0---|0---|";
//(5)
txt1+="5* 5_ 5,4 3_|2- 2_ 2,3 4_|3* 1_ 1 2_ 3_|4 3 2 1_ 7.,1|";
txt2+="1.,5. 1 5.,1 5.|7..,5. 7. 5.,7. 2|6.._ 3. 6._ 3. 1|4.. 6. 5..-|";
txt3+="0---|0---|0---|0 1 0 0|";
txt4+="0---|0---|0---|0---|";
//(9)
txt1+="1---";
txt2+="6..,3. 6._ 7. 0*";
txt3+="0---";
txt4+="0---";
/*/(9)
txt1+="1-------|0 0 5,1' 2'_|2' 3'* 3',2' 1',2'_,1'_|";
txt2+="6..,3. 6._ 7. 1 6._|6..,3. 6._ 7. 1*|1.,5. 1_ 2*-|7..,5. 1_ 2*-|";
txt3+="0 0_ 3 0 0_|0 0_ 3 0*|0 0_ 3 0*|0 0_ 3 0*|";
txt4+="0---|0---|0 0_ 5 0*|0 0_ 5 0*|";
//(13)
txt1+="6-- 6,7|1'_ 2' 7* 6_|5*- 5,1' 2'_|2' 3'* 4',3' 1',2'_,1'_|";
txt2+="6..,3. 1_ 4 6._ 4,6.|5..,2 5._ 7. 5._ 7.|1.,5. 1_ 2- 5._|7..,5. 1_ 2*-|";
txt3+="0* 5 0*|0* 2 0*|0* 3- 0_|0* 3* 0|";
txt4+="0---|0---|0* 5- 0_|0* 5* 0|";*/

Export.push({
	name:"此情可待 - Right Here Waiting"
	,url:"https://www.everyonepiano.cn/Number-3254-1.html"
	,meterDuration:800
	,timbre:2
	,texts:[txt1,txt2,txt3,txt4]
});


/** 独角戏 **/
var txt1="";
txt1+="1,1 1,1 1,6.|3--|";
txt1+="1',7 6,5 3,5|3- 5,3|2,2 2,1 2,6.|5,5 5,3 2,3|2,2 2,3 2,6.|";
txt1+="7.--|1,1 1,1 1,6.|3--|1',7 6,5 3,5|3- 5,3|";
txt1+="2,2 2,1 2,6.|5,5 5,3 2,3|7,7 7,1' 7,6|7--";

Export.push({
	name:"独角戏 - 是谁导演这场戏"
	,url:"https://www.qinyipu.com/jianpu/jianpudaquan/41440.html"
	,meterDuration:600
	,timbre:4
	,texts:[txt1]
});


/** 少女的祈祷 **/
var txt1="";
txt1+="1__,2__|5,3 3,3,4,3|2,5. 2,2,3,2|1,1 1__ 3._ 4.__|5. 0_ 5.__ 5.__|6.*_ 6.__ 6._ 7.__ 1__|";
txt1+="3_ 2__ 1__ 5.*_ 5.__|6.*_ 6.__ 6._ 7.__ 1__|2 0_ 1__ 2__|5,3 3,3,4,3|2,5. 2,2,3,2|1*_ 3.__ 3._ 1__ 7.__|5. 0_ 5.__ 5.__|";
txt1+="6.*_ 6.__ 6._ 7.__ 1__|3__ 2_ 1__ 5.*_ 5.__|6.*_ 6.__ 6._ 7.__ 1__|2|";

Export.push({
	name:"少女的祈祷 - 怕发生的永远别发生"
	,url:"https://www.qinyipu.com/jianpu/jianpudaquan/13221.html"
	,meterDuration:650
	,timbre:2.5
	,texts:[txt1]
});


/** 花香 **/
var txt1="3 0_ 0,1 2,3|2,2 3,2 0_ 0_|1 0_ 0,1 2,3|";
txt1+="2,2 3,2 0,0|6. 7._ 1 2*|5 1,1 0,1 2,3|";
txt1+="4 3 2_ 1 3| 2 1 2*| 0 |1 2 3 4|";
txt1+="5 6,5 0_ 6 7_|1' 1' 1' 1'|1'__,7__ 6 5 5_ 5,5|";
txt1+="6,1 1,1 0,6 6,6|5,1 1,1 0,1 2,3|4 3 2 1_|";
txt1+="3 2 1 2*|";

Export.push({
	name:"花香 - 风伴着花谢了又开"
	,url:"http://www.jianpu.cn/pu/84/84509.htm"
	,meterDuration:500
	,timbre:3
	,texts:[txt1]
});


/** 爱情转移 **/
var txt1=""
txt1+="1'*|1''* 7'*|1'',7' 6',5' 6',5' 3',2'|";
txt1+="3',5' 6'_ 5'* 5'_ 6',5'|6'* 6'_ 5' 6',5'|5'_ 3'-- 0__|";
txt1+="R[0.4]|3'',1'',6',3' 3'',1'',6',3'|3'',1'',6',3' 3'',1'',6',3'|2'',7',5',2' 2'',7',5',2'|2'',7',5',2' 2'',7',5',2'|1'',6',4',1' 1'',6',4',1'|5',2',7,5 2',7,5,2|";
txt1+="1'--|R|"; //https://www.ys613.cn/yuepu/19646.html
txt1+="0|R R R R|R R R R|";
txt1+="5._ 6.,1 2,1|2_ 3 3 3_ 2,1|";
txt1+="2_ 3 3 3_ 2,1|2_ 1 6._ 1 2,3|3 0,5. 6.,1 2,1|2_ 1 5 3_ 2,3|";
txt1+="2_ 1 3 3_ 2,1|2 2,2 2 1,6.|2 0,5. 6.,1 2,1|2_ 3 5 3_ 2,1|";
txt1+="2_ 3 6 3_ 2,1|2 1,6. 1 2,3|5 0,5. 6.,1 2,1|2_ 1 6 5_ 3,2|";
txt1+="2_ 1 3 3_ 2,1|2 2,2 2 1,6.|1 0,1 1',7 7,6|7,6 0,5 6,5 3,2|";
txt1+="3,5 6,5 0,5 6,5|6*_ 6*_ 6_ 6*_ 5*_ 6_|5_ 3* 0,1 2,3|5,3 2,1 0,1 2,3|";
txt1+="6,3 2,1 0,1 1,1|1' 1',1' 1' 6,1'|6_ 5 5_ 0,1 1',7|1',7 6,5 6,5 3,2|";
txt1+="3,5 6,5 0,5 6,5|6*_ 6*_ 6_ 6*_ 5*_ 6_|5_ 3* 0,1 2,3|5,3 2,1 0,1 2,3|";
txt1+="6,3 2,1|1'|0,1' 6,1'|6,5 0,5 6,5 3,2|3,2 0,1 6._ 1,2|1---|";

Export.push({
	name:"爱情转移 - 爱情呼叫转移主题曲"
	,url:"https://www.qinyipu.com/jianpu/jianpudaquan/81943.html"
	,meterDuration:500
	,timbre:2.5
	,texts:[txt1]
});


/** 同桌的你 **/
var txt1="";
txt1+="5',5' 5',5' 3',4'|5'* 7'*|6',6' 6',6' 4',6'|5'--|5',5' 5',5' 7',6'|";
txt1+="5'* 4'*|4',4' 4',4' 3',2'|1'--|5',5' 5',5' 3',4'|5'* 7'*|";
txt1+="6',6' 6',6' 4',6'|5'--|5',5' 5',5' 7',6'|5'* 4'*|4',4' 4',4' 3',2'|";
txt1+="1'--|1'',1'' 1'',1'' 5',6'|1'' 1''_ 3''*|2'',2'' 2'',2'' 1'',7'|6'--|";
txt1+="7',7' 7',7' 7',1''|2''* 5'*|7',7' 1'',2'' 1'',7'|1''--|1'',1'' 1'',1'' 5',6'|";
txt1+="1'' 1''_ 3''*|2'',2'' 2'',2'' 1'',7'|6'--|7',7' 7',7' 7',1''|";
txt1+="2''* 5'*|7',7' 1'',2'' 1'',7'|1''--";

Export.push({
	name:"同桌的你 - 谁娶了多愁善感的你"
	,url:"https://www.qinyipu.com/jianpu/jianpudaquan/195407.html"
	,meterDuration:600
	,timbre:1.5
	,texts:[txt1]
});


/** 祝你一路顺风 **/
var txt1="";
txt1+="1'__,2'__|3',3'_,3'_ 3',3'_,2'_ 3'_,2'_,1' 0,3,3,5|1'*,1'_ 1',1'_,7_ 1'_,7_,6 0,6,3,5|6*,6_ 6,5 6__,5__ 4 1__,2__|";
txt1+="3,5,5,6 5*,3_ 2__,5__ 6__ 5*_ 1'__,2'__|3'*,3'_ 3',2' 3'_,2'_,1' 0,3,3,5|1'*,1'_ 1',7 1'_,7_,6 0,6,3,5|";
txt1+="6*,6_ 6,5 6__,5__ 4 1__,2__|3,5,5,3 2*,2_ 1-|";
var txtN="3_,2__ 3*_ 2__,1__ 5_ 6__ 5*_ 3__,5__|";
txtN+="6,6,6,6 6_,3__ 5** 0*,3_|6,6,6,5 6,6_,1'_ 5,5,5,6 3*,1_|"
txt1+=txtN+"2,2,2,2 2,2_,1_ 2__,3_ 2**|";
txt1+=txtN+"2,2,2,2 2,2_,1_ 2__,3_ 1 1__|1',1',1',1' 1'_,2'_,1' 6_,5,6_ 3*,1_|2*,1_ 2,2_,3_ 2 0*,1_|";
txt1+="1',1',1',1' 1'_,2'_,1' 6_,5,6_ 1'*,5_|6*,5_ 6,1' 2'* 1'__,2'__|";
var txtN="3',2',1',2' 3',2'_,1'_ 2',3',2',1' 0,3,3,5|";
txtN+="6,5,6,1' 5,5_,6_ 3 0,3,2,3|5_,6__ 5_ 5__,3__ 5__ 6_,1'__ 6_ 6__,5__ 6__|";
txt1+=txtN+"1'*,1'_ 6,6 2'* 1'__,2'__|";
txt1+=txtN+="3'*_ 2'*_ 6_ 1'-|";

Export.push({
	name:"祝你一路顺风 - 面带着微微笑，用力的挥挥手"
	,url:"https://www.qinyipu.com/jianpu/jianpudaquan/41510.html"
	,meterDuration:725
	,timbre:2
	,texts:[txt1]
});


/** 纸短情长，节选 **/
var txt1="";
txt1+="0,1 1,2|3 3,3 0,3 2,1|7. 5,5 0,5 6,7|1' 3,3 0,1' 7,1'|7 3_ 5* 5,6|";
txt1+="1',5 6,6 0,6 5,4|5_ 3 2_ 1 0,6._,1_|3,2 2,1 2_ 5 1|2 0,1 1,2|";
txt1+="3,3 2,3 0,3 2,1|5,5 3,5 0,5 6,7|1',1' 1',1' 1',7 6,7|6_ 3 5* 5,6|";
txt1+="1',5 6,6 0,6 5,3|5_ 3 2_ 1 0,6._,1_|3,2 2,1 2_ 1 6._|1";

Export.push({
	name:"纸短情长 - 好想你，在每一个雨季"
	,url:"https://www.qinyipu.com/jianpu/jianpudaquan/7339.html"
	,meterDuration:600
	,timbre:2.5
	,texts:[txt1]
});


/** 我爱你不问归期，节选 **/
var txt1="";
txt1+="5,4|";
txt1+="3 0*,3_ 3_ 3__ 4_ 5*_|5-  1,1|1' 0*,1'_ 1',7 6,1'|7_ 5__ 5-  3,5|";
txt1+="6 0*,6_ 6,7 1',2'|5-  3,5|4 0*,3_ 4_ 3__ 4_ 5_|2-  5,4|";
txt1+="3 0*,3_ 3_ 3__ 4_ 5*_|5-  1,1|1' 0*,1'_ 1',7 6,1'|7_ 5__ 5-  3,5|";
txt1+="6 0*,6_ 6,7 1',2'|5-  3,5|6 0*,1'_ 1'_ 6__ 1'*_ 1'_|2'- 0,1' 1',2'|";
txt1+="4',3' 2'_,1'* 1',2' 3',5'|6',5' 6'_ 2'*_ 3'_ 2'_,1'_,1'|1',1' 1',6 1',2' 4'_|3'*- 0,3' 4',5'|";
txt1+="6',4' 4'_,3'* 3',2' 3'__ 4'_|5'*_ 4'_ 3'_,2' 3'*_ 3'_ 2',1'|1',1' 1',6 1',5' 5'_|2'*- 0,1' 1',2'|";
txt1+="4',3' 2'_,1'* 1',2' 3'_,5'|6'*_ 5'_ 5'__ 6'_ 2'*_ 3'_ 2'_,1'_,1'|1',1' 1',6 1',2' 4'_|3'*- 0,3' 4',5'|";
txt1+="6',4' 4'_,3'* 3',2' 3'__ 4'_|5'*_ 3'_ 3'__ 2'_ 3'*_ 2'_ 2',3'_|4',3' 2'_,1'* 5',1' 2'_ 1'---";

Export.push({
	name:"我爱你不问归期 - 是想念，如你温柔过境"
	,url:"https://www.qinyipu.com/jianpu/jianpudaquan/214639.html"
	,meterDuration:550
	,timbre:2
	,texts:[txt1]
});


/** 出埃及记，开头一段 **/
var txt1="",txt2="";
//(1)
txt1+="2-- 6|5-- 2|";
txt2+="R[0.1] 2..,6.. 4.,6.. 4.,6.. 4.,6..|5..,2. 7.,2. 7.,2. 7.,2.|";
//(5)
txt1+="4 5 3* 1_|2-- 6|1'-- 7|1' 2' 7* 5_|";
txt2+="6..,4. 2,4. 1.,5. 3,5.|2..,6.. 4.,6.. 4.,6.. 4.,6..|6..,3. 1,3. 1,3. 1,3.|4..,1. 6.,1. 5..,2. 7.,2.|";
//(9)
txt1+="6---|0 6 1'* 3'_|2'* 6_ 6-|0 2' 6* 2'_|";
txt2+="6..,3. 1,3. 7.,3. 6.,3.|6..,3. 1,3. 1,3. 1,3.|2..,6.. 4.,6.. 2..,6.. 4.,6..|2..,6.. 4.,6.. 2..,6.. 4.,6..|";
//(13)
txt1+="3'* 6_ 6-|0 0 5 6|6 1' 6* 4_|5 6 4 2|";
txt2+="6..,3. 1,3. 1,3. 1,3.|6..,3. 1,3. 1.,5. 3,5.|5..,2. 6.,2. 6..,3. 1,3.|1.,5. 3,5. 2.,6. 4,6.|";
//(17)
txt1+="3,6. 2,3 6.,2 3,5|6.,2,3,5 6.,2,3,5 6.,1,3,6 6.,1,3,6|6,1',3',6' 6,1',3',6' 6,1'',3'',6'' 6',1'',3'',6''|2'-- 6'|";
txt2+="6..---|6..- 6..-|6.. 6.. 6.. 6..|2..,6.. 4.,6.. 4.,6.. 4.,6..|";
//(21)
txt1+="5'-- 2'|4'* 5'_ 3'* 1'_|2'-- 6'|1'-- 7|";
txt2+="5..,2. 7.,2. 7.,2. 7.,2.|6..,4. 2,4. 1.,5. 3,5.|2..,6.. 4.,6.. 4.,6.. 4.,6..|6..,3. 1,3. 1,3. 1,3.|";
//(25)
txt1+="1'* 2'_ 7* 5_|6---";
txt2+="4..,1. 6.,1. 5..,2. 7.,2.|6..,3. 1,3. 7.,3. 6.,3.";

Export.push({
	name:"出埃及记 - 电影出埃及记主题曲"
	,url:"https://www.qinyipu.com/jianpu/chunyinle/2388.html"
	,meterDuration:550
	,timbre:4
	,texts:[txt1, txt2]
});



/** Victory，中间一段 **/
var txt1="",txt2="",txt3="",txt4="";
//(33)
txt1+="R 0* 3_ 1',7 6,5|3 5 3 3,1|2 5. 5. 2,5.|4 3 2 1|";
txt2+="R[0.3] 6.* 6._ 6.,6. 6.,7.|1.* 1._ 1.,1. 1.,1.|5.* 5._ 5.,5. 4.,3.|2.* 2._ 2.,2. 1.,7.|";
txt3+="R[0.3] 0---|0---|0---|0---|";
txt4+="R[0.3] 0---|0---|0---|0---|";
//(37)
txt1+="6.* 3_ 1',7 6,5|3 5 5 5,1'|2' 5 5 1',3'|2' 6 6 5|";
txt2+="6.* 6._ 6.,6. 6.,7.|1. 1.,1. 1.,1. 1.,1.|5.* 5._ 5.,5. 4.,3.|2.* 2._ 2.,2. 1.,7.|";
txt3+="0---|0---|0---|0---|";
txt4+="0---|0---|0---|0---|";
//(41)
txt1+="6* 3_ 1',7 6,5|3 5 3 3,1|2 5. 5. 2,5.|4 5 6 7|";
txt2+="6.* 6._ 6.,6. 6.,7.|1.* 1._ 1.,1. 1.,1.|5.* 5._ 5.,5. 4.,3.|2. 2.,2. 3.,2. 4.,2.|";
txt3+="0---|0---|0---|0---|";
txt4+="0---|0---|0---|0---|";
//(45)
txt1+="6* 3_ 1',7 1',2'|3' 5 3',4' 3',1'|2'* 1'_ 7 5|4- 6 7|";
txt2+="6.* 6._ 6.,6. 6.,7.|1. 1.,1. 1.,1. 1.,1.|5.* 5._ 5.,5. 4.,3.|2. 2.,2. 2.,2. 1.,1.|";
txt3+="0---|0---|0---|0---|";
txt4+="0---|0---|0---|0---|";
//(49)
txt1+="R 1 6. 1,6. 7.,6.|0 0,5_,6_ 7 0,6_,7_|1' 0 1 7.|0 0,1'_,2'_ 3'-|";
txt2+="R 4. 4. 4. 4.|0 0,5._,6._ 7. 0,6._,7._|1 0,6. 6.,6. 6.,6.|0 0,1_,2_ 3-|";
txt3+="4 0--|4. 4. 5.,5. 5.,5.|6.,6. 6.,6. 6.,6. 6.,6.|6.,6. 6.,6. 1.,1. 1.,1.|";
txt4+="6 0--|0---|6.,6._,3._ 6.,6. 6.,6._,3._ 6.,6.|6.,6._,3._ 6.,6. 1.,1. 1.,1.|";
//(53)
txt1+="1 6. 1,6. 7.,6.|0 0,5_,6_ 7 0,6_,7_|1' 0 1 7.|";
txt2+="4.,4. 4.,4. 4.,4. 4.,4.|0 0,5._,6._ 7. 0,6._,7._|1 0,6. 6.,6. 6.,6.|";
txt3+="6 0--|4. 4. 5.,5. 5.,5.|6.,6. 6.,6. 6.,6. 6.,6.|";
txt4+="4 0--|4.,4._,4._ 4.,4. 5.,5._,2._ 5.,5.|6.,6._,3._ 6.,6. 6.,6._,3._ 6.,6.|";

Export.push({
	name:"Victory - 高潮部分"
	,url:"https://www.qinyipu.com/jianpu/jianpudaquan/191805.html"
	,meterDuration:450
	,timbre:4
	,texts:[txt1, txt2, txt3, txt4]
});
var vtxt1=txt1,vtxt2=txt2,vtxt3=txt3,vtxt4=txt4;

// Victory，开头一段，写都写了 留着凑个数
var txt1="",txt2="",txt3="",txt4="";
//(1)
txt1+="0---|0---|0---|0---|";
txt2+="0* 6._ 1,6. 1,6.|0* 6._ 1,6. 1,6.|0* 5._ 1,5. 1,5.|0* 5._ 7.,5. 7.,5.|";
txt3+="R[0.3] 6.---|6.---|5.---|5.---|";
txt4+="R[0.3] 0---|0---|0---|0---|";
//(5)
txt1+="0---|0---|0---|0---|";
txt2+="0* 6._ 4,6. 4,6.|0* 6._ 4,6. 4,6.|0* 1_ 4,1 4,1|0* 1_ 6,5 4,3|";
txt3+="2.---|2.---|4.---|4.---|";
txt4+="0---|0---|0---|0---|";
//(9)
txt1+="0* 1_ 3,1 3,1|0* 1_ 3,1 3,1|0* 1_ 3,1 3,1|0---|";
txt2+="0* 6._ 1,6. 1,6.|0* 6._ 1,6. 1,6.|0* 5._ 1,5. 1,5.|0* 1_ 5,4 3,2|";
txt3+="6.---|6.---|1.---|1.---|";
txt4+="0---|0---|0---|0---|";
//(13)
txt1+="0---|0---|0---|0---|";
txt2+="0* 5._ 2,5. 2,5.|0* 5._ 2,2 1,7.|0* 5._ 7.,5. 7.,5.|0* 5._ 2,2 1,7.|";
txt3+="5.---|5.---|3.---|3.---|";
txt4+="0---|0---|0---|0---|";
//(17)
txt1+="0 0,6. 1,6. 1,6.|0 0,6. 1,0 1,7.|0 0 1,0 1,0|0 0 0,1 0,1|";
txt2+="4.,4. 4.,4. 4.,4. 4.,4.|4.,4. 4.,4. 5.,5. 5.,5.|6.,6. 6.,6. 6.,6. 6.,6.|6.,6. 6.,6. 1,5. 1,7.|";
txt3+="4..- 4..-|4.. 4.. 5..,5.. 5..,5..|6..- 6..-|6.. 6.. 1..,1. 1.,1.|";
txt4+="0---|0---|0---|0---|";
//(21)
txt1+="0 0,6. 1,6. 1,6.|0 0,6. 2,2 1,7.|0 0 1,0 1,0|0 0 3,0 2,0|";
txt2+="4.,4. 4.,4. 4.,4. 4.,4.|4.,4. 4.,4. 5.,5. 5.,5.|6.,6. 6.,6. 6.,6. 6.,6.|1,1 1,1 1,1 1,1|";
txt3+="4..- 4..-|4.. 4.. 5..,5.. 5..,5..|6..- 6..-|1. 1. 1.,1. 1.,1.|";
txt4+="0---|0---|0---|0---|";
//(25)
txt1+="0 0,6. 1,6. 1,6.|0 0,6. 1,0 1,7.|0 0 3,0 3,0|0 0 5,0 4,3|";
txt2+="4.- 4.-|4. 4. 5.,5. 5.,5.|6.- 6.-|1. 1. 1.,1. 1.,1.|";
txt3+="1.- 1.-|1. 1. 2.,2. 2.,2.|3.- 3.-|5. 5. 5.,5. 5.,5.|";
txt4+="0---|0---|0---|0---|";
//(29)
txt1+="0 0,6. 1,6. 1,6.|0 0,2 4,2 4,2|6.,6. 6.,7. 6.,7. 6.,7.|5.,5. 5.,5. 5.,5. 5.,5.|";
txt2+="4.- 4.-|4.,4. 4.,4. 4.,4. 4.,4.|7.- 7.,7. 7.,7.|3.,3. 3.,3. 3.,3. 3.,3.|";
txt3+="1.- 1.-|6. 6. 6.,6. 6.,6.|3.- 3.,3. 3.,3.|0 0,7. 3,7. 3,7.|";
txt4+="0---|2. 2. 2.,2. 2.,2.|0 0 3,0 3,0|7.,7. 7.,7. 7.,7. 7.,7.|";

Export.push({
	name:"Victory - 开头部分+高潮部分"
	,url:"https://www.qinyipu.com/jianpu/jianpudaquan/191805.html"
	,meterDuration:450
	,timbre:4
	,texts:[txt1+vtxt1, txt2+vtxt2, txt3+vtxt3, txt4+vtxt4]
});


//音符试听
var txt1="1.-----";
txt1+="|0";
for(var i=1;i<=7;i++){ txt1+=" "+i+".."; }
txt1+="|0";
for(var i=1;i<=7;i++){ txt1+=" "+i+"."; }
txt1+="|0";
for(var i=1;i<=7;i++){ txt1+=" "+i+""; }
txt1+="|0";
for(var i=1;i<=7;i++){ txt1+=" "+i+"'"; }
txt1+="|0";
for(var i=1;i<=7;i++){ txt1+=" "+i+"''"; }
txt1+="|1.--|";
for(var i=1;i<=7;i++){
	txt1+="0 "+i+"..";
	txt1+=" "+i+".";
	txt1+=" "+i+"";
	txt1+=" "+i+"'";
	txt1+=" "+i+"''|";
}
var raw=txt1;
txt1+=" Q[0.5] "+raw;
txt1+=" A[0.8] "+raw;
txt1+=" T[0.8] "+raw;
txt1+="1.-----";

Export.push({
	name:"音符试听 - 1234567"
	,meterDuration:450
	,timbre:4
	,texts:[txt1]
});


})();
