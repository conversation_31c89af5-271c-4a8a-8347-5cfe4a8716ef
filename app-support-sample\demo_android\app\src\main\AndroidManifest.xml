<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.github.xianyuecn.recorder">

    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.RECORD_AUDIO"/>
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS"/>

    <application
        android:icon="@drawable/icon"
        android:label="Recorder Demo"
        android:allowBackup="true"
        android:supportsRtl="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:theme="@style/AppTheme">

        <meta-data android:name="android.max_aspect" android:value="9.9"/>

        <activity android:name=".MainActivity"
            android:launchMode="singleTask"
            android:exported="true"
            android:configChanges="screenLayout|screenSize|keyboardHidden|orientation"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />

                <action android:name="android.intent.action.VIEW" />
            </intent-filter>
        </activity>

    </application>

</manifest>