!function(e){var t={};function n(r){if(t[r])return t[r].exports;var a=t[r]={i:r,l:!1,exports:{}};return e[r].call(a.exports,a,a.exports,n),a.l=!0,a.exports}n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var a in e)n.d(r,a,function(t){return e[t]}.bind(null,a));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=21)}([function(e,t,n){"use strict";var r,a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};r=function(){return this}();try{r=r||new Function("return this")()}catch(e){"object"===("undefined"==typeof window?"undefined":a(window))&&(r=window)}e.exports=r},function(e,t,n){"use strict";function r(e,t,n,r,a,i,o,s){var l,c="function"==typeof e?e.options:e;if(t&&(c.render=t,c.staticRenderFns=n,c._compiled=!0),r&&(c.functional=!0),i&&(c._scopeId="data-v-"+i),o?(l=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),a&&a.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(o)},c._ssrRegister=l):a&&(l=s?function(){a.call(this,this.$root.$options.shadowRoot)}:a),l)if(c.functional){c._injectStyles=l;var f=c.render;c.render=function(e,t){return l.call(t),f(e,t)}}else{var u=c.beforeCreate;c.beforeCreate=u?[].concat(u,l):[l]}return{exports:e,options:c}}n.d(t,"a",(function(){return r}))},function(e,t,n){"use strict";e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},function(e,t,n){"use strict";n.r(t);var r=n(4),a=n.n(r);for(var i in r)"default"!==i&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t.default=a.a},function(e,t,n){"use strict";var r,a=n(13),i=(r=a)&&r.__esModule?r:{default:r};n(14),n(15),n(16),e.exports={data:function(){return{type:"mp3",bitRate:16,sampleRate:16e3,duration:0,durationTxt:"0",powerLevel:0,logs:[]}},created:function(){this.Rec=i.default},methods:{recOpen:function(){var e=this;(this.rec=(0,i.default)({type:e.type,bitRate:+e.bitRate,sampleRate:+e.sampleRate,onProcess:function(t,n,r,a){e.duration=r,e.durationTxt=e.formatMs(r,1),e.powerLevel=n,e.wave.input(t[t.length-1],n,a)}})).open((function(){e.reclog("已打开:"+e.type+" "+e.sampleRate+"hz "+e.bitRate+"kbps",2),e.wave=i.default.WaveView({elem:".ctrlProcessWave"})}),(function(t,n){e.reclog((n?"UserNotAllow，":"")+"打开失败："+t,1)}))},recClose:function(){var e=this.rec;this.rec=null,e?(e.close(),this.reclog("已关闭")):this.reclog("未打开录音",1)},recStart:function(){if(this.rec&&i.default.IsOpen()){this.rec.start();var e=this.rec.set;this.reclog("录制中："+e.type+" "+e.sampleRate+"hz "+e.bitRate+"kbps")}else this.reclog("未打开录音",1)},recPause:function(){this.rec&&i.default.IsOpen()?this.rec.pause():this.reclog("未打开录音",1)},recResume:function(){this.rec&&i.default.IsOpen()?this.rec.resume():this.reclog("未打开录音",1)},recStop:function(){if(this.rec&&i.default.IsOpen()){var e=this,t=e.rec;t.stop((function(n,r){e.reclog("已录制:","",{blob:n,duration:r,durationTxt:e.formatMs(r),rec:t})}),(function(t){e.reclog("录音失败："+t,1)}))}else e.reclog("未打开录音",1)},recPlayLast:function(){this.recLogLast?this.recplay(this.recLogLast.idx):this.reclog("请先录音，然后停止后再播放",1)},recUploadLast:function(){if(this.recLogLast){var e=this,t=this.recLogLast.res.blob,n="http://127.0.0.1:9528",r=function(t,n){return function(){4==t.readyState&&(200==t.status?e.reclog(n+'上传成功 <span style="color:#999">response: '+t.responseText+"</span>",2):(e.reclog(n+"没有完成上传，演示上传地址无需关注上传结果，只要浏览器控制台内Network面板内看到的请求数据结构是预期的就ok了。","#d8c1a0"),console.error(n+"上传失败",t.status,t.responseText)))}};e.reclog("开始上传到"+n+"，请稍候... （你可以先到源码 /assets/node-localServer 目录内执行 npm run start 来运行本地测试服务器）");var a=new FileReader;a.onloadend=function(){var e="";e+="mime="+encodeURIComponent(t.type),e+="&upfile_b64="+encodeURIComponent((/.+;\s*base64\s*,\s*(.+)$/i.exec(a.result)||[])[1]);var i=new XMLHttpRequest;i.open("POST",n+"/uploadBase64"),i.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),i.onreadystatechange=r(i,"上传方式一【Base64】"),i.send(e)},a.readAsDataURL(t);var i=new FormData;i.append("upfile",t,"recorder.mp3");var o=new XMLHttpRequest;o.open("POST",n+"/upload"),o.onreadystatechange=r(o,"上传方式二【FormData】"),o.send(i)}else this.reclog("请先录音，然后停止后再上传",1)},recDownLast:function(){this.recLogLast?this.recdown(this.recLogLast.idx):this.reclog("请先录音，然后停止后再下载",1)},reclog:function(e,t,n){var r={idx:this.logs.length,msg:e,color:t,res:n,playMsg:"",down:0,down64Val:""};n&&n.blob&&(this.recLogLast=r),this.logs.splice(0,0,r)},recplay:function(e){var t=this,n=this.logs[this.logs.length-e-1];n.play=(n.play||0)+1;var r=function(e){n.playMsg='<span style="color:green">'+n.play+"</span> "+t.getTime()+" "+e};r("");var a=this.$refs.LogAudioPlayer;a.controls=!0,a.ended||a.paused||a.pause(),a.onerror=function(e){r('<span style="color:red">播放失败['+a.error.code+"]"+a.error.message+"</span>")},a.src=(window.URL||webkitURL).createObjectURL(n.res.blob),a.play()},recdown:function(e){var t=this.logs[this.logs.length-e-1];t.down=(t.down||0)+1;var n="rec-"+(t=t.res).duration+"ms-"+(t.rec.set.bitRate||"-")+"kbps-"+(t.rec.set.sampleRate||"-")+"hz."+(t.rec.set.type||(/\w+$/.exec(t.blob.type)||[])[0]||"unknown"),r=document.createElement("A");r.href=(window.URL||webkitURL).createObjectURL(t.blob),r.download=n,r.click()},recdown64:function(e){var t=this.logs[this.logs.length-e-1],n=new FileReader;n.onloadend=function(){t.down64Val=n.result},n.readAsDataURL(t.res.blob)},getTime:function(){var e=new Date;return("0"+e.getHours()).substr(-2)+":"+("0"+e.getMinutes()).substr(-2)+":"+("0"+e.getSeconds()).substr(-2)},formatMs:function(e,t){var n=e%1e3,r=(e=(e-n)/1e3)%60,a=(e=(e-r)/60)%60,i=e=(e-a)/60;return(i?i+":":"")+(t||i+a?("0"+a).substr(-2)+":":"")+(t||i+a+r?("0"+r).substr(-2)+"″":"")+("00"+n).substr(-3)},intp:function(e,t){return(e=null==e?"-":e+"").length>=t?e:("_______"+e).substr(-t)}}}},function(e,t,n){var r=n(24);"string"==typeof r&&(r=[[e.i,r,""]]);var a={insert:"head",singleton:!1};n(18)(r,a);r.locals&&(e.exports=r.locals)},,,,function(e,t,n){"use strict";(function(e,n){Object.defineProperty(t,"__esModule",{value:!0});var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a=Object.freeze({});
/*!
 * Vue.js v2.6.10
 * (c) 2014-2019 Evan You
 * Released under the MIT License.
 */function i(e){return null==e}function o(e){return null!=e}function s(e){return!0===e}function l(e){return"string"==typeof e||"number"==typeof e||"symbol"===(void 0===e?"undefined":r(e))||"boolean"==typeof e}function c(e){return null!==e&&"object"===(void 0===e?"undefined":r(e))}var f=Object.prototype.toString;function u(e){return"[object Object]"===f.call(e)}function p(e){return"[object RegExp]"===f.call(e)}function _(e){var t=parseFloat(String(e));return t>=0&&Math.floor(t)===t&&isFinite(e)}function d(e){return o(e)&&"function"==typeof e.then&&"function"==typeof e.catch}function h(e){return null==e?"":Array.isArray(e)||u(e)&&e.toString===f?JSON.stringify(e,null,2):String(e)}function v(e){var t=parseFloat(e);return isNaN(t)?e:t}function m(e,t){for(var n=Object.create(null),r=e.split(","),a=0;a<r.length;a++)n[r[a]]=!0;return t?function(e){return n[e.toLowerCase()]}:function(e){return n[e]}}var b=m("slot,component",!0),g=m("key,ref,slot,slot-scope,is");function y(e,t){if(e.length){var n=e.indexOf(t);if(n>-1)return e.splice(n,1)}}var w=Object.prototype.hasOwnProperty;function S(e,t){return w.call(e,t)}function x(e){var t=Object.create(null);return function(n){return t[n]||(t[n]=e(n))}}var k=/-(\w)/g,A=x((function(e){return e.replace(k,(function(e,t){return t?t.toUpperCase():""}))})),M=x((function(e){return e.charAt(0).toUpperCase()+e.slice(1)})),T=/\B([A-Z])/g,R=x((function(e){return e.replace(T,"-$1").toLowerCase()}));var C=Function.prototype.bind?function(e,t){return e.bind(t)}:function(e,t){function n(n){var r=arguments.length;return r?r>1?e.apply(t,arguments):e.call(t,n):e.call(t)}return n._length=e.length,n};function E(e,t){t=t||0;for(var n=e.length-t,r=new Array(n);n--;)r[n]=e[n+t];return r}function B(e,t){for(var n in t)e[n]=t[n];return e}function O(e){for(var t={},n=0;n<e.length;n++)e[n]&&B(t,e[n]);return t}function L(e,t,n){}var I=function(e,t,n){return!1},P=function(e){return e};function $(e,t){if(e===t)return!0;var n=c(e),r=c(t);if(!n||!r)return!n&&!r&&String(e)===String(t);try{var a=Array.isArray(e),i=Array.isArray(t);if(a&&i)return e.length===t.length&&e.every((function(e,n){return $(e,t[n])}));if(e instanceof Date&&t instanceof Date)return e.getTime()===t.getTime();if(a||i)return!1;var o=Object.keys(e),s=Object.keys(t);return o.length===s.length&&o.every((function(n){return $(e[n],t[n])}))}catch(e){return!1}}function N(e,t){for(var n=0;n<e.length;n++)if($(e[n],t))return n;return-1}function D(e){var t=!1;return function(){t||(t=!0,e.apply(this,arguments))}}var H=["component","directive","filter"],j=["beforeCreate","created","beforeMount","mounted","beforeUpdate","updated","beforeDestroy","destroyed","activated","deactivated","errorCaptured","serverPrefetch"],F={optionMergeStrategies:Object.create(null),silent:!1,productionTip:!1,devtools:!1,performance:!1,errorHandler:null,warnHandler:null,ignoredElements:[],keyCodes:Object.create(null),isReservedTag:I,isReservedAttr:I,isUnknownElement:I,getTagNamespace:L,parsePlatformTagName:P,mustUseProp:I,async:!0,_lifecycleHooks:j},V=/a-zA-Z\u00B7\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u037D\u037F-\u1FFF\u200C-\u200D\u203F-\u2040\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD/;function z(e){var t=(e+"").charCodeAt(0);return 36===t||95===t}function U(e,t,n,r){Object.defineProperty(e,t,{value:n,enumerable:!!r,writable:!0,configurable:!0})}var X=new RegExp("[^"+V.source+".$_\\d]");var q,Y="__proto__"in{},W="undefined"!=typeof window,G="undefined"!=typeof WXEnvironment&&!!WXEnvironment.platform,K=G&&WXEnvironment.platform.toLowerCase(),Z=W&&window.navigator.userAgent.toLowerCase(),Q=Z&&/msie|trident/.test(Z),J=Z&&Z.indexOf("msie 9.0")>0,ee=Z&&Z.indexOf("edge/")>0,te=(Z&&Z.indexOf("android"),Z&&/iphone|ipad|ipod|ios/.test(Z)||"ios"===K),ne=(Z&&/chrome\/\d+/.test(Z),Z&&/phantomjs/.test(Z),Z&&Z.match(/firefox\/(\d+)/)),re={}.watch,ae=!1;if(W)try{var ie={};Object.defineProperty(ie,"passive",{get:function(){ae=!0}}),window.addEventListener("test-passive",null,ie)}catch(e){}var oe=function(){return void 0===q&&(q=!W&&!G&&void 0!==e&&(e.process&&"server"===e.process.env.VUE_ENV)),q},se=W&&window.__VUE_DEVTOOLS_GLOBAL_HOOK__;function le(e){return"function"==typeof e&&/native code/.test(e.toString())}var ce,fe="undefined"!=typeof Symbol&&le(Symbol)&&"undefined"!=typeof Reflect&&le(Reflect.ownKeys);ce="undefined"!=typeof Set&&le(Set)?Set:function(){function e(){this.set=Object.create(null)}return e.prototype.has=function(e){return!0===this.set[e]},e.prototype.add=function(e){this.set[e]=!0},e.prototype.clear=function(){this.set=Object.create(null)},e}();var ue=L,pe=0,_e=function(){this.id=pe++,this.subs=[]};_e.prototype.addSub=function(e){this.subs.push(e)},_e.prototype.removeSub=function(e){y(this.subs,e)},_e.prototype.depend=function(){_e.target&&_e.target.addDep(this)},_e.prototype.notify=function(){var e=this.subs.slice();for(var t=0,n=e.length;t<n;t++)e[t].update()},_e.target=null;var de=[];function he(e){de.push(e),_e.target=e}function ve(){de.pop(),_e.target=de[de.length-1]}var me=function(e,t,n,r,a,i,o,s){this.tag=e,this.data=t,this.children=n,this.text=r,this.elm=a,this.ns=void 0,this.context=i,this.fnContext=void 0,this.fnOptions=void 0,this.fnScopeId=void 0,this.key=t&&t.key,this.componentOptions=o,this.componentInstance=void 0,this.parent=void 0,this.raw=!1,this.isStatic=!1,this.isRootInsert=!0,this.isComment=!1,this.isCloned=!1,this.isOnce=!1,this.asyncFactory=s,this.asyncMeta=void 0,this.isAsyncPlaceholder=!1},be={child:{configurable:!0}};be.child.get=function(){return this.componentInstance},Object.defineProperties(me.prototype,be);var ge=function(e){void 0===e&&(e="");var t=new me;return t.text=e,t.isComment=!0,t};function ye(e){return new me(void 0,void 0,void 0,String(e))}function we(e){var t=new me(e.tag,e.data,e.children&&e.children.slice(),e.text,e.elm,e.context,e.componentOptions,e.asyncFactory);return t.ns=e.ns,t.isStatic=e.isStatic,t.key=e.key,t.isComment=e.isComment,t.fnContext=e.fnContext,t.fnOptions=e.fnOptions,t.fnScopeId=e.fnScopeId,t.asyncMeta=e.asyncMeta,t.isCloned=!0,t}var Se=Array.prototype,xe=Object.create(Se);["push","pop","shift","unshift","splice","sort","reverse"].forEach((function(e){var t=Se[e];U(xe,e,(function(){for(var n=[],r=arguments.length;r--;)n[r]=arguments[r];var a,i=t.apply(this,n),o=this.__ob__;switch(e){case"push":case"unshift":a=n;break;case"splice":a=n.slice(2)}return a&&o.observeArray(a),o.dep.notify(),i}))}));var ke=Object.getOwnPropertyNames(xe),Ae=!0;function Me(e){Ae=e}var Te=function(e){this.value=e,this.dep=new _e,this.vmCount=0,U(e,"__ob__",this),Array.isArray(e)?(Y?function(e,t){e.__proto__=t}(e,xe):function(e,t,n){for(var r=0,a=n.length;r<a;r++){var i=n[r];U(e,i,t[i])}}(e,xe,ke),this.observeArray(e)):this.walk(e)};function Re(e,t){var n;if(c(e)&&!(e instanceof me))return S(e,"__ob__")&&e.__ob__ instanceof Te?n=e.__ob__:Ae&&!oe()&&(Array.isArray(e)||u(e))&&Object.isExtensible(e)&&!e._isVue&&(n=new Te(e)),t&&n&&n.vmCount++,n}function Ce(e,t,n,r,a){var i=new _e,o=Object.getOwnPropertyDescriptor(e,t);if(!o||!1!==o.configurable){var s=o&&o.get,l=o&&o.set;s&&!l||2!==arguments.length||(n=e[t]);var c=!a&&Re(n);Object.defineProperty(e,t,{enumerable:!0,configurable:!0,get:function(){var t=s?s.call(e):n;return _e.target&&(i.depend(),c&&(c.dep.depend(),Array.isArray(t)&&Oe(t))),t},set:function(t){var r=s?s.call(e):n;t===r||t!=t&&r!=r||s&&!l||(l?l.call(e,t):n=t,c=!a&&Re(t),i.notify())}})}}function Ee(e,t,n){if(Array.isArray(e)&&_(t))return e.length=Math.max(e.length,t),e.splice(t,1,n),n;if(t in e&&!(t in Object.prototype))return e[t]=n,n;var r=e.__ob__;return e._isVue||r&&r.vmCount?n:r?(Ce(r.value,t,n),r.dep.notify(),n):(e[t]=n,n)}function Be(e,t){if(Array.isArray(e)&&_(t))e.splice(t,1);else{var n=e.__ob__;e._isVue||n&&n.vmCount||S(e,t)&&(delete e[t],n&&n.dep.notify())}}function Oe(e){for(var t=void 0,n=0,r=e.length;n<r;n++)(t=e[n])&&t.__ob__&&t.__ob__.dep.depend(),Array.isArray(t)&&Oe(t)}Te.prototype.walk=function(e){for(var t=Object.keys(e),n=0;n<t.length;n++)Ce(e,t[n])},Te.prototype.observeArray=function(e){for(var t=0,n=e.length;t<n;t++)Re(e[t])};var Le=F.optionMergeStrategies;function Ie(e,t){if(!t)return e;for(var n,r,a,i=fe?Reflect.ownKeys(t):Object.keys(t),o=0;o<i.length;o++)"__ob__"!==(n=i[o])&&(r=e[n],a=t[n],S(e,n)?r!==a&&u(r)&&u(a)&&Ie(r,a):Ee(e,n,a));return e}function Pe(e,t,n){return n?function(){var r="function"==typeof t?t.call(n,n):t,a="function"==typeof e?e.call(n,n):e;return r?Ie(r,a):a}:t?e?function(){return Ie("function"==typeof t?t.call(this,this):t,"function"==typeof e?e.call(this,this):e)}:t:e}function $e(e,t){var n=t?e?e.concat(t):Array.isArray(t)?t:[t]:e;return n?function(e){for(var t=[],n=0;n<e.length;n++)-1===t.indexOf(e[n])&&t.push(e[n]);return t}(n):n}function Ne(e,t,n,r){var a=Object.create(e||null);return t?B(a,t):a}Le.data=function(e,t,n){return n?Pe(e,t,n):t&&"function"!=typeof t?e:Pe(e,t)},j.forEach((function(e){Le[e]=$e})),H.forEach((function(e){Le[e+"s"]=Ne})),Le.watch=function(e,t,n,r){if(e===re&&(e=void 0),t===re&&(t=void 0),!t)return Object.create(e||null);if(!e)return t;var a={};for(var i in B(a,e),t){var o=a[i],s=t[i];o&&!Array.isArray(o)&&(o=[o]),a[i]=o?o.concat(s):Array.isArray(s)?s:[s]}return a},Le.props=Le.methods=Le.inject=Le.computed=function(e,t,n,r){if(!e)return t;var a=Object.create(null);return B(a,e),t&&B(a,t),a},Le.provide=Pe;var De=function(e,t){return void 0===t?e:t};function He(e,t,n){if("function"==typeof t&&(t=t.options),function(e,t){var n=e.props;if(n){var r,a,i={};if(Array.isArray(n))for(r=n.length;r--;)"string"==typeof(a=n[r])&&(i[A(a)]={type:null});else if(u(n))for(var o in n)a=n[o],i[A(o)]=u(a)?a:{type:a};else 0;e.props=i}}(t),function(e,t){var n=e.inject;if(n){var r=e.inject={};if(Array.isArray(n))for(var a=0;a<n.length;a++)r[n[a]]={from:n[a]};else if(u(n))for(var i in n){var o=n[i];r[i]=u(o)?B({from:i},o):{from:o}}else 0}}(t),function(e){var t=e.directives;if(t)for(var n in t){var r=t[n];"function"==typeof r&&(t[n]={bind:r,update:r})}}(t),!t._base&&(t.extends&&(e=He(e,t.extends,n)),t.mixins))for(var r=0,a=t.mixins.length;r<a;r++)e=He(e,t.mixins[r],n);var i,o={};for(i in e)s(i);for(i in t)S(e,i)||s(i);function s(r){var a=Le[r]||De;o[r]=a(e[r],t[r],n,r)}return o}function je(e,t,n,r){if("string"==typeof n){var a=e[t];if(S(a,n))return a[n];var i=A(n);if(S(a,i))return a[i];var o=M(i);return S(a,o)?a[o]:a[n]||a[i]||a[o]}}function Fe(e,t,n,r){var a=t[e],i=!S(n,e),o=n[e],s=Ue(Boolean,a.type);if(s>-1)if(i&&!S(a,"default"))o=!1;else if(""===o||o===R(e)){var l=Ue(String,a.type);(l<0||s<l)&&(o=!0)}if(void 0===o){o=function(e,t,n){if(!S(t,"default"))return;var r=t.default;0;if(e&&e.$options.propsData&&void 0===e.$options.propsData[n]&&void 0!==e._props[n])return e._props[n];return"function"==typeof r&&"Function"!==Ve(t.type)?r.call(e):r}(r,a,e);var c=Ae;Me(!0),Re(o),Me(c)}return o}function Ve(e){var t=e&&e.toString().match(/^\s*function (\w+)/);return t?t[1]:""}function ze(e,t){return Ve(e)===Ve(t)}function Ue(e,t){if(!Array.isArray(t))return ze(t,e)?0:-1;for(var n=0,r=t.length;n<r;n++)if(ze(t[n],e))return n;return-1}function Xe(e,t,n){he();try{if(t)for(var r=t;r=r.$parent;){var a=r.$options.errorCaptured;if(a)for(var i=0;i<a.length;i++)try{if(!1===a[i].call(r,e,t,n))return}catch(e){Ye(e,r,"errorCaptured hook")}}Ye(e,t,n)}finally{ve()}}function qe(e,t,n,r,a){var i;try{(i=n?e.apply(t,n):e.call(t))&&!i._isVue&&d(i)&&!i._handled&&(i.catch((function(e){return Xe(e,r,a+" (Promise/async)")})),i._handled=!0)}catch(e){Xe(e,r,a)}return i}function Ye(e,t,n){if(F.errorHandler)try{return F.errorHandler.call(null,e,t,n)}catch(t){t!==e&&We(t,null,"config.errorHandler")}We(e,t,n)}function We(e,t,n){if(!W&&!G||"undefined"==typeof console)throw e;console.error(e)}var Ge,Ke=!1,Ze=[],Qe=!1;function Je(){Qe=!1;var e=Ze.slice(0);Ze.length=0;for(var t=0;t<e.length;t++)e[t]()}if("undefined"!=typeof Promise&&le(Promise)){var et=Promise.resolve();Ge=function(){et.then(Je),te&&setTimeout(L)},Ke=!0}else if(Q||"undefined"==typeof MutationObserver||!le(MutationObserver)&&"[object MutationObserverConstructor]"!==MutationObserver.toString())Ge=void 0!==n&&le(n)?function(){n(Je)}:function(){setTimeout(Je,0)};else{var tt=1,nt=new MutationObserver(Je),rt=document.createTextNode(String(tt));nt.observe(rt,{characterData:!0}),Ge=function(){tt=(tt+1)%2,rt.data=String(tt)},Ke=!0}function at(e,t){var n;if(Ze.push((function(){if(e)try{e.call(t)}catch(e){Xe(e,t,"nextTick")}else n&&n(t)})),Qe||(Qe=!0,Ge()),!e&&"undefined"!=typeof Promise)return new Promise((function(e){n=e}))}var it=new ce;function ot(e){!function e(t,n){var r,a,i=Array.isArray(t);if(!i&&!c(t)||Object.isFrozen(t)||t instanceof me)return;if(t.__ob__){var o=t.__ob__.dep.id;if(n.has(o))return;n.add(o)}if(i)for(r=t.length;r--;)e(t[r],n);else for(a=Object.keys(t),r=a.length;r--;)e(t[a[r]],n)}(e,it),it.clear()}var st=x((function(e){var t="&"===e.charAt(0),n="~"===(e=t?e.slice(1):e).charAt(0),r="!"===(e=n?e.slice(1):e).charAt(0);return{name:e=r?e.slice(1):e,once:n,capture:r,passive:t}}));function lt(e,t){function n(){var e=arguments,r=n.fns;if(!Array.isArray(r))return qe(r,null,arguments,t,"v-on handler");for(var a=r.slice(),i=0;i<a.length;i++)qe(a[i],null,e,t,"v-on handler")}return n.fns=e,n}function ct(e,t,n,r,a,o){var l,c,f,u;for(l in e)c=e[l],f=t[l],u=st(l),i(c)||(i(f)?(i(c.fns)&&(c=e[l]=lt(c,o)),s(u.once)&&(c=e[l]=a(u.name,c,u.capture)),n(u.name,c,u.capture,u.passive,u.params)):c!==f&&(f.fns=c,e[l]=f));for(l in t)i(e[l])&&r((u=st(l)).name,t[l],u.capture)}function ft(e,t,n){var r;e instanceof me&&(e=e.data.hook||(e.data.hook={}));var a=e[t];function l(){n.apply(this,arguments),y(r.fns,l)}i(a)?r=lt([l]):o(a.fns)&&s(a.merged)?(r=a).fns.push(l):r=lt([a,l]),r.merged=!0,e[t]=r}function ut(e,t,n,r,a){if(o(t)){if(S(t,n))return e[n]=t[n],a||delete t[n],!0;if(S(t,r))return e[n]=t[r],a||delete t[r],!0}return!1}function pt(e){return l(e)?[ye(e)]:Array.isArray(e)?function e(t,n){var r,a,c,f,u=[];for(r=0;r<t.length;r++)i(a=t[r])||"boolean"==typeof a||(c=u.length-1,f=u[c],Array.isArray(a)?a.length>0&&(_t((a=e(a,(n||"")+"_"+r))[0])&&_t(f)&&(u[c]=ye(f.text+a[0].text),a.shift()),u.push.apply(u,a)):l(a)?_t(f)?u[c]=ye(f.text+a):""!==a&&u.push(ye(a)):_t(a)&&_t(f)?u[c]=ye(f.text+a.text):(s(t._isVList)&&o(a.tag)&&i(a.key)&&o(n)&&(a.key="__vlist"+n+"_"+r+"__"),u.push(a)));return u}(e):void 0}function _t(e){return o(e)&&o(e.text)&&!1===e.isComment}function dt(e,t){if(e){for(var n=Object.create(null),r=fe?Reflect.ownKeys(e):Object.keys(e),a=0;a<r.length;a++){var i=r[a];if("__ob__"!==i){for(var o=e[i].from,s=t;s;){if(s._provided&&S(s._provided,o)){n[i]=s._provided[o];break}s=s.$parent}if(!s)if("default"in e[i]){var l=e[i].default;n[i]="function"==typeof l?l.call(t):l}else 0}}return n}}function ht(e,t){if(!e||!e.length)return{};for(var n={},r=0,a=e.length;r<a;r++){var i=e[r],o=i.data;if(o&&o.attrs&&o.attrs.slot&&delete o.attrs.slot,i.context!==t&&i.fnContext!==t||!o||null==o.slot)(n.default||(n.default=[])).push(i);else{var s=o.slot,l=n[s]||(n[s]=[]);"template"===i.tag?l.push.apply(l,i.children||[]):l.push(i)}}for(var c in n)n[c].every(vt)&&delete n[c];return n}function vt(e){return e.isComment&&!e.asyncFactory||" "===e.text}function mt(e,t,n){var r,i=Object.keys(t).length>0,o=e?!!e.$stable:!i,s=e&&e.$key;if(e){if(e._normalized)return e._normalized;if(o&&n&&n!==a&&s===n.$key&&!i&&!n.$hasNormal)return n;for(var l in r={},e)e[l]&&"$"!==l[0]&&(r[l]=bt(t,l,e[l]))}else r={};for(var c in t)c in r||(r[c]=gt(t,c));return e&&Object.isExtensible(e)&&(e._normalized=r),U(r,"$stable",o),U(r,"$key",s),U(r,"$hasNormal",i),r}function bt(e,t,n){var a=function(){var e=arguments.length?n.apply(null,arguments):n({});return(e=e&&"object"===(void 0===e?"undefined":r(e))&&!Array.isArray(e)?[e]:pt(e))&&(0===e.length||1===e.length&&e[0].isComment)?void 0:e};return n.proxy&&Object.defineProperty(e,t,{get:a,enumerable:!0,configurable:!0}),a}function gt(e,t){return function(){return e[t]}}function yt(e,t){var n,r,a,i,s;if(Array.isArray(e)||"string"==typeof e)for(n=new Array(e.length),r=0,a=e.length;r<a;r++)n[r]=t(e[r],r);else if("number"==typeof e)for(n=new Array(e),r=0;r<e;r++)n[r]=t(r+1,r);else if(c(e))if(fe&&e[Symbol.iterator]){n=[];for(var l=e[Symbol.iterator](),f=l.next();!f.done;)n.push(t(f.value,n.length)),f=l.next()}else for(i=Object.keys(e),n=new Array(i.length),r=0,a=i.length;r<a;r++)s=i[r],n[r]=t(e[s],s,r);return o(n)||(n=[]),n._isVList=!0,n}function wt(e,t,n,r){var a,i=this.$scopedSlots[e];i?(n=n||{},r&&(n=B(B({},r),n)),a=i(n)||t):a=this.$slots[e]||t;var o=n&&n.slot;return o?this.$createElement("template",{slot:o},a):a}function St(e){return je(this.$options,"filters",e)||P}function xt(e,t){return Array.isArray(e)?-1===e.indexOf(t):e!==t}function kt(e,t,n,r,a){var i=F.keyCodes[t]||n;return a&&r&&!F.keyCodes[t]?xt(a,r):i?xt(i,e):r?R(r)!==t:void 0}function At(e,t,n,r,a){if(n)if(c(n)){var i;Array.isArray(n)&&(n=O(n));var o=function(o){if("class"===o||"style"===o||g(o))i=e;else{var s=e.attrs&&e.attrs.type;i=r||F.mustUseProp(t,s,o)?e.domProps||(e.domProps={}):e.attrs||(e.attrs={})}var l=A(o),c=R(o);l in i||c in i||(i[o]=n[o],a&&((e.on||(e.on={}))["update:"+o]=function(e){n[o]=e}))};for(var s in n)o(s)}else;return e}function Mt(e,t){var n=this._staticTrees||(this._staticTrees=[]),r=n[e];return r&&!t||Rt(r=n[e]=this.$options.staticRenderFns[e].call(this._renderProxy,null,this),"__static__"+e,!1),r}function Tt(e,t,n){return Rt(e,"__once__"+t+(n?"_"+n:""),!0),e}function Rt(e,t,n){if(Array.isArray(e))for(var r=0;r<e.length;r++)e[r]&&"string"!=typeof e[r]&&Ct(e[r],t+"_"+r,n);else Ct(e,t,n)}function Ct(e,t,n){e.isStatic=!0,e.key=t,e.isOnce=n}function Et(e,t){if(t)if(u(t)){var n=e.on=e.on?B({},e.on):{};for(var r in t){var a=n[r],i=t[r];n[r]=a?[].concat(a,i):i}}else;return e}function Bt(e,t,n,r){t=t||{$stable:!n};for(var a=0;a<e.length;a++){var i=e[a];Array.isArray(i)?Bt(i,t,n):i&&(i.proxy&&(i.fn.proxy=!0),t[i.key]=i.fn)}return r&&(t.$key=r),t}function Ot(e,t){for(var n=0;n<t.length;n+=2){var r=t[n];"string"==typeof r&&r&&(e[t[n]]=t[n+1])}return e}function Lt(e,t){return"string"==typeof e?t+e:e}function It(e){e._o=Tt,e._n=v,e._s=h,e._l=yt,e._t=wt,e._q=$,e._i=N,e._m=Mt,e._f=St,e._k=kt,e._b=At,e._v=ye,e._e=ge,e._u=Bt,e._g=Et,e._d=Ot,e._p=Lt}function Pt(e,t,n,r,i){var o,l=this,c=i.options;S(r,"_uid")?(o=Object.create(r))._original=r:(o=r,r=r._original);var f=s(c._compiled),u=!f;this.data=e,this.props=t,this.children=n,this.parent=r,this.listeners=e.on||a,this.injections=dt(c.inject,r),this.slots=function(){return l.$slots||mt(e.scopedSlots,l.$slots=ht(n,r)),l.$slots},Object.defineProperty(this,"scopedSlots",{enumerable:!0,get:function(){return mt(e.scopedSlots,this.slots())}}),f&&(this.$options=c,this.$slots=this.slots(),this.$scopedSlots=mt(e.scopedSlots,this.$slots)),c._scopeId?this._c=function(e,t,n,a){var i=Vt(o,e,t,n,a,u);return i&&!Array.isArray(i)&&(i.fnScopeId=c._scopeId,i.fnContext=r),i}:this._c=function(e,t,n,r){return Vt(o,e,t,n,r,u)}}function $t(e,t,n,r,a){var i=we(e);return i.fnContext=n,i.fnOptions=r,t.slot&&((i.data||(i.data={})).slot=t.slot),i}function Nt(e,t){for(var n in t)e[A(n)]=t[n]}It(Pt.prototype);var Dt={init:function(e,t){if(e.componentInstance&&!e.componentInstance._isDestroyed&&e.data.keepAlive){var n=e;Dt.prepatch(n,n)}else{(e.componentInstance=function(e,t){var n={_isComponent:!0,_parentVnode:e,parent:t},r=e.data.inlineTemplate;o(r)&&(n.render=r.render,n.staticRenderFns=r.staticRenderFns);return new e.componentOptions.Ctor(n)}(e,Qt)).$mount(t?e.elm:void 0,t)}},prepatch:function(e,t){var n=t.componentOptions;!function(e,t,n,r,i){0;var o=r.data.scopedSlots,s=e.$scopedSlots,l=!!(o&&!o.$stable||s!==a&&!s.$stable||o&&e.$scopedSlots.$key!==o.$key),c=!!(i||e.$options._renderChildren||l);e.$options._parentVnode=r,e.$vnode=r,e._vnode&&(e._vnode.parent=r);if(e.$options._renderChildren=i,e.$attrs=r.data.attrs||a,e.$listeners=n||a,t&&e.$options.props){Me(!1);for(var f=e._props,u=e.$options._propKeys||[],p=0;p<u.length;p++){var _=u[p],d=e.$options.props;f[_]=Fe(_,d,t,e)}Me(!0),e.$options.propsData=t}n=n||a;var h=e.$options._parentListeners;e.$options._parentListeners=n,Zt(e,n,h),c&&(e.$slots=ht(i,r.context),e.$forceUpdate());0}(t.componentInstance=e.componentInstance,n.propsData,n.listeners,t,n.children)},insert:function(e){var t,n=e.context,r=e.componentInstance;r._isMounted||(r._isMounted=!0,nn(r,"mounted")),e.data.keepAlive&&(n._isMounted?((t=r)._inactive=!1,an.push(t)):tn(r,!0))},destroy:function(e){var t=e.componentInstance;t._isDestroyed||(e.data.keepAlive?function e(t,n){if(n&&(t._directInactive=!0,en(t)))return;if(!t._inactive){t._inactive=!0;for(var r=0;r<t.$children.length;r++)e(t.$children[r]);nn(t,"deactivated")}}(t,!0):t.$destroy())}},Ht=Object.keys(Dt);function jt(e,t,n,r,l){if(!i(e)){var f=n.$options._base;if(c(e)&&(e=f.extend(e)),"function"==typeof e){var u;if(i(e.cid)&&void 0===(e=function(e,t){if(s(e.error)&&o(e.errorComp))return e.errorComp;if(o(e.resolved))return e.resolved;var n=Ut;n&&o(e.owners)&&-1===e.owners.indexOf(n)&&e.owners.push(n);if(s(e.loading)&&o(e.loadingComp))return e.loadingComp;if(n&&!o(e.owners)){var r=e.owners=[n],a=!0,l=null,f=null;n.$on("hook:destroyed",(function(){return y(r,n)}));var u=function(e){for(var t=0,n=r.length;t<n;t++)r[t].$forceUpdate();e&&(r.length=0,null!==l&&(clearTimeout(l),l=null),null!==f&&(clearTimeout(f),f=null))},p=D((function(n){e.resolved=Xt(n,t),a?r.length=0:u(!0)})),_=D((function(t){o(e.errorComp)&&(e.error=!0,u(!0))})),h=e(p,_);return c(h)&&(d(h)?i(e.resolved)&&h.then(p,_):d(h.component)&&(h.component.then(p,_),o(h.error)&&(e.errorComp=Xt(h.error,t)),o(h.loading)&&(e.loadingComp=Xt(h.loading,t),0===h.delay?e.loading=!0:l=setTimeout((function(){l=null,i(e.resolved)&&i(e.error)&&(e.loading=!0,u(!1))}),h.delay||200)),o(h.timeout)&&(f=setTimeout((function(){f=null,i(e.resolved)&&_(null)}),h.timeout)))),a=!1,e.loading?e.loadingComp:e.resolved}}(u=e,f)))return function(e,t,n,r,a){var i=ge();return i.asyncFactory=e,i.asyncMeta={data:t,context:n,children:r,tag:a},i}(u,t,n,r,l);t=t||{},An(e),o(t.model)&&function(e,t){var n=e.model&&e.model.prop||"value",r=e.model&&e.model.event||"input";(t.attrs||(t.attrs={}))[n]=t.model.value;var a=t.on||(t.on={}),i=a[r],s=t.model.callback;o(i)?(Array.isArray(i)?-1===i.indexOf(s):i!==s)&&(a[r]=[s].concat(i)):a[r]=s}(e.options,t);var p=function(e,t,n){var r=t.options.props;if(!i(r)){var a={},s=e.attrs,l=e.props;if(o(s)||o(l))for(var c in r){var f=R(c);ut(a,l,c,f,!0)||ut(a,s,c,f,!1)}return a}}(t,e);if(s(e.options.functional))return function(e,t,n,r,i){var s=e.options,l={},c=s.props;if(o(c))for(var f in c)l[f]=Fe(f,c,t||a);else o(n.attrs)&&Nt(l,n.attrs),o(n.props)&&Nt(l,n.props);var u=new Pt(n,l,i,r,e),p=s.render.call(null,u._c,u);if(p instanceof me)return $t(p,n,u.parent,s,u);if(Array.isArray(p)){for(var _=pt(p)||[],d=new Array(_.length),h=0;h<_.length;h++)d[h]=$t(_[h],n,u.parent,s,u);return d}}(e,p,t,n,r);var _=t.on;if(t.on=t.nativeOn,s(e.options.abstract)){var h=t.slot;t={},h&&(t.slot=h)}!function(e){for(var t=e.hook||(e.hook={}),n=0;n<Ht.length;n++){var r=Ht[n],a=t[r],i=Dt[r];a===i||a&&a._merged||(t[r]=a?Ft(i,a):i)}}(t);var v=e.options.name||l;return new me("vue-component-"+e.cid+(v?"-"+v:""),t,void 0,void 0,void 0,n,{Ctor:e,propsData:p,listeners:_,tag:l,children:r},u)}}}function Ft(e,t){var n=function(n,r){e(n,r),t(n,r)};return n._merged=!0,n}function Vt(e,t,n,r,a,f){return(Array.isArray(n)||l(n))&&(a=r,r=n,n=void 0),s(f)&&(a=2),function(e,t,n,r,a){if(o(n)&&o(n.__ob__))return ge();o(n)&&o(n.is)&&(t=n.is);if(!t)return ge();0;Array.isArray(r)&&"function"==typeof r[0]&&((n=n||{}).scopedSlots={default:r[0]},r.length=0);2===a?r=pt(r):1===a&&(r=function(e){for(var t=0;t<e.length;t++)if(Array.isArray(e[t]))return Array.prototype.concat.apply([],e);return e}(r));var l,f;if("string"==typeof t){var u;f=e.$vnode&&e.$vnode.ns||F.getTagNamespace(t),l=F.isReservedTag(t)?new me(F.parsePlatformTagName(t),n,r,void 0,void 0,e):n&&n.pre||!o(u=je(e.$options,"components",t))?new me(t,n,r,void 0,void 0,e):jt(u,n,e,r,t)}else l=jt(t,n,e,r);return Array.isArray(l)?l:o(l)?(o(f)&&function e(t,n,r){t.ns=n,"foreignObject"===t.tag&&(n=void 0,r=!0);if(o(t.children))for(var a=0,l=t.children.length;a<l;a++){var c=t.children[a];o(c.tag)&&(i(c.ns)||s(r)&&"svg"!==c.tag)&&e(c,n,r)}}(l,f),o(n)&&function(e){c(e.style)&&ot(e.style);c(e.class)&&ot(e.class)}(n),l):ge()}(e,t,n,r,a)}var zt,Ut=null;function Xt(e,t){return(e.__esModule||fe&&"Module"===e[Symbol.toStringTag])&&(e=e.default),c(e)?t.extend(e):e}function qt(e){return e.isComment&&e.asyncFactory}function Yt(e){if(Array.isArray(e))for(var t=0;t<e.length;t++){var n=e[t];if(o(n)&&(o(n.componentOptions)||qt(n)))return n}}function Wt(e,t){zt.$on(e,t)}function Gt(e,t){zt.$off(e,t)}function Kt(e,t){var n=zt;return function r(){var a=t.apply(null,arguments);null!==a&&n.$off(e,r)}}function Zt(e,t,n){zt=e,ct(t,n||{},Wt,Gt,Kt,e),zt=void 0}var Qt=null;function Jt(e){var t=Qt;return Qt=e,function(){Qt=t}}function en(e){for(;e&&(e=e.$parent);)if(e._inactive)return!0;return!1}function tn(e,t){if(t){if(e._directInactive=!1,en(e))return}else if(e._directInactive)return;if(e._inactive||null===e._inactive){e._inactive=!1;for(var n=0;n<e.$children.length;n++)tn(e.$children[n]);nn(e,"activated")}}function nn(e,t){he();var n=e.$options[t],r=t+" hook";if(n)for(var a=0,i=n.length;a<i;a++)qe(n[a],e,null,e,r);e._hasHookEvent&&e.$emit("hook:"+t),ve()}var rn=[],an=[],on={},sn=!1,ln=!1,cn=0;var fn=0,un=Date.now;if(W&&!Q){var pn=window.performance;pn&&"function"==typeof pn.now&&un()>document.createEvent("Event").timeStamp&&(un=function(){return pn.now()})}function _n(){var e,t;for(fn=un(),ln=!0,rn.sort((function(e,t){return e.id-t.id})),cn=0;cn<rn.length;cn++)(e=rn[cn]).before&&e.before(),t=e.id,on[t]=null,e.run();var n=an.slice(),r=rn.slice();cn=rn.length=an.length=0,on={},sn=ln=!1,function(e){for(var t=0;t<e.length;t++)e[t]._inactive=!0,tn(e[t],!0)}(n),function(e){var t=e.length;for(;t--;){var n=e[t],r=n.vm;r._watcher===n&&r._isMounted&&!r._isDestroyed&&nn(r,"updated")}}(r),se&&F.devtools&&se.emit("flush")}var dn=0,hn=function(e,t,n,r,a){this.vm=e,a&&(e._watcher=this),e._watchers.push(this),r?(this.deep=!!r.deep,this.user=!!r.user,this.lazy=!!r.lazy,this.sync=!!r.sync,this.before=r.before):this.deep=this.user=this.lazy=this.sync=!1,this.cb=n,this.id=++dn,this.active=!0,this.dirty=this.lazy,this.deps=[],this.newDeps=[],this.depIds=new ce,this.newDepIds=new ce,this.expression="","function"==typeof t?this.getter=t:(this.getter=function(e){if(!X.test(e)){var t=e.split(".");return function(e){for(var n=0;n<t.length;n++){if(!e)return;e=e[t[n]]}return e}}}(t),this.getter||(this.getter=L)),this.value=this.lazy?void 0:this.get()};hn.prototype.get=function(){var e;he(this);var t=this.vm;try{e=this.getter.call(t,t)}catch(e){if(!this.user)throw e;Xe(e,t,'getter for watcher "'+this.expression+'"')}finally{this.deep&&ot(e),ve(),this.cleanupDeps()}return e},hn.prototype.addDep=function(e){var t=e.id;this.newDepIds.has(t)||(this.newDepIds.add(t),this.newDeps.push(e),this.depIds.has(t)||e.addSub(this))},hn.prototype.cleanupDeps=function(){for(var e=this.deps.length;e--;){var t=this.deps[e];this.newDepIds.has(t.id)||t.removeSub(this)}var n=this.depIds;this.depIds=this.newDepIds,this.newDepIds=n,this.newDepIds.clear(),n=this.deps,this.deps=this.newDeps,this.newDeps=n,this.newDeps.length=0},hn.prototype.update=function(){this.lazy?this.dirty=!0:this.sync?this.run():function(e){var t=e.id;if(null==on[t]){if(on[t]=!0,ln){for(var n=rn.length-1;n>cn&&rn[n].id>e.id;)n--;rn.splice(n+1,0,e)}else rn.push(e);sn||(sn=!0,at(_n))}}(this)},hn.prototype.run=function(){if(this.active){var e=this.get();if(e!==this.value||c(e)||this.deep){var t=this.value;if(this.value=e,this.user)try{this.cb.call(this.vm,e,t)}catch(e){Xe(e,this.vm,'callback for watcher "'+this.expression+'"')}else this.cb.call(this.vm,e,t)}}},hn.prototype.evaluate=function(){this.value=this.get(),this.dirty=!1},hn.prototype.depend=function(){for(var e=this.deps.length;e--;)this.deps[e].depend()},hn.prototype.teardown=function(){if(this.active){this.vm._isBeingDestroyed||y(this.vm._watchers,this);for(var e=this.deps.length;e--;)this.deps[e].removeSub(this);this.active=!1}};var vn={enumerable:!0,configurable:!0,get:L,set:L};function mn(e,t,n){vn.get=function(){return this[t][n]},vn.set=function(e){this[t][n]=e},Object.defineProperty(e,n,vn)}function bn(e){e._watchers=[];var t=e.$options;t.props&&function(e,t){var n=e.$options.propsData||{},r=e._props={},a=e.$options._propKeys=[];e.$parent&&Me(!1);var i=function(i){a.push(i);var o=Fe(i,t,n,e);Ce(r,i,o),i in e||mn(e,"_props",i)};for(var o in t)i(o);Me(!0)}(e,t.props),t.methods&&function(e,t){e.$options.props;for(var n in t)e[n]="function"!=typeof t[n]?L:C(t[n],e)}(e,t.methods),t.data?function(e){var t=e.$options.data;u(t=e._data="function"==typeof t?function(e,t){he();try{return e.call(t,t)}catch(e){return Xe(e,t,"data()"),{}}finally{ve()}}(t,e):t||{})||(t={});var n=Object.keys(t),r=e.$options.props,a=(e.$options.methods,n.length);for(;a--;){var i=n[a];0,r&&S(r,i)||z(i)||mn(e,"_data",i)}Re(t,!0)}(e):Re(e._data={},!0),t.computed&&function(e,t){var n=e._computedWatchers=Object.create(null),r=oe();for(var a in t){var i=t[a],o="function"==typeof i?i:i.get;0,r||(n[a]=new hn(e,o||L,L,gn)),a in e||yn(e,a,i)}}(e,t.computed),t.watch&&t.watch!==re&&function(e,t){for(var n in t){var r=t[n];if(Array.isArray(r))for(var a=0;a<r.length;a++)xn(e,n,r[a]);else xn(e,n,r)}}(e,t.watch)}var gn={lazy:!0};function yn(e,t,n){var r=!oe();"function"==typeof n?(vn.get=r?wn(t):Sn(n),vn.set=L):(vn.get=n.get?r&&!1!==n.cache?wn(t):Sn(n.get):L,vn.set=n.set||L),Object.defineProperty(e,t,vn)}function wn(e){return function(){var t=this._computedWatchers&&this._computedWatchers[e];if(t)return t.dirty&&t.evaluate(),_e.target&&t.depend(),t.value}}function Sn(e){return function(){return e.call(this,this)}}function xn(e,t,n,r){return u(n)&&(r=n,n=n.handler),"string"==typeof n&&(n=e[n]),e.$watch(t,n,r)}var kn=0;function An(e){var t=e.options;if(e.super){var n=An(e.super);if(n!==e.superOptions){e.superOptions=n;var r=function(e){var t,n=e.options,r=e.sealedOptions;for(var a in n)n[a]!==r[a]&&(t||(t={}),t[a]=n[a]);return t}(e);r&&B(e.extendOptions,r),(t=e.options=He(n,e.extendOptions)).name&&(t.components[t.name]=e)}}return t}function Mn(e){this._init(e)}function Tn(e){e.cid=0;var t=1;e.extend=function(e){e=e||{};var n=this,r=n.cid,a=e._Ctor||(e._Ctor={});if(a[r])return a[r];var i=e.name||n.options.name;var o=function(e){this._init(e)};return(o.prototype=Object.create(n.prototype)).constructor=o,o.cid=t++,o.options=He(n.options,e),o.super=n,o.options.props&&function(e){var t=e.options.props;for(var n in t)mn(e.prototype,"_props",n)}(o),o.options.computed&&function(e){var t=e.options.computed;for(var n in t)yn(e.prototype,n,t[n])}(o),o.extend=n.extend,o.mixin=n.mixin,o.use=n.use,H.forEach((function(e){o[e]=n[e]})),i&&(o.options.components[i]=o),o.superOptions=n.options,o.extendOptions=e,o.sealedOptions=B({},o.options),a[r]=o,o}}function Rn(e){return e&&(e.Ctor.options.name||e.tag)}function Cn(e,t){return Array.isArray(e)?e.indexOf(t)>-1:"string"==typeof e?e.split(",").indexOf(t)>-1:!!p(e)&&e.test(t)}function En(e,t){var n=e.cache,r=e.keys,a=e._vnode;for(var i in n){var o=n[i];if(o){var s=Rn(o.componentOptions);s&&!t(s)&&Bn(n,i,r,a)}}}function Bn(e,t,n,r){var a=e[t];!a||r&&a.tag===r.tag||a.componentInstance.$destroy(),e[t]=null,y(n,t)}!function(e){e.prototype._init=function(e){var t=this;t._uid=kn++,t._isVue=!0,e&&e._isComponent?function(e,t){var n=e.$options=Object.create(e.constructor.options),r=t._parentVnode;n.parent=t.parent,n._parentVnode=r;var a=r.componentOptions;n.propsData=a.propsData,n._parentListeners=a.listeners,n._renderChildren=a.children,n._componentTag=a.tag,t.render&&(n.render=t.render,n.staticRenderFns=t.staticRenderFns)}(t,e):t.$options=He(An(t.constructor),e||{},t),t._renderProxy=t,t._self=t,function(e){var t=e.$options,n=t.parent;if(n&&!t.abstract){for(;n.$options.abstract&&n.$parent;)n=n.$parent;n.$children.push(e)}e.$parent=n,e.$root=n?n.$root:e,e.$children=[],e.$refs={},e._watcher=null,e._inactive=null,e._directInactive=!1,e._isMounted=!1,e._isDestroyed=!1,e._isBeingDestroyed=!1}(t),function(e){e._events=Object.create(null),e._hasHookEvent=!1;var t=e.$options._parentListeners;t&&Zt(e,t)}(t),function(e){e._vnode=null,e._staticTrees=null;var t=e.$options,n=e.$vnode=t._parentVnode,r=n&&n.context;e.$slots=ht(t._renderChildren,r),e.$scopedSlots=a,e._c=function(t,n,r,a){return Vt(e,t,n,r,a,!1)},e.$createElement=function(t,n,r,a){return Vt(e,t,n,r,a,!0)};var i=n&&n.data;Ce(e,"$attrs",i&&i.attrs||a,null,!0),Ce(e,"$listeners",t._parentListeners||a,null,!0)}(t),nn(t,"beforeCreate"),function(e){var t=dt(e.$options.inject,e);t&&(Me(!1),Object.keys(t).forEach((function(n){Ce(e,n,t[n])})),Me(!0))}(t),bn(t),function(e){var t=e.$options.provide;t&&(e._provided="function"==typeof t?t.call(e):t)}(t),nn(t,"created"),t.$options.el&&t.$mount(t.$options.el)}}(Mn),function(e){var t={get:function(){return this._data}},n={get:function(){return this._props}};Object.defineProperty(e.prototype,"$data",t),Object.defineProperty(e.prototype,"$props",n),e.prototype.$set=Ee,e.prototype.$delete=Be,e.prototype.$watch=function(e,t,n){if(u(t))return xn(this,e,t,n);(n=n||{}).user=!0;var r=new hn(this,e,t,n);if(n.immediate)try{t.call(this,r.value)}catch(e){Xe(e,this,'callback for immediate watcher "'+r.expression+'"')}return function(){r.teardown()}}}(Mn),function(e){var t=/^hook:/;e.prototype.$on=function(e,n){var r=this;if(Array.isArray(e))for(var a=0,i=e.length;a<i;a++)r.$on(e[a],n);else(r._events[e]||(r._events[e]=[])).push(n),t.test(e)&&(r._hasHookEvent=!0);return r},e.prototype.$once=function(e,t){var n=this;function r(){n.$off(e,r),t.apply(n,arguments)}return r.fn=t,n.$on(e,r),n},e.prototype.$off=function(e,t){var n=this;if(!arguments.length)return n._events=Object.create(null),n;if(Array.isArray(e)){for(var r=0,a=e.length;r<a;r++)n.$off(e[r],t);return n}var i,o=n._events[e];if(!o)return n;if(!t)return n._events[e]=null,n;for(var s=o.length;s--;)if((i=o[s])===t||i.fn===t){o.splice(s,1);break}return n},e.prototype.$emit=function(e){var t=this,n=t._events[e];if(n){n=n.length>1?E(n):n;for(var r=E(arguments,1),a='event handler for "'+e+'"',i=0,o=n.length;i<o;i++)qe(n[i],t,r,t,a)}return t}}(Mn),function(e){e.prototype._update=function(e,t){var n=this,r=n.$el,a=n._vnode,i=Jt(n);n._vnode=e,n.$el=a?n.__patch__(a,e):n.__patch__(n.$el,e,t,!1),i(),r&&(r.__vue__=null),n.$el&&(n.$el.__vue__=n),n.$vnode&&n.$parent&&n.$vnode===n.$parent._vnode&&(n.$parent.$el=n.$el)},e.prototype.$forceUpdate=function(){this._watcher&&this._watcher.update()},e.prototype.$destroy=function(){var e=this;if(!e._isBeingDestroyed){nn(e,"beforeDestroy"),e._isBeingDestroyed=!0;var t=e.$parent;!t||t._isBeingDestroyed||e.$options.abstract||y(t.$children,e),e._watcher&&e._watcher.teardown();for(var n=e._watchers.length;n--;)e._watchers[n].teardown();e._data.__ob__&&e._data.__ob__.vmCount--,e._isDestroyed=!0,e.__patch__(e._vnode,null),nn(e,"destroyed"),e.$off(),e.$el&&(e.$el.__vue__=null),e.$vnode&&(e.$vnode.parent=null)}}}(Mn),function(e){It(e.prototype),e.prototype.$nextTick=function(e){return at(e,this)},e.prototype._render=function(){var e,t=this,n=t.$options,r=n.render,a=n._parentVnode;a&&(t.$scopedSlots=mt(a.data.scopedSlots,t.$slots,t.$scopedSlots)),t.$vnode=a;try{Ut=t,e=r.call(t._renderProxy,t.$createElement)}catch(n){Xe(n,t,"render"),e=t._vnode}finally{Ut=null}return Array.isArray(e)&&1===e.length&&(e=e[0]),e instanceof me||(e=ge()),e.parent=a,e}}(Mn);var On=[String,RegExp,Array],Ln={KeepAlive:{name:"keep-alive",abstract:!0,props:{include:On,exclude:On,max:[String,Number]},created:function(){this.cache=Object.create(null),this.keys=[]},destroyed:function(){for(var e in this.cache)Bn(this.cache,e,this.keys)},mounted:function(){var e=this;this.$watch("include",(function(t){En(e,(function(e){return Cn(t,e)}))})),this.$watch("exclude",(function(t){En(e,(function(e){return!Cn(t,e)}))}))},render:function(){var e=this.$slots.default,t=Yt(e),n=t&&t.componentOptions;if(n){var r=Rn(n),a=this.include,i=this.exclude;if(a&&(!r||!Cn(a,r))||i&&r&&Cn(i,r))return t;var o=this.cache,s=this.keys,l=null==t.key?n.Ctor.cid+(n.tag?"::"+n.tag:""):t.key;o[l]?(t.componentInstance=o[l].componentInstance,y(s,l),s.push(l)):(o[l]=t,s.push(l),this.max&&s.length>parseInt(this.max)&&Bn(o,s[0],s,this._vnode)),t.data.keepAlive=!0}return t||e&&e[0]}}};!function(e){var t={get:function(){return F}};Object.defineProperty(e,"config",t),e.util={warn:ue,extend:B,mergeOptions:He,defineReactive:Ce},e.set=Ee,e.delete=Be,e.nextTick=at,e.observable=function(e){return Re(e),e},e.options=Object.create(null),H.forEach((function(t){e.options[t+"s"]=Object.create(null)})),e.options._base=e,B(e.options.components,Ln),function(e){e.use=function(e){var t=this._installedPlugins||(this._installedPlugins=[]);if(t.indexOf(e)>-1)return this;var n=E(arguments,1);return n.unshift(this),"function"==typeof e.install?e.install.apply(e,n):"function"==typeof e&&e.apply(null,n),t.push(e),this}}(e),function(e){e.mixin=function(e){return this.options=He(this.options,e),this}}(e),Tn(e),function(e){H.forEach((function(t){e[t]=function(e,n){return n?("component"===t&&u(n)&&(n.name=n.name||e,n=this.options._base.extend(n)),"directive"===t&&"function"==typeof n&&(n={bind:n,update:n}),this.options[t+"s"][e]=n,n):this.options[t+"s"][e]}}))}(e)}(Mn),Object.defineProperty(Mn.prototype,"$isServer",{get:oe}),Object.defineProperty(Mn.prototype,"$ssrContext",{get:function(){return this.$vnode&&this.$vnode.ssrContext}}),Object.defineProperty(Mn,"FunctionalRenderContext",{value:Pt}),Mn.version="2.6.10";var In=m("style,class"),Pn=m("input,textarea,option,select,progress"),$n=function(e,t,n){return"value"===n&&Pn(e)&&"button"!==t||"selected"===n&&"option"===e||"checked"===n&&"input"===e||"muted"===n&&"video"===e},Nn=m("contenteditable,draggable,spellcheck"),Dn=m("events,caret,typing,plaintext-only"),Hn=m("allowfullscreen,async,autofocus,autoplay,checked,compact,controls,declare,default,defaultchecked,defaultmuted,defaultselected,defer,disabled,enabled,formnovalidate,hidden,indeterminate,inert,ismap,itemscope,loop,multiple,muted,nohref,noresize,noshade,novalidate,nowrap,open,pauseonexit,readonly,required,reversed,scoped,seamless,selected,sortable,translate,truespeed,typemustmatch,visible"),jn="http://www.w3.org/1999/xlink",Fn=function(e){return":"===e.charAt(5)&&"xlink"===e.slice(0,5)},Vn=function(e){return Fn(e)?e.slice(6,e.length):""},zn=function(e){return null==e||!1===e};function Un(e){for(var t=e.data,n=e,r=e;o(r.componentInstance);)(r=r.componentInstance._vnode)&&r.data&&(t=Xn(r.data,t));for(;o(n=n.parent);)n&&n.data&&(t=Xn(t,n.data));return function(e,t){if(o(e)||o(t))return qn(e,Yn(t));return""}(t.staticClass,t.class)}function Xn(e,t){return{staticClass:qn(e.staticClass,t.staticClass),class:o(e.class)?[e.class,t.class]:t.class}}function qn(e,t){return e?t?e+" "+t:e:t||""}function Yn(e){return Array.isArray(e)?function(e){for(var t,n="",r=0,a=e.length;r<a;r++)o(t=Yn(e[r]))&&""!==t&&(n&&(n+=" "),n+=t);return n}(e):c(e)?function(e){var t="";for(var n in e)e[n]&&(t&&(t+=" "),t+=n);return t}(e):"string"==typeof e?e:""}var Wn={svg:"http://www.w3.org/2000/svg",math:"http://www.w3.org/1998/Math/MathML"},Gn=m("html,body,base,head,link,meta,style,title,address,article,aside,footer,header,h1,h2,h3,h4,h5,h6,hgroup,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,rtc,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,menuitem,summary,content,element,shadow,template,blockquote,iframe,tfoot"),Kn=m("svg,animate,circle,clippath,cursor,defs,desc,ellipse,filter,font-face,foreignObject,g,glyph,image,line,marker,mask,missing-glyph,path,pattern,polygon,polyline,rect,switch,symbol,text,textpath,tspan,use,view",!0),Zn=function(e){return Gn(e)||Kn(e)};function Qn(e){return Kn(e)?"svg":"math"===e?"math":void 0}var Jn=Object.create(null);var er=m("text,number,password,search,email,tel,url");function tr(e){if("string"==typeof e){var t=document.querySelector(e);return t||document.createElement("div")}return e}var nr=Object.freeze({createElement:function(e,t){var n=document.createElement(e);return"select"!==e||t.data&&t.data.attrs&&void 0!==t.data.attrs.multiple&&n.setAttribute("multiple","multiple"),n},createElementNS:function(e,t){return document.createElementNS(Wn[e],t)},createTextNode:function(e){return document.createTextNode(e)},createComment:function(e){return document.createComment(e)},insertBefore:function(e,t,n){e.insertBefore(t,n)},removeChild:function(e,t){e.removeChild(t)},appendChild:function(e,t){e.appendChild(t)},parentNode:function(e){return e.parentNode},nextSibling:function(e){return e.nextSibling},tagName:function(e){return e.tagName},setTextContent:function(e,t){e.textContent=t},setStyleScope:function(e,t){e.setAttribute(t,"")}}),rr={create:function(e,t){ar(t)},update:function(e,t){e.data.ref!==t.data.ref&&(ar(e,!0),ar(t))},destroy:function(e){ar(e,!0)}};function ar(e,t){var n=e.data.ref;if(o(n)){var r=e.context,a=e.componentInstance||e.elm,i=r.$refs;t?Array.isArray(i[n])?y(i[n],a):i[n]===a&&(i[n]=void 0):e.data.refInFor?Array.isArray(i[n])?i[n].indexOf(a)<0&&i[n].push(a):i[n]=[a]:i[n]=a}}var ir=new me("",{},[]),or=["create","activate","update","remove","destroy"];function sr(e,t){return e.key===t.key&&(e.tag===t.tag&&e.isComment===t.isComment&&o(e.data)===o(t.data)&&function(e,t){if("input"!==e.tag)return!0;var n,r=o(n=e.data)&&o(n=n.attrs)&&n.type,a=o(n=t.data)&&o(n=n.attrs)&&n.type;return r===a||er(r)&&er(a)}(e,t)||s(e.isAsyncPlaceholder)&&e.asyncFactory===t.asyncFactory&&i(t.asyncFactory.error))}function lr(e,t,n){var r,a,i={};for(r=t;r<=n;++r)o(a=e[r].key)&&(i[a]=r);return i}var cr={create:fr,update:fr,destroy:function(e){fr(e,ir)}};function fr(e,t){(e.data.directives||t.data.directives)&&function(e,t){var n,r,a,i=e===ir,o=t===ir,s=pr(e.data.directives,e.context),l=pr(t.data.directives,t.context),c=[],f=[];for(n in l)r=s[n],a=l[n],r?(a.oldValue=r.value,a.oldArg=r.arg,dr(a,"update",t,e),a.def&&a.def.componentUpdated&&f.push(a)):(dr(a,"bind",t,e),a.def&&a.def.inserted&&c.push(a));if(c.length){var u=function(){for(var n=0;n<c.length;n++)dr(c[n],"inserted",t,e)};i?ft(t,"insert",u):u()}f.length&&ft(t,"postpatch",(function(){for(var n=0;n<f.length;n++)dr(f[n],"componentUpdated",t,e)}));if(!i)for(n in s)l[n]||dr(s[n],"unbind",e,e,o)}(e,t)}var ur=Object.create(null);function pr(e,t){var n,r,a=Object.create(null);if(!e)return a;for(n=0;n<e.length;n++)(r=e[n]).modifiers||(r.modifiers=ur),a[_r(r)]=r,r.def=je(t.$options,"directives",r.name);return a}function _r(e){return e.rawName||e.name+"."+Object.keys(e.modifiers||{}).join(".")}function dr(e,t,n,r,a){var i=e.def&&e.def[t];if(i)try{i(n.elm,e,n,r,a)}catch(r){Xe(r,n.context,"directive "+e.name+" "+t+" hook")}}var hr=[rr,cr];function vr(e,t){var n=t.componentOptions;if(!(o(n)&&!1===n.Ctor.options.inheritAttrs||i(e.data.attrs)&&i(t.data.attrs))){var r,a,s=t.elm,l=e.data.attrs||{},c=t.data.attrs||{};for(r in o(c.__ob__)&&(c=t.data.attrs=B({},c)),c)a=c[r],l[r]!==a&&mr(s,r,a);for(r in(Q||ee)&&c.value!==l.value&&mr(s,"value",c.value),l)i(c[r])&&(Fn(r)?s.removeAttributeNS(jn,Vn(r)):Nn(r)||s.removeAttribute(r))}}function mr(e,t,n){e.tagName.indexOf("-")>-1?br(e,t,n):Hn(t)?zn(n)?e.removeAttribute(t):(n="allowfullscreen"===t&&"EMBED"===e.tagName?"true":t,e.setAttribute(t,n)):Nn(t)?e.setAttribute(t,function(e,t){return zn(t)||"false"===t?"false":"contenteditable"===e&&Dn(t)?t:"true"}(t,n)):Fn(t)?zn(n)?e.removeAttributeNS(jn,Vn(t)):e.setAttributeNS(jn,t,n):br(e,t,n)}function br(e,t,n){if(zn(n))e.removeAttribute(t);else{if(Q&&!J&&"TEXTAREA"===e.tagName&&"placeholder"===t&&""!==n&&!e.__ieph){e.addEventListener("input",(function t(n){n.stopImmediatePropagation(),e.removeEventListener("input",t)})),e.__ieph=!0}e.setAttribute(t,n)}}var gr={create:vr,update:vr};function yr(e,t){var n=t.elm,r=t.data,a=e.data;if(!(i(r.staticClass)&&i(r.class)&&(i(a)||i(a.staticClass)&&i(a.class)))){var s=Un(t),l=n._transitionClasses;o(l)&&(s=qn(s,Yn(l))),s!==n._prevClass&&(n.setAttribute("class",s),n._prevClass=s)}}var wr,Sr,xr,kr,Ar,Mr,Tr={create:yr,update:yr},Rr=/[\w).+\-_$\]]/;function Cr(e){var t,n,r,a,i,o=!1,s=!1,l=!1,c=!1,f=0,u=0,p=0,_=0;for(r=0;r<e.length;r++)if(n=t,t=e.charCodeAt(r),o)39===t&&92!==n&&(o=!1);else if(s)34===t&&92!==n&&(s=!1);else if(l)96===t&&92!==n&&(l=!1);else if(c)47===t&&92!==n&&(c=!1);else if(124!==t||124===e.charCodeAt(r+1)||124===e.charCodeAt(r-1)||f||u||p){switch(t){case 34:s=!0;break;case 39:o=!0;break;case 96:l=!0;break;case 40:p++;break;case 41:p--;break;case 91:u++;break;case 93:u--;break;case 123:f++;break;case 125:f--}if(47===t){for(var d=r-1,h=void 0;d>=0&&" "===(h=e.charAt(d));d--);h&&Rr.test(h)||(c=!0)}}else void 0===a?(_=r+1,a=e.slice(0,r).trim()):v();function v(){(i||(i=[])).push(e.slice(_,r).trim()),_=r+1}if(void 0===a?a=e.slice(0,r).trim():0!==_&&v(),i)for(r=0;r<i.length;r++)a=Er(a,i[r]);return a}function Er(e,t){var n=t.indexOf("(");if(n<0)return'_f("'+t+'")('+e+")";var r=t.slice(0,n),a=t.slice(n+1);return'_f("'+r+'")('+e+(")"!==a?","+a:a)}function Br(e,t){console.error("[Vue compiler]: "+e)}function Or(e,t){return e?e.map((function(e){return e[t]})).filter((function(e){return e})):[]}function Lr(e,t,n,r,a){(e.props||(e.props=[])).push(Vr({name:t,value:n,dynamic:a},r)),e.plain=!1}function Ir(e,t,n,r,a){(a?e.dynamicAttrs||(e.dynamicAttrs=[]):e.attrs||(e.attrs=[])).push(Vr({name:t,value:n,dynamic:a},r)),e.plain=!1}function Pr(e,t,n,r){e.attrsMap[t]=n,e.attrsList.push(Vr({name:t,value:n},r))}function $r(e,t,n,r,a,i,o,s){(e.directives||(e.directives=[])).push(Vr({name:t,rawName:n,value:r,arg:a,isDynamicArg:i,modifiers:o},s)),e.plain=!1}function Nr(e,t,n){return n?"_p("+t+',"'+e+'")':e+t}function Dr(e,t,n,r,i,o,s,l){var c;(r=r||a).right?l?t="("+t+")==='click'?'contextmenu':("+t+")":"click"===t&&(t="contextmenu",delete r.right):r.middle&&(l?t="("+t+")==='click'?'mouseup':("+t+")":"click"===t&&(t="mouseup")),r.capture&&(delete r.capture,t=Nr("!",t,l)),r.once&&(delete r.once,t=Nr("~",t,l)),r.passive&&(delete r.passive,t=Nr("&",t,l)),r.native?(delete r.native,c=e.nativeEvents||(e.nativeEvents={})):c=e.events||(e.events={});var f=Vr({value:n.trim(),dynamic:l},s);r!==a&&(f.modifiers=r);var u=c[t];Array.isArray(u)?i?u.unshift(f):u.push(f):c[t]=u?i?[f,u]:[u,f]:f,e.plain=!1}function Hr(e,t,n){var r=jr(e,":"+t)||jr(e,"v-bind:"+t);if(null!=r)return Cr(r);if(!1!==n){var a=jr(e,t);if(null!=a)return JSON.stringify(a)}}function jr(e,t,n){var r;if(null!=(r=e.attrsMap[t]))for(var a=e.attrsList,i=0,o=a.length;i<o;i++)if(a[i].name===t){a.splice(i,1);break}return n&&delete e.attrsMap[t],r}function Fr(e,t){for(var n=e.attrsList,r=0,a=n.length;r<a;r++){var i=n[r];if(t.test(i.name))return n.splice(r,1),i}}function Vr(e,t){return t&&(null!=t.start&&(e.start=t.start),null!=t.end&&(e.end=t.end)),e}function zr(e,t,n){var r=n||{},a=r.number,i="$$v";r.trim&&(i="(typeof $$v === 'string'? $$v.trim(): $$v)"),a&&(i="_n("+i+")");var o=Ur(t,i);e.model={value:"("+t+")",expression:JSON.stringify(t),callback:"function ($$v) {"+o+"}"}}function Ur(e,t){var n=function(e){if(e=e.trim(),wr=e.length,e.indexOf("[")<0||e.lastIndexOf("]")<wr-1)return(kr=e.lastIndexOf("."))>-1?{exp:e.slice(0,kr),key:'"'+e.slice(kr+1)+'"'}:{exp:e,key:null};Sr=e,kr=Ar=Mr=0;for(;!qr();)Yr(xr=Xr())?Gr(xr):91===xr&&Wr(xr);return{exp:e.slice(0,Ar),key:e.slice(Ar+1,Mr)}}(e);return null===n.key?e+"="+t:"$set("+n.exp+", "+n.key+", "+t+")"}function Xr(){return Sr.charCodeAt(++kr)}function qr(){return kr>=wr}function Yr(e){return 34===e||39===e}function Wr(e){var t=1;for(Ar=kr;!qr();)if(Yr(e=Xr()))Gr(e);else if(91===e&&t++,93===e&&t--,0===t){Mr=kr;break}}function Gr(e){for(var t=e;!qr()&&(e=Xr())!==t;);}var Kr;function Zr(e,t,n){var r=Kr;return function a(){var i=t.apply(null,arguments);null!==i&&ea(e,a,n,r)}}var Qr=Ke&&!(ne&&Number(ne[1])<=53);function Jr(e,t,n,r){if(Qr){var a=fn,i=t;t=i._wrapper=function(e){if(e.target===e.currentTarget||e.timeStamp>=a||e.timeStamp<=0||e.target.ownerDocument!==document)return i.apply(this,arguments)}}Kr.addEventListener(e,t,ae?{capture:n,passive:r}:n)}function ea(e,t,n,r){(r||Kr).removeEventListener(e,t._wrapper||t,n)}function ta(e,t){if(!i(e.data.on)||!i(t.data.on)){var n=t.data.on||{},r=e.data.on||{};Kr=t.elm,function(e){if(o(e.__r)){var t=Q?"change":"input";e[t]=[].concat(e.__r,e[t]||[]),delete e.__r}o(e.__c)&&(e.change=[].concat(e.__c,e.change||[]),delete e.__c)}(n),ct(n,r,Jr,ea,Zr,t.context),Kr=void 0}}var na,ra={create:ta,update:ta};function aa(e,t){if(!i(e.data.domProps)||!i(t.data.domProps)){var n,r,a=t.elm,s=e.data.domProps||{},l=t.data.domProps||{};for(n in o(l.__ob__)&&(l=t.data.domProps=B({},l)),s)n in l||(a[n]="");for(n in l){if(r=l[n],"textContent"===n||"innerHTML"===n){if(t.children&&(t.children.length=0),r===s[n])continue;1===a.childNodes.length&&a.removeChild(a.childNodes[0])}if("value"===n&&"PROGRESS"!==a.tagName){a._value=r;var c=i(r)?"":String(r);ia(a,c)&&(a.value=c)}else if("innerHTML"===n&&Kn(a.tagName)&&i(a.innerHTML)){(na=na||document.createElement("div")).innerHTML="<svg>"+r+"</svg>";for(var f=na.firstChild;a.firstChild;)a.removeChild(a.firstChild);for(;f.firstChild;)a.appendChild(f.firstChild)}else if(r!==s[n])try{a[n]=r}catch(e){}}}}function ia(e,t){return!e.composing&&("OPTION"===e.tagName||function(e,t){var n=!0;try{n=document.activeElement!==e}catch(e){}return n&&e.value!==t}(e,t)||function(e,t){var n=e.value,r=e._vModifiers;if(o(r)){if(r.number)return v(n)!==v(t);if(r.trim)return n.trim()!==t.trim()}return n!==t}(e,t))}var oa={create:aa,update:aa},sa=x((function(e){var t={},n=/:(.+)/;return e.split(/;(?![^(]*\))/g).forEach((function(e){if(e){var r=e.split(n);r.length>1&&(t[r[0].trim()]=r[1].trim())}})),t}));function la(e){var t=ca(e.style);return e.staticStyle?B(e.staticStyle,t):t}function ca(e){return Array.isArray(e)?O(e):"string"==typeof e?sa(e):e}var fa,ua=/^--/,pa=/\s*!important$/,_a=function(e,t,n){if(ua.test(t))e.style.setProperty(t,n);else if(pa.test(n))e.style.setProperty(R(t),n.replace(pa,""),"important");else{var r=ha(t);if(Array.isArray(n))for(var a=0,i=n.length;a<i;a++)e.style[r]=n[a];else e.style[r]=n}},da=["Webkit","Moz","ms"],ha=x((function(e){if(fa=fa||document.createElement("div").style,"filter"!==(e=A(e))&&e in fa)return e;for(var t=e.charAt(0).toUpperCase()+e.slice(1),n=0;n<da.length;n++){var r=da[n]+t;if(r in fa)return r}}));function va(e,t){var n=t.data,r=e.data;if(!(i(n.staticStyle)&&i(n.style)&&i(r.staticStyle)&&i(r.style))){var a,s,l=t.elm,c=r.staticStyle,f=r.normalizedStyle||r.style||{},u=c||f,p=ca(t.data.style)||{};t.data.normalizedStyle=o(p.__ob__)?B({},p):p;var _=function(e,t){var n,r={};if(t)for(var a=e;a.componentInstance;)(a=a.componentInstance._vnode)&&a.data&&(n=la(a.data))&&B(r,n);(n=la(e.data))&&B(r,n);for(var i=e;i=i.parent;)i.data&&(n=la(i.data))&&B(r,n);return r}(t,!0);for(s in u)i(_[s])&&_a(l,s,"");for(s in _)(a=_[s])!==u[s]&&_a(l,s,null==a?"":a)}}var ma={create:va,update:va},ba=/\s+/;function ga(e,t){if(t&&(t=t.trim()))if(e.classList)t.indexOf(" ")>-1?t.split(ba).forEach((function(t){return e.classList.add(t)})):e.classList.add(t);else{var n=" "+(e.getAttribute("class")||"")+" ";n.indexOf(" "+t+" ")<0&&e.setAttribute("class",(n+t).trim())}}function ya(e,t){if(t&&(t=t.trim()))if(e.classList)t.indexOf(" ")>-1?t.split(ba).forEach((function(t){return e.classList.remove(t)})):e.classList.remove(t),e.classList.length||e.removeAttribute("class");else{for(var n=" "+(e.getAttribute("class")||"")+" ",r=" "+t+" ";n.indexOf(r)>=0;)n=n.replace(r," ");(n=n.trim())?e.setAttribute("class",n):e.removeAttribute("class")}}function wa(e){if(e){if("object"===(void 0===e?"undefined":r(e))){var t={};return!1!==e.css&&B(t,Sa(e.name||"v")),B(t,e),t}return"string"==typeof e?Sa(e):void 0}}var Sa=x((function(e){return{enterClass:e+"-enter",enterToClass:e+"-enter-to",enterActiveClass:e+"-enter-active",leaveClass:e+"-leave",leaveToClass:e+"-leave-to",leaveActiveClass:e+"-leave-active"}})),xa=W&&!J,ka="transition",Aa="transitionend",Ma="animation",Ta="animationend";xa&&(void 0===window.ontransitionend&&void 0!==window.onwebkittransitionend&&(ka="WebkitTransition",Aa="webkitTransitionEnd"),void 0===window.onanimationend&&void 0!==window.onwebkitanimationend&&(Ma="WebkitAnimation",Ta="webkitAnimationEnd"));var Ra=W?window.requestAnimationFrame?window.requestAnimationFrame.bind(window):setTimeout:function(e){return e()};function Ca(e){Ra((function(){Ra(e)}))}function Ea(e,t){var n=e._transitionClasses||(e._transitionClasses=[]);n.indexOf(t)<0&&(n.push(t),ga(e,t))}function Ba(e,t){e._transitionClasses&&y(e._transitionClasses,t),ya(e,t)}function Oa(e,t,n){var r=Ia(e,t),a=r.type,i=r.timeout,o=r.propCount;if(!a)return n();var s="transition"===a?Aa:Ta,l=0,c=function(){e.removeEventListener(s,f),n()},f=function(t){t.target===e&&++l>=o&&c()};setTimeout((function(){l<o&&c()}),i+1),e.addEventListener(s,f)}var La=/\b(transform|all)(,|$)/;function Ia(e,t){var n,r=window.getComputedStyle(e),a=(r[ka+"Delay"]||"").split(", "),i=(r[ka+"Duration"]||"").split(", "),o=Pa(a,i),s=(r[Ma+"Delay"]||"").split(", "),l=(r[Ma+"Duration"]||"").split(", "),c=Pa(s,l),f=0,u=0;return"transition"===t?o>0&&(n="transition",f=o,u=i.length):"animation"===t?c>0&&(n="animation",f=c,u=l.length):u=(n=(f=Math.max(o,c))>0?o>c?"transition":"animation":null)?"transition"===n?i.length:l.length:0,{type:n,timeout:f,propCount:u,hasTransform:"transition"===n&&La.test(r[ka+"Property"])}}function Pa(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max.apply(null,t.map((function(t,n){return $a(t)+$a(e[n])})))}function $a(e){return 1e3*Number(e.slice(0,-1).replace(",","."))}function Na(e,t){var n=e.elm;o(n._leaveCb)&&(n._leaveCb.cancelled=!0,n._leaveCb());var r=wa(e.data.transition);if(!i(r)&&!o(n._enterCb)&&1===n.nodeType){for(var a=r.css,s=r.type,l=r.enterClass,f=r.enterToClass,u=r.enterActiveClass,p=r.appearClass,_=r.appearToClass,d=r.appearActiveClass,h=r.beforeEnter,m=r.enter,b=r.afterEnter,g=r.enterCancelled,y=r.beforeAppear,w=r.appear,S=r.afterAppear,x=r.appearCancelled,k=r.duration,A=Qt,M=Qt.$vnode;M&&M.parent;)A=M.context,M=M.parent;var T=!A._isMounted||!e.isRootInsert;if(!T||w||""===w){var R=T&&p?p:l,C=T&&d?d:u,E=T&&_?_:f,B=T&&y||h,O=T&&"function"==typeof w?w:m,L=T&&S||b,I=T&&x||g,P=v(c(k)?k.enter:k);0;var $=!1!==a&&!J,N=ja(O),H=n._enterCb=D((function(){$&&(Ba(n,E),Ba(n,C)),H.cancelled?($&&Ba(n,R),I&&I(n)):L&&L(n),n._enterCb=null}));e.data.show||ft(e,"insert",(function(){var t=n.parentNode,r=t&&t._pending&&t._pending[e.key];r&&r.tag===e.tag&&r.elm._leaveCb&&r.elm._leaveCb(),O&&O(n,H)})),B&&B(n),$&&(Ea(n,R),Ea(n,C),Ca((function(){Ba(n,R),H.cancelled||(Ea(n,E),N||(Ha(P)?setTimeout(H,P):Oa(n,s,H)))}))),e.data.show&&(t&&t(),O&&O(n,H)),$||N||H()}}}function Da(e,t){var n=e.elm;o(n._enterCb)&&(n._enterCb.cancelled=!0,n._enterCb());var r=wa(e.data.transition);if(i(r)||1!==n.nodeType)return t();if(!o(n._leaveCb)){var a=r.css,s=r.type,l=r.leaveClass,f=r.leaveToClass,u=r.leaveActiveClass,p=r.beforeLeave,_=r.leave,d=r.afterLeave,h=r.leaveCancelled,m=r.delayLeave,b=r.duration,g=!1!==a&&!J,y=ja(_),w=v(c(b)?b.leave:b);0;var S=n._leaveCb=D((function(){n.parentNode&&n.parentNode._pending&&(n.parentNode._pending[e.key]=null),g&&(Ba(n,f),Ba(n,u)),S.cancelled?(g&&Ba(n,l),h&&h(n)):(t(),d&&d(n)),n._leaveCb=null}));m?m(x):x()}function x(){S.cancelled||(!e.data.show&&n.parentNode&&((n.parentNode._pending||(n.parentNode._pending={}))[e.key]=e),p&&p(n),g&&(Ea(n,l),Ea(n,u),Ca((function(){Ba(n,l),S.cancelled||(Ea(n,f),y||(Ha(w)?setTimeout(S,w):Oa(n,s,S)))}))),_&&_(n,S),g||y||S())}}function Ha(e){return"number"==typeof e&&!isNaN(e)}function ja(e){if(i(e))return!1;var t=e.fns;return o(t)?ja(Array.isArray(t)?t[0]:t):(e._length||e.length)>1}function Fa(e,t){!0!==t.data.show&&Na(t)}var Va=function(e){var t,n,r={},a=e.modules,c=e.nodeOps;for(t=0;t<or.length;++t)for(r[or[t]]=[],n=0;n<a.length;++n)o(a[n][or[t]])&&r[or[t]].push(a[n][or[t]]);function f(e){var t=c.parentNode(e);o(t)&&c.removeChild(t,e)}function u(e,t,n,a,i,l,f){if(o(e.elm)&&o(l)&&(e=l[f]=we(e)),e.isRootInsert=!i,!function(e,t,n,a){var i=e.data;if(o(i)){var l=o(e.componentInstance)&&i.keepAlive;if(o(i=i.hook)&&o(i=i.init)&&i(e,!1),o(e.componentInstance))return p(e,t),_(n,e.elm,a),s(l)&&function(e,t,n,a){var i,s=e;for(;s.componentInstance;)if(s=s.componentInstance._vnode,o(i=s.data)&&o(i=i.transition)){for(i=0;i<r.activate.length;++i)r.activate[i](ir,s);t.push(s);break}_(n,e.elm,a)}(e,t,n,a),!0}}(e,t,n,a)){var u=e.data,h=e.children,m=e.tag;o(m)?(e.elm=e.ns?c.createElementNS(e.ns,m):c.createElement(m,e),b(e),d(e,h,t),o(u)&&v(e,t),_(n,e.elm,a)):s(e.isComment)?(e.elm=c.createComment(e.text),_(n,e.elm,a)):(e.elm=c.createTextNode(e.text),_(n,e.elm,a))}}function p(e,t){o(e.data.pendingInsert)&&(t.push.apply(t,e.data.pendingInsert),e.data.pendingInsert=null),e.elm=e.componentInstance.$el,h(e)?(v(e,t),b(e)):(ar(e),t.push(e))}function _(e,t,n){o(e)&&(o(n)?c.parentNode(n)===e&&c.insertBefore(e,t,n):c.appendChild(e,t))}function d(e,t,n){if(Array.isArray(t)){0;for(var r=0;r<t.length;++r)u(t[r],n,e.elm,null,!0,t,r)}else l(e.text)&&c.appendChild(e.elm,c.createTextNode(String(e.text)))}function h(e){for(;e.componentInstance;)e=e.componentInstance._vnode;return o(e.tag)}function v(e,n){for(var a=0;a<r.create.length;++a)r.create[a](ir,e);o(t=e.data.hook)&&(o(t.create)&&t.create(ir,e),o(t.insert)&&n.push(e))}function b(e){var t;if(o(t=e.fnScopeId))c.setStyleScope(e.elm,t);else for(var n=e;n;)o(t=n.context)&&o(t=t.$options._scopeId)&&c.setStyleScope(e.elm,t),n=n.parent;o(t=Qt)&&t!==e.context&&t!==e.fnContext&&o(t=t.$options._scopeId)&&c.setStyleScope(e.elm,t)}function g(e,t,n,r,a,i){for(;r<=a;++r)u(n[r],i,e,t,!1,n,r)}function y(e){var t,n,a=e.data;if(o(a))for(o(t=a.hook)&&o(t=t.destroy)&&t(e),t=0;t<r.destroy.length;++t)r.destroy[t](e);if(o(t=e.children))for(n=0;n<e.children.length;++n)y(e.children[n])}function w(e,t,n,r){for(;n<=r;++n){var a=t[n];o(a)&&(o(a.tag)?(S(a),y(a)):f(a.elm))}}function S(e,t){if(o(t)||o(e.data)){var n,a=r.remove.length+1;for(o(t)?t.listeners+=a:t=function(e,t){function n(){0==--n.listeners&&f(e)}return n.listeners=t,n}(e.elm,a),o(n=e.componentInstance)&&o(n=n._vnode)&&o(n.data)&&S(n,t),n=0;n<r.remove.length;++n)r.remove[n](e,t);o(n=e.data.hook)&&o(n=n.remove)?n(e,t):t()}else f(e.elm)}function x(e,t,n,r){for(var a=n;a<r;a++){var i=t[a];if(o(i)&&sr(e,i))return a}}function k(e,t,n,a,l,f){if(e!==t){o(t.elm)&&o(a)&&(t=a[l]=we(t));var p=t.elm=e.elm;if(s(e.isAsyncPlaceholder))o(t.asyncFactory.resolved)?T(e.elm,t,n):t.isAsyncPlaceholder=!0;else if(s(t.isStatic)&&s(e.isStatic)&&t.key===e.key&&(s(t.isCloned)||s(t.isOnce)))t.componentInstance=e.componentInstance;else{var _,d=t.data;o(d)&&o(_=d.hook)&&o(_=_.prepatch)&&_(e,t);var v=e.children,m=t.children;if(o(d)&&h(t)){for(_=0;_<r.update.length;++_)r.update[_](e,t);o(_=d.hook)&&o(_=_.update)&&_(e,t)}i(t.text)?o(v)&&o(m)?v!==m&&function(e,t,n,r,a){var s,l,f,p=0,_=0,d=t.length-1,h=t[0],v=t[d],m=n.length-1,b=n[0],y=n[m],S=!a;for(0;p<=d&&_<=m;)i(h)?h=t[++p]:i(v)?v=t[--d]:sr(h,b)?(k(h,b,r,n,_),h=t[++p],b=n[++_]):sr(v,y)?(k(v,y,r,n,m),v=t[--d],y=n[--m]):sr(h,y)?(k(h,y,r,n,m),S&&c.insertBefore(e,h.elm,c.nextSibling(v.elm)),h=t[++p],y=n[--m]):sr(v,b)?(k(v,b,r,n,_),S&&c.insertBefore(e,v.elm,h.elm),v=t[--d],b=n[++_]):(i(s)&&(s=lr(t,p,d)),i(l=o(b.key)?s[b.key]:x(b,t,p,d))?u(b,r,e,h.elm,!1,n,_):sr(f=t[l],b)?(k(f,b,r,n,_),t[l]=void 0,S&&c.insertBefore(e,f.elm,h.elm)):u(b,r,e,h.elm,!1,n,_),b=n[++_]);p>d?g(e,i(n[m+1])?null:n[m+1].elm,n,_,m,r):_>m&&w(0,t,p,d)}(p,v,m,n,f):o(m)?(o(e.text)&&c.setTextContent(p,""),g(p,null,m,0,m.length-1,n)):o(v)?w(0,v,0,v.length-1):o(e.text)&&c.setTextContent(p,""):e.text!==t.text&&c.setTextContent(p,t.text),o(d)&&o(_=d.hook)&&o(_=_.postpatch)&&_(e,t)}}}function A(e,t,n){if(s(n)&&o(e.parent))e.parent.data.pendingInsert=t;else for(var r=0;r<t.length;++r)t[r].data.hook.insert(t[r])}var M=m("attrs,class,staticClass,staticStyle,key");function T(e,t,n,r){var a,i=t.tag,l=t.data,c=t.children;if(r=r||l&&l.pre,t.elm=e,s(t.isComment)&&o(t.asyncFactory))return t.isAsyncPlaceholder=!0,!0;if(o(l)&&(o(a=l.hook)&&o(a=a.init)&&a(t,!0),o(a=t.componentInstance)))return p(t,n),!0;if(o(i)){if(o(c))if(e.hasChildNodes())if(o(a=l)&&o(a=a.domProps)&&o(a=a.innerHTML)){if(a!==e.innerHTML)return!1}else{for(var f=!0,u=e.firstChild,_=0;_<c.length;_++){if(!u||!T(u,c[_],n,r)){f=!1;break}u=u.nextSibling}if(!f||u)return!1}else d(t,c,n);if(o(l)){var h=!1;for(var m in l)if(!M(m)){h=!0,v(t,n);break}!h&&l.class&&ot(l.class)}}else e.data!==t.text&&(e.data=t.text);return!0}return function(e,t,n,a){if(!i(t)){var l,f=!1,p=[];if(i(e))f=!0,u(t,p);else{var _=o(e.nodeType);if(!_&&sr(e,t))k(e,t,p,null,null,a);else{if(_){if(1===e.nodeType&&e.hasAttribute("data-server-rendered")&&(e.removeAttribute("data-server-rendered"),n=!0),s(n)&&T(e,t,p))return A(t,p,!0),e;l=e,e=new me(c.tagName(l).toLowerCase(),{},[],void 0,l)}var d=e.elm,v=c.parentNode(d);if(u(t,p,d._leaveCb?null:v,c.nextSibling(d)),o(t.parent))for(var m=t.parent,b=h(t);m;){for(var g=0;g<r.destroy.length;++g)r.destroy[g](m);if(m.elm=t.elm,b){for(var S=0;S<r.create.length;++S)r.create[S](ir,m);var x=m.data.hook.insert;if(x.merged)for(var M=1;M<x.fns.length;M++)x.fns[M]()}else ar(m);m=m.parent}o(v)?w(0,[e],0,0):o(e.tag)&&y(e)}}return A(t,p,f),t.elm}o(e)&&y(e)}}({nodeOps:nr,modules:[gr,Tr,ra,oa,ma,W?{create:Fa,activate:Fa,remove:function(e,t){!0!==e.data.show?Da(e,t):t()}}:{}].concat(hr)});J&&document.addEventListener("selectionchange",(function(){var e=document.activeElement;e&&e.vmodel&&Ka(e,"input")}));var za={inserted:function(e,t,n,r){"select"===n.tag?(r.elm&&!r.elm._vOptions?ft(n,"postpatch",(function(){za.componentUpdated(e,t,n)})):Ua(e,t,n.context),e._vOptions=[].map.call(e.options,Ya)):("textarea"===n.tag||er(e.type))&&(e._vModifiers=t.modifiers,t.modifiers.lazy||(e.addEventListener("compositionstart",Wa),e.addEventListener("compositionend",Ga),e.addEventListener("change",Ga),J&&(e.vmodel=!0)))},componentUpdated:function(e,t,n){if("select"===n.tag){Ua(e,t,n.context);var r=e._vOptions,a=e._vOptions=[].map.call(e.options,Ya);if(a.some((function(e,t){return!$(e,r[t])})))(e.multiple?t.value.some((function(e){return qa(e,a)})):t.value!==t.oldValue&&qa(t.value,a))&&Ka(e,"change")}}};function Ua(e,t,n){Xa(e,t,n),(Q||ee)&&setTimeout((function(){Xa(e,t,n)}),0)}function Xa(e,t,n){var r=t.value,a=e.multiple;if(!a||Array.isArray(r)){for(var i,o,s=0,l=e.options.length;s<l;s++)if(o=e.options[s],a)i=N(r,Ya(o))>-1,o.selected!==i&&(o.selected=i);else if($(Ya(o),r))return void(e.selectedIndex!==s&&(e.selectedIndex=s));a||(e.selectedIndex=-1)}}function qa(e,t){return t.every((function(t){return!$(t,e)}))}function Ya(e){return"_value"in e?e._value:e.value}function Wa(e){e.target.composing=!0}function Ga(e){e.target.composing&&(e.target.composing=!1,Ka(e.target,"input"))}function Ka(e,t){var n=document.createEvent("HTMLEvents");n.initEvent(t,!0,!0),e.dispatchEvent(n)}function Za(e){return!e.componentInstance||e.data&&e.data.transition?e:Za(e.componentInstance._vnode)}var Qa={model:za,show:{bind:function(e,t,n){var r=t.value,a=(n=Za(n)).data&&n.data.transition,i=e.__vOriginalDisplay="none"===e.style.display?"":e.style.display;r&&a?(n.data.show=!0,Na(n,(function(){e.style.display=i}))):e.style.display=r?i:"none"},update:function(e,t,n){var r=t.value;!r!=!t.oldValue&&((n=Za(n)).data&&n.data.transition?(n.data.show=!0,r?Na(n,(function(){e.style.display=e.__vOriginalDisplay})):Da(n,(function(){e.style.display="none"}))):e.style.display=r?e.__vOriginalDisplay:"none")},unbind:function(e,t,n,r,a){a||(e.style.display=e.__vOriginalDisplay)}}},Ja={name:String,appear:Boolean,css:Boolean,mode:String,type:String,enterClass:String,leaveClass:String,enterToClass:String,leaveToClass:String,enterActiveClass:String,leaveActiveClass:String,appearClass:String,appearActiveClass:String,appearToClass:String,duration:[Number,String,Object]};function ei(e){var t=e&&e.componentOptions;return t&&t.Ctor.options.abstract?ei(Yt(t.children)):e}function ti(e){var t={},n=e.$options;for(var r in n.propsData)t[r]=e[r];var a=n._parentListeners;for(var i in a)t[A(i)]=a[i];return t}function ni(e,t){if(/\d-keep-alive$/.test(t.tag))return e("keep-alive",{props:t.componentOptions.propsData})}var ri=function(e){return e.tag||qt(e)},ai=function(e){return"show"===e.name},ii={name:"transition",props:Ja,abstract:!0,render:function(e){var t=this,n=this.$slots.default;if(n&&(n=n.filter(ri)).length){0;var r=this.mode;0;var a=n[0];if(function(e){for(;e=e.parent;)if(e.data.transition)return!0}(this.$vnode))return a;var i=ei(a);if(!i)return a;if(this._leaving)return ni(e,a);var o="__transition-"+this._uid+"-";i.key=null==i.key?i.isComment?o+"comment":o+i.tag:l(i.key)?0===String(i.key).indexOf(o)?i.key:o+i.key:i.key;var s=(i.data||(i.data={})).transition=ti(this),c=this._vnode,f=ei(c);if(i.data.directives&&i.data.directives.some(ai)&&(i.data.show=!0),f&&f.data&&!function(e,t){return t.key===e.key&&t.tag===e.tag}(i,f)&&!qt(f)&&(!f.componentInstance||!f.componentInstance._vnode.isComment)){var u=f.data.transition=B({},s);if("out-in"===r)return this._leaving=!0,ft(u,"afterLeave",(function(){t._leaving=!1,t.$forceUpdate()})),ni(e,a);if("in-out"===r){if(qt(i))return c;var p,_=function(){p()};ft(s,"afterEnter",_),ft(s,"enterCancelled",_),ft(u,"delayLeave",(function(e){p=e}))}}return a}}},oi=B({tag:String,moveClass:String},Ja);function si(e){e.elm._moveCb&&e.elm._moveCb(),e.elm._enterCb&&e.elm._enterCb()}function li(e){e.data.newPos=e.elm.getBoundingClientRect()}function ci(e){var t=e.data.pos,n=e.data.newPos,r=t.left-n.left,a=t.top-n.top;if(r||a){e.data.moved=!0;var i=e.elm.style;i.transform=i.WebkitTransform="translate("+r+"px,"+a+"px)",i.transitionDuration="0s"}}delete oi.mode;var fi={Transition:ii,TransitionGroup:{props:oi,beforeMount:function(){var e=this,t=this._update;this._update=function(n,r){var a=Jt(e);e.__patch__(e._vnode,e.kept,!1,!0),e._vnode=e.kept,a(),t.call(e,n,r)}},render:function(e){for(var t=this.tag||this.$vnode.data.tag||"span",n=Object.create(null),r=this.prevChildren=this.children,a=this.$slots.default||[],i=this.children=[],o=ti(this),s=0;s<a.length;s++){var l=a[s];if(l.tag)if(null!=l.key&&0!==String(l.key).indexOf("__vlist"))i.push(l),n[l.key]=l,(l.data||(l.data={})).transition=o;else;}if(r){for(var c=[],f=[],u=0;u<r.length;u++){var p=r[u];p.data.transition=o,p.data.pos=p.elm.getBoundingClientRect(),n[p.key]?c.push(p):f.push(p)}this.kept=e(t,null,c),this.removed=f}return e(t,null,i)},updated:function(){var e=this.prevChildren,t=this.moveClass||(this.name||"v")+"-move";e.length&&this.hasMove(e[0].elm,t)&&(e.forEach(si),e.forEach(li),e.forEach(ci),this._reflow=document.body.offsetHeight,e.forEach((function(e){if(e.data.moved){var n=e.elm,r=n.style;Ea(n,t),r.transform=r.WebkitTransform=r.transitionDuration="",n.addEventListener(Aa,n._moveCb=function e(r){r&&r.target!==n||r&&!/transform$/.test(r.propertyName)||(n.removeEventListener(Aa,e),n._moveCb=null,Ba(n,t))})}})))},methods:{hasMove:function(e,t){if(!xa)return!1;if(this._hasMove)return this._hasMove;var n=e.cloneNode();e._transitionClasses&&e._transitionClasses.forEach((function(e){ya(n,e)})),ga(n,t),n.style.display="none",this.$el.appendChild(n);var r=Ia(n);return this.$el.removeChild(n),this._hasMove=r.hasTransform}}}};Mn.config.mustUseProp=$n,Mn.config.isReservedTag=Zn,Mn.config.isReservedAttr=In,Mn.config.getTagNamespace=Qn,Mn.config.isUnknownElement=function(e){if(!W)return!0;if(Zn(e))return!1;if(e=e.toLowerCase(),null!=Jn[e])return Jn[e];var t=document.createElement(e);return e.indexOf("-")>-1?Jn[e]=t.constructor===window.HTMLUnknownElement||t.constructor===window.HTMLElement:Jn[e]=/HTMLUnknownElement/.test(t.toString())},B(Mn.options.directives,Qa),B(Mn.options.components,fi),Mn.prototype.__patch__=W?Va:L,Mn.prototype.$mount=function(e,t){return function(e,t,n){var r;return e.$el=t,e.$options.render||(e.$options.render=ge),nn(e,"beforeMount"),r=function(){e._update(e._render(),n)},new hn(e,r,L,{before:function(){e._isMounted&&!e._isDestroyed&&nn(e,"beforeUpdate")}},!0),n=!1,null==e.$vnode&&(e._isMounted=!0,nn(e,"mounted")),e}(this,e=e&&W?tr(e):void 0,t)},W&&setTimeout((function(){F.devtools&&se&&se.emit("init",Mn)}),0);var ui=/\{\{((?:.|\r?\n)+?)\}\}/g,pi=/[-.*+?^${}()|[\]\/\\]/g,_i=x((function(e){var t=e[0].replace(pi,"\\$&"),n=e[1].replace(pi,"\\$&");return new RegExp(t+"((?:.|\\n)+?)"+n,"g")}));var di={staticKeys:["staticClass"],transformNode:function(e,t){t.warn;var n=jr(e,"class");n&&(e.staticClass=JSON.stringify(n));var r=Hr(e,"class",!1);r&&(e.classBinding=r)},genData:function(e){var t="";return e.staticClass&&(t+="staticClass:"+e.staticClass+","),e.classBinding&&(t+="class:"+e.classBinding+","),t}};var hi,vi={staticKeys:["staticStyle"],transformNode:function(e,t){t.warn;var n=jr(e,"style");n&&(e.staticStyle=JSON.stringify(sa(n)));var r=Hr(e,"style",!1);r&&(e.styleBinding=r)},genData:function(e){var t="";return e.staticStyle&&(t+="staticStyle:"+e.staticStyle+","),e.styleBinding&&(t+="style:("+e.styleBinding+"),"),t}},mi=function(e){return(hi=hi||document.createElement("div")).innerHTML=e,hi.textContent},bi=m("area,base,br,col,embed,frame,hr,img,input,isindex,keygen,link,meta,param,source,track,wbr"),gi=m("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr,source"),yi=m("address,article,aside,base,blockquote,body,caption,col,colgroup,dd,details,dialog,div,dl,dt,fieldset,figcaption,figure,footer,form,h1,h2,h3,h4,h5,h6,head,header,hgroup,hr,html,legend,li,menuitem,meta,optgroup,option,param,rp,rt,source,style,summary,tbody,td,tfoot,th,thead,title,tr,track"),wi=/^\s*([^\s"'<>\/=]+)(?:\s*(=)\s*(?:"([^"]*)"+|'([^']*)'+|([^\s"'=<>`]+)))?/,Si=/^\s*((?:v-[\w-]+:|@|:|#)\[[^=]+\][^\s"'<>\/=]*)(?:\s*(=)\s*(?:"([^"]*)"+|'([^']*)'+|([^\s"'=<>`]+)))?/,xi="[a-zA-Z_][\\-\\.0-9_a-zA-Z"+V.source+"]*",ki="((?:"+xi+"\\:)?"+xi+")",Ai=new RegExp("^<"+ki),Mi=/^\s*(\/?)>/,Ti=new RegExp("^<\\/"+ki+"[^>]*>"),Ri=/^<!DOCTYPE [^>]+>/i,Ci=/^<!\--/,Ei=/^<!\[/,Bi=m("script,style,textarea",!0),Oi={},Li={"&lt;":"<","&gt;":">","&quot;":'"',"&amp;":"&","&#10;":"\n","&#9;":"\t","&#39;":"'"},Ii=/&(?:lt|gt|quot|amp|#39);/g,Pi=/&(?:lt|gt|quot|amp|#39|#10|#9);/g,$i=m("pre,textarea",!0),Ni=function(e,t){return e&&$i(e)&&"\n"===t[0]};function Di(e,t){var n=t?Pi:Ii;return e.replace(n,(function(e){return Li[e]}))}var Hi,ji,Fi,Vi,zi,Ui,Xi,qi,Yi=/^@|^v-on:/,Wi=/^v-|^@|^:/,Gi=/([\s\S]*?)\s+(?:in|of)\s+([\s\S]*)/,Ki=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,Zi=/^\(|\)$/g,Qi=/^\[.*\]$/,Ji=/:(.*)$/,eo=/^:|^\.|^v-bind:/,to=/\.[^.\]]+(?=[^\]]*$)/g,no=/^v-slot(:|$)|^#/,ro=/[\r\n]/,ao=/\s+/g,io=x(mi);function oo(e,t,n){return{type:1,tag:e,attrsList:t,attrsMap:_o(t),rawAttrsMap:{},parent:n,children:[]}}function so(e,t){Hi=t.warn||Br,Ui=t.isPreTag||I,Xi=t.mustUseProp||I,qi=t.getTagNamespace||I;var n=t.isReservedTag||I;(function(e){return!!e.component||!n(e.tag)}),Fi=Or(t.modules,"transformNode"),Vi=Or(t.modules,"preTransformNode"),zi=Or(t.modules,"postTransformNode"),ji=t.delimiters;var r,a,i=[],o=!1!==t.preserveWhitespace,s=t.whitespace,l=!1,c=!1;function f(e){if(u(e),l||e.processed||(e=lo(e,t)),i.length||e===r||r.if&&(e.elseif||e.else)&&fo(r,{exp:e.elseif,block:e}),a&&!e.forbidden)if(e.elseif||e.else)o=e,(s=function(e){for(var t=e.length;t--;){if(1===e[t].type)return e[t];e.pop()}}(a.children))&&s.if&&fo(s,{exp:o.elseif,block:o});else{if(e.slotScope){var n=e.slotTarget||'"default"';(a.scopedSlots||(a.scopedSlots={}))[n]=e}a.children.push(e),e.parent=a}var o,s;e.children=e.children.filter((function(e){return!e.slotScope})),u(e),e.pre&&(l=!1),Ui(e.tag)&&(c=!1);for(var f=0;f<zi.length;f++)zi[f](e,t)}function u(e){if(!c)for(var t;(t=e.children[e.children.length-1])&&3===t.type&&" "===t.text;)e.children.pop()}return function(e,t){for(var n,r,a=[],i=t.expectHTML,o=t.isUnaryTag||I,s=t.canBeLeftOpenTag||I,l=0;e;){if(n=e,r&&Bi(r)){var c=0,f=r.toLowerCase(),u=Oi[f]||(Oi[f]=new RegExp("([\\s\\S]*?)(</"+f+"[^>]*>)","i")),p=e.replace(u,(function(e,n,r){return c=r.length,Bi(f)||"noscript"===f||(n=n.replace(/<!\--([\s\S]*?)-->/g,"$1").replace(/<!\[CDATA\[([\s\S]*?)]]>/g,"$1")),Ni(f,n)&&(n=n.slice(1)),t.chars&&t.chars(n),""}));l+=e.length-p.length,e=p,M(f,l-c,l)}else{var _=e.indexOf("<");if(0===_){if(Ci.test(e)){var d=e.indexOf("--\x3e");if(d>=0){t.shouldKeepComment&&t.comment(e.substring(4,d),l,l+d+3),x(d+3);continue}}if(Ei.test(e)){var h=e.indexOf("]>");if(h>=0){x(h+2);continue}}var v=e.match(Ri);if(v){x(v[0].length);continue}var m=e.match(Ti);if(m){var b=l;x(m[0].length),M(m[1],b,l);continue}var g=k();if(g){A(g),Ni(g.tagName,e)&&x(1);continue}}var y=void 0,w=void 0,S=void 0;if(_>=0){for(w=e.slice(_);!(Ti.test(w)||Ai.test(w)||Ci.test(w)||Ei.test(w)||(S=w.indexOf("<",1))<0);)_+=S,w=e.slice(_);y=e.substring(0,_)}_<0&&(y=e),y&&x(y.length),t.chars&&y&&t.chars(y,l-y.length,l)}if(e===n){t.chars&&t.chars(e);break}}function x(t){l+=t,e=e.substring(t)}function k(){var t=e.match(Ai);if(t){var n,r,a={tagName:t[1],attrs:[],start:l};for(x(t[0].length);!(n=e.match(Mi))&&(r=e.match(Si)||e.match(wi));)r.start=l,x(r[0].length),r.end=l,a.attrs.push(r);if(n)return a.unarySlash=n[1],x(n[0].length),a.end=l,a}}function A(e){var n=e.tagName,l=e.unarySlash;i&&("p"===r&&yi(n)&&M(r),s(n)&&r===n&&M(n));for(var c=o(n)||!!l,f=e.attrs.length,u=new Array(f),p=0;p<f;p++){var _=e.attrs[p],d=_[3]||_[4]||_[5]||"",h="a"===n&&"href"===_[1]?t.shouldDecodeNewlinesForHref:t.shouldDecodeNewlines;u[p]={name:_[1],value:Di(d,h)}}c||(a.push({tag:n,lowerCasedTag:n.toLowerCase(),attrs:u,start:e.start,end:e.end}),r=n),t.start&&t.start(n,u,c,e.start,e.end)}function M(e,n,i){var o,s;if(null==n&&(n=l),null==i&&(i=l),e)for(s=e.toLowerCase(),o=a.length-1;o>=0&&a[o].lowerCasedTag!==s;o--);else o=0;if(o>=0){for(var c=a.length-1;c>=o;c--)t.end&&t.end(a[c].tag,n,i);a.length=o,r=o&&a[o-1].tag}else"br"===s?t.start&&t.start(e,[],!0,n,i):"p"===s&&(t.start&&t.start(e,[],!1,n,i),t.end&&t.end(e,n,i))}M()}(e,{warn:Hi,expectHTML:t.expectHTML,isUnaryTag:t.isUnaryTag,canBeLeftOpenTag:t.canBeLeftOpenTag,shouldDecodeNewlines:t.shouldDecodeNewlines,shouldDecodeNewlinesForHref:t.shouldDecodeNewlinesForHref,shouldKeepComment:t.comments,outputSourceRange:t.outputSourceRange,start:function(e,n,o,s,u){var p=a&&a.ns||qi(e);Q&&"svg"===p&&(n=function(e){for(var t=[],n=0;n<e.length;n++){var r=e[n];ho.test(r.name)||(r.name=r.name.replace(vo,""),t.push(r))}return t}(n));var _,d=oo(e,n,a);p&&(d.ns=p),"style"!==(_=d).tag&&("script"!==_.tag||_.attrsMap.type&&"text/javascript"!==_.attrsMap.type)||oe()||(d.forbidden=!0);for(var h=0;h<Vi.length;h++)d=Vi[h](d,t)||d;l||(!function(e){null!=jr(e,"v-pre")&&(e.pre=!0)}(d),d.pre&&(l=!0)),Ui(d.tag)&&(c=!0),l?function(e){var t=e.attrsList,n=t.length;if(n)for(var r=e.attrs=new Array(n),a=0;a<n;a++)r[a]={name:t[a].name,value:JSON.stringify(t[a].value)},null!=t[a].start&&(r[a].start=t[a].start,r[a].end=t[a].end);else e.pre||(e.plain=!0)}(d):d.processed||(co(d),function(e){var t=jr(e,"v-if");if(t)e.if=t,fo(e,{exp:t,block:e});else{null!=jr(e,"v-else")&&(e.else=!0);var n=jr(e,"v-else-if");n&&(e.elseif=n)}}(d),function(e){null!=jr(e,"v-once")&&(e.once=!0)}(d)),r||(r=d),o?f(d):(a=d,i.push(d))},end:function(e,t,n){var r=i[i.length-1];i.length-=1,a=i[i.length-1],f(r)},chars:function(e,t,n){if(a&&(!Q||"textarea"!==a.tag||a.attrsMap.placeholder!==e)){var r,i,f,u=a.children;if(e=c||e.trim()?"script"===(r=a).tag||"style"===r.tag?e:io(e):u.length?s?"condense"===s&&ro.test(e)?"":" ":o?" ":"":"")c||"condense"!==s||(e=e.replace(ao," ")),!l&&" "!==e&&(i=function(e,t){var n=t?_i(t):ui;if(n.test(e)){for(var r,a,i,o=[],s=[],l=n.lastIndex=0;r=n.exec(e);){(a=r.index)>l&&(s.push(i=e.slice(l,a)),o.push(JSON.stringify(i)));var c=Cr(r[1].trim());o.push("_s("+c+")"),s.push({"@binding":c}),l=a+r[0].length}return l<e.length&&(s.push(i=e.slice(l)),o.push(JSON.stringify(i))),{expression:o.join("+"),tokens:s}}}(e,ji))?f={type:2,expression:i.expression,tokens:i.tokens,text:e}:" "===e&&u.length&&" "===u[u.length-1].text||(f={type:3,text:e}),f&&u.push(f)}},comment:function(e,t,n){if(a){var r={type:3,text:e,isComment:!0};0,a.children.push(r)}}}),r}function lo(e,t){var n;!function(e){var t=Hr(e,"key");if(t){e.key=t}}(e),e.plain=!e.key&&!e.scopedSlots&&!e.attrsList.length,function(e){var t=Hr(e,"ref");t&&(e.ref=t,e.refInFor=function(e){var t=e;for(;t;){if(void 0!==t.for)return!0;t=t.parent}return!1}(e))}(e),function(e){var t;"template"===e.tag?(t=jr(e,"scope"),e.slotScope=t||jr(e,"slot-scope")):(t=jr(e,"slot-scope"))&&(e.slotScope=t);var n=Hr(e,"slot");n&&(e.slotTarget='""'===n?'"default"':n,e.slotTargetDynamic=!(!e.attrsMap[":slot"]&&!e.attrsMap["v-bind:slot"]),"template"===e.tag||e.slotScope||Ir(e,"slot",n,function(e,t){return e.rawAttrsMap[":"+t]||e.rawAttrsMap["v-bind:"+t]||e.rawAttrsMap[t]}(e,"slot")));if("template"===e.tag){var r=Fr(e,no);if(r){0;var a=uo(r),i=a.name,o=a.dynamic;e.slotTarget=i,e.slotTargetDynamic=o,e.slotScope=r.value||"_empty_"}}else{var s=Fr(e,no);if(s){0;var l=e.scopedSlots||(e.scopedSlots={}),c=uo(s),f=c.name,u=c.dynamic,p=l[f]=oo("template",[],e);p.slotTarget=f,p.slotTargetDynamic=u,p.children=e.children.filter((function(e){if(!e.slotScope)return e.parent=p,!0})),p.slotScope=s.value||"_empty_",e.children=[],e.plain=!1}}}(e),"slot"===(n=e).tag&&(n.slotName=Hr(n,"name")),function(e){var t;(t=Hr(e,"is"))&&(e.component=t);null!=jr(e,"inline-template")&&(e.inlineTemplate=!0)}(e);for(var r=0;r<Fi.length;r++)e=Fi[r](e,t)||e;return function(e){var t,n,r,a,i,o,s,l,c=e.attrsList;for(t=0,n=c.length;t<n;t++){if(r=a=c[t].name,i=c[t].value,Wi.test(r))if(e.hasBindings=!0,(o=po(r.replace(Wi,"")))&&(r=r.replace(to,"")),eo.test(r))r=r.replace(eo,""),i=Cr(i),(l=Qi.test(r))&&(r=r.slice(1,-1)),o&&(o.prop&&!l&&"innerHtml"===(r=A(r))&&(r="innerHTML"),o.camel&&!l&&(r=A(r)),o.sync&&(s=Ur(i,"$event"),l?Dr(e,'"update:"+('+r+")",s,null,!1,0,c[t],!0):(Dr(e,"update:"+A(r),s,null,!1,0,c[t]),R(r)!==A(r)&&Dr(e,"update:"+R(r),s,null,!1,0,c[t])))),o&&o.prop||!e.component&&Xi(e.tag,e.attrsMap.type,r)?Lr(e,r,i,c[t],l):Ir(e,r,i,c[t],l);else if(Yi.test(r))r=r.replace(Yi,""),(l=Qi.test(r))&&(r=r.slice(1,-1)),Dr(e,r,i,o,!1,0,c[t],l);else{var f=(r=r.replace(Wi,"")).match(Ji),u=f&&f[1];l=!1,u&&(r=r.slice(0,-(u.length+1)),Qi.test(u)&&(u=u.slice(1,-1),l=!0)),$r(e,r,a,i,u,l,o,c[t])}else Ir(e,r,JSON.stringify(i),c[t]),!e.component&&"muted"===r&&Xi(e.tag,e.attrsMap.type,r)&&Lr(e,r,"true",c[t])}}(e),e}function co(e){var t;if(t=jr(e,"v-for")){var n=function(e){var t=e.match(Gi);if(!t)return;var n={};n.for=t[2].trim();var r=t[1].trim().replace(Zi,""),a=r.match(Ki);a?(n.alias=r.replace(Ki,"").trim(),n.iterator1=a[1].trim(),a[2]&&(n.iterator2=a[2].trim())):n.alias=r;return n}(t);n&&B(e,n)}}function fo(e,t){e.ifConditions||(e.ifConditions=[]),e.ifConditions.push(t)}function uo(e){var t=e.name.replace(no,"");return t||"#"!==e.name[0]&&(t="default"),Qi.test(t)?{name:t.slice(1,-1),dynamic:!0}:{name:'"'+t+'"',dynamic:!1}}function po(e){var t=e.match(to);if(t){var n={};return t.forEach((function(e){n[e.slice(1)]=!0})),n}}function _o(e){for(var t={},n=0,r=e.length;n<r;n++)t[e[n].name]=e[n].value;return t}var ho=/^xmlns:NS\d+/,vo=/^NS\d+:/;function mo(e){return oo(e.tag,e.attrsList.slice(),e.parent)}var bo=[di,vi,{preTransformNode:function(e,t){if("input"===e.tag){var n,r=e.attrsMap;if(!r["v-model"])return;if((r[":type"]||r["v-bind:type"])&&(n=Hr(e,"type")),r.type||n||!r["v-bind"]||(n="("+r["v-bind"]+").type"),n){var a=jr(e,"v-if",!0),i=a?"&&("+a+")":"",o=null!=jr(e,"v-else",!0),s=jr(e,"v-else-if",!0),l=mo(e);co(l),Pr(l,"type","checkbox"),lo(l,t),l.processed=!0,l.if="("+n+")==='checkbox'"+i,fo(l,{exp:l.if,block:l});var c=mo(e);jr(c,"v-for",!0),Pr(c,"type","radio"),lo(c,t),fo(l,{exp:"("+n+")==='radio'"+i,block:c});var f=mo(e);return jr(f,"v-for",!0),Pr(f,":type",n),lo(f,t),fo(l,{exp:a,block:f}),o?l.else=!0:s&&(l.elseif=s),l}}}}];var go,yo,wo={expectHTML:!0,modules:bo,directives:{model:function(e,t,n){n;var r=t.value,a=t.modifiers,i=e.tag,o=e.attrsMap.type;if(e.component)return zr(e,r,a),!1;if("select"===i)!function(e,t,n){var r='var $$selectedVal = Array.prototype.filter.call($event.target.options,function(o){return o.selected}).map(function(o){var val = "_value" in o ? o._value : o.value;return '+(n&&n.number?"_n(val)":"val")+"});";r=r+" "+Ur(t,"$event.target.multiple ? $$selectedVal : $$selectedVal[0]"),Dr(e,"change",r,null,!0)}(e,r,a);else if("input"===i&&"checkbox"===o)!function(e,t,n){var r=n&&n.number,a=Hr(e,"value")||"null",i=Hr(e,"true-value")||"true",o=Hr(e,"false-value")||"false";Lr(e,"checked","Array.isArray("+t+")?_i("+t+","+a+")>-1"+("true"===i?":("+t+")":":_q("+t+","+i+")")),Dr(e,"change","var $$a="+t+",$$el=$event.target,$$c=$$el.checked?("+i+"):("+o+");if(Array.isArray($$a)){var $$v="+(r?"_n("+a+")":a)+",$$i=_i($$a,$$v);if($$el.checked){$$i<0&&("+Ur(t,"$$a.concat([$$v])")+")}else{$$i>-1&&("+Ur(t,"$$a.slice(0,$$i).concat($$a.slice($$i+1))")+")}}else{"+Ur(t,"$$c")+"}",null,!0)}(e,r,a);else if("input"===i&&"radio"===o)!function(e,t,n){var r=n&&n.number,a=Hr(e,"value")||"null";Lr(e,"checked","_q("+t+","+(a=r?"_n("+a+")":a)+")"),Dr(e,"change",Ur(t,a),null,!0)}(e,r,a);else if("input"===i||"textarea"===i)!function(e,t,n){var r=e.attrsMap.type;0;var a=n||{},i=a.lazy,o=a.number,s=a.trim,l=!i&&"range"!==r,c=i?"change":"range"===r?"__r":"input",f="$event.target.value";s&&(f="$event.target.value.trim()");o&&(f="_n("+f+")");var u=Ur(t,f);l&&(u="if($event.target.composing)return;"+u);Lr(e,"value","("+t+")"),Dr(e,c,u,null,!0),(s||o)&&Dr(e,"blur","$forceUpdate()")}(e,r,a);else{if(!F.isReservedTag(i))return zr(e,r,a),!1}return!0},text:function(e,t){t.value&&Lr(e,"textContent","_s("+t.value+")",t)},html:function(e,t){t.value&&Lr(e,"innerHTML","_s("+t.value+")",t)}},isPreTag:function(e){return"pre"===e},isUnaryTag:bi,mustUseProp:$n,canBeLeftOpenTag:gi,isReservedTag:Zn,getTagNamespace:Qn,staticKeys:function(e){return e.reduce((function(e,t){return e.concat(t.staticKeys||[])}),[]).join(",")}(bo)},So=x((function(e){return m("type,tag,attrsList,attrsMap,plain,parent,children,attrs,start,end,rawAttrsMap"+(e?","+e:""))}));function xo(e,t){e&&(go=So(t.staticKeys||""),yo=t.isReservedTag||I,function e(t){if(t.static=function(e){if(2===e.type)return!1;if(3===e.type)return!0;return!(!e.pre&&(e.hasBindings||e.if||e.for||b(e.tag)||!yo(e.tag)||function(e){for(;e.parent;){if("template"!==(e=e.parent).tag)return!1;if(e.for)return!0}return!1}(e)||!Object.keys(e).every(go)))}(t),1===t.type){if(!yo(t.tag)&&"slot"!==t.tag&&null==t.attrsMap["inline-template"])return;for(var n=0,r=t.children.length;n<r;n++){var a=t.children[n];e(a),a.static||(t.static=!1)}if(t.ifConditions)for(var i=1,o=t.ifConditions.length;i<o;i++){var s=t.ifConditions[i].block;e(s),s.static||(t.static=!1)}}}(e),function e(t,n){if(1===t.type){if((t.static||t.once)&&(t.staticInFor=n),t.static&&t.children.length&&(1!==t.children.length||3!==t.children[0].type))return void(t.staticRoot=!0);if(t.staticRoot=!1,t.children)for(var r=0,a=t.children.length;r<a;r++)e(t.children[r],n||!!t.for);if(t.ifConditions)for(var i=1,o=t.ifConditions.length;i<o;i++)e(t.ifConditions[i].block,n)}}(e,!1))}var ko=/^([\w$_]+|\([^)]*?\))\s*=>|^function\s*(?:[\w$]+)?\s*\(/,Ao=/\([^)]*?\);*$/,Mo=/^[A-Za-z_$][\w$]*(?:\.[A-Za-z_$][\w$]*|\['[^']*?']|\["[^"]*?"]|\[\d+]|\[[A-Za-z_$][\w$]*])*$/,To={esc:27,tab:9,enter:13,space:32,up:38,left:37,right:39,down:40,delete:[8,46]},Ro={esc:["Esc","Escape"],tab:"Tab",enter:"Enter",space:[" ","Spacebar"],up:["Up","ArrowUp"],left:["Left","ArrowLeft"],right:["Right","ArrowRight"],down:["Down","ArrowDown"],delete:["Backspace","Delete","Del"]},Co=function(e){return"if("+e+")return null;"},Eo={stop:"$event.stopPropagation();",prevent:"$event.preventDefault();",self:Co("$event.target !== $event.currentTarget"),ctrl:Co("!$event.ctrlKey"),shift:Co("!$event.shiftKey"),alt:Co("!$event.altKey"),meta:Co("!$event.metaKey"),left:Co("'button' in $event && $event.button !== 0"),middle:Co("'button' in $event && $event.button !== 1"),right:Co("'button' in $event && $event.button !== 2")};function Bo(e,t){var n=t?"nativeOn:":"on:",r="",a="";for(var i in e){var o=Oo(e[i]);e[i]&&e[i].dynamic?a+=i+","+o+",":r+='"'+i+'":'+o+","}return r="{"+r.slice(0,-1)+"}",a?n+"_d("+r+",["+a.slice(0,-1)+"])":n+r}function Oo(e){if(!e)return"function(){}";if(Array.isArray(e))return"["+e.map((function(e){return Oo(e)})).join(",")+"]";var t=Mo.test(e.value),n=ko.test(e.value),r=Mo.test(e.value.replace(Ao,""));if(e.modifiers){var a="",i="",o=[];for(var s in e.modifiers)if(Eo[s])i+=Eo[s],To[s]&&o.push(s);else if("exact"===s){var l=e.modifiers;i+=Co(["ctrl","shift","alt","meta"].filter((function(e){return!l[e]})).map((function(e){return"$event."+e+"Key"})).join("||"))}else o.push(s);return o.length&&(a+=function(e){return"if(!$event.type.indexOf('key')&&"+e.map(Lo).join("&&")+")return null;"}(o)),i&&(a+=i),"function($event){"+a+(t?"return "+e.value+"($event)":n?"return ("+e.value+")($event)":r?"return "+e.value:e.value)+"}"}return t||n?e.value:"function($event){"+(r?"return "+e.value:e.value)+"}"}function Lo(e){var t=parseInt(e,10);if(t)return"$event.keyCode!=="+t;var n=To[e],r=Ro[e];return"_k($event.keyCode,"+JSON.stringify(e)+","+JSON.stringify(n)+",$event.key,"+JSON.stringify(r)+")"}var Io={on:function(e,t){e.wrapListeners=function(e){return"_g("+e+","+t.value+")"}},bind:function(e,t){e.wrapData=function(n){return"_b("+n+",'"+e.tag+"',"+t.value+","+(t.modifiers&&t.modifiers.prop?"true":"false")+(t.modifiers&&t.modifiers.sync?",true":"")+")"}},cloak:L},Po=function(e){this.options=e,this.warn=e.warn||Br,this.transforms=Or(e.modules,"transformCode"),this.dataGenFns=Or(e.modules,"genData"),this.directives=B(B({},Io),e.directives);var t=e.isReservedTag||I;this.maybeComponent=function(e){return!!e.component||!t(e.tag)},this.onceId=0,this.staticRenderFns=[],this.pre=!1};function $o(e,t){var n=new Po(t);return{render:"with(this){return "+(e?No(e,n):'_c("div")')+"}",staticRenderFns:n.staticRenderFns}}function No(e,t){if(e.parent&&(e.pre=e.pre||e.parent.pre),e.staticRoot&&!e.staticProcessed)return Do(e,t);if(e.once&&!e.onceProcessed)return Ho(e,t);if(e.for&&!e.forProcessed)return Fo(e,t);if(e.if&&!e.ifProcessed)return jo(e,t);if("template"!==e.tag||e.slotTarget||t.pre){if("slot"===e.tag)return function(e,t){var n=e.slotName||'"default"',r=Xo(e,t),a="_t("+n+(r?","+r:""),i=e.attrs||e.dynamicAttrs?Wo((e.attrs||[]).concat(e.dynamicAttrs||[]).map((function(e){return{name:A(e.name),value:e.value,dynamic:e.dynamic}}))):null,o=e.attrsMap["v-bind"];!i&&!o||r||(a+=",null");i&&(a+=","+i);o&&(a+=(i?"":",null")+","+o);return a+")"}(e,t);var n;if(e.component)n=function(e,t,n){var r=t.inlineTemplate?null:Xo(t,n,!0);return"_c("+e+","+Vo(t,n)+(r?","+r:"")+")"}(e.component,e,t);else{var r;(!e.plain||e.pre&&t.maybeComponent(e))&&(r=Vo(e,t));var a=e.inlineTemplate?null:Xo(e,t,!0);n="_c('"+e.tag+"'"+(r?","+r:"")+(a?","+a:"")+")"}for(var i=0;i<t.transforms.length;i++)n=t.transforms[i](e,n);return n}return Xo(e,t)||"void 0"}function Do(e,t){e.staticProcessed=!0;var n=t.pre;return e.pre&&(t.pre=e.pre),t.staticRenderFns.push("with(this){return "+No(e,t)+"}"),t.pre=n,"_m("+(t.staticRenderFns.length-1)+(e.staticInFor?",true":"")+")"}function Ho(e,t){if(e.onceProcessed=!0,e.if&&!e.ifProcessed)return jo(e,t);if(e.staticInFor){for(var n="",r=e.parent;r;){if(r.for){n=r.key;break}r=r.parent}return n?"_o("+No(e,t)+","+t.onceId+++","+n+")":No(e,t)}return Do(e,t)}function jo(e,t,n,r){return e.ifProcessed=!0,function e(t,n,r,a){if(!t.length)return a||"_e()";var i=t.shift();return i.exp?"("+i.exp+")?"+o(i.block)+":"+e(t,n,r,a):""+o(i.block);function o(e){return r?r(e,n):e.once?Ho(e,n):No(e,n)}}(e.ifConditions.slice(),t,n,r)}function Fo(e,t,n,r){var a=e.for,i=e.alias,o=e.iterator1?","+e.iterator1:"",s=e.iterator2?","+e.iterator2:"";return e.forProcessed=!0,(r||"_l")+"(("+a+"),function("+i+o+s+"){return "+(n||No)(e,t)+"})"}function Vo(e,t){var n="{",r=function(e,t){var n=e.directives;if(!n)return;var r,a,i,o,s="directives:[",l=!1;for(r=0,a=n.length;r<a;r++){i=n[r],o=!0;var c=t.directives[i.name];c&&(o=!!c(e,i,t.warn)),o&&(l=!0,s+='{name:"'+i.name+'",rawName:"'+i.rawName+'"'+(i.value?",value:("+i.value+"),expression:"+JSON.stringify(i.value):"")+(i.arg?",arg:"+(i.isDynamicArg?i.arg:'"'+i.arg+'"'):"")+(i.modifiers?",modifiers:"+JSON.stringify(i.modifiers):"")+"},")}if(l)return s.slice(0,-1)+"]"}(e,t);r&&(n+=r+","),e.key&&(n+="key:"+e.key+","),e.ref&&(n+="ref:"+e.ref+","),e.refInFor&&(n+="refInFor:true,"),e.pre&&(n+="pre:true,"),e.component&&(n+='tag:"'+e.tag+'",');for(var a=0;a<t.dataGenFns.length;a++)n+=t.dataGenFns[a](e);if(e.attrs&&(n+="attrs:"+Wo(e.attrs)+","),e.props&&(n+="domProps:"+Wo(e.props)+","),e.events&&(n+=Bo(e.events,!1)+","),e.nativeEvents&&(n+=Bo(e.nativeEvents,!0)+","),e.slotTarget&&!e.slotScope&&(n+="slot:"+e.slotTarget+","),e.scopedSlots&&(n+=function(e,t,n){var r=e.for||Object.keys(t).some((function(e){var n=t[e];return n.slotTargetDynamic||n.if||n.for||zo(n)})),a=!!e.if;if(!r)for(var i=e.parent;i;){if(i.slotScope&&"_empty_"!==i.slotScope||i.for){r=!0;break}i.if&&(a=!0),i=i.parent}var o=Object.keys(t).map((function(e){return Uo(t[e],n)})).join(",");return"scopedSlots:_u(["+o+"]"+(r?",null,true":"")+(!r&&a?",null,false,"+function(e){var t=5381,n=e.length;for(;n;)t=33*t^e.charCodeAt(--n);return t>>>0}(o):"")+")"}(e,e.scopedSlots,t)+","),e.model&&(n+="model:{value:"+e.model.value+",callback:"+e.model.callback+",expression:"+e.model.expression+"},"),e.inlineTemplate){var i=function(e,t){var n=e.children[0];0;if(n&&1===n.type){var r=$o(n,t.options);return"inlineTemplate:{render:function(){"+r.render+"},staticRenderFns:["+r.staticRenderFns.map((function(e){return"function(){"+e+"}"})).join(",")+"]}"}}(e,t);i&&(n+=i+",")}return n=n.replace(/,$/,"")+"}",e.dynamicAttrs&&(n="_b("+n+',"'+e.tag+'",'+Wo(e.dynamicAttrs)+")"),e.wrapData&&(n=e.wrapData(n)),e.wrapListeners&&(n=e.wrapListeners(n)),n}function zo(e){return 1===e.type&&("slot"===e.tag||e.children.some(zo))}function Uo(e,t){var n=e.attrsMap["slot-scope"];if(e.if&&!e.ifProcessed&&!n)return jo(e,t,Uo,"null");if(e.for&&!e.forProcessed)return Fo(e,t,Uo);var r="_empty_"===e.slotScope?"":String(e.slotScope),a="function("+r+"){return "+("template"===e.tag?e.if&&n?"("+e.if+")?"+(Xo(e,t)||"undefined")+":undefined":Xo(e,t)||"undefined":No(e,t))+"}",i=r?"":",proxy:true";return"{key:"+(e.slotTarget||'"default"')+",fn:"+a+i+"}"}function Xo(e,t,n,r,a){var i=e.children;if(i.length){var o=i[0];if(1===i.length&&o.for&&"template"!==o.tag&&"slot"!==o.tag){var s=n?t.maybeComponent(o)?",1":",0":"";return""+(r||No)(o,t)+s}var l=n?function(e,t){for(var n=0,r=0;r<e.length;r++){var a=e[r];if(1===a.type){if(qo(a)||a.ifConditions&&a.ifConditions.some((function(e){return qo(e.block)}))){n=2;break}(t(a)||a.ifConditions&&a.ifConditions.some((function(e){return t(e.block)})))&&(n=1)}}return n}(i,t.maybeComponent):0,c=a||Yo;return"["+i.map((function(e){return c(e,t)})).join(",")+"]"+(l?","+l:"")}}function qo(e){return void 0!==e.for||"template"===e.tag||"slot"===e.tag}function Yo(e,t){return 1===e.type?No(e,t):3===e.type&&e.isComment?function(e){return"_e("+JSON.stringify(e.text)+")"}(e):function(e){return"_v("+(2===e.type?e.expression:Go(JSON.stringify(e.text)))+")"}(e)}function Wo(e){for(var t="",n="",r=0;r<e.length;r++){var a=e[r],i=Go(a.value);a.dynamic?n+=a.name+","+i+",":t+='"'+a.name+'":'+i+","}return t="{"+t.slice(0,-1)+"}",n?"_d("+t+",["+n.slice(0,-1)+"])":t}function Go(e){return e.replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")}new RegExp("\\b"+"do,if,for,let,new,try,var,case,else,with,await,break,catch,class,const,super,throw,while,yield,delete,export,import,return,switch,default,extends,finally,continue,debugger,function,arguments".split(",").join("\\b|\\b")+"\\b"),new RegExp("\\b"+"delete,typeof,void".split(",").join("\\s*\\([^\\)]*\\)|\\b")+"\\s*\\([^\\)]*\\)");function Ko(e,t){try{return new Function(e)}catch(n){return t.push({err:n,code:e}),L}}function Zo(e){var t=Object.create(null);return function(n,r,a){(r=B({},r)).warn;delete r.warn;var i=r.delimiters?String(r.delimiters)+n:n;if(t[i])return t[i];var o=e(n,r);var s={},l=[];return s.render=Ko(o.render,l),s.staticRenderFns=o.staticRenderFns.map((function(e){return Ko(e,l)})),t[i]=s}}var Qo,Jo,es=(Qo=function(e,t){var n=so(e.trim(),t);!1!==t.optimize&&xo(n,t);var r=$o(n,t);return{ast:n,render:r.render,staticRenderFns:r.staticRenderFns}},function(e){function t(t,n){var r=Object.create(e),a=[],i=[];if(n)for(var o in n.modules&&(r.modules=(e.modules||[]).concat(n.modules)),n.directives&&(r.directives=B(Object.create(e.directives||null),n.directives)),n)"modules"!==o&&"directives"!==o&&(r[o]=n[o]);r.warn=function(e,t,n){(n?i:a).push(e)};var s=Qo(t.trim(),r);return s.errors=a,s.tips=i,s}return{compile:t,compileToFunctions:Zo(t)}})(wo),ts=(es.compile,es.compileToFunctions);function ns(e){return(Jo=Jo||document.createElement("div")).innerHTML=e?'<a href="\n"/>':'<div a="\n"/>',Jo.innerHTML.indexOf("&#10;")>0}var rs=!!W&&ns(!1),as=!!W&&ns(!0),is=x((function(e){var t=tr(e);return t&&t.innerHTML})),os=Mn.prototype.$mount;Mn.prototype.$mount=function(e,t){if((e=e&&tr(e))===document.body||e===document.documentElement)return this;var n=this.$options;if(!n.render){var r=n.template;if(r)if("string"==typeof r)"#"===r.charAt(0)&&(r=is(r));else{if(!r.nodeType)return this;r=r.innerHTML}else e&&(r=function(e){if(e.outerHTML)return e.outerHTML;var t=document.createElement("div");return t.appendChild(e.cloneNode(!0)),t.innerHTML}(e));if(r){0;var a=ts(r,{outputSourceRange:!1,shouldDecodeNewlines:rs,shouldDecodeNewlinesForHref:as,delimiters:n.delimiters,comments:n.comments},this),i=a.render,o=a.staticRenderFns;n.render=i,n.staticRenderFns=o}}return os.call(this,e,t)},Mn.compile=ts,t.default=Mn}).call(this,n(0),n(10).setImmediate)},function(e,t,n){"use strict";(function(e){var r=void 0!==e&&e||"undefined"!=typeof self&&self||window,a=Function.prototype.apply;function i(e,t){this._id=e,this._clearFn=t}t.setTimeout=function(){return new i(a.call(setTimeout,r,arguments),clearTimeout)},t.setInterval=function(){return new i(a.call(setInterval,r,arguments),clearInterval)},t.clearTimeout=t.clearInterval=function(e){e&&e.close()},i.prototype.unref=i.prototype.ref=function(){},i.prototype.close=function(){this._clearFn.call(r,this._id)},t.enroll=function(e,t){clearTimeout(e._idleTimeoutId),e._idleTimeout=t},t.unenroll=function(e){clearTimeout(e._idleTimeoutId),e._idleTimeout=-1},t._unrefActive=t.active=function(e){clearTimeout(e._idleTimeoutId);var t=e._idleTimeout;t>=0&&(e._idleTimeoutId=setTimeout((function(){e._onTimeout&&e._onTimeout()}),t))},n(11),t.setImmediate="undefined"!=typeof self&&self.setImmediate||void 0!==e&&e.setImmediate||void 0,t.clearImmediate="undefined"!=typeof self&&self.clearImmediate||void 0!==e&&e.clearImmediate||void 0}).call(this,n(0))},function(e,t,n){"use strict";(function(e,t){!function(e,n){if(!e.setImmediate){var r,a,i,o,s,l=1,c={},f=!1,u=e.document,p=Object.getPrototypeOf&&Object.getPrototypeOf(e);p=p&&p.setTimeout?p:e,"[object process]"==={}.toString.call(e.process)?r=function(e){t.nextTick((function(){d(e)}))}:!function(){if(e.postMessage&&!e.importScripts){var t=!0,n=e.onmessage;return e.onmessage=function(){t=!1},e.postMessage("","*"),e.onmessage=n,t}}()?e.MessageChannel?((i=new MessageChannel).port1.onmessage=function(e){d(e.data)},r=function(e){i.port2.postMessage(e)}):u&&"onreadystatechange"in u.createElement("script")?(a=u.documentElement,r=function(e){var t=u.createElement("script");t.onreadystatechange=function(){d(e),t.onreadystatechange=null,a.removeChild(t),t=null},a.appendChild(t)}):r=function(e){setTimeout(d,0,e)}:(o="setImmediate$"+Math.random()+"$",s=function(t){t.source===e&&"string"==typeof t.data&&0===t.data.indexOf(o)&&d(+t.data.slice(o.length))},e.addEventListener?e.addEventListener("message",s,!1):e.attachEvent("onmessage",s),r=function(t){e.postMessage(o+t,"*")}),p.setImmediate=function(e){"function"!=typeof e&&(e=new Function(""+e));for(var t=new Array(arguments.length-1),n=0;n<t.length;n++)t[n]=arguments[n+1];var a={callback:e,args:t};return c[l]=a,r(l),l++},p.clearImmediate=_}function _(e){delete c[e]}function d(e){if(f)setTimeout(d,0,e);else{var t=c[e];if(t){f=!0;try{!function(e){var t=e.callback,n=e.args;switch(n.length){case 0:t();break;case 1:t(n[0]);break;case 2:t(n[0],n[1]);break;case 3:t(n[0],n[1],n[2]);break;default:t.apply(void 0,n)}}(t)}finally{_(e),f=!1}}}}}("undefined"==typeof self?void 0===e?void 0:e:self)}).call(this,n(0),n(12))},function(e,t,n){"use strict";var r,a,i=e.exports={};function o(){throw new Error("setTimeout has not been defined")}function s(){throw new Error("clearTimeout has not been defined")}function l(e){if(r===setTimeout)return setTimeout(e,0);if((r===o||!r)&&setTimeout)return r=setTimeout,setTimeout(e,0);try{return r(e,0)}catch(t){try{return r.call(null,e,0)}catch(t){return r.call(this,e,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:o}catch(e){r=o}try{a="function"==typeof clearTimeout?clearTimeout:s}catch(e){a=s}}();var c,f=[],u=!1,p=-1;function _(){u&&c&&(u=!1,c.length?f=c.concat(f):p=-1,f.length&&d())}function d(){if(!u){var e=l(_);u=!0;for(var t=f.length;t;){for(c=f,f=[];++p<t;)c&&c[p].run();p=-1,t=f.length}c=null,u=!1,function(e){if(a===clearTimeout)return clearTimeout(e);if((a===s||!a)&&clearTimeout)return a=clearTimeout,clearTimeout(e);try{a(e)}catch(t){try{return a.call(null,e)}catch(t){return a.call(this,e)}}}(e)}}function h(e,t){this.fun=e,this.array=t}function v(){}i.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];f.push(new h(e,t)),1!==f.length||u||l(d)},h.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=v,i.addListener=v,i.once=v,i.off=v,i.removeListener=v,i.removeAllListeners=v,i.emit=v,i.prependListener=v,i.prependOnceListener=v,i.listeners=function(e){return[]},i.binding=function(e){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},function(e,t,n){"use strict";(function(e){var r,a,i,o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};a="object"==("undefined"==typeof window?"undefined":o(window))&&!!window.document,function(e,t){var n=function(){},r=function(e){return"number"==typeof e},a=function(e){return JSON.stringify(e)},i=function(e){return new I(e)},s=i.LM="2025-01-11 09:28",l="https://github.com/xiangyuecn/Recorder",c="Recorder",f=e[c];if(f&&f.LM==s)f.CLog(f.i18n.$T("K8zP::重复导入{1}",0,c),3);else{i.IsOpen=function(){var e=i.Stream;if(e){var t=S(e)[0];if(t){var n=t.readyState;return"live"==n||n==t.LIVE}}return!1},i.BufferSize=4096,i.Destroy=function(){for(var e in x(c+" Destroy"),y(),u)u[e]()};var u={};i.BindDestroy=function(e,t){u[e]=t},i.Support=function(){if(!t)return!1;var e=navigator.mediaDevices||{};return e.getUserMedia||(e=navigator).getUserMedia||(e.getUserMedia=e.webkitGetUserMedia||e.mozGetUserMedia||e.msGetUserMedia),!!e.getUserMedia&&(i.Scope=e,!!i.GetContext())},i.GetContext=function(e){if(!t)return null;var n=window.AudioContext;if(n||(n=window.webkitAudioContext),!n)return null;var r=i.Ctx,a=0;return r||(r=i.Ctx=new n,a=1,i.NewCtxs=i.NewCtxs||[],i.BindDestroy("Ctx",(function(){var e=i.Ctx;e&&e.close&&(p(e),i.Ctx=0);var t=i.NewCtxs;i.NewCtxs=[];for(var n=0;n<t.length;n++)p(t[n])}))),e&&r.close&&(a||(r._useC||p(r),r=new n),r._useC=1,i.NewCtxs.push(r)),r},i.CloseNewCtx=function(e){if(e&&e.close){p(e);for(var t=i.NewCtxs||[],n=t.length,r=0;r<t.length;r++)if(t[r]==e){t.splice(r,1);break}x(O("mSxV::剩{1}个GetContext未close",0,n+"-1="+t.length),t.length?3:0)}};var p=function(e){if(e&&e.close&&!e._isC&&(e._isC=1,"closed"!=e.state))try{e.close()}catch(e){x("ctx close err",1,e)}},_=i.ResumeCtx=function(e,t,n,r){var a=0,i=0,o=0,s=0,l="EventListener",c="ResumeCtx ",f=function(t,c){i&&u(),a||(a=1,t&&r(t,s),c&&n(s)),c&&(!e._LsSC&&e["add"+l]&&e["add"+l]("statechange",p),e._LsSC=1,o=1)},u=function(e){if(!e||!i){i=e?1:0;for(var t=["focus","mousedown","mouseup","touchstart","touchend"],n=0;n<t.length;n++)window[(e?"add":"remove")+l](t[n],p,!0)}},p=function(){var n=e.state,r=d(n);if(!a&&!t(r?++s:s))return f();r?(o&&x(c+"sc "+n,3),u(1),e.resume().then((function(){o&&x(c+"sc "+e.state),f(0,1)})).catch((function(t){x(c+"error",1,t),d(e.state)||f(t.message||"error")}))):"closed"==n?(o&&!e._isC&&x(c+"sc "+n,1),f("ctx closed")):f(0,1)};p()},d=i.CtxSpEnd=function(e){return"suspended"==e||"interrupted"==e},h=function(e){var t=e.state,n="ctx.state="+t;return d(t)&&(n+=O("nMIy::（注意：ctx不是running状态，rec.open和start至少要有一个在用户操作(触摸、点击等)时进行调用，否则将在rec.start时尝试进行ctx.resume，可能会产生兼容性问题(仅iOS)，请参阅文档中runningContext配置）")),n};i.ConnectEnableWebM=!0,i.ConnectEnableWorklet=!1;var v=function(e){var t=e.BufferSize||i.BufferSize,n=e.Stream,r=n._c,o=r.sampleRate,s={},l=S(n)[0],f=null,u="";if(l&&l.getSettings){var p=(f=l.getSettings()).sampleRate;p&&p!=o&&(u=O("eS8i::Stream的采样率{1}不等于{2}，将进行采样率转换（注意：音质不会变好甚至可能变差），主要在移动端未禁用回声消除时会产生此现象，浏览器有回声消除时可能只会返回16k采样率的音频数据，",0,p,o))}n._ts=f,x(u+"Stream TrackSet: "+a(f),u?3:0);var d,h,v,m=function(e){var t=n._m=r.createMediaStreamSource(n),a=r.destination,i="createMediaStreamDestination";r[i]&&(a=n._d=r[i]()),t.connect(e),e.connect(a)},y="",w=n._call,k=function(e,t){for(var n in w){if(t!=o){s.index=0;var r=(s=i.SampleData([e],t,o,s,{_sum:1})).data,a=s._sum}else{s={};for(var l=e.length,c=(r=new Int16Array(l),a=0,0);c<l;c++){var f=Math.max(-1,Math.min(1,e[c]));f=f<0?32768*f:32767*f,r[c]=f,a+=Math.abs(f)}}for(var u in w)w[u](r,a);return}},A=r.createScriptProcessor||r.createJavaScriptNode,T=O("ZGlf::。由于{1}内部1秒375次回调，在移动端可能会有性能问题导致回调丢失录音变短，PC端无影响，暂不建议开启{1}。",0,"audioWorklet"),R=function(){h=n.isWorklet=!1,b(n),x(O("7TU0::Connect采用老的{1}，",0,"ScriptProcessor")+B.get(O(i.ConnectEnableWorklet?"JwCL::但已设置{1}尝试启用{2}":"VGjB::可设置{1}尝试启用{2}",2),[c+".ConnectEnableWorklet=true","audioWorklet"])+y+T,3);var e=n._p=A.call(r,t,1,1);m(e),e.onaudioprocess=function(e){var t=e.inputBuffer.getChannelData(0);k(t,o)}},C=function(){d=n.isWebM=!1,g(n),h=n.isWorklet=!A||i.ConnectEnableWorklet;var e=window.AudioWorkletNode;if(h&&r.audioWorklet&&e){var a=function(){return h&&n._na},s=n._na=function(){""!==v&&(clearTimeout(v),v=setTimeout((function(){v=0,a()&&(x(O("MxX1::{1}未返回任何音频，恢复使用{2}",0,"audioWorklet","ScriptProcessor"),3),A&&R())}),500))},l=function(){if(a()){var i=n._n=new e(r,"RecProc",{processorOptions:{bufferSize:t}});m(i),i.port.onmessage=function(e){v&&(clearTimeout(v),v=""),a()?k(e.data.val,o):h||x(O("XUap::{1}多余回调",0,"audioWorklet"),3)},x(O("yOta::Connect采用{1}，设置{2}可恢复老式{3}",0,"audioWorklet",c+".ConnectEnableWorklet=false","ScriptProcessor")+y+T,3)}},f=function(){if(a())if(r.RecProc)l();else{var e,t,n=(t="class RecProc extends AudioWorkletProcessor{",t+="constructor "+(e=function(e){return e.toString().replace(/^function|DEL_/g,"").replace(/\$RA/g,"Recorder audioWorklet")})((function(e){DEL_super(e);var t=this,n=e.processorOptions.bufferSize;t.bufferSize=n,t.buffer=new Float32Array(2*n),t.pos=0,t.port.onmessage=function(e){e.data.kill&&(t.kill=!0,$C.log("$RA kill call"))},$C.log("$RA .ctor call",e)})),t+="process "+e((function(e,t,n){var r=this.bufferSize,a=this.buffer,i=this.pos;if((e=(e[0]||[])[0]||[]).length){a.set(e,i);var o=~~((i+=e.length)/r)*r;if(o){this.port.postMessage({val:a.slice(0,o)});var s=a.subarray(o,i);(a=new Float32Array(2*r)).set(s),i=s.length,this.buffer=a}this.pos=i}return!this.kill})),t=(t+='}try{registerProcessor("RecProc", RecProc)}catch(e){$C.error("Recorder audioWorklet Reg Error",e)}').replace(/\$C\./g,"console."),"data:text/javascript;base64,"+btoa(unescape(encodeURIComponent(t))));r.audioWorklet.addModule(n).then((function(e){a()&&(r.RecProc=1,l(),v&&s())})).catch((function(e){x("audioWorklet.addModule Error",1,e),a()&&R()}))}};_(r,(function(){return a()}),f,f)}else R()};!function(){var e=window.MediaRecorder;d=n.isWebM=i.ConnectEnableWebM;var r=e&&"ondataavailable"in e.prototype&&e.isTypeSupported("audio/webm; codecs=pcm");if(y=r?"":O("VwPd::（此浏览器不支持{1}）",0,"MediaRecorder.WebM.PCM"),d&&r){var a=function(){return d&&n._ra};n._ra=function(){""!==v&&(clearTimeout(v),v=setTimeout((function(){a()&&(x(O("vHnb::{1}未返回任何音频，降级使用{2}",0,"MediaRecorder","audioWorklet"),3),C())}),500))};var o=Object.assign({mimeType:"audio/webm; codecs=pcm"},i.ConnectWebMOptions),s=n._r=new e(n,o),l=n._rd={};s.ondataavailable=function(e){var t=new FileReader;t.onloadend=function(){if(a()){var e=M(new Uint8Array(t.result),l);if(!e)return;if(-1==e)return void C();v&&(clearTimeout(v),v=""),k(e,l.webmSR)}else d||x(O("O9P7::{1}多余回调",0,"MediaRecorder"),3)},t.readAsArrayBuffer(e.data)};try{s.start(~~(t/48)),x(O("LMEm::Connect采用{1}，设置{2}可恢复使用{3}或老式{4}",0,"MediaRecorder.WebM.PCM",c+".ConnectEnableWebM=false","audioWorklet","ScriptProcessor"))}catch(e){x("mr start err",1,e),C()}}else C()}()},m=function(e){e._na&&e._na(),e._ra&&e._ra()},b=function(e){e._na=null,e._n&&(e._n.port.postMessage({kill:!0}),e._n.disconnect(),e._n=null)},g=function(e){if(e._ra=null,e._r){try{e._r.stop()}catch(e){x("mr stop err",1,e)}e._r=null}},y=function(e){var t=(e=e||i)==i,n=e.Stream;n&&(n._m&&(n._m.disconnect(),n._m=null),!n._RC&&n._c&&i.CloseNewCtx(n._c),n._RC=null,n._c=null,n._d&&(w(n._d.stream),n._d=null),n._p&&(n._p.disconnect(),n._p.onaudioprocess=n._p=null),b(n),g(n),t&&w(n)),e.Stream=0},w=i.StopS_=function(e){for(var t=S(e),n=0;n<t.length;n++){var r=t[n];r.stop&&r.stop()}e.stop&&e.stop()},S=function(e){var t=0,n=0,r=[];e.getAudioTracks&&(t=e.getAudioTracks(),n=e.getVideoTracks()),t||(t=e.audioTracks,n=e.videoTracks);for(var a=0,i=t?t.length:0;a<i;a++)r.push(t[a]);for(a=0,i=n?n.length:0;a<i;a++)r.push(n[a]);return r};i.SampleData=function(e,t,n,r,a){var o="SampleData";r||(r={});var s,l=r.index||0,c=r.offset||0,f=r.raisePrev||0,u=r.filter;u&&u.fn&&(u.sr&&u.sr!=t||u.srn&&u.srn!=n)&&(u=null,x(O("d48C::{1}的filter采样率变了，重设滤波",0,o),3)),u||(u=n<=t?{fn:(s=n>3*t/4?0:n/2*3/4)?i.IIRFilter(!0,t,s):0}:{fn:(s=t>3*n/4?0:t/2*3/4)?i.IIRFilter(!0,n,s):0}),u.sr=t,u.srn=n;var p=u.fn,_=r.frameNext||[];a||(a={});var d=a.frameSize||1;a.frameType&&(d="mp3"==a.frameType?1152:1);var h=a._sum,v=0,m=e.length;l>m+1&&x(O("tlbC::{1}似乎传入了未重置chunk {2}",0,o,l+">"+m),3);for(var b=0,g=l;g<m;g++)b+=e[g].length;var y=t/n;if(y>1)b=Math.max(0,b-Math.floor(c)),b=Math.floor(b/y);else if(y<1){var w=1/y;b=Math.floor(b*w)}b+=_.length;var S=new Int16Array(b),k=0;for(g=0;g<_.length;g++)S[k]=_[g],k++;for(;l<m;l++){var A=e[l],M=A instanceof Float32Array,T=(g=c,A.length),R=p&&p.Embed,C=0,E=0,B=0,L=0;if(y<1){for(var I=k+g,P=f,$=0;$<T;$++){var N=A[$];M&&(N=(N=Math.max(-1,Math.min(1,N)))<0?32768*N:32767*N);var D=Math.floor(I);I+=w;for(var H=Math.floor(I),j=(N-P)/(H-D),F=1;D<H;D++,F++){var V=Math.floor(P+F*j);R?(B=V,L=R.b0*B+R.b1*R.x1+R.b0*R.x2-R.a1*R.y1-R.a2*R.y2,R.x2=R.x1,R.x1=B,R.y2=R.y1,R.y1=L,V=L):V=p?p(V):V,V>32767?V=32767:V<-32768&&(V=-32768),h&&(v+=Math.abs(V)),S[D]=V,k++}P=f=N,g+=w}c=g%1}else{$=0;for(var z=0;$<T;$++,z++)if(z<T&&(N=A[z],M&&(N=(N=Math.max(-1,Math.min(1,N)))<0?32768*N:32767*N),R?(B=N,L=R.b0*B+R.b1*R.x1+R.b0*R.x2-R.a1*R.y1-R.a2*R.y2,R.x2=R.x1,R.x1=B,R.y2=R.y1,R.y1=L):L=p?p(N):N),C=E,E=L,0!=z){var U=Math.floor(g);if($==U){var X=C+((Math.ceil(g)<T?E:C)-C)*(g-U);X>32767?X=32767:X<-32768&&(X=-32768),h&&(v+=Math.abs(X)),S[k]=X,k++,g+=y}}else $--;c=Math.max(0,g-T)}}y<1&&k+1==b&&(b--,S=new Int16Array(S.buffer.slice(0,2*b))),k-1!=b&&k!=b&&x(o+" idx:"+k+" != size:"+b,3),_=null;var q=b%d;if(q>0){var Y=2*(b-q);_=new Int16Array(S.buffer.slice(Y)),S=new Int16Array(S.buffer.slice(0,Y))}var W={index:l,offset:c,raisePrev:f,filter:u,frameNext:_,sampleRate:n,data:S};return h&&(W._sum=v),W},i.IIRFilter=function(e,t,n){var r=2*Math.PI*n/t,a=Math.sin(r),i=Math.cos(r),o=a/2,s=1+o,l=-2*i/s,c=(1-o)/s;if(e)var f=(1-i)/2/s,u=(1-i)/s;else f=(1+i)/2/s,u=-(1+i)/s;var p=0,_=0,d=0,h=0,v=0,m=function(e){return d=f*e+u*p+f*_-l*h-c*v,_=p,p=e,v=h,h=d,d};return m.Embed={x1:0,x2:0,y1:0,y2:0,b0:f,b1:u,a1:l,a2:c},m},i.PowerLevel=function(e,t){var n=e/t||0;return n<1251?Math.round(n/1250*10):Math.round(Math.min(100,Math.max(0,100*(1+Math.log(n/1e4)/Math.log(10)))))},i.PowerDBFS=function(e){var t=Math.max(.1,e||0);return t=Math.min(t,32767),t=20*Math.log(t/32767)/Math.log(10),Math.max(-100,Math.round(t))},i.CLog=function(e,t){if("object"==("undefined"==typeof console?"undefined":o(console))){var n=new Date,a=("0"+n.getMinutes()).substr(-2)+":"+("0"+n.getSeconds()).substr(-2)+"."+("00"+n.getMilliseconds()).substr(-3),s=this&&this.envIn&&this.envCheck&&this.id,l=["["+a+" "+c+(s?":"+s:"")+"]"+e],f=arguments,u=i.CLog,p=2,_=u.log||console.log;for(r(t)?_=1==t?u.error||console.error:3==t?u.warn||console.warn:_:p=1;p<f.length;p++)l.push(f[p]);k?_&&_("[IsLoser]"+l[0],l.length>1?l:""):_.apply(console,l)}};var x=function(){i.CLog.apply(this,arguments)},k=!0;try{k=!console.log.apply}catch(e){}var A=0;i.Sync={O:9,C:9},i.prototype=I.prototype={CLog:x,_streamStore:function(){return this.set.sourceStream?this:i},_streamGet:function(){return this._streamStore().Stream},_streamCtx:function(){var e=this._streamGet();return e&&e._c},open:function(e,r){var c=this,f=c.set,u=c._streamStore(),p=0;e=e||n;var _=function(e,t){t=!!t,c.CLog(O("5tWi::录音open失败：")+e+",isUserNotAllow:"+t,1),p&&i.CloseNewCtx(p),r&&r(e,t)};c._streamTag="getUserMedia";var d=function(){c.CLog("open ok, id:"+c.id+" stream:"+c._streamTag),e(),c._SO=0},m=u.Sync,b=++m.O,g=m.C;if(c._O=c._O_=b,c._SO=c._S,t){var w=c.envCheck({envName:"H5",canProcess:!0});if(w)_(O("A5bm::不能录音：")+w);else{var S,x=function(){(S=f.runningContext)||(S=p=i.GetContext(!0))};if(f.sourceStream){if(c._streamTag="set.sourceStream",!i.GetContext())return void _(O("1iU7::不支持此浏览器从流中获取录音"));x(),y(u);var k=c.Stream=f.sourceStream;k._c=S,k._RC=f.runningContext,k._call={};try{v(u)}catch(e){return y(u),void _(O("BTW2::从流中打开录音失败：")+e.message)}d()}else{var A=function(e,t){try{window.top.a}catch(e){return void _(O("Nclz::无权录音(跨域，请尝试给iframe添加麦克风访问策略，如{1})",0,'allow="camera;microphone"'))}M(1,e)&&(/Found/i.test(e)?_(t+O("jBa9::，无可用麦克风")):_(t))},M=function(e,t){if(/Permission|Allow/i.test(t))e&&_(O("gyO5::用户拒绝了录音权限"),!0);else{if(!1!==window.isSecureContext)return 1;e&&_(O("oWNo::浏览器禁止不安全页面录音，可开启https解决"))}};if(i.IsOpen())d();else if(i.Support()){x();var T,R,C=function(e){setTimeout((function(){e._call={};var t=i.Stream;t&&(y(),e._call=t._call),i.Stream=e,e._c=S,e._RC=f.runningContext,function(){if(g!=m.C||!c._O){var e=O("dFm8::open被取消");return b==m.O?c.close():e=O("VtJO::open被中断"),_(e),!0}}()||(i.IsOpen()?(t&&c.CLog(O("upb8::发现同时多次调用open"),1),v(u),d()):_(O("Q1GA::录音功能无效：无音频流")))}),100)},E=function(e){var t=e.name||e.message||e.code+":"+e,n="";1==B&&M(0,t)&&(n=O("KxE2::，将尝试禁用回声消除后重试"));var r=O("xEQR::请求录音权限错误"),a=O("bDOG::无法录音：");c.CLog(r+n+"|"+e,n||R?3:1,e),n?(T=t,R=e,L(1)):R?(c.CLog(r+"|"+R,1,R),A(T,a+R)):A(t,a+e)},B=0,L=function(e){B++;var t="audioTrackSet",n="echoCancellation",r="noiseSuppression",u=JSON.parse(a(f[t]||!0));c.CLog("open... "+B+" "+t+":"+a(u)),e&&("object"!=(void 0===u?"undefined":o(u))&&(u={}),u.autoGainControl=!1,u[n]=!1,u[r]=!1),u.sampleRate&&c.CLog(O("IjL3::注意：已配置{1}参数，可能会出现浏览器不能正确选用麦克风、移动端无法启用回声消除等现象",0,t+".sampleRate"),3);var p={audio:u,video:f.videoTrackSet||!1};try{var _=i.Scope.getUserMedia(p,C,E)}catch(e){c.CLog("getUserMedia",3,e),p={audio:!0,video:!1},_=i.Scope.getUserMedia(p,C,E)}c.CLog("getUserMedia("+a(p)+") "+h(S)+O("RiWe::，未配置 {1} 时浏览器可能会自动启用回声消除，移动端未禁用回声消除时可能会降低系统播放音量（关闭录音后可恢复）和仅提供16k采样率的音频流（不需要回声消除时可明确配置成禁用来获得48k高音质的流），请参阅文档中{2}配置",0,"audioTrackSet:{echoCancellation,noiseSuppression,autoGainControl}",t)+"("+l+") LM:"+s+" UA:"+navigator.userAgent),_&&_.then&&_.then(C).catch(E)};L()}else A("",O("COxc::此浏览器不支持录音"))}}}else _(O.G("NonBrowser-1",["open"])+O("EMJq::，可尝试使用RecordApp解决方案")+"("+l+"/tree/master/app-support-sample)")},close:function(e){e=e||n;var t=this._streamStore();this._stop();var r=" stream:"+this._streamTag,a=t.Sync;if(this._O=0,this._O_!=a.O)return this.CLog(O("hWVz::close被忽略（因为同时open了多个rec，只有最后一个会真正close）")+r,3),void e();a.C++,y(t),this.CLog("close,"+r),e()},mock:function(e,t){return this._stop(),this.isMock=1,this.mockEnvInfo=null,this.buffers=[e],this.recSize=e.length,this._setSrcSR(t),this._streamTag="mock",this},_setSrcSR:function(e){var t=this.set,n=t.sampleRate;n>e?t.sampleRate=e:n=0,this.srcSampleRate=e,this.CLog("srcSampleRate: "+e+" set.sampleRate: "+t.sampleRate+(n?" "+O("UHvm::忽略")+": "+n:""),n?3:0)},envCheck:function(e){var t,n=this.set,r="CPU_BE";if(t||i[r]||"function"!=typeof Int8Array||new Int8Array(new Int32Array([1]).buffer)[0]||(L(r),t=O("Essp::不支持{1}架构",0,r)),!t){var a=n.type,o=this[a+"_envCheck"];n.takeoffEncodeChunk&&(o?e.canProcess||(t=O("7uMV::{1}环境不支持实时处理",0,e.envName)):t=O("2XBl::{1}类型不支持设置takeoffEncodeChunk",0,a)+(this[a]?"":O("LG7e::(未加载编码器)"))),!t&&o&&(t=this[a+"_envCheck"](e,n))}return t||""},envStart:function(e,t){var n=this.set;if(this.isMock=e?1:0,this.mockEnvInfo=e,this.buffers=[],this.recSize=0,e&&(this._streamTag="env$"+e.envName),this.state=1,this.envInLast=0,this.envInFirst=0,this.envInFix=0,this.envInFixTs=[],this._setSrcSR(t),this.engineCtx=0,this[n.type+"_start"]){var r=this.engineCtx=this[n.type+"_start"](n);r&&(r.pcmDatas=[],r.pcmSize=0)}},envResume:function(){this.envInFixTs=[]},envIn:function(e,t){var n=this,r=n.set,a=n.engineCtx;if(1==n.state){var o=n.srcSampleRate,s=e.length,l=i.PowerLevel(t,s),c=n.buffers,f=c.length;c.push(e);var u=c,p=f,_=Date.now(),d=Math.round(s/o*1e3);n.envInLast=_,1==n.buffers.length&&(n.envInFirst=_-d);var h=n.envInFixTs;h.splice(0,0,{t:_,d:d});for(var v=_,m=0,b=0;b<h.length;b++){var g=h[b];if(_-g.t>3e3){h.length=b;break}v=g.t,m+=g.d}var y=h[1],w=_-v;if(w-m>w/3&&(y&&w>1e3||h.length>=6)){var S=_-y.t-d;if(S>d/5){var x=!r.disableEnvInFix;if(n.CLog("["+_+"]"+B.get(O(x?"4Kfd::补偿{1}ms":"bM5i::未补偿{1}ms",1),[S]),3),n.envInFix+=S,x){var k=new Int16Array(S*o/1e3);s+=k.length,c.push(k)}}}var A=n.recSize,M=s,T=A+M;if(n.recSize=T,a){var R=i.SampleData(c,o,r.sampleRate,a.chunkInfo);a.chunkInfo=R,T=(A=a.pcmSize)+(M=R.data.length),a.pcmSize=T,c=a.pcmDatas,f=c.length,c.push(R.data),o=R.sampleRate}var C=Math.round(T/o*1e3),E=c.length,L=u.length,I=function(){for(var e=P?0:-M,t=null==c[0],i=f;i<E;i++){var o=c[i];null==o?t=1:(e+=o.length,a&&o.length&&n[r.type+"_encode"](a,o))}if(t&&a)for(i=p,u[0]&&(i=0);i<L;i++)u[i]=null;t&&(e=P?M:0,c[0]=null),a?a.pcmSize+=e:n.recSize+=e},P=0;try{P=!0===(P=r.onProcess(c,l,C,o,f,I))}catch(e){console.error("rec.set.onProcess"+O("gFUF::回调出错是不允许的，需保证不会抛异常"),e)}var $=Date.now()-_;if($>10&&n.envInFirst-_>1e3&&n.CLog("rec.set.onProcess"+O("2ghS::低性能，耗时{1}ms",0,$),3),P){var N=0;for(b=f;b<E;b++)null==c[b]?N=1:c[b]=new Int16Array(0);N?n.CLog(O("ufqH::未进入异步前不能清除buffers"),3):a?a.pcmSize-=M:n.recSize-=M}else I()}else n.state||n.CLog("envIn at state=0",3)},start:function(){var e=this,t=1;if(e.set.sourceStream?e.Stream||(t=0):i.IsOpen()||(t=0),t){var n=e._streamCtx();if(e.CLog(O("kLDN::start 开始录音，")+h(n)+" stream:"+e._streamTag),e._stop(),e.envStart(null,n.sampleRate),e.state=3,e._SO&&e._SO+1!=e._S)e.CLog(O("Bp2y::start被中断"),3);else{e._SO=0;var r=function(){3==e.state&&(e.state=1,e.resume())},a="AudioContext resume: ";e._streamGet()._call[e.id]=function(){e.CLog(a+n.state+"|stream ok"),r()},_(n,(function(t){return t&&e.CLog(a+"wait..."),3==e.state}),(function(t){t&&e.CLog(a+n.state),r()}),(function(t){e.CLog(a+n.state+O("upkE::，可能无法录音：")+t,1),r()}))}}else e.CLog(O("6WmN::start失败：未open"),1)},pause:function(){var e=this._streamGet();this.state&&(this.state=2,this.CLog("pause"),e&&delete e._call[this.id])},resume:function(){var e=this,t=e._streamGet(),n="resume(wait ctx)";if(3==e.state)e.CLog(n);else if(e.state){e.state=1,e.CLog("resume"),e.envResume(),t&&(t._call[e.id]=function(t,n){1==e.state&&e.envIn(t,n)},m(t));var r=e._streamCtx();r&&_(r,(function(t){return t&&e.CLog(n+"..."),1==e.state}),(function(a){a&&e.CLog(n+r.state),m(t)}),(function(t){e.CLog(n+r.state+"[err]"+t,1)}))}},_stop:function(e){var t=this.set;this.isMock||this._S++,this.state&&(this.pause(),this.state=0),!e&&this[t.type+"_stop"]&&(this[t.type+"_stop"](this.engineCtx),this.engineCtx=0)},stop:function(e,t,n){var r,a=this,o=a.set,f=a.envInLast-a.envInFirst,u=f&&a.buffers.length;a.CLog(O("Xq4s::stop 和start时差:")+(f?f+"ms "+O("3CQP::补偿:")+a.envInFix+"ms envIn:"+u+" fps:"+(u/f*1e3).toFixed(1):"-")+" stream:"+a._streamTag+" ("+l+") LM:"+s);var p=function(){a._stop(),n&&a.close()},_=function(e){a.CLog(O("u8JG::结束录音失败：")+e,1),t&&t(e),p()},d=function(t,n,s){var l="DefaultDataType",f=a.dataType||i[l]||"blob",u="dataType="+f,d=t instanceof ArrayBuffer,h=0,v=d?t.byteLength:t.size;if("arraybuffer"==f?d||(h=1):"blob"==f?"function"!=typeof Blob?h=O.G("NonBrowser-1",[u])+O("1skY::，请设置{1}",0,c+"."+l+'="arraybuffer"'):(d&&(t=new Blob([t],{type:n})),t instanceof Blob||(h=1),n=t.type||n):h=O.G("NotSupport-1",[u]),a.CLog(O("Wv7l::结束录音 编码花{1}ms 音频时长{2}ms 文件大小{3}b",0,Date.now()-r,s,v)+" "+u+","+n),h)_(1!=h?h:O("Vkbd::{1}编码器返回的不是{2}",0,o.type,f)+", "+u);else{if(o.takeoffEncodeChunk)a.CLog(O("QWnr::启用takeoffEncodeChunk后stop返回的blob长度为0不提供音频数据"),3);else if(v<Math.max(50,s/5))return void _(O("Sz2H::生成的{1}无效",0,o.type));e&&e(t,s,n),p()}};if(!a.isMock){var h=3==a.state;if(!a.state||h)return void _(O("wf9t::未开始录音")+(h?O("Dl2c::，开始录音前无用户交互导致AudioContext未运行"):""))}a._stop(!0);var v=a.recSize;if(v)if(a[o.type]){if(a.isMock){var m=a.envCheck(a.mockEnvInfo||{envName:"mock",canProcess:!1});if(m)return void _(O("AxOH::录音错误：")+m)}var b=a.engineCtx;if(a[o.type+"_complete"]&&b){var g=Math.round(b.pcmSize/o.sampleRate*1e3);return r=Date.now(),void a[o.type+"_complete"](b,(function(e,t){d(e,t,g)}),_)}if(r=Date.now(),a.buffers[0]){var y=i.SampleData(a.buffers,a.srcSampleRate,o.sampleRate);o.sampleRate=y.sampleRate;var w=y.data;g=Math.round(w.length/o.sampleRate*1e3),a.CLog(O("CxeT::采样:{1} 花:{2}ms",0,v+"->"+w.length,Date.now()-r)),setTimeout((function(){r=Date.now(),a[o.type](w,(function(e,t){d(e,t,g)}),(function(e){_(e)}))}))}else _(O("xkKd::音频buffers被释放"))}else _(O("xGuI::未加载{1}编码器，请尝试到{2}的src/engine内找到{1}的编码器并加载",0,o.type,c));else _(O("Ltz3::未采集到录音"))}};var M=function(e,t){t.pos||(t.pos=[0],t.tracks={},t.bytes=[]);var n=t.tracks,r=[t.pos[0]],a=function(){t.pos[0]=r[0]},i=t.bytes.length,o=new Uint8Array(i+e.length);if(o.set(t.bytes),o.set(e,i),t.bytes=o,!t._ht){if(C(o,r),E(o,r),!T(C(o,r),[24,83,128,103]))return;for(C(o,r);r[0]<o.length;){var s=C(o,r),l=E(o,r),c=[0],f=0;if(!l)return;if(T(s,[22,84,174,107])){for(;c[0]<l.length;){var u=C(l,c),p=E(l,c),_=[0],d={channels:0,sampleRate:0};if(T(u,[174]))for(;_[0]<p.length;){var h=C(p,_),v=E(p,_),m=[0];if(T(h,[215])){var b=R(v);d.number=b,n[b]=d}else if(T(h,[131]))1==(b=R(v))?d.type="video":2==b?(d.type="audio",f||(t.track0=d),d.idx=f++):d.type="Type-"+b;else if(T(h,[134])){for(var g="",y=0;y<v.length;y++)g+=String.fromCharCode(v[y]);d.codec=g}else if(T(h,[225]))for(;m[0]<v.length;){var w=C(v,m),S=E(v,m);if(T(w,[181])){b=0;var k=new Uint8Array(S.reverse()).buffer;4==S.length?b=new Float32Array(k)[0]:8==S.length?b=new Float64Array(k)[0]:x("WebM Track !Float",1,S),d.sampleRate=Math.round(b)}else T(w,[98,100])?d.bitDepth=R(S):T(w,[159])&&(d.channels=R(S))}}}t._ht=1,x("WebM Tracks",n),a();break}}}var A=t.track0;if(A){var M=A.sampleRate;if(t.webmSR=M,16==A.bitDepth&&/FLOAT/i.test(A.codec)&&(A.bitDepth=32,x("WebM 16->32 bit",3)),M<8e3||32!=A.bitDepth||A.channels<1||!/(\b|_)PCM\b/i.test(A.codec))return t.bytes=[],t.bad||x("WebM Track Unexpected",3,t),t.bad=1,-1;for(var B=[],O=0;r[0]<o.length&&(u=C(o,r),p=E(o,r));){if(T(u,[163])){var L=15&p[0];if(!(d=n[L]))return x("WebM !Track"+L,1,n),-1;if(0===d.idx){var I=new Uint8Array(p.length-4);for(y=4;y<p.length;y++)I[y-4]=p[y];B.push(I),O+=I.length}}a()}if(O){var P=new Uint8Array(o.length-t.pos[0]);P.set(o.subarray(t.pos[0])),t.bytes=P,t.pos[0]=0,I=new Uint8Array(O),y=0;for(var $=0;y<B.length;y++)I.set(B[y],$),$+=B[y].length;if(k=new Float32Array(I.buffer),A.channels>1){var N=[];for(y=0;y<k.length;)N.push(k[y]),y+=A.channels;k=new Float32Array(N)}return k}}},T=function(e,t){if(!e||e.length!=t.length)return!1;if(1==e.length)return e[0]==t[0];for(var n=0;n<e.length;n++)if(e[n]!=t[n])return!1;return!0},R=function(e){for(var t="",n=0;n<e.length;n++){var r=e[n];t+=(r<16?"0":"")+r.toString(16)}return parseInt(t,16)||0},C=function(e,t,n){var r=t[0];if(!(r>=e.length)){var a=("0000000"+e[r].toString(2)).substr(-8),i=/^(0*1)(\d*)$/.exec(a);if(i){var o=i[1].length,s=[];if(!(r+o>e.length)){for(var l=0;l<o;l++)s[l]=e[r],r++;return n&&(s[0]=parseInt(i[2]||"0",2)),t[0]=r,s}}}},E=function(e,t){var n=C(e,t,1);if(n){var r=R(n),a=t[0],i=[];if(r<2147483647){if(a+r>e.length)return;for(var o=0;o<r;o++)i[o]=e[a],a++}return t[0]=a,i}},B=i.i18n={lang:"zh-CN",alias:{"zh-CN":"zh","en-US":"en"},locales:{},data:{},put:function(e,t){var n=c+".i18n.put: ",r=e.overwrite;r=null==r||r;var a=e.lang;if(!(a=B.alias[a]||a))throw new Error(n+"set.lang?");var i=B.locales[a];i||(i={},B.locales[a]=i);for(var o,s=/^([\w\-]+):/,l=0;l<t.length;l++){var f=t[l];if(o=s.exec(f)){var u=o[1];f=f.substr(u.length+1),!r&&i[u]||(i[u]=f)}else x(n+"'key:'? "+f,3,e)}},get:function(){return B.v_G.apply(null,arguments)},v_G:function(e,t,n){t=t||[],n=n||B.lang,n=B.alias[n]||n;var r=B.locales[n],a=r&&r[e]||"";return a||"zh"==n?(B.lastLang=n,"=Empty"==a?"":a.replace(/\{(\d+)(\!?)\}/g,(function(n,r,i){return n=t[(r=+r||0)-1],(r<1||r>t.length)&&(n="{?}",x("i18n["+e+"] no {"+r+"}: "+a,3)),i?"":n}))):"en"==n?B.v_G(e,t,"zh"):B.v_G(e,t,"en")},$T:function(){return B.v_T.apply(null,arguments)},v_T:function(){for(var e,t=arguments,n="",a=[],i=0,o=c+".i18n.$T:",s=/^([\w\-]*):/,l=0;l<t.length;l++){var f=t[l];if(0==l){if(!(n=(e=s.exec(f))&&e[1]))throw new Error(o+"0 'key:'?");f=f.substr(n.length+1)}if(-1===i)a.push(f);else{if(i)throw new Error(o+" bad args");if(0===f)i=-1;else if(r(f)){if(f<1)throw new Error(o+" bad args");i=f}else{var u=1==l?"en":l?"":"zh";if((e=s.exec(f))&&(u=e[1]||u,f=f.substr(e[1].length+1)),!e||!u)throw new Error(o+l+" 'lang:'?");B.put({lang:u,overwrite:!1},[n+":"+f])}}}return n?i>0?n:B.v_G(n,a):""}},O=B.$T;O.G=B.get,O("NonBrowser-1::非浏览器环境，不支持{1}",1),O("IllegalArgs-1::参数错误：{1}",1),O("NeedImport-2::调用{1}需要先导入{2}",2),O("NotSupport-1::不支持：{1}",1),i.TrafficImgUrl="//ia.51.la/go1?id=20469973&pvFlag=1";var L=i.Traffic=function(e){if(t){e=e?"/"+c+"/Report/"+e:"";var n=i.TrafficImgUrl;if(n){var r=i.Traffic,a=/^(https?:..[^\/#]*\/?)[^#]*/i.exec(location.href)||[],o=a[1]||"http://file/",s=(a[0]||o)+e;0==n.indexOf("//")&&(n=/^https:/i.test(s)?"https:"+n:"http:"+n),e&&(n=n+"&cu="+encodeURIComponent(o+e)),r[s]||(r[s]=1,(new Image).src=n,x("Traffic Analysis Image: "+(e||c+".TrafficImgUrl="+i.TrafficImgUrl)))}}};f&&(x(O("8HO5::覆盖导入{1}",0,c),1),f.Destroy()),e[c]=i}function I(e){this.id=++A,L();var t={type:"mp3",onProcess:n};for(var a in e)t[a]=e[a];this.set=t;var i=t.bitRate,o=t.sampleRate;(i&&!r(i)||o&&!r(o))&&this.CLog(O.G("IllegalArgs-1",[O("VtS4::{1}和{2}必须是数值",0,"sampleRate","bitRate")]),1,e),t.bitRate=+i||16,t.sampleRate=+o||16e3,this.state=0,this._S=9,this.Sync={O:9,C:9}}}(i=a?window:Object,a),void 0===(r=function(){return i.Recorder}.call(t,n,t,e))||(e.exports=r),"object"==o(e)&&e.exports&&(e.exports=i.Recorder)}).call(this,n(2)(e))},function(e,t,n){"use strict";var r,a,i,o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};r="object"==("undefined"==typeof window?"undefined":o(window))&&!!window.document,a=(r?window:Object).Recorder,i=a.i18n,function(e,t,n,r){var a="48000, 44100, 32000, 24000, 22050, 16000, 12000, 11025, 8000",i="8, 16, 24, 32, 40, 48, 56, 64, 80, 96, 112, 128, 144, 160, 192, 224, 256, 320";e.prototype.enc_mp3={stable:!0,takeEC:"full",getTestMsg:function(){return n("Zm7L::采样率范围：{1}；比特率范围：{2}（不同比特率支持的采样率范围不同，小于32kbps时采样率需小于32000）",0,a,i)}};var o,s=function(t){var r=t.bitRate,o=t.sampleRate,s=o;if(-1==(" "+i+",").indexOf(" "+r+",")&&e.CLog(n("eGB9::{1}不在mp3支持的取值范围：{2}",0,"bitRate="+r,i),3),-1==(" "+a+",").indexOf(" "+o+",")){for(var l=a.split(", "),c=[],f=0;f<l.length;f++)c.push({v:+l[f],s:Math.abs(l[f]-o)});c.sort((function(e,t){return e.s-t.s})),s=c[0].v,t.sampleRate=s,e.CLog(n("zLTa::sampleRate已更新为{1}，因为{2}不在mp3支持的取值范围：{3}",0,s,o,a),3)}},l=function(){return n.G("NeedImport-2",["mp3.js","src/engine/mp3-engine.js"])},c=r&&"function"==typeof Worker;e.prototype.mp3=function(t,n,r){var a=this.set,i=t.length;if(e.lamejs){if(c){var o=this.mp3_start(a);if(o){if(o.isW)return this.mp3_encode(o,t),void this.mp3_complete(o,n,r,1);this.mp3_stop(o)}}s(a);var f=new e.lamejs.Mp3Encoder(1,a.sampleRate,a.bitRate),u=new Int8Array(5e5),d=0,h=0,v=0;!function e(){try{if(h<i)var o=f.encodeBuffer(t.subarray(h,h+57600));else v=1,o=f.flush()}catch(e){if(console.error(e),!v)try{f.flush()}catch(e){console.error(e)}return void r("MP3 Encoder: "+e.message)}var s=o.length;if(s>0){if(d+s>u.length){var l=new Int8Array(u.length+Math.max(5e5,s));l.set(u.subarray(0,d)),u=l}u.set(o,d),d+=s}if(h<i)h+=57600,setTimeout(e);else{var c=[u.buffer.slice(0,d)],m=p.fn(c,d,i,a.sampleRate);_(m,a),n(c[0]||new ArrayBuffer(0),"audio/mp3")}}()}else r(l())},e.BindDestroy("mp3Worker",(function(){o&&(e.CLog("mp3Worker Destroy"),o.terminate(),o=null)})),e.prototype.mp3_envCheck=function(t,r){var a="";return r.takeoffEncodeChunk&&(u()||(a=n("yhUs::当前浏览器版本太低，无法实时处理"))),a||e.lamejs||(a=l()),a},e.prototype.mp3_start=function(e){return u(e)};var f={id:0},u=function t(r,a){var i,l=function(e){var t=e.data,n=i.wkScope.wk_ctxs,r=i.wkScope.wk_lame,a=i.wkScope.wk_mp3TrimFix,o=n[t.id];if("init"==t.action)n[t.id]={sampleRate:t.sampleRate,bitRate:t.bitRate,takeoff:t.takeoff,pcmSize:0,memory:new Int8Array(5e5),mOffset:0,encObj:new r.Mp3Encoder(1,t.sampleRate,t.bitRate)};else if(!o)return;var s=function(e){var t=e.length;if(o.mOffset+t>o.memory.length){var n=new Int8Array(o.memory.length+Math.max(5e5,t));n.set(o.memory.subarray(0,o.mOffset)),o.memory=n}o.memory.set(e,o.mOffset),o.mOffset+=t};switch(t.action){case"stop":if(!o.isCp)try{o.encObj.flush()}catch(e){console.error(e)}o.encObj=null,delete n[t.id];break;case"encode":if(o.isCp)break;o.pcmSize+=t.pcm.length;try{var l=o.encObj.encodeBuffer(t.pcm)}catch(e){o.err=e,console.error(e)}l&&l.length>0&&(o.takeoff?d.onmessage({action:"takeoff",id:t.id,chunk:l}):s(l));break;case"complete":o.isCp=1;try{l=o.encObj.flush()}catch(e){o.err=e,console.error(e)}if(l&&l.length>0&&(o.takeoff?d.onmessage({action:"takeoff",id:t.id,chunk:l}):s(l)),o.err){d.onmessage({action:t.action,id:t.id,err:"MP3 Encoder: "+o.err.message});break}var c=[o.memory.buffer.slice(0,o.mOffset)],f=a.fn(c,o.mOffset,o.pcmSize,o.sampleRate);d.onmessage({action:t.action,id:t.id,blob:c[0]||new ArrayBuffer(0),meta:f})}},u=function(e){d.onmessage=function(t){var n=t;e&&(n=t.data);var r=f[n.id];r&&("takeoff"==n.action?r.set.takeoffEncodeChunk(new Uint8Array(n.chunk.buffer)):(r.call&&r.call(n),r.call=null))}},_=function(){var e={worker:d,set:r};return r?(e.id=++f.id,f[e.id]=e,s(r),d.postMessage({action:"init",id:e.id,sampleRate:r.sampleRate,bitRate:r.bitRate,takeoff:!!r.takeoffEncodeChunk,x:new Int16Array(5)})):d.postMessage({x:new Int16Array(5)}),e},d=o;if(a||!c)return e.CLog(n("k9PT::当前环境不支持Web Worker，mp3实时编码器运行在主线程中"),3),d={postMessage:function(e){l({data:e})}},i={wkScope:{wk_ctxs:{},wk_lame:e.lamejs,wk_mp3TrimFix:p}},u(),_();try{if(!d){var h=(l+"").replace(/[\w\$]+\.onmessage/g,"self.postMessage"),v=");wk_lame();self.onmessage="+(h=h.replace(/[\w\$]+\.wkScope/g,"wkScope"));v+=";var wkScope={ wk_ctxs:{},wk_lame:wk_lame",v+=",wk_mp3TrimFix:{rm:"+p.rm+",fn:"+p.fn+"} }";var m=e.lamejs.toString(),b=(window.URL||webkitURL).createObjectURL(new Blob(["var wk_lame=(",m,v],{type:"text/javascript"}));d=new Worker(b),setTimeout((function(){(window.URL||webkitURL).revokeObjectURL(b)}),1e4),u(1)}var g=_();return g.isW=1,o=d,g}catch(e){return d&&d.terminate(),console.error(e),t(r,1)}};e.prototype.mp3_stop=function(t){if(t&&t.worker){t.worker.postMessage({action:"stop",id:t.id}),t.worker=null,delete f[t.id];var r=-1;for(var a in f)r++;r&&e.CLog(n("fT6M::mp3 worker剩{1}个未stop",0,r),3)}},e.prototype.mp3_encode=function(e,t){e&&e.worker&&e.worker.postMessage({action:"encode",id:e.id,pcm:t})},e.prototype.mp3_complete=function(e,t,r,a){var i=this;e&&e.worker?(e.call=function(n){a&&i.mp3_stop(e),n.err?r(n.err):(_(n.meta,e.set),t(n.blob,"audio/mp3"))},e.worker.postMessage({action:"complete",id:e.id})):r(n("mPxH::mp3编码器未start"))},e.mp3ReadMeta=function(e,t){var n="undefined"!=typeof window&&window.parseInt||"undefined"!=typeof self&&self.parseInt||parseInt,r=new Uint8Array(e[0]||[]);if(r.length<4)return null;var a=function(e,t){return("0000000"+((t||r)[e]||0).toString(2)).substr(-8)},i=a(0)+a(1),o=a(2)+a(3);if(!/^1{11}/.test(i))return null;var s={"00":2.5,10:2,11:1}[i.substr(11,2)],l={"01":3}[i.substr(13,2)],c={1:[44100,48e3,32e3],2:[22050,24e3,16e3],2.5:[11025,12e3,8e3]}[s];c&&(c=c[n(o.substr(4,2),2)]);var f=[[0,8,16,24,32,40,48,56,64,80,96,112,128,144,160],[0,32,40,48,56,64,80,96,112,128,160,192,224,256,320]][1==s?1:0][n(o.substr(0,4),2)];if(!(s&&l&&f&&c))return null;for(var u=Math.round(8*t/f),p=1==l?384:2==l||1==s?1152:576,_=p/c*1e3,d=Math.floor(p*f/8/c*1e3),h=0,v=0,m=0;m<e.length;m++){var b=e[m];if((v+=b.byteLength)>=d+3){var g=new Uint8Array(b);h="1"==a(b.byteLength-(v-(d+3)+1),g).charAt(6);break}}return h&&d++,{version:s,layer:l,sampleRate:c,bitRate:f,duration:u,size:t,hasPadding:h,frameSize:d,frameDurationFloat:_}};var p={rm:e.mp3ReadMeta,fn:function(e,t,n,r){var a=this.rm(e,t);if(!a)return{size:t,err:"mp3 unknown format"};var i=Math.round(n/r*1e3),o=Math.floor((a.duration-i)/a.frameDurationFloat);if(o>0){var s=o*a.frameSize-(a.hasPadding?1:0);t-=s;for(var l=0,c=[],f=0;f<e.length;f++){var u=e[f];if(s<=0)break;s>=u.byteLength?(s-=u.byteLength,c.push(u),e.splice(f,1),f--):(e[f]=u.slice(s),l=u,s=0)}if(!this.rm(e,t)){for(l&&(e[0]=l),f=0;f<c.length;f++)e.splice(f,0,c[f]);a.err="mp3 fix error: 已还原，错误原因不明"}var p=a.trimFix={};p.remove=o,p.removeDuration=Math.round(o*a.frameDurationFloat),p.duration=Math.round(8*t/a.bitRate)}return a}},_=function(t,r){var a="MP3 Info: ";(t.sampleRate&&t.sampleRate!=r.sampleRate||t.bitRate&&t.bitRate!=r.bitRate)&&(e.CLog(a+n("uY9i::和设置的不匹配{1}，已更新成{2}",0,"set:"+r.bitRate+"kbps "+r.sampleRate+"hz","set:"+t.bitRate+"kbps "+t.sampleRate+"hz"),3,r),r.sampleRate=t.sampleRate,r.bitRate=t.bitRate);var i=t.trimFix;i?(a+=n("iMSm::Fix移除{1}帧",0,i.remove)+" "+i.removeDuration+"ms -> "+i.duration+"ms",i.remove>2&&(t.err=(t.err?t.err+", ":"")+n("b9zm::移除帧数过多"))):a+=(t.duration||"-")+"ms",t.err?e.CLog(a,t.size?1:0,t.err,t):e.CLog(a,t)}}(a,0,i.$T,r)},function(e,t,n){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};!function(e){function t(){var e=function(e){return Math.log(e)/Math.log(10)},n=function(e){throw new Error("abort("+e+")")};function r(e){return new Int8Array(e)}function a(e){return new Int16Array(e)}function i(e){return new Int32Array(e)}function o(e){return new Float32Array(e)}function s(e){return new Float64Array(e)}function l(e){if(1==e.length)return o(e[0]);var t=e[0];e=e.slice(1);for(var n=[],r=0;r<t;r++)n.push(l(e));return n}function c(e){if(1==e.length)return i(e[0]);var t=e[0];e=e.slice(1);for(var n=[],r=0;r<t;r++)n.push(c(e));return n}function f(e){if(1==e.length)return a(e[0]);var t=e[0];e=e.slice(1);for(var n=[],r=0;r<t;r++)n.push(f(e));return n}function u(e){if(1==e.length)return new Array(e[0]);var t=e[0];e=e.slice(1);for(var n=[],r=0;r<t;r++)n.push(u(e));return n}var p={fill:function(e,t,n,r){if(2==arguments.length)for(var a=0;a<e.length;a++)e[a]=arguments[1];else for(a=t;a<n;a++)e[a]=r}},_={arraycopy:function(e,t,n,r,a){for(var i=t+a;t<i;)n[r++]=e[t++]}},d={};function h(e){this.ordinal=e}d.SQRT2=1.4142135623730951,d.FAST_LOG10=function(t){return e(t)},d.FAST_LOG10_X=function(t,n){return e(t)*n},h.short_block_allowed=new h(0),h.short_block_coupled=new h(1),h.short_block_dispensed=new h(2),h.short_block_forced=new h(3);var v={};function m(e){this.ordinal=e}function b(e){var t=e;this.ordinal=function(){return t}}function g(){this.getLameShortVersion=function(){return"3.98.4"}}function y(){var e=null;function t(e){this.bits=0|e}this.qupvt=null,this.setModules=function(t){this.qupvt=t,e=t};var r=[[0,0],[0,0],[0,0],[0,0],[0,0],[0,1],[1,1],[1,1],[1,2],[2,2],[2,3],[2,3],[3,4],[3,4],[3,4],[4,5],[4,5],[4,6],[5,6],[5,6],[5,7],[6,7],[6,7]];function a(e,t,n,r,a,i){var o=.5946/t;for(e>>=1;0!=e--;)a[i++]=o>n[r++]?0:1,a[i++]=o>n[r++]?0:1}function o(t,n,r,a,i,o){var s=(t>>=1)%2;for(t>>=1;0!=t--;){var l,c,f,u,p,_,d,h;l=r[a++]*n,c=r[a++]*n,p=0|l,f=r[a++]*n,_=0|c,u=r[a++]*n,d=0|f,l+=e.adj43[p],h=0|u,c+=e.adj43[_],i[o++]=0|l,f+=e.adj43[d],i[o++]=0|c,u+=e.adj43[h],i[o++]=0|f,i[o++]=0|u}0!=s&&(p=0|(l=r[a++]*n),_=0|(c=r[a++]*n),l+=e.adj43[p],c+=e.adj43[_],i[o++]=0|l,i[o++]=0|c)}var s=[1,2,5,7,7,10,10,13,13,13,13,13,13,13,13];function l(e,t,r,a){var i=function(e,t,n){var r=0,a=0;do{var i=e[t++],o=e[t++];r<i&&(r=i),a<o&&(a=o)}while(t<n);return r<a&&(r=a),r}(e,t,r);switch(i){case 0:return i;case 1:return function(e,t,n,r){var a=0,i=T.ht[1].hlen;do{var o=2*e[t+0]+e[t+1];t+=2,a+=i[o]}while(t<n);return r.bits+=a,1}(e,t,r,a);case 2:case 3:return function(e,t,n,r,a){var i,o,s=0,l=T.ht[r].xlen;o=2==r?T.table23:T.table56;do{var c=e[t+0]*l+e[t+1];t+=2,s+=o[c]}while(t<n);return i=65535&s,(s>>=16)>i&&(s=i,r++),a.bits+=s,r}(e,t,r,s[i-1],a);case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 12:case 13:case 14:case 15:return function(e,t,n,r,a){var i=0,o=0,s=0,l=T.ht[r].xlen,c=T.ht[r].hlen,f=T.ht[r+1].hlen,u=T.ht[r+2].hlen;do{var p=e[t+0]*l+e[t+1];t+=2,i+=c[p],o+=f[p],s+=u[p]}while(t<n);var _=r;return i>o&&(i=o,_++),i>s&&(i=s,_=r+2),a.bits+=i,_}(e,t,r,s[i-1],a);default:var o,l;for(i>$.IXMAX_VAL&&n(),i-=15,o=24;o<32&&!(T.ht[o].linmax>=i);o++);for(l=o-8;l<24&&!(T.ht[l].linmax>=i);l++);return function(e,t,n,r,a,i){var o,s=65536*T.ht[r].xlen+T.ht[a].xlen,l=0;do{var c=e[t++],f=e[t++];0!=c&&(c>14&&(c=15,l+=s),c*=16),0!=f&&(f>14&&(f=15,l+=s),c+=f),l+=T.largetbl[c]}while(t<n);return o=65535&l,(l>>=16)>o&&(l=o,r=a),i.bits+=l,r}(e,t,r,l,o,a)}}function c(e,n,r,a,i,o,s,c){for(var f=n.big_values,u=2;u<z.SBMAX_l+1;u++){var p=e.scalefac_band.l[u];if(p>=f)break;var _=i[u-2]+n.count1bits;if(r.part2_3_length<=_)break;var d=new t(_),h=l(a,p,f,d);_=d.bits,r.part2_3_length<=_||(r.assign(n),r.part2_3_length=_,r.region0_count=o[u-2],r.region1_count=u-2-o[u-2],r.table_select[0]=s[u-2],r.table_select[1]=c[u-2],r.table_select[2]=h)}}this.noquant_count_bits=function(e,r,a){var i=r.l3_enc,o=Math.min(576,r.max_nonzero_coeff+2>>1<<1);for(null!=a&&(a.sfb_count1=0);o>1&&0==(i[o-1]|i[o-2]);o-=2);r.count1=o;for(var s=0,c=0;o>3;o-=4){var f;if((2147483647&(i[o-1]|i[o-2]|i[o-3]|i[o-4]))>1)break;f=2*(2*(2*i[o-4]+i[o-3])+i[o-2])+i[o-1],s+=T.t32l[f],c+=T.t33l[f]}var u=s;if(r.count1table_select=0,s>c&&(u=c,r.count1table_select=1),r.count1bits=u,r.big_values=o,0==o)return u;if(r.block_type==z.SHORT_TYPE)(s=3*e.scalefac_band.s[3])>r.big_values&&(s=r.big_values),c=r.big_values;else if(r.block_type==z.NORM_TYPE){if(s=r.region0_count=e.bv_scf[o-2],c=r.region1_count=e.bv_scf[o-1],c=e.scalefac_band.l[s+c+2],s=e.scalefac_band.l[s+1],c<o){var p=new t(u);r.table_select[2]=l(i,c,o,p),u=p.bits}}else r.region0_count=7,r.region1_count=z.SBMAX_l-1-7-1,(s=e.scalefac_band.l[8])>(c=o)&&(s=c);if(s=Math.min(s,o),c=Math.min(c,o),0<s&&(p=new t(u),r.table_select[0]=l(i,0,s,p),u=p.bits),s<c&&(p=new t(u),r.table_select[1]=l(i,s,c,p),u=p.bits),2==e.use_best_huffman&&n(),null!=a&&r.block_type==z.NORM_TYPE){for(var _=0;e.scalefac_band.l[_]<r.big_values;)_++;a.sfb_count1=_}return u},this.count_bits=function(t,r,i,s){var l=i.l3_enc,c=$.IXMAX_VAL/e.IPOW20(i.global_gain);return i.xrpow_max>c?$.LARGE_BITS:(function(t,r,i,s,l){var c,f,u,_=0,d=0,h=0,v=0,m=r,b=0,g=m,y=0,w=t,S=0;for(u=null!=l&&s.global_gain==l.global_gain,f=s.block_type==z.SHORT_TYPE?38:21,c=0;c<=f;c++){var x=-1;if((u||s.block_type==z.NORM_TYPE)&&(x=s.global_gain-(s.scalefac[c]+(0!=s.preflag?e.pretab[c]:0)<<s.scalefac_scale+1)-8*s.subblock_gain[s.window[c]]),u&&l.step[c]==x)0!=d&&(o(d,i,w,S,g,y),d=0),0!=h&&n();else{var k,A=s.width[c];if(_+s.width[c]>s.max_nonzero_coeff&&(k=s.max_nonzero_coeff-_+1,p.fill(r,s.max_nonzero_coeff,576,0),(A=k)<0&&(A=0),c=f+1),0==d&&0==h&&(g=m,y=b,w=t,S=v),null!=l&&l.sfb_count1>0&&c>=l.sfb_count1&&l.step[c]>0&&x>=l.step[c]?(0!=d&&(o(d,i,w,S,g,y),d=0,g=m,y=b,w=t,S=v),h+=A):(0!=h&&(a(h,i,w,S,g,y),h=0,g=m,y=b,w=t,S=v),d+=A),A<=0){0!=h&&n(),0!=d&&n();break}}c<=f&&(b+=s.width[c],v+=s.width[c],_+=s.width[c])}0!=d&&(o(d,i,w,S,g,y),d=0),0!=h&&n()}(r,l,e.IPOW20(i.global_gain),i,s),0!=(2&t.substep_shaping)&&n(),this.noquant_count_bits(t,i,s))},this.best_huffman_divide=function(e,n){var r=new D,a=n.l3_enc,o=i(23),s=i(23),f=i(23),u=i(23);if(n.block_type!=z.SHORT_TYPE||1!=e.mode_gr){r.assign(n),n.block_type==z.NORM_TYPE&&(function(e,n,r,a,i,o,s){for(var c=n.big_values,f=0;f<=22;f++)a[f]=$.LARGE_BITS;for(f=0;f<16;f++){var u=e.scalefac_band.l[f+1];if(u>=c)break;var p=0,_=new t(p),d=l(r,0,u,_);p=_.bits;for(var h=0;h<8;h++){var v=e.scalefac_band.l[f+h+2];if(v>=c)break;var m=p,b=l(r,u,v,_=new t(m));m=_.bits,a[f+h]>m&&(a[f+h]=m,i[f+h]=f,o[f+h]=d,s[f+h]=b)}}}(e,n,a,o,s,f,u),c(e,r,n,a,o,s,f,u));var p=r.big_values;if(!(0==p||(a[p-2]|a[p-1])>1||(p=n.count1+2)>576)){r.assign(n),r.count1=p;for(var _=0,d=0;p>r.big_values;p-=4){var h=2*(2*(2*a[p-4]+a[p-3])+a[p-2])+a[p-1];_+=T.t32l[h],d+=T.t33l[h]}if(r.big_values=p,r.count1table_select=0,_>d&&(_=d,r.count1table_select=1),r.count1bits=_,r.block_type==z.NORM_TYPE)c(e,r,n,a,o,s,f,u);else{if(r.part2_3_length=_,(_=e.scalefac_band.l[8])>p&&(_=p),_>0){var v=new t(r.part2_3_length);r.table_select[0]=l(a,0,_,v),r.part2_3_length=v.bits}p>_&&(v=new t(r.part2_3_length),r.table_select[1]=l(a,_,p,v),r.part2_3_length=v.bits),n.part2_3_length>r.part2_3_length&&n.assign(r)}}}};var f=[1,1,1,1,8,2,2,2,4,4,4,8,8,8,16,16],u=[1,2,4,8,1,2,4,8,2,4,8,2,4,8,4,8],_=[0,0,0,0,3,1,1,1,2,2,2,3,3,3,4,4],d=[0,1,2,3,0,1,2,3,1,2,3,1,2,3,2,3];y.slen1_tab=_,y.slen2_tab=d,this.best_scalefac_store=function(t,n,r,a){var i,o,s,l,c=a.tt[n][r],p=0;for(s=0,i=0;i<c.sfbmax;i++){var h=c.width[i];for(s+=h,l=-h;l<0&&0==c.l3_enc[l+s];l++);0==l&&(c.scalefac[i]=p=-2)}if(0==c.scalefac_scale&&0==c.preflag){var v=0;for(i=0;i<c.sfbmax;i++)c.scalefac[i]>0&&(v|=c.scalefac[i]);if(0==(1&v)&&0!=v){for(i=0;i<c.sfbmax;i++)c.scalefac[i]>0&&(c.scalefac[i]>>=1);c.scalefac_scale=p=1}}if(0==c.preflag&&c.block_type!=z.SHORT_TYPE&&2==t.mode_gr){for(i=11;i<z.SBPSY_l&&!(c.scalefac[i]<e.pretab[i]&&-2!=c.scalefac[i]);i++);if(i==z.SBPSY_l){for(i=11;i<z.SBPSY_l;i++)c.scalefac[i]>0&&(c.scalefac[i]-=e.pretab[i]);c.preflag=p=1}}for(o=0;o<4;o++)a.scfsi[r][o]=0;for(2==t.mode_gr&&1==n&&a.tt[0][r].block_type!=z.SHORT_TYPE&&a.tt[1][r].block_type!=z.SHORT_TYPE&&(function(e,t){for(var n,r=t.tt[1][e],a=t.tt[0][e],i=0;i<T.scfsi_band.length-1;i++){for(n=T.scfsi_band[i];n<T.scfsi_band[i+1]&&!(a.scalefac[n]!=r.scalefac[n]&&r.scalefac[n]>=0);n++);if(n==T.scfsi_band[i+1]){for(n=T.scfsi_band[i];n<T.scfsi_band[i+1];n++)r.scalefac[n]=-1;t.scfsi[e][i]=1}}var o=0,s=0;for(n=0;n<11;n++)-1!=r.scalefac[n]&&(s++,o<r.scalefac[n]&&(o=r.scalefac[n]));for(var l=0,c=0;n<z.SBPSY_l;n++)-1!=r.scalefac[n]&&(c++,l<r.scalefac[n]&&(l=r.scalefac[n]));for(i=0;i<16;i++)if(o<f[i]&&l<u[i]){var p=_[i]*s+d[i]*c;r.part2_length>p&&(r.part2_length=p,r.scalefac_compress=i)}}(r,a),p=0),i=0;i<c.sfbmax;i++)-2==c.scalefac[i]&&(c.scalefac[i]=0);0!=p&&(2==t.mode_gr?this.scale_bitcount(c):this.scale_bitcount_lsf(t,c))};var h=[0,18,36,54,54,36,54,72,54,72,90,72,90,108,108,126],v=[0,18,36,54,51,35,53,71,52,70,88,69,87,105,104,122],m=[0,10,20,30,33,21,31,41,32,42,52,43,53,63,64,74];this.scale_bitcount=function(t){var n,r,a,i=0,o=0,s=t.scalefac;if(t.block_type==z.SHORT_TYPE)a=h,0!=t.mixed_block_flag&&(a=v);else if(a=m,0==t.preflag){for(r=11;r<z.SBPSY_l&&!(s[r]<e.pretab[r]);r++);if(r==z.SBPSY_l)for(t.preflag=1,r=11;r<z.SBPSY_l;r++)s[r]-=e.pretab[r]}for(r=0;r<t.sfbdivide;r++)i<s[r]&&(i=s[r]);for(;r<t.sfbmax;r++)o<s[r]&&(o=s[r]);for(t.part2_length=$.LARGE_BITS,n=0;n<16;n++)i<f[n]&&o<u[n]&&t.part2_length>a[n]&&(t.part2_length=a[n],t.scalefac_compress=n);return t.part2_length==$.LARGE_BITS};var b=[[15,15,7,7],[15,15,7,0],[7,3,0,0],[15,31,31,0],[7,7,7,0],[3,3,0,0]];this.scale_bitcount_lsf=function(t,n){var r,a,o,s,l,c,f,u,p=i(4),_=n.scalefac;for(r=0!=n.preflag?2:0,f=0;f<4;f++)p[f]=0;if(n.block_type==z.SHORT_TYPE){a=1;var d=e.nr_of_sfb_block[r][a];for(u=0,o=0;o<4;o++)for(s=d[o]/3,f=0;f<s;f++,u++)for(l=0;l<3;l++)_[3*u+l]>p[o]&&(p[o]=_[3*u+l])}else for(a=0,d=e.nr_of_sfb_block[r][a],u=0,o=0;o<4;o++)for(s=d[o],f=0;f<s;f++,u++)_[u]>p[o]&&(p[o]=_[u]);for(c=!1,o=0;o<4;o++)p[o]>b[r][o]&&(c=!0);if(!c){var h,v,m,y;for(n.sfb_partition_table=e.nr_of_sfb_block[r][a],o=0;o<4;o++)n.slen[o]=g[p[o]];switch(h=n.slen[0],v=n.slen[1],m=n.slen[2],y=n.slen[3],r){case 0:n.scalefac_compress=(5*h+v<<4)+(m<<2)+y;break;case 1:n.scalefac_compress=400+(5*h+v<<2)+m;break;case 2:n.scalefac_compress=500+3*h+v}}if(!c)for(n.part2_length=0,o=0;o<4;o++)n.part2_length+=n.slen[o]*n.sfb_partition_table[o];return c};var g=[0,1,2,2,3,3,3,3,4,4,4,4,4,4,4,4];this.huffman_init=function(e){for(var t=2;t<=576;t+=2){for(var n,a=0;e.scalefac_band.l[++a]<t;);for(n=r[a][0];e.scalefac_band.l[n+1]>t;)n--;for(n<0&&(n=r[a][0]),e.bv_scf[t-2]=n,n=r[a][1];e.scalefac_band.l[n+e.bv_scf[t-2]+2]>t;)n--;n<0&&(n=r[a][1]),e.bv_scf[t-1]=n}}}function w(){}function S(){function e(e,t,n,r,a,i,o,s,l,c,f,u,p,_){this.quant_comp=t,this.quant_comp_s=n,this.safejoint=r,this.nsmsfix=a,this.st_lrm=i,this.st_s=o,this.nsbass=s,this.scale=l,this.masking_adj=c,this.ath_lower=f,this.ath_curve=u,this.interch=p,this.sfscale=_}var t;function r(e,t,r){n()}this.setModules=function(e){t=e};var a=[new e(8,9,9,0,0,6.6,145,0,.95,0,-30,11,.0012,1),new e(16,9,9,0,0,6.6,145,0,.95,0,-25,11,.001,1),new e(24,9,9,0,0,6.6,145,0,.95,0,-20,11,.001,1),new e(32,9,9,0,0,6.6,145,0,.95,0,-15,11,.001,1),new e(40,9,9,0,0,6.6,145,0,.95,0,-10,11,9e-4,1),new e(48,9,9,0,0,6.6,145,0,.95,0,-10,11,9e-4,1),new e(56,9,9,0,0,6.6,145,0,.95,0,-6,11,8e-4,1),new e(64,9,9,0,0,6.6,145,0,.95,0,-2,11,8e-4,1),new e(80,9,9,0,0,6.6,145,0,.95,0,0,8,7e-4,1),new e(96,9,9,0,2.5,6.6,145,0,.95,0,1,5.5,6e-4,1),new e(112,9,9,0,2.25,6.6,145,0,.95,0,2,4.5,5e-4,1),new e(128,9,9,0,1.95,6.4,140,0,.95,0,3,4,2e-4,1),new e(160,9,9,1,1.79,6,135,0,.95,-2,5,3.5,0,1),new e(192,9,9,1,1.49,5.6,125,0,.97,-4,7,3,0,0),new e(224,9,9,1,1.25,5.2,125,0,.98,-6,9,2,0,0),new e(256,9,9,1,.97,5.2,125,0,1,-8,10,1,0,0),new e(320,9,9,1,.9,5.2,125,0,1,-10,12,0,0,0)];function i(e,n,r){var i=n,o=t.nearestBitrateFullIndex(n);if(e.VBR=m.vbr_abr,e.VBR_mean_bitrate_kbps=i,e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,320),e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,8),e.brate=e.VBR_mean_bitrate_kbps,e.VBR_mean_bitrate_kbps>320&&(e.disable_reservoir=!0),a[o].safejoint>0&&(e.exp_nspsytune=2|e.exp_nspsytune),a[o].sfscale>0&&(e.internal_flags.noise_shaping=2),Math.abs(a[o].nsbass)>0){var s=int(4*a[o].nsbass);s<0&&(s+=64),e.exp_nspsytune=e.exp_nspsytune|s<<2}return 0!=r?e.quant_comp=a[o].quant_comp:Math.abs(e.quant_comp- -1)>0||(e.quant_comp=a[o].quant_comp),0!=r?e.quant_comp_short=a[o].quant_comp_s:Math.abs(e.quant_comp_short- -1)>0||(e.quant_comp_short=a[o].quant_comp_s),0!=r?e.msfix=a[o].nsmsfix:Math.abs(e.msfix- -1)>0||(e.msfix=a[o].nsmsfix),0!=r?e.internal_flags.nsPsy.attackthre=a[o].st_lrm:Math.abs(e.internal_flags.nsPsy.attackthre- -1)>0||(e.internal_flags.nsPsy.attackthre=a[o].st_lrm),0!=r?e.internal_flags.nsPsy.attackthre_s=a[o].st_s:Math.abs(e.internal_flags.nsPsy.attackthre_s- -1)>0||(e.internal_flags.nsPsy.attackthre_s=a[o].st_s),0!=r?e.scale=a[o].scale:Math.abs(e.scale- -1)>0||(e.scale=a[o].scale),0!=r?e.maskingadjust=a[o].masking_adj:Math.abs(e.maskingadjust-0)>0||(e.maskingadjust=a[o].masking_adj),a[o].masking_adj>0?0!=r?e.maskingadjust_short=.9*a[o].masking_adj:Math.abs(e.maskingadjust_short-0)>0||(e.maskingadjust_short=.9*a[o].masking_adj):0!=r?e.maskingadjust_short=1.1*a[o].masking_adj:Math.abs(e.maskingadjust_short-0)>0||(e.maskingadjust_short=1.1*a[o].masking_adj),0!=r?e.ATHlower=-a[o].ath_lower/10:Math.abs(10*-e.ATHlower-0)>0||(e.ATHlower=-a[o].ath_lower/10),0!=r?e.ATHcurve=a[o].ath_curve:Math.abs(e.ATHcurve- -1)>0||(e.ATHcurve=a[o].ath_curve),0!=r?e.interChRatio=a[o].interch:Math.abs(e.interChRatio- -1)>0||(e.interChRatio=a[o].interch),n}this.apply_preset=function(e,t,n){switch(t){case Z.R3MIX:t=Z.V3,e.VBR=m.vbr_mtrh;break;case Z.MEDIUM:t=Z.V4,e.VBR=m.vbr_rh;break;case Z.MEDIUM_FAST:t=Z.V4,e.VBR=m.vbr_mtrh;break;case Z.STANDARD:t=Z.V2,e.VBR=m.vbr_rh;break;case Z.STANDARD_FAST:t=Z.V2,e.VBR=m.vbr_mtrh;break;case Z.EXTREME:t=Z.V0,e.VBR=m.vbr_rh;break;case Z.EXTREME_FAST:t=Z.V0,e.VBR=m.vbr_mtrh;break;case Z.INSANE:return t=320,e.preset=t,i(e,t,n),e.VBR=m.vbr_off,t}switch(e.preset=t,t){case Z.V9:case Z.V8:case Z.V7:case Z.V6:case Z.V5:case Z.V4:case Z.V3:case Z.V2:case Z.V1:case Z.V0:return r(),t}return 8<=t&&t<=320?i(e,t,n):(e.preset=0,t)}}function x(){var e;this.setModules=function(t){e=t},this.ResvFrameBegin=function(t,r){var a,i=t.internal_flags,o=i.l3_side,s=e.getframebits(t);r.bits=(s-8*i.sideinfo_len)/i.mode_gr;var l=2048*i.mode_gr-8;t.brate>320?n():(a=11520,t.strict_ISO&&n()),i.ResvMax=a-s,i.ResvMax>l&&(i.ResvMax=l),(i.ResvMax<0||t.disable_reservoir)&&(i.ResvMax=0);var c=r.bits*i.mode_gr+Math.min(i.ResvSize,i.ResvMax);return c>a&&(c=a),o.resvDrain_pre=0,null!=i.pinfo&&n(),c},this.ResvMaxBits=function(e,t,n,r){var a,i=e.internal_flags,o=i.ResvSize,s=i.ResvMax;0!=r&&(o+=t),0!=(1&i.substep_shaping)&&(s*=.9),n.bits=t,10*o>9*s?(a=o-9*s/10,n.bits+=a,i.substep_shaping|=128):(a=0,i.substep_shaping&=127,e.disable_reservoir||0!=(1&i.substep_shaping)||(n.bits-=.1*t));var l=o<6*i.ResvMax/10?o:6*i.ResvMax/10;return(l-=a)<0&&(l=0),l},this.ResvAdjust=function(e,t){e.ResvSize-=t.part2_3_length+t.part2_length},this.ResvFrameEnd=function(e,t){var n,r=e.l3_side;e.ResvSize+=t*e.mode_gr;var a=0;r.resvDrain_post=0,r.resvDrain_pre=0,0!=(n=e.ResvSize%8)&&(a+=n),(n=e.ResvSize-a-e.ResvMax)>0&&(a+=n);var i=Math.min(8*r.main_data_begin,a)/8;r.resvDrain_pre+=8*i,a-=8*i,e.ResvSize-=8*i,r.main_data_begin-=i,r.resvDrain_post+=a,e.ResvSize-=a}}function k(){this.setModules=function(e,t,n){};var e=[0,49345,49537,320,49921,960,640,49729,50689,1728,1920,51009,1280,50625,50305,1088,52225,3264,3456,52545,3840,53185,52865,3648,2560,51905,52097,2880,51457,2496,2176,51265,55297,6336,6528,55617,6912,56257,55937,6720,7680,57025,57217,8e3,56577,7616,7296,56385,5120,54465,54657,5440,55041,6080,5760,54849,53761,4800,4992,54081,4352,53697,53377,4160,61441,12480,12672,61761,13056,62401,62081,12864,13824,63169,63361,14144,62721,13760,13440,62529,15360,64705,64897,15680,65281,16320,16e3,65089,64001,15040,15232,64321,14592,63937,63617,14400,10240,59585,59777,10560,60161,11200,10880,59969,60929,11968,12160,61249,11520,60865,60545,11328,58369,9408,9600,58689,9984,59329,59009,9792,8704,58049,58241,9024,57601,8640,8320,57409,40961,24768,24960,41281,25344,41921,41601,25152,26112,42689,42881,26432,42241,26048,25728,42049,27648,44225,44417,27968,44801,28608,28288,44609,43521,27328,27520,43841,26880,43457,43137,26688,30720,47297,47489,31040,47873,31680,31360,47681,48641,32448,32640,48961,32e3,48577,48257,31808,46081,29888,30080,46401,30464,47041,46721,30272,29184,45761,45953,29504,45313,29120,28800,45121,20480,37057,37249,20800,37633,21440,21120,37441,38401,22208,22400,38721,21760,38337,38017,21568,39937,23744,23936,40257,24320,40897,40577,24128,23040,39617,39809,23360,39169,22976,22656,38977,34817,18624,18816,35137,19200,35777,35457,19008,19968,36545,36737,20288,36097,19904,19584,35905,17408,33985,34177,17728,34561,18368,18048,34369,33281,17088,17280,33601,16640,33217,32897,16448];function t(t,n){return n=n>>8^e[255&(n^t)]}this.updateMusicCRC=function(e,n,r,a){for(var i=0;i<a;++i)e[0]=t(n[r+i],e[0])}}function A(){var e=this,t=null,a=null;this.setModules=function(e,n,r,i){t=r,a=i};var o=null,s=0,l=0,c=0;function f(e){_.arraycopy(e.header[e.w_ptr].buf,0,o,l,e.sideinfo_len),l+=e.sideinfo_len,s+=8*e.sideinfo_len,e.w_ptr=e.w_ptr+1&W.MAX_HEADER_BUF-1}function u(e,t,n){for(;n>0;){var r;0==c&&(c=8,l++,e.header[e.w_ptr].write_timing==s&&f(e),o[l]=0),n-=r=Math.min(n,c),c-=r,o[l]|=t>>n<<c,s+=r}}function d(e,n){var r,a=e.internal_flags;if(n>=8&&(u(a,76,8),n-=8),n>=8&&(u(a,65,8),n-=8),n>=8&&(u(a,77,8),n-=8),n>=8&&(u(a,69,8),n-=8),n>=32){var i=t.getLameShortVersion();if(n>=32)for(r=0;r<i.length&&n>=8;++r)n-=8,u(a,i.charCodeAt(r),8)}for(;n>=1;n-=1)u(a,a.ancillary_flag,1),a.ancillary_flag^=e.disable_reservoir?0:1}function h(e,t,n){for(var r=e.header[e.h_ptr].ptr;n>0;){var a=Math.min(n,8-(7&r));n-=a,e.header[e.h_ptr].buf[r>>3]|=t>>n<<8-(7&r)-a,r+=a}e.header[e.h_ptr].ptr=r}function v(e,t){var n,r=T.ht[t.count1table_select+32],a=0,i=t.big_values,o=t.big_values;for(n=(t.count1-t.big_values)/4;n>0;--n){var s=0,l=0;0!=t.l3_enc[i+0]&&(l+=8,t.xr[o+0]<0&&s++),0!=t.l3_enc[i+1]&&(l+=4,s*=2,t.xr[o+1]<0&&s++),0!=t.l3_enc[i+2]&&(l+=2,s*=2,t.xr[o+2]<0&&s++),0!=t.l3_enc[i+3]&&(l++,s*=2,t.xr[o+3]<0&&s++),i+=4,o+=4,u(e,s+r.table[l],r.hlen[l]),a+=r.hlen[l]}return a}function m(e,t,n,r,a){var i=T.ht[t],o=0;if(0==t)return o;for(var s=n;s<r;s+=2){var l=0,c=0,f=i.xlen,p=i.xlen,_=0,d=a.l3_enc[s],h=a.l3_enc[s+1];0!=d&&(a.xr[s]<0&&_++,l--),t>15&&(d>14&&(_|=d-15<<1,c=f,d=15),h>14&&(_<<=f,_|=h-15,c+=f,h=15),p=16),0!=h&&(_<<=1,a.xr[s+1]<0&&_++,l--),d=d*p+h,c-=l,l+=i.hlen[d],u(e,i.table[d],l),u(e,_,c),o+=l+c}return o}function b(e,t){var n=3*e.scalefac_band.s[3];n>t.big_values&&(n=t.big_values);var r=m(e,t.table_select[0],0,n,t);return r+=m(e,t.table_select[1],n,t.big_values,t)}function g(e,t){var n,r,a,i;n=t.big_values;var o=t.region0_count+1;return a=e.scalefac_band.l[o],o+=t.region1_count+1,a>n&&(a=n),(i=e.scalefac_band.l[o])>n&&(i=n),r=m(e,t.table_select[0],0,a,t),r+=m(e,t.table_select[1],a,i,t),r+=m(e,t.table_select[2],i,n,t)}function w(){this.total=0}function S(t,r){var a,i,o,c=t.internal_flags;return c.w_ptr,-1==(o=c.h_ptr-1)&&(o=W.MAX_HEADER_BUF-1),a=c.header[o].write_timing-s,r.total=a,a>=0&&n(),a+=i=e.getframebits(t),r.total+=i,r.total%8!=0?r.total=1+r.total/8:r.total=r.total/8,r.total+=l+1,a}this.getframebits=function(e){var t,n=e.internal_flags;return t=0!=n.bitrate_index?T.bitrate_table[e.version][n.bitrate_index]:e.brate,8*(0|72e3*(e.version+1)*t/e.out_samplerate+n.padding)},this.flush_bitstream=function(e){var t,r,a=e.internal_flags,i=a.h_ptr-1;-1==i&&(i=W.MAX_HEADER_BUF-1),t=a.l3_side,(r=S(e,new w))<0||(d(e,r),a.ResvSize=0,t.main_data_begin=0,a.findReplayGain&&n(),a.findPeakSample&&n())},this.format_bitstream=function(e){var t,r=e.internal_flags;t=r.l3_side;var a=this.getframebits(e);d(e,t.resvDrain_pre),function(e,t){var r,a,i,o=e.internal_flags;if(r=o.l3_side,o.header[o.h_ptr].ptr=0,p.fill(o.header[o.h_ptr].buf,0,o.sideinfo_len,0),e.out_samplerate<16e3?h(o,4094,12):h(o,4095,12),h(o,e.version,1),h(o,1,2),h(o,e.error_protection?0:1,1),h(o,o.bitrate_index,4),h(o,o.samplerate_index,2),h(o,o.padding,1),h(o,e.extension,1),h(o,e.mode.ordinal(),2),h(o,o.mode_ext,2),h(o,e.copyright,1),h(o,e.original,1),h(o,e.emphasis,2),e.error_protection&&h(o,0,16),1==e.version){for(h(o,r.main_data_begin,9),2==o.channels_out?h(o,r.private_bits,3):h(o,r.private_bits,5),i=0;i<o.channels_out;i++){var s;for(s=0;s<4;s++)h(o,r.scfsi[i][s],1)}for(a=0;a<2;a++)for(i=0;i<o.channels_out;i++)h(o,(l=r.tt[a][i]).part2_3_length+l.part2_length,12),h(o,l.big_values/2,9),h(o,l.global_gain,8),h(o,l.scalefac_compress,4),l.block_type!=z.NORM_TYPE?(h(o,1,1),h(o,l.block_type,2),h(o,l.mixed_block_flag,1),14==l.table_select[0]&&(l.table_select[0]=16),h(o,l.table_select[0],5),14==l.table_select[1]&&(l.table_select[1]=16),h(o,l.table_select[1],5),h(o,l.subblock_gain[0],3),h(o,l.subblock_gain[1],3),h(o,l.subblock_gain[2],3)):(h(o,0,1),14==l.table_select[0]&&(l.table_select[0]=16),h(o,l.table_select[0],5),14==l.table_select[1]&&(l.table_select[1]=16),h(o,l.table_select[1],5),14==l.table_select[2]&&(l.table_select[2]=16),h(o,l.table_select[2],5),h(o,l.region0_count,4),h(o,l.region1_count,3)),h(o,l.preflag,1),h(o,l.scalefac_scale,1),h(o,l.count1table_select,1)}else for(h(o,r.main_data_begin,8),h(o,r.private_bits,o.channels_out),a=0,i=0;i<o.channels_out;i++){var l;h(o,(l=r.tt[a][i]).part2_3_length+l.part2_length,12),h(o,l.big_values/2,9),h(o,l.global_gain,8),h(o,l.scalefac_compress,9),l.block_type!=z.NORM_TYPE?(h(o,1,1),h(o,l.block_type,2),h(o,l.mixed_block_flag,1),14==l.table_select[0]&&(l.table_select[0]=16),h(o,l.table_select[0],5),14==l.table_select[1]&&(l.table_select[1]=16),h(o,l.table_select[1],5),h(o,l.subblock_gain[0],3),h(o,l.subblock_gain[1],3),h(o,l.subblock_gain[2],3)):(h(o,0,1),14==l.table_select[0]&&(l.table_select[0]=16),h(o,l.table_select[0],5),14==l.table_select[1]&&(l.table_select[1]=16),h(o,l.table_select[1],5),14==l.table_select[2]&&(l.table_select[2]=16),h(o,l.table_select[2],5),h(o,l.region0_count,4),h(o,l.region1_count,3)),h(o,l.scalefac_scale,1),h(o,l.count1table_select,1)}e.error_protection&&n();var c=o.h_ptr;o.h_ptr=c+1&W.MAX_HEADER_BUF-1,o.header[o.h_ptr].write_timing=o.header[c].write_timing+t,o.h_ptr,o.w_ptr}(e,a);var i=8*r.sideinfo_len;if(i+=function(e){var t,n,r,a,i=0,o=e.internal_flags,s=o.l3_side;if(1==e.version)for(t=0;t<2;t++)for(n=0;n<o.channels_out;n++){var l=s.tt[t][n],c=y.slen1_tab[l.scalefac_compress],f=y.slen2_tab[l.scalefac_compress];for(a=0,r=0;r<l.sfbdivide;r++)-1!=l.scalefac[r]&&(u(o,l.scalefac[r],c),a+=c);for(;r<l.sfbmax;r++)-1!=l.scalefac[r]&&(u(o,l.scalefac[r],f),a+=f);l.block_type==z.SHORT_TYPE?a+=b(o,l):a+=g(o,l),i+=a+=v(o,l)}else for(t=0,n=0;n<o.channels_out;n++){var p,_,d=0;if(a=0,r=0,_=0,(l=s.tt[t][n]).block_type==z.SHORT_TYPE){for(;_<4;_++){var h=l.sfb_partition_table[_]/3,m=l.slen[_];for(p=0;p<h;p++,r++)u(o,Math.max(l.scalefac[3*r+0],0),m),u(o,Math.max(l.scalefac[3*r+1],0),m),u(o,Math.max(l.scalefac[3*r+2],0),m),d+=3*m}a+=b(o,l)}else{for(;_<4;_++)for(h=l.sfb_partition_table[_],m=l.slen[_],p=0;p<h;p++,r++)u(o,Math.max(l.scalefac[r],0),m),d+=m;a+=g(o,l)}i+=d+(a+=v(o,l))}return i}(e),d(e,t.resvDrain_post),i+=t.resvDrain_post,t.main_data_begin+=(a-i)/8,S(e,new w),r.ResvSize,8*t.main_data_begin!=r.ResvSize&&(r.ResvSize=8*t.main_data_begin),s>1e9){var o;for(o=0;o<W.MAX_HEADER_BUF;++o)r.header[o].write_timing-=s;s=0}return 0},this.copy_buffer=function(e,t,r,s,f){var u=l+1;if(u<=0)return 0;if(0!=s&&u>s)return-1;if(_.arraycopy(o,0,t,r,u),l=-1,c=0,0!=f){var p=i(1);p[0]=e.nMusicCRC,a.updateMusicCRC(p,t,r,u),e.nMusicCRC=p[0],u>0&&(e.VBR_seek_table.nBytesWritten+=u),e.decode_on_the_fly&&n()}return u},this.init_bit_stream_w=function(e){o=r(Z.LAME_MAXMP3BUFFER),e.h_ptr=e.w_ptr=0,e.header[e.h_ptr].write_timing=0,l=-1,c=0,s=0}}function M(e,t,n,r){this.xlen=e,this.linmax=t,this.table=n,this.hlen=r}v.MAX_VALUE=34028235e31,m.vbr_off=new m(0),m.vbr_mt=new m(1),m.vbr_rh=new m(2),m.vbr_abr=new m(3),m.vbr_mtrh=new m(4),m.vbr_default=m.vbr_mtrh,b.STEREO=new b(0),b.JOINT_STEREO=new b(1),b.DUAL_CHANNEL=new b(2),b.MONO=new b(3),b.NOT_SET=new b(4),w.STEPS_per_dB=100,w.MAX_dB=120,w.GAIN_NOT_ENOUGH_SAMPLES=-24601,w.GAIN_ANALYSIS_ERROR=0,w.GAIN_ANALYSIS_OK=1,w.INIT_GAIN_ANALYSIS_ERROR=0,w.INIT_GAIN_ANALYSIS_OK=1,w.YULE_ORDER=10,w.MAX_ORDER=w.YULE_ORDER,w.MAX_SAMP_FREQ=48e3,w.RMS_WINDOW_TIME_NUMERATOR=1,w.RMS_WINDOW_TIME_DENOMINATOR=20,w.MAX_SAMPLES_PER_WINDOW=w.MAX_SAMP_FREQ*w.RMS_WINDOW_TIME_NUMERATOR/w.RMS_WINDOW_TIME_DENOMINATOR+1,k.NUMTOCENTRIES=100,k.MAXFRAMESIZE=2880,A.EQ=function(e,t){return Math.abs(e)>Math.abs(t)?Math.abs(e-t)<=1e-6*Math.abs(e):Math.abs(e-t)<=1e-6*Math.abs(t)},A.NEQ=function(e,t){return!A.EQ(e,t)};var T={};function R(e){this.bits=e}function C(){this.over_noise=0,this.tot_noise=0,this.max_noise=0,this.over_count=0,this.over_SSD=0,this.bits=0}function E(){this.setModules=function(e,t){}}function B(){this.useAdjust=0,this.aaSensitivityP=0,this.adjust=0,this.adjustLimit=0,this.decay=0,this.floor=0,this.l=o(z.SBMAX_l),this.s=o(z.SBMAX_s),this.psfb21=o(z.PSFB21),this.psfb12=o(z.PSFB12),this.cb_l=o(z.CBANDS),this.cb_s=o(z.CBANDS),this.eql_w=o(z.BLKSIZE/2)}function O(){this.class_id=0,this.num_samples=0,this.num_channels=0,this.in_samplerate=0,this.out_samplerate=0,this.scale=0,this.scale_left=0,this.scale_right=0,this.analysis=!1,this.bWriteVbrTag=!1,this.decode_only=!1,this.quality=0,this.mode=b.STEREO,this.force_ms=!1,this.free_format=!1,this.findReplayGain=!1,this.decode_on_the_fly=!1,this.write_id3tag_automatic=!1,this.brate=0,this.compression_ratio=0,this.copyright=0,this.original=0,this.extension=0,this.emphasis=0,this.error_protection=0,this.strict_ISO=!1,this.disable_reservoir=!1,this.quant_comp=0,this.quant_comp_short=0,this.experimentalY=!1,this.experimentalZ=0,this.exp_nspsytune=0,this.preset=0,this.VBR=null,this.VBR_q_frac=0,this.VBR_q=0,this.VBR_mean_bitrate_kbps=0,this.VBR_min_bitrate_kbps=0,this.VBR_max_bitrate_kbps=0,this.VBR_hard_min=0,this.lowpassfreq=0,this.highpassfreq=0,this.lowpasswidth=0,this.highpasswidth=0,this.maskingadjust=0,this.maskingadjust_short=0,this.ATHonly=!1,this.ATHshort=!1,this.noATH=!1,this.ATHtype=0,this.ATHcurve=0,this.ATHlower=0,this.athaa_type=0,this.athaa_loudapprox=0,this.athaa_sensitivity=0,this.short_blocks=null,this.useTemporal=!1,this.interChRatio=0,this.msfix=0,this.tune=!1,this.tune_value_a=0,this.version=0,this.encoder_delay=0,this.encoder_padding=0,this.framesize=0,this.frameNum=0,this.lame_allocated_gfp=0,this.internal_flags=null}function L(e){var t=e;this.quantize=t,this.iteration_loop=function(e,t,r,a){var s=e.internal_flags,l=o(H.SFBMAX),c=o(576),f=i(2),u=0,p=s.l3_side,_=new R(u);this.quantize.rv.ResvFrameBegin(e,_),u=_.bits;for(var d=0;d<s.mode_gr;d++){this.quantize.qupvt.on_pe(e,t,f,u,d,d),s.mode_ext==z.MPG_MD_MS_LR&&n();for(var h=0;h<s.channels_out;h++){var v,m,b=p.tt[d][h];b.block_type!=z.SHORT_TYPE?(v=0,m=s.PSY.mask_adjust-v):(v=0,m=s.PSY.mask_adjust_short-v),s.masking_lower=Math.pow(10,.1*m),this.quantize.init_outer_loop(s,b),this.quantize.init_xrpow(s,b,c)&&(this.quantize.qupvt.calc_xmin(e,a[d][h],b,l),this.quantize.outer_loop(e,b,l,c,h,f[h])),this.quantize.iteration_finish_one(s,d,h)}}this.quantize.rv.ResvFrameEnd(s,u)}}function I(){}function P(e,t,n,r){this.l=i(1+z.SBMAX_l),this.s=i(1+z.SBMAX_s),this.psfb21=i(1+z.PSFB21),this.psfb12=i(1+z.PSFB12);var a=this.l,o=this.s;4==arguments.length&&(this.arrL=arguments[0],this.arrS=arguments[1],this.arr21=arguments[2],this.arr12=arguments[3],_.arraycopy(this.arrL,0,a,0,Math.min(this.arrL.length,this.l.length)),_.arraycopy(this.arrS,0,o,0,Math.min(this.arrS.length,this.s.length)),_.arraycopy(this.arr21,0,this.psfb21,0,Math.min(this.arr21.length,this.psfb21.length)),_.arraycopy(this.arr12,0,this.psfb12,0,Math.min(this.arr12.length,this.psfb12.length)))}function $(){var t=null,r=null,a=null;this.setModules=function(e,n,i){t=e,r=n,a=i},this.IPOW20=function(e){return p[e]};var s=$.IXMAX_VAL+2,l=$.Q_MAX,c=$.Q_MAX2;$.LARGE_BITS,this.nr_of_sfb_block=[[[6,5,5,5],[9,9,9,9],[6,9,9,9]],[[6,5,7,3],[9,9,12,6],[6,9,12,6]],[[11,10,0,0],[18,18,0,0],[15,18,0,0]],[[7,7,7,0],[12,12,12,0],[6,15,12,0]],[[6,6,6,3],[12,9,9,6],[6,12,9,6]],[[8,8,5,0],[15,12,9,0],[6,18,9,0]]];var f=[0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,2,2,3,3,3,2,0];this.pretab=f,this.sfBandIndex=[new P([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,24,32,42,56,74,100,132,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new P([0,6,12,18,24,30,36,44,54,66,80,96,114,136,162,194,232,278,332,394,464,540,576],[0,4,8,12,18,26,36,48,62,80,104,136,180,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new P([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,26,36,48,62,80,104,134,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new P([0,4,8,12,16,20,24,30,36,44,52,62,74,90,110,134,162,196,238,288,342,418,576],[0,4,8,12,16,22,30,40,52,66,84,106,136,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new P([0,4,8,12,16,20,24,30,36,42,50,60,72,88,106,128,156,190,230,276,330,384,576],[0,4,8,12,16,22,28,38,50,64,80,100,126,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new P([0,4,8,12,16,20,24,30,36,44,54,66,82,102,126,156,194,240,296,364,448,550,576],[0,4,8,12,16,22,30,42,58,78,104,138,180,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new P([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,26,36,48,62,80,104,134,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new P([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,26,36,48,62,80,104,134,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new P([0,12,24,36,48,60,72,88,108,132,160,192,232,280,336,400,476,566,568,570,572,574,576],[0,8,16,24,36,52,72,96,124,160,162,164,166,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0])];var u=o(l+c+1),p=o(l),_=o(s),h=o(s);function b(e,t){var n=a.ATHformula(t,e);return n-=100,n=Math.pow(10,n/10+e.ATHlower)}function g(e){this.s=e}this.adj43=h,this.iteration_init=function(r){var a,i=r.internal_flags,o=i.l3_side;if(0==i.iteration_init_init){for(i.iteration_init_init=1,o.main_data_begin=0,function(t){for(var r=t.internal_flags.ATH.l,a=t.internal_flags.ATH.psfb21,i=t.internal_flags.ATH.s,o=t.internal_flags.ATH.psfb12,s=t.internal_flags,l=t.out_samplerate,c=0;c<z.SBMAX_l;c++){var f=s.scalefac_band.l[c],u=s.scalefac_band.l[c+1];r[c]=v.MAX_VALUE;for(var p=f;p<u;p++){var _=b(t,p*l/1152);r[c]=Math.min(r[c],_)}}for(c=0;c<z.PSFB21;c++)for(f=s.scalefac_band.psfb21[c],u=s.scalefac_band.psfb21[c+1],a[c]=v.MAX_VALUE,p=f;p<u;p++)_=b(t,p*l/1152),a[c]=Math.min(a[c],_);for(c=0;c<z.SBMAX_s;c++){for(f=s.scalefac_band.s[c],u=s.scalefac_band.s[c+1],i[c]=v.MAX_VALUE,p=f;p<u;p++)_=b(t,p*l/384),i[c]=Math.min(i[c],_);i[c]*=s.scalefac_band.s[c+1]-s.scalefac_band.s[c]}for(c=0;c<z.PSFB12;c++){for(f=s.scalefac_band.psfb12[c],u=s.scalefac_band.psfb12[c+1],o[c]=v.MAX_VALUE,p=f;p<u;p++)_=b(t,p*l/384),o[c]=Math.min(o[c],_);o[c]*=s.scalefac_band.s[13]-s.scalefac_band.s[12]}t.noATH&&n(),s.ATH.floor=10*e(b(t,-1))}(r),_[0]=0,a=1;a<s;a++)_[a]=Math.pow(a,4/3);for(a=0;a<s-1;a++)h[a]=a+1-Math.pow(.5*(_[a]+_[a+1]),.75);for(h[a]=.5,a=0;a<l;a++)p[a]=Math.pow(2,-.1875*(a-210));for(a=0;a<=l+c;a++)u[a]=Math.pow(2,.25*(a-210-c));var f,d,m,g;for(t.huffman_init(i),(a=r.exp_nspsytune>>2&63)>=32&&(a-=64),f=Math.pow(10,a/4/10),(a=r.exp_nspsytune>>8&63)>=32&&(a-=64),d=Math.pow(10,a/4/10),(a=r.exp_nspsytune>>14&63)>=32&&(a-=64),m=Math.pow(10,a/4/10),(a=r.exp_nspsytune>>20&63)>=32&&(a-=64),g=m*Math.pow(10,a/4/10),a=0;a<z.SBMAX_l;a++)y=a<=6?f:a<=13?d:a<=20?m:g,i.nsPsy.longfact[a]=y;for(a=0;a<z.SBMAX_s;a++){var y;y=a<=5?f:a<=10?d:a<=11?m:g,i.nsPsy.shortfact[a]=y}}},this.on_pe=function(e,t,a,o,s,l){var c,f,u=e.internal_flags,p=0,_=i(2),d=new R(p),h=r.ResvMaxBits(e,o,d,l),v=(p=d.bits)+h;for(v>W.MAX_BITS_PER_GRANULE&&(v=W.MAX_BITS_PER_GRANULE),c=0,f=0;f<u.channels_out;++f)a[f]=Math.min(W.MAX_BITS_PER_CHANNEL,p/u.channels_out),_[f]=0|a[f]*t[s][f]/700-a[f],_[f]>3*o/4&&(_[f]=3*o/4),_[f]<0&&(_[f]=0),_[f]+a[f]>W.MAX_BITS_PER_CHANNEL&&(_[f]=Math.max(0,W.MAX_BITS_PER_CHANNEL-a[f])),c+=_[f];if(c>h)for(f=0;f<u.channels_out;++f)_[f]=h*_[f]/c;for(f=0;f<u.channels_out;++f)a[f]+=_[f],h-=_[f];for(c=0,f=0;f<u.channels_out;++f)c+=a[f];return c>W.MAX_BITS_PER_GRANULE&&n(),v},this.athAdjust=function(e,t,n){var r=90.30873362,a=d.FAST_LOG10_X(t,10),i=e*e,o=0;return a-=n,i>1e-20&&(o=1+d.FAST_LOG10_X(i,10/r)),o<0&&(o=0),a*=o,a+=n+r-94.82444863,Math.pow(10,.1*a)},this.calc_xmin=function(e,t,r,a){var i,o=0,s=e.internal_flags,l=0,c=0,f=s.ATH,u=r.xr,p=e.VBR==m.vbr_mtrh?1:0,_=s.masking_lower;for(e.VBR!=m.vbr_mtrh&&e.VBR!=m.vbr_mt||(_=1),i=0;i<r.psy_lmax;i++){S=(w=e.VBR==m.vbr_rh||e.VBR==m.vbr_mtrh?athAdjust(f.adjust,f.l[i],f.floor):f.adjust*f.l[i])/(b=r.width[i]),x=2220446049250313e-31,R=b>>1,T=0;do{T+=C=u[l]*u[l],x+=C<S?C:S,T+=E=u[++l]*u[l],x+=E<S?E:S,l++}while(--R>0);T>w&&c++,i==z.SBPSY_l&&n(),0!=p&&(w=x),e.ATHonly||(k=t.en.l[i])>0&&(M=T*t.thm.l[i]*_/k,0!=p&&(M*=s.nsPsy.longfact[i]),w<M&&(w=M)),a[o++]=0!=p?w:w*s.nsPsy.longfact[i]}var d=575;if(r.block_type!=z.SHORT_TYPE)for(var h=576;0!=h--&&A.EQ(u[h],0);)d=h;r.max_nonzero_coeff=d;for(var v=r.sfb_smin;i<r.psymax;v++,i+=3){var b,g,y;for(y=e.VBR==m.vbr_rh||e.VBR==m.vbr_mtrh?athAdjust(f.adjust,f.s[v],f.floor):f.adjust*f.s[v],b=r.width[i],g=0;g<3;g++){var w,S,x,k,M,T=0,R=b>>1;S=y/b,x=2220446049250313e-31;do{var C,E;T+=C=u[l]*u[l],x+=C<S?C:S,T+=E=u[++l]*u[l],x+=E<S?E:S,l++}while(--R>0);T>y&&c++,v==z.SBPSY_s&&n(),w=0!=p?x:y,e.ATHonly||e.ATHshort||(k=t.en.s[v][g])>0&&(M=T*t.thm.s[v][g]*_/k,0!=p&&(M*=s.nsPsy.shortfact[v]),w<M&&(w=M)),a[o++]=0!=p?w:w*s.nsPsy.shortfact[v]}e.useTemporal&&(a[o-3]>a[o-3+1]&&(a[o-3+1]+=(a[o-3]-a[o-3+1])*s.decay),a[o-3+1]>a[o-3+2]&&(a[o-3+2]+=(a[o-3+1]-a[o-3+2])*s.decay))}return c},this.calc_noise_core=function(e,t,n,r){var a=0,i=t.s,s=e.l3_enc;if(i>e.count1)for(;0!=n--;)c=e.xr[i],i++,a+=c*c,c=e.xr[i],i++,a+=c*c;else if(i>e.big_values){var l=o(2);for(l[0]=0,l[1]=r;0!=n--;)c=Math.abs(e.xr[i])-l[s[i]],i++,a+=c*c,c=Math.abs(e.xr[i])-l[s[i]],i++,a+=c*c}else for(;0!=n--;){var c;c=Math.abs(e.xr[i])-_[s[i]]*r,i++,a+=c*c,c=Math.abs(e.xr[i])-_[s[i]]*r,i++,a+=c*c}return t.s=i,a},this.calc_noise=function(e,t,n,r,a){var i,o,s=0,l=0,c=0,p=0,_=0,h=-20,v=0,m=e.scalefac,b=0;for(r.over_SSD=0,i=0;i<e.psymax;i++){var y,w=e.global_gain-(m[b++]+(0!=e.preflag?f[i]:0)<<e.scalefac_scale+1)-8*e.subblock_gain[e.window[i]],S=0;if(null!=a&&a.step[i]==w)S=a.noise[i],v+=e.width[i],n[s++]=S/t[l++],S=a.noise_log[i];else{var x,k=u[w+$.Q_MAX2];o=e.width[i]>>1,v+e.width[i]>e.max_nonzero_coeff&&(o=(x=e.max_nonzero_coeff-v+1)>0?x>>1:0);var A=new g(v);S=this.calc_noise_core(e,A,o,k),v=A.s,null!=a&&(a.step[i]=w,a.noise[i]=S),S=n[s++]=S/t[l++],S=d.FAST_LOG10(Math.max(S,1e-20)),null!=a&&(a.noise_log[i]=S)}null!=a&&(a.global_gain=e.global_gain),_+=S,S>0&&(y=Math.max(0|10*S+.5,1),r.over_SSD+=y*y,c++,p+=S),h=Math.max(h,S)}return r.over_count=c,r.tot_noise=_,r.over_noise=p,r.max_noise=h,c}}function N(){this.global_gain=0,this.sfb_count1=0,this.step=i(39),this.noise=o(39),this.noise_log=o(39)}function D(){this.xr=o(576),this.l3_enc=i(576),this.scalefac=i(H.SFBMAX),this.xrpow_max=0,this.part2_3_length=0,this.big_values=0,this.count1=0,this.global_gain=0,this.scalefac_compress=0,this.block_type=0,this.mixed_block_flag=0,this.table_select=i(3),this.subblock_gain=i(4),this.region0_count=0,this.region1_count=0,this.preflag=0,this.scalefac_scale=0,this.count1table_select=0,this.part2_length=0,this.sfb_lmax=0,this.sfb_smin=0,this.psy_lmax=0,this.sfbmax=0,this.psymax=0,this.sfbdivide=0,this.width=i(H.SFBMAX),this.window=i(H.SFBMAX),this.count1bits=0,this.sfb_partition_table=null,this.slen=i(4),this.max_nonzero_coeff=0;var e=this;function t(e){return new Int32Array(e)}this.assign=function(n){var r;e.xr=(r=n.xr,new Float32Array(r)),e.l3_enc=t(n.l3_enc),e.scalefac=t(n.scalefac),e.xrpow_max=n.xrpow_max,e.part2_3_length=n.part2_3_length,e.big_values=n.big_values,e.count1=n.count1,e.global_gain=n.global_gain,e.scalefac_compress=n.scalefac_compress,e.block_type=n.block_type,e.mixed_block_flag=n.mixed_block_flag,e.table_select=t(n.table_select),e.subblock_gain=t(n.subblock_gain),e.region0_count=n.region0_count,e.region1_count=n.region1_count,e.preflag=n.preflag,e.scalefac_scale=n.scalefac_scale,e.count1table_select=n.count1table_select,e.part2_length=n.part2_length,e.sfb_lmax=n.sfb_lmax,e.sfb_smin=n.sfb_smin,e.psy_lmax=n.psy_lmax,e.sfbmax=n.sfbmax,e.psymax=n.psymax,e.sfbdivide=n.sfbdivide,e.width=t(n.width),e.window=t(n.window),e.count1bits=n.count1bits,e.sfb_partition_table=n.sfb_partition_table.slice(0),e.slen=t(n.slen),e.max_nonzero_coeff=n.max_nonzero_coeff}}T.t1HB=[1,1,1,0],T.t2HB=[1,2,1,3,1,1,3,2,0],T.t3HB=[3,2,1,1,1,1,3,2,0],T.t5HB=[1,2,6,5,3,1,4,4,7,5,7,1,6,1,1,0],T.t6HB=[7,3,5,1,6,2,3,2,5,4,4,1,3,3,2,0],T.t7HB=[1,2,10,19,16,10,3,3,7,10,5,3,11,4,13,17,8,4,12,11,18,15,11,2,7,6,9,14,3,1,6,4,5,3,2,0],T.t8HB=[3,4,6,18,12,5,5,1,2,16,9,3,7,3,5,14,7,3,19,17,15,13,10,4,13,5,8,11,5,1,12,4,4,1,1,0],T.t9HB=[7,5,9,14,15,7,6,4,5,5,6,7,7,6,8,8,8,5,15,6,9,10,5,1,11,7,9,6,4,1,14,4,6,2,6,0],T.t10HB=[1,2,10,23,35,30,12,17,3,3,8,12,18,21,12,7,11,9,15,21,32,40,19,6,14,13,22,34,46,23,18,7,20,19,33,47,27,22,9,3,31,22,41,26,21,20,5,3,14,13,10,11,16,6,5,1,9,8,7,8,4,4,2,0],T.t11HB=[3,4,10,24,34,33,21,15,5,3,4,10,32,17,11,10,11,7,13,18,30,31,20,5,25,11,19,59,27,18,12,5,35,33,31,58,30,16,7,5,28,26,32,19,17,15,8,14,14,12,9,13,14,9,4,1,11,4,6,6,6,3,2,0],T.t12HB=[9,6,16,33,41,39,38,26,7,5,6,9,23,16,26,11,17,7,11,14,21,30,10,7,17,10,15,12,18,28,14,5,32,13,22,19,18,16,9,5,40,17,31,29,17,13,4,2,27,12,11,15,10,7,4,1,27,12,8,12,6,3,1,0],T.t13HB=[1,5,14,21,34,51,46,71,42,52,68,52,67,44,43,19,3,4,12,19,31,26,44,33,31,24,32,24,31,35,22,14,15,13,23,36,59,49,77,65,29,40,30,40,27,33,42,16,22,20,37,61,56,79,73,64,43,76,56,37,26,31,25,14,35,16,60,57,97,75,114,91,54,73,55,41,48,53,23,24,58,27,50,96,76,70,93,84,77,58,79,29,74,49,41,17,47,45,78,74,115,94,90,79,69,83,71,50,59,38,36,15,72,34,56,95,92,85,91,90,86,73,77,65,51,44,43,42,43,20,30,44,55,78,72,87,78,61,46,54,37,30,20,16,53,25,41,37,44,59,54,81,66,76,57,54,37,18,39,11,35,33,31,57,42,82,72,80,47,58,55,21,22,26,38,22,53,25,23,38,70,60,51,36,55,26,34,23,27,14,9,7,34,32,28,39,49,75,30,52,48,40,52,28,18,17,9,5,45,21,34,64,56,50,49,45,31,19,12,15,10,7,6,3,48,23,20,39,36,35,53,21,16,23,13,10,6,1,4,2,16,15,17,27,25,20,29,11,17,12,16,8,1,1,0,1],T.t15HB=[7,12,18,53,47,76,124,108,89,123,108,119,107,81,122,63,13,5,16,27,46,36,61,51,42,70,52,83,65,41,59,36,19,17,15,24,41,34,59,48,40,64,50,78,62,80,56,33,29,28,25,43,39,63,55,93,76,59,93,72,54,75,50,29,52,22,42,40,67,57,95,79,72,57,89,69,49,66,46,27,77,37,35,66,58,52,91,74,62,48,79,63,90,62,40,38,125,32,60,56,50,92,78,65,55,87,71,51,73,51,70,30,109,53,49,94,88,75,66,122,91,73,56,42,64,44,21,25,90,43,41,77,73,63,56,92,77,66,47,67,48,53,36,20,71,34,67,60,58,49,88,76,67,106,71,54,38,39,23,15,109,53,51,47,90,82,58,57,48,72,57,41,23,27,62,9,86,42,40,37,70,64,52,43,70,55,42,25,29,18,11,11,118,68,30,55,50,46,74,65,49,39,24,16,22,13,14,7,91,44,39,38,34,63,52,45,31,52,28,19,14,8,9,3,123,60,58,53,47,43,32,22,37,24,17,12,15,10,2,1,71,37,34,30,28,20,17,26,21,16,10,6,8,6,2,0],T.t16HB=[1,5,14,44,74,63,110,93,172,149,138,242,225,195,376,17,3,4,12,20,35,62,53,47,83,75,68,119,201,107,207,9,15,13,23,38,67,58,103,90,161,72,127,117,110,209,206,16,45,21,39,69,64,114,99,87,158,140,252,212,199,387,365,26,75,36,68,65,115,101,179,164,155,264,246,226,395,382,362,9,66,30,59,56,102,185,173,265,142,253,232,400,388,378,445,16,111,54,52,100,184,178,160,133,257,244,228,217,385,366,715,10,98,48,91,88,165,157,148,261,248,407,397,372,380,889,884,8,85,84,81,159,156,143,260,249,427,401,392,383,727,713,708,7,154,76,73,141,131,256,245,426,406,394,384,735,359,710,352,11,139,129,67,125,247,233,229,219,393,743,737,720,885,882,439,4,243,120,118,115,227,223,396,746,742,736,721,712,706,223,436,6,202,224,222,218,216,389,386,381,364,888,443,707,440,437,1728,4,747,211,210,208,370,379,734,723,714,1735,883,877,876,3459,865,2,377,369,102,187,726,722,358,711,709,866,1734,871,3458,870,434,0,12,10,7,11,10,17,11,9,13,12,10,7,5,3,1,3],T.t24HB=[15,13,46,80,146,262,248,434,426,669,653,649,621,517,1032,88,14,12,21,38,71,130,122,216,209,198,327,345,319,297,279,42,47,22,41,74,68,128,120,221,207,194,182,340,315,295,541,18,81,39,75,70,134,125,116,220,204,190,178,325,311,293,271,16,147,72,69,135,127,118,112,210,200,188,352,323,306,285,540,14,263,66,129,126,119,114,214,202,192,180,341,317,301,281,262,12,249,123,121,117,113,215,206,195,185,347,330,308,291,272,520,10,435,115,111,109,211,203,196,187,353,332,313,298,283,531,381,17,427,212,208,205,201,193,186,177,169,320,303,286,268,514,377,16,335,199,197,191,189,181,174,333,321,305,289,275,521,379,371,11,668,184,183,179,175,344,331,314,304,290,277,530,383,373,366,10,652,346,171,168,164,318,309,299,287,276,263,513,375,368,362,6,648,322,316,312,307,302,292,284,269,261,512,376,370,364,359,4,620,300,296,294,288,282,273,266,515,380,374,369,365,361,357,2,1033,280,278,274,267,264,259,382,378,372,367,363,360,358,356,0,43,20,19,17,15,13,11,9,7,6,4,7,5,3,1,3],T.t32HB=[1,10,8,20,12,20,16,32,14,12,24,0,28,16,24,16],T.t33HB=[15,28,26,48,22,40,36,64,14,24,20,32,12,16,8,0],T.t1l=[1,4,3,5],T.t2l=[1,4,7,4,5,7,6,7,8],T.t3l=[2,3,7,4,4,7,6,7,8],T.t5l=[1,4,7,8,4,5,8,9,7,8,9,10,8,8,9,10],T.t6l=[3,4,6,8,4,4,6,7,5,6,7,8,7,7,8,9],T.t7l=[1,4,7,9,9,10,4,6,8,9,9,10,7,7,9,10,10,11,8,9,10,11,11,11,8,9,10,11,11,12,9,10,11,12,12,12],T.t8l=[2,4,7,9,9,10,4,4,6,10,10,10,7,6,8,10,10,11,9,10,10,11,11,12,9,9,10,11,12,12,10,10,11,11,13,13],T.t9l=[3,4,6,7,9,10,4,5,6,7,8,10,5,6,7,8,9,10,7,7,8,9,9,10,8,8,9,9,10,11,9,9,10,10,11,11],T.t10l=[1,4,7,9,10,10,10,11,4,6,8,9,10,11,10,10,7,8,9,10,11,12,11,11,8,9,10,11,12,12,11,12,9,10,11,12,12,12,12,12,10,11,12,12,13,13,12,13,9,10,11,12,12,12,13,13,10,10,11,12,12,13,13,13],T.t11l=[2,4,6,8,9,10,9,10,4,5,6,8,10,10,9,10,6,7,8,9,10,11,10,10,8,8,9,11,10,12,10,11,9,10,10,11,11,12,11,12,9,10,11,12,12,13,12,13,9,9,9,10,11,12,12,12,9,9,10,11,12,12,12,12],T.t12l=[4,4,6,8,9,10,10,10,4,5,6,7,9,9,10,10,6,6,7,8,9,10,9,10,7,7,8,8,9,10,10,10,8,8,9,9,10,10,10,11,9,9,10,10,10,11,10,11,9,9,9,10,10,11,11,12,10,10,10,11,11,11,11,12],T.t13l=[1,5,7,8,9,10,10,11,10,11,12,12,13,13,14,14,4,6,8,9,10,10,11,11,11,11,12,12,13,14,14,14,7,8,9,10,11,11,12,12,11,12,12,13,13,14,15,15,8,9,10,11,11,12,12,12,12,13,13,13,13,14,15,15,9,9,11,11,12,12,13,13,12,13,13,14,14,15,15,16,10,10,11,12,12,12,13,13,13,13,14,13,15,15,16,16,10,11,12,12,13,13,13,13,13,14,14,14,15,15,16,16,11,11,12,13,13,13,14,14,14,14,15,15,15,16,18,18,10,10,11,12,12,13,13,14,14,14,14,15,15,16,17,17,11,11,12,12,13,13,13,15,14,15,15,16,16,16,18,17,11,12,12,13,13,14,14,15,14,15,16,15,16,17,18,19,12,12,12,13,14,14,14,14,15,15,15,16,17,17,17,18,12,13,13,14,14,15,14,15,16,16,17,17,17,18,18,18,13,13,14,15,15,15,16,16,16,16,16,17,18,17,18,18,14,14,14,15,15,15,17,16,16,19,17,17,17,19,18,18,13,14,15,16,16,16,17,16,17,17,18,18,21,20,21,18],T.t15l=[3,5,6,8,8,9,10,10,10,11,11,12,12,12,13,14,5,5,7,8,9,9,10,10,10,11,11,12,12,12,13,13,6,7,7,8,9,9,10,10,10,11,11,12,12,13,13,13,7,8,8,9,9,10,10,11,11,11,12,12,12,13,13,13,8,8,9,9,10,10,11,11,11,11,12,12,12,13,13,13,9,9,9,10,10,10,11,11,11,11,12,12,13,13,13,14,10,9,10,10,10,11,11,11,11,12,12,12,13,13,14,14,10,10,10,11,11,11,11,12,12,12,12,12,13,13,13,14,10,10,10,11,11,11,11,12,12,12,12,13,13,14,14,14,10,10,11,11,11,11,12,12,12,13,13,13,13,14,14,14,11,11,11,11,12,12,12,12,12,13,13,13,13,14,15,14,11,11,11,11,12,12,12,12,13,13,13,13,14,14,14,15,12,12,11,12,12,12,13,13,13,13,13,13,14,14,15,15,12,12,12,12,12,13,13,13,13,14,14,14,14,14,15,15,13,13,13,13,13,13,13,13,14,14,14,14,15,15,14,15,13,13,13,13,13,13,13,14,14,14,14,14,15,15,15,15],T.t16_5l=[1,5,7,9,10,10,11,11,12,12,12,13,13,13,14,11,4,6,8,9,10,11,11,11,12,12,12,13,14,13,14,11,7,8,9,10,11,11,12,12,13,12,13,13,13,14,14,12,9,9,10,11,11,12,12,12,13,13,14,14,14,15,15,13,10,10,11,11,12,12,13,13,13,14,14,14,15,15,15,12,10,10,11,11,12,13,13,14,13,14,14,15,15,15,16,13,11,11,11,12,13,13,13,13,14,14,14,14,15,15,16,13,11,11,12,12,13,13,13,14,14,15,15,15,15,17,17,13,11,12,12,13,13,13,14,14,15,15,15,15,16,16,16,13,12,12,12,13,13,14,14,15,15,15,15,16,15,16,15,14,12,13,12,13,14,14,14,14,15,16,16,16,17,17,16,13,13,13,13,13,14,14,15,16,16,16,16,16,16,15,16,14,13,14,14,14,14,15,15,15,15,17,16,16,16,16,18,14,15,14,14,14,15,15,16,16,16,18,17,17,17,19,17,14,14,15,13,14,16,16,15,16,16,17,18,17,19,17,16,14,11,11,11,12,12,13,13,13,14,14,14,14,14,14,14,12],T.t16l=[1,5,7,9,10,10,11,11,12,12,12,13,13,13,14,10,4,6,8,9,10,11,11,11,12,12,12,13,14,13,14,10,7,8,9,10,11,11,12,12,13,12,13,13,13,14,14,11,9,9,10,11,11,12,12,12,13,13,14,14,14,15,15,12,10,10,11,11,12,12,13,13,13,14,14,14,15,15,15,11,10,10,11,11,12,13,13,14,13,14,14,15,15,15,16,12,11,11,11,12,13,13,13,13,14,14,14,14,15,15,16,12,11,11,12,12,13,13,13,14,14,15,15,15,15,17,17,12,11,12,12,13,13,13,14,14,15,15,15,15,16,16,16,12,12,12,12,13,13,14,14,15,15,15,15,16,15,16,15,13,12,13,12,13,14,14,14,14,15,16,16,16,17,17,16,12,13,13,13,13,14,14,15,16,16,16,16,16,16,15,16,13,13,14,14,14,14,15,15,15,15,17,16,16,16,16,18,13,15,14,14,14,15,15,16,16,16,18,17,17,17,19,17,13,14,15,13,14,16,16,15,16,16,17,18,17,19,17,16,13,10,10,10,11,11,12,12,12,13,13,13,13,13,13,13,10],T.t24l=[4,5,7,8,9,10,10,11,11,12,12,12,12,12,13,10,5,6,7,8,9,10,10,11,11,11,12,12,12,12,12,10,7,7,8,9,9,10,10,11,11,11,11,12,12,12,13,9,8,8,9,9,10,10,10,11,11,11,11,12,12,12,12,9,9,9,9,10,10,10,10,11,11,11,12,12,12,12,13,9,10,9,10,10,10,10,11,11,11,11,12,12,12,12,12,9,10,10,10,10,10,11,11,11,11,12,12,12,12,12,13,9,11,10,10,10,11,11,11,11,12,12,12,12,12,13,13,10,11,11,11,11,11,11,11,11,11,12,12,12,12,13,13,10,11,11,11,11,11,11,11,12,12,12,12,12,13,13,13,10,12,11,11,11,11,12,12,12,12,12,12,13,13,13,13,10,12,12,11,11,11,12,12,12,12,12,12,13,13,13,13,10,12,12,12,12,12,12,12,12,12,12,13,13,13,13,13,10,12,12,12,12,12,12,12,12,13,13,13,13,13,13,13,10,13,12,12,12,12,12,12,13,13,13,13,13,13,13,13,10,9,9,9,9,9,9,9,9,9,9,9,10,10,10,10,6],T.t32l=[1,5,5,7,5,8,7,9,5,7,7,9,7,9,9,10],T.t33l=[4,5,5,6,5,6,6,7,5,6,6,7,6,7,7,8],T.ht=[new M(0,0,null,null),new M(2,0,T.t1HB,T.t1l),new M(3,0,T.t2HB,T.t2l),new M(3,0,T.t3HB,T.t3l),new M(0,0,null,null),new M(4,0,T.t5HB,T.t5l),new M(4,0,T.t6HB,T.t6l),new M(6,0,T.t7HB,T.t7l),new M(6,0,T.t8HB,T.t8l),new M(6,0,T.t9HB,T.t9l),new M(8,0,T.t10HB,T.t10l),new M(8,0,T.t11HB,T.t11l),new M(8,0,T.t12HB,T.t12l),new M(16,0,T.t13HB,T.t13l),new M(0,0,null,T.t16_5l),new M(16,0,T.t15HB,T.t15l),new M(1,1,T.t16HB,T.t16l),new M(2,3,T.t16HB,T.t16l),new M(3,7,T.t16HB,T.t16l),new M(4,15,T.t16HB,T.t16l),new M(6,63,T.t16HB,T.t16l),new M(8,255,T.t16HB,T.t16l),new M(10,1023,T.t16HB,T.t16l),new M(13,8191,T.t16HB,T.t16l),new M(4,15,T.t24HB,T.t24l),new M(5,31,T.t24HB,T.t24l),new M(6,63,T.t24HB,T.t24l),new M(7,127,T.t24HB,T.t24l),new M(8,255,T.t24HB,T.t24l),new M(9,511,T.t24HB,T.t24l),new M(11,2047,T.t24HB,T.t24l),new M(13,8191,T.t24HB,T.t24l),new M(0,0,T.t32HB,T.t32l),new M(0,0,T.t33HB,T.t33l)],T.largetbl=[65540,327685,458759,589832,655369,655370,720906,720907,786443,786444,786444,851980,851980,851980,917517,655370,262149,393222,524295,589832,655369,720906,720906,720907,786443,786443,786444,851980,917516,851980,917516,655370,458759,524295,589832,655369,720905,720906,786442,786443,851979,786443,851979,851980,851980,917516,917517,720905,589832,589832,655369,720905,720906,786442,786442,786443,851979,851979,917515,917516,917516,983052,983052,786441,655369,655369,720905,720906,786442,786442,851978,851979,851979,917515,917516,917516,983052,983052,983053,720905,655370,655369,720906,720906,786442,851978,851979,917515,851979,917515,917516,983052,983052,983052,1048588,786441,720906,720906,720906,786442,851978,851979,851979,851979,917515,917516,917516,917516,983052,983052,1048589,786441,720907,720906,786442,786442,851979,851979,851979,917515,917516,983052,983052,983052,983052,1114125,1114125,786442,720907,786443,786443,851979,851979,851979,917515,917515,983051,983052,983052,983052,1048588,1048589,1048589,786442,786443,786443,786443,851979,851979,917515,917515,983052,983052,983052,983052,1048588,983053,1048589,983053,851978,786444,851979,786443,851979,917515,917516,917516,917516,983052,1048588,1048588,1048589,1114125,1114125,1048589,786442,851980,851980,851979,851979,917515,917516,983052,1048588,1048588,1048588,1048588,1048589,1048589,983053,1048589,851978,851980,917516,917516,917516,917516,983052,983052,983052,983052,1114124,1048589,1048589,1048589,1048589,1179661,851978,983052,917516,917516,917516,983052,983052,1048588,1048588,1048589,1179661,1114125,1114125,1114125,1245197,1114125,851978,917517,983052,851980,917516,1048588,1048588,983052,1048589,1048589,1114125,1179661,1114125,1245197,1114125,1048589,851978,655369,655369,655369,720905,720905,786441,786441,786441,851977,851977,851977,851978,851978,851978,851978,655366],T.table23=[65538,262147,458759,262148,327684,458759,393222,458759,524296],T.table56=[65539,262148,458758,524296,262148,327684,524294,589831,458757,524294,589831,655368,524295,524295,589832,655369],T.bitrate_table=[[0,8,16,24,32,40,48,56,64,80,96,112,128,144,160,-1],[0,32,40,48,56,64,80,96,112,128,160,192,224,256,320,-1],[0,8,16,24,32,40,48,56,64,-1,-1,-1,-1,-1,-1,-1]],T.samplerate_table=[[22050,24e3,16e3,-1],[44100,48e3,32e3,-1],[11025,12e3,8e3,-1]],T.scfsi_band=[0,6,11,16,21],$.Q_MAX=257,$.Q_MAX2=116,$.LARGE_BITS=1e5,$.IXMAX_VAL=8206;var H={};function j(){var e,t;this.rv=null,this.qupvt=null;var r,a=new E;function i(e){this.ordinal=e}function s(e){for(var t=0;t<e.sfbmax;t++)if(e.scalefac[t]+e.subblock_gain[e.window[t]]==0)return!1;return!0}function l(e,t,r,a,i){var o;switch(e){default:case 9:t.over_count>0?(o=r.over_SSD<=t.over_SSD,r.over_SSD==t.over_SSD&&(o=r.bits<t.bits)):o=r.max_noise<0&&10*r.max_noise+r.bits<=10*t.max_noise+t.bits;break;case 0:o=r.over_count<t.over_count||r.over_count==t.over_count&&r.over_noise<t.over_noise||r.over_count==t.over_count&&A.EQ(r.over_noise,t.over_noise)&&r.tot_noise<t.tot_noise;break;case 8:n();case 1:o=r.max_noise<t.max_noise;break;case 2:o=r.tot_noise<t.tot_noise;break;case 3:o=r.tot_noise<t.tot_noise&&r.max_noise<t.max_noise;break;case 4:o=r.max_noise<=0&&t.max_noise>.2||r.max_noise<=0&&t.max_noise<0&&t.max_noise>r.max_noise-.2&&r.tot_noise<t.tot_noise||r.max_noise<=0&&t.max_noise>0&&t.max_noise>r.max_noise-.2&&r.tot_noise<t.tot_noise+t.over_noise||r.max_noise>0&&t.max_noise>-.05&&t.max_noise>r.max_noise-.1&&r.tot_noise+r.over_noise<t.tot_noise+t.over_noise||r.max_noise>0&&t.max_noise>-.1&&t.max_noise>r.max_noise-.15&&r.tot_noise+r.over_noise+r.over_noise<t.tot_noise+t.over_noise+t.over_noise;break;case 5:o=r.over_noise<t.over_noise||A.EQ(r.over_noise,t.over_noise)&&r.tot_noise<t.tot_noise;break;case 6:o=r.over_noise<t.over_noise||A.EQ(r.over_noise,t.over_noise)&&(r.max_noise<t.max_noise||A.EQ(r.max_noise,t.max_noise)&&r.tot_noise<=t.tot_noise);break;case 7:o=r.over_count<t.over_count||r.over_noise<t.over_noise}return 0==t.over_count&&(o=o&&r.bits<t.bits),o}function c(e,a,i,o,l){var c=e.internal_flags;!function(e,t,r,a,i){var o,s=e.internal_flags;o=0==t.scalefac_scale?1.2968395546510096:1.6817928305074292;for(var l=0,c=0;c<t.sfbmax;c++)l<r[c]&&(l=r[c]);var f=s.noise_shaping_amp;switch(3==f&&n(),f){case 2:break;case 1:l>1?l=Math.pow(l,.5):l*=.95;break;case 0:default:l>1?l=1:l*=.95}var u=0;for(c=0;c<t.sfbmax;c++){var p,_=t.width[c];if(u+=_,!(r[c]<l)){for(0!=(2&s.substep_shaping)&&n(),t.scalefac[c]++,p=-_;p<0;p++)a[u+p]*=o,a[u+p]>t.xrpow_max&&(t.xrpow_max=a[u+p]);if(2==s.noise_shaping_amp)return}}}(e,a,i,o);var f=s(a);return!(f||(f=2==c.mode_gr?r.scale_bitcount(a):r.scale_bitcount_lsf(c,a))&&(c.noise_shaping>1&&(p.fill(c.pseudohalf,0),0==a.scalefac_scale?(function(e,n){for(var r=0,a=0;a<e.sfbmax;a++){var i=e.width[a],o=e.scalefac[a];if(0!=e.preflag&&(o+=t.pretab[a]),r+=i,0!=(1&o)){o++;for(var s=-i;s<0;s++)n[r+s]*=1.2968395546510096,n[r+s]>e.xrpow_max&&(e.xrpow_max=n[r+s])}e.scalefac[a]=o>>1}e.preflag=0,e.scalefac_scale=1}(a,o),f=!1):a.block_type==z.SHORT_TYPE&&c.subblock_gain>0&&(f=function(e,n,r){var a,i=n.scalefac;for(a=0;a<n.sfb_lmax;a++)if(i[a]>=16)return!0;for(var o=0;o<3;o++){var s=0,l=0;for(a=n.sfb_lmax+o;a<n.sfbdivide;a+=3)s<i[a]&&(s=i[a]);for(;a<n.sfbmax;a+=3)l<i[a]&&(l=i[a]);if(!(s<16&&l<8)){if(n.subblock_gain[o]>=7)return!0;n.subblock_gain[o]++;var c=e.scalefac_band.l[n.sfb_lmax];for(a=n.sfb_lmax+o;a<n.sfbmax;a+=3){var f=n.width[a],u=i[a];if((u-=4>>n.scalefac_scale)>=0)i[a]=u,c+=3*f;else{i[a]=0;var p=210+(u<<n.scalefac_scale+1);d=t.IPOW20(p),c+=f*(o+1);for(var _=-f;_<0;_++)r[c+_]*=d,r[c+_]>n.xrpow_max&&(n.xrpow_max=r[c+_]);c+=f*(3-o-1)}}var d=t.IPOW20(202);for(c+=n.width[a]*(o+1),_=-n.width[a];_<0;_++)r[c+_]*=d,r[c+_]>n.xrpow_max&&(n.xrpow_max=r[c+_])}}return!1}(c,a,o)||s(a))),f||(f=2==c.mode_gr?r.scale_bitcount(a):r.scale_bitcount_lsf(c,a)),f))}this.setModules=function(n,i,o,s){e=i,this.rv=i,t=o,this.qupvt=o,r=s,a.setModules(t,r)},this.init_xrpow=function(e,t,n){var r=0,a=0|t.max_nonzero_coeff;if(t.xrpow_max=0,p.fill(n,a,576,0),(r=function(e,t,n,r){r=0;for(var a=0;a<=n;++a){var i=Math.abs(e.xr[a]);r+=i,t[a]=Math.sqrt(i*Math.sqrt(i)),t[a]>e.xrpow_max&&(e.xrpow_max=t[a])}return r}(t,n,a,r))>1e-20){var i=0;0!=(2&e.substep_shaping)&&(i=1);for(var o=0;o<t.psymax;o++)e.pseudohalf[o]=i;return!0}return p.fill(t.l3_enc,0,576,0),!1},this.init_outer_loop=function(e,r){r.part2_3_length=0,r.big_values=0,r.count1=0,r.global_gain=210,r.scalefac_compress=0,r.table_select[0]=0,r.table_select[1]=0,r.table_select[2]=0,r.subblock_gain[0]=0,r.subblock_gain[1]=0,r.subblock_gain[2]=0,r.subblock_gain[3]=0,r.region0_count=0,r.region1_count=0,r.preflag=0,r.scalefac_scale=0,r.count1table_select=0,r.part2_length=0,r.sfb_lmax=z.SBPSY_l,r.sfb_smin=z.SBPSY_s,r.psy_lmax=e.sfb21_extra?z.SBMAX_l:z.SBPSY_l,r.psymax=r.psy_lmax,r.sfbmax=r.sfb_lmax,r.sfbdivide=11;for(var a=0;a<z.SBMAX_l;a++)r.width[a]=e.scalefac_band.l[a+1]-e.scalefac_band.l[a],r.window[a]=3;if(r.block_type==z.SHORT_TYPE){var i=o(576);r.sfb_smin=0,r.sfb_lmax=0,0!=r.mixed_block_flag&&n(),r.psymax=r.sfb_lmax+3*((e.sfb21_extra?z.SBMAX_s:z.SBPSY_s)-r.sfb_smin),r.sfbmax=r.sfb_lmax+3*(z.SBPSY_s-r.sfb_smin),r.sfbdivide=r.sfbmax-18,r.psy_lmax=r.sfb_lmax;var s=e.scalefac_band.l[r.sfb_lmax];for(_.arraycopy(r.xr,0,i,0,576),a=r.sfb_smin;a<z.SBMAX_s;a++)for(var l=e.scalefac_band.s[a],c=e.scalefac_band.s[a+1],f=0;f<3;f++)for(var u=l;u<c;u++)r.xr[s++]=i[3*u+f];var d=r.sfb_lmax;for(a=r.sfb_smin;a<z.SBMAX_s;a++)r.width[d]=r.width[d+1]=r.width[d+2]=e.scalefac_band.s[a+1]-e.scalefac_band.s[a],r.window[d]=0,r.window[d+1]=1,r.window[d+2]=2,d+=3}r.count1bits=0,r.sfb_partition_table=t.nr_of_sfb_block[0][0],r.slen[0]=0,r.slen[1]=0,r.slen[2]=0,r.slen[3]=0,r.max_nonzero_coeff=575,p.fill(r.scalefac,0),function(e,n){var r=e.ATH,a=n.xr;if(n.block_type!=z.SHORT_TYPE)for(var i=!1,o=z.PSFB21-1;o>=0&&!i;o--){var s=e.scalefac_band.psfb21[o],l=e.scalefac_band.psfb21[o+1],c=t.athAdjust(r.adjust,r.psfb21[o],r.floor);e.nsPsy.longfact[21]>1e-12&&(c*=e.nsPsy.longfact[21]);for(var f=l-1;f>=s;f--){if(!(Math.abs(a[f])<c)){i=!0;break}a[f]=0}}else for(var u=0;u<3;u++)for(i=!1,o=z.PSFB12-1;o>=0&&!i;o--){l=(s=3*e.scalefac_band.s[12]+(e.scalefac_band.s[13]-e.scalefac_band.s[12])*u+(e.scalefac_band.psfb12[o]-e.scalefac_band.psfb12[0]))+(e.scalefac_band.psfb12[o+1]-e.scalefac_band.psfb12[o]);var p=t.athAdjust(r.adjust,r.psfb12[o],r.floor);for(e.nsPsy.shortfact[12]>1e-12&&(p*=e.nsPsy.shortfact[12]),f=l-1;f>=s;f--){if(!(Math.abs(a[f])<p)){i=!0;break}a[f]=0}}}(e,r)},i.BINSEARCH_NONE=new i(0),i.BINSEARCH_UP=new i(1),i.BINSEARCH_DOWN=new i(2),this.outer_loop=function(e,a,s,f,u,p){var d=e.internal_flags,h=new D,v=o(576),b=o(H.SFBMAX),g=new C,y=new N,w=9999999,S=!1;if(function(e,t,a,o,s){var l,c=e.CurrentStep[o],f=!1,u=e.OldValue[o],p=i.BINSEARCH_NONE;for(t.global_gain=u,a-=t.part2_length;;){var _;if(l=r.count_bits(e,s,t,null),1==c||l==a)break;l>a?(p==i.BINSEARCH_DOWN&&(f=!0),f&&(c/=2),p=i.BINSEARCH_UP,_=c):(p==i.BINSEARCH_UP&&(f=!0),f&&(c/=2),p=i.BINSEARCH_DOWN,_=-c),t.global_gain+=_,t.global_gain<0&&n(),t.global_gain>255&&n()}for(;l>a&&t.global_gain<255;)t.global_gain++,l=r.count_bits(e,s,t,null);e.CurrentStep[o]=u-t.global_gain>=4?4:2,e.OldValue[o]=t.global_gain,t.part2_3_length=l}(d,a,p,u,f),0==d.noise_shaping)return 100;t.calc_noise(a,s,b,g,y),g.bits=a.part2_3_length,h.assign(a);var x=0;for(_.arraycopy(f,0,v,0,576);!S;){do{var k,A=new C,M=255;if(k=0!=(2&d.substep_shaping)?20:3,d.sfb21_extra&&n(),!c(e,h,b,f))break;0!=h.scalefac_scale&&(M=254);var T=p-h.part2_length;if(T<=0)break;for(;(h.part2_3_length=r.count_bits(d,f,h,y))>T&&h.global_gain<=M;)h.global_gain++;if(h.global_gain>M)break;if(0==g.over_count){for(;(h.part2_3_length=r.count_bits(d,f,h,y))>w&&h.global_gain<=M;)h.global_gain++;if(h.global_gain>M)break}if(t.calc_noise(h,s,b,A,y),A.bits=h.part2_3_length,0!=(l(a.block_type!=z.SHORT_TYPE?e.quant_comp:e.quant_comp_short,g,A)?1:0))w=a.part2_3_length,g=A,a.assign(h),x=0,_.arraycopy(f,0,v,0,576);else if(0==d.full_outer_loop){if(++x>k&&0==g.over_count)break;d.noise_shaping_amp,d.noise_shaping_amp}}while(h.global_gain+h.scalefac_scale<255);3==d.noise_shaping_amp?n():S=!0}return e.VBR==m.vbr_rh||e.VBR==m.vbr_mtrh?_.arraycopy(v,0,f,0,576):0!=(1&d.substep_shaping)&&n(),g.over_count},this.iteration_finish_one=function(t,n,a){var i=t.l3_side,o=i.tt[n][a];r.best_scalefac_store(t,n,a,i),1==t.use_best_huffman&&r.best_huffman_divide(t,o),e.ResvAdjust(t,o)}}function F(){var e=[-.1482523854003001,32.308141959636465,296.40344946382766,883.1344870032432,11113.947376231741,1057.2713659324597,305.7402417275812,30.825928907280012,3.8533188138216365,59.42900443849514,709.5899960123345,5281.91112291017,-5829.66483675846,-817.6293103748613,-76.91656988279972,-4.594269939176596,.9063471690191471,.1960342806591213,-.15466694054279598,34.324387823855965,301.8067566458425,817.599602898885,11573.795901679885,1181.2520595540152,321.59731579894424,31.232021761053772,3.7107095756221318,53.650946155329365,684.167428119626,5224.56624370173,-6366.391851890084,-908.9766368219582,-89.83068876699639,-5.411397422890401,.8206787908286602,.3901806440322567,-.16070888947830023,36.147034243915876,304.11815768187864,732.7429163887613,11989.60988270091,1300.012278487897,335.28490093152146,31.48816102859945,3.373875931311736,47.232241542899175,652.7371796173471,5132.414255594984,-6909.087078780055,-1001.9990371107289,-103.62185754286375,-6.104916304710272,.7416505462720353,.5805693545089249,-.16636367662261495,37.751650073343995,303.01103387567713,627.9747488785183,12358.763425278165,1412.2779918482834,346.7496836825721,31.598286663170416,3.1598635433980946,40.57878626349686,616.1671130880391,5007.833007176154,-7454.040671756168,-1095.7960341867115,-118.24411666465777,-6.818469345853504,.6681786379192989,.7653668647301797,-.1716176790982088,39.11551877123304,298.3413246578966,503.5259106886539,12679.589408408976,1516.5821921214542,355.9850766329023,31.395241710249053,2.9164211881972335,33.79716964664243,574.8943997801362,4853.234992253242,-7997.57021486075,-1189.7624067269965,-133.6444792601766,-7.7202770609839915,.5993769336819237,.9427934736519954,-.17645823955292173,40.21879108166477,289.9982036694474,359.3226160751053,12950.259102786438,1612.1013903507662,362.85067106591504,31.045922092242872,2.822222032597987,26.988862316190684,529.8996541764288,4671.371946949588,-8535.899136645805,-1282.5898586244496,-149.58553632943463,-8.643494270763135,.5345111359507916,1.111140466039205,-.36174739330527045,41.04429910497807,277.5463268268618,195.6386023135583,13169.43812144731,1697.6433561479398,367.40983966190305,30.557037410382826,2.531473372857427,20.070154905927314,481.50208566532336,4464.970341588308,-9065.36882077239,-1373.62841526722,-166.1660487028118,-9.58289321133207,.4729647758913199,1.268786568327291,-.36970682634889585,41.393213350082036,261.2935935556502,12.935476055240873,13336.131683328815,1772.508612059496,369.76534388639965,29.751323653701338,2.4023193045459172,13.304795348228817,430.5615775526625,4237.0568611071185,-9581.931701634761,-1461.6913552409758,-183.12733958476446,-10.718010163869403,.41421356237309503,1.414213562373095,-.37677560326535325,41.619486213528496,241.05423794991074,-187.94665032361226,13450.063605744153,1836.153896465782,369.4908799925761,29.001847876923147,2.0714759319987186,6.779591200894186,377.7767837205709,3990.386575512536,-10081.709459700915,-1545.947424837898,-200.3762958015653,-11.864482073055006,.3578057213145241,1.546020906725474,-.3829366947518991,41.1516456456653,216.47684307105183,-406.1569483347166,13511.136535077321,1887.8076599260432,367.3025214564151,28.136213436723654,1.913880671464418,.3829366947518991,323.85365704338597,3728.1472257487526,-10561.233882199509,-1625.2025997821418,-217.62525175416,-13.015432208941645,.3033466836073424,1.66293922460509,-.5822628872992417,40.35639251440489,188.20071124269245,-640.2706748618148,13519.21490106562,1927.6022433578062,362.8197642637487,26.968821921868447,1.7463817695935329,-5.62650678237171,269.3016715297017,3453.386536448852,-11016.145278780888,-1698.6569643425091,-234.7658734267683,-14.16351421663124,.2504869601913055,1.76384252869671,-.5887180101749253,39.23429103868072,155.76096234403798,-889.2492977967378,13475.470561874661,1955.0535223723712,356.4450994756727,25.894952980042156,1.5695032905781554,-11.181939564328772,214.80884394039484,3169.1640829158237,-11443.321309975563,-1765.1588461316153,-251.68908574481912,-15.49755935939164,.198912367379658,1.847759065022573,-.7912582233652842,37.39369355329111,119.699486012458,-1151.0956593239027,13380.446257078214,1970.3952110853447,348.01959814116185,24.731487364283044,1.3850130831637748,-16.421408865300393,161.05030052864092,2878.3322807850063,-11838.991423510031,-1823.985884688674,-268.2854986386903,-16.81724543849939,.1483359875383474,1.913880671464418,-.7960642926861912,35.2322109610459,80.01928065061526,-1424.0212633405113,13235.794061869668,1973.804052543835,337.9908651258184,23.289159354463873,1.3934255946442087,-21.099669467133474,108.48348407242611,2583.700758091299,-12199.726194855148,-1874.2780658979746,-284.2467154529415,-18.11369784385905,.09849140335716425,1.961570560806461,-.998795456205172,32.56307803611191,36.958364584370486,-1706.075448829146,13043.287458812016,1965.3831106103316,326.43182772364605,22.175018750622293,1.198638339011324,-25.371248002043963,57.53505923036915,2288.41886619975,-12522.674544337233,-1914.8400385312243,-299.26241273417224,-19.37805630698734,.04912684976946725,1.990369453344394,.035780907*d.SQRT2*.5/2384e-9,.017876148*d.SQRT2*.5/2384e-9,.003134727*d.SQRT2*.5/2384e-9,.002457142*d.SQRT2*.5/2384e-9,971317e-9*d.SQRT2*.5/2384e-9,218868e-9*d.SQRT2*.5/2384e-9,101566e-9*d.SQRT2*.5/2384e-9,13828e-9*d.SQRT2*.5/2384e-9,12804.797818791945,1945.5515939597317,313.4244966442953,20.801593959731544,1995.1556208053692,9.000838926174497,-29.20218120805369],t=[[2382191739347913e-28,6423305872147834e-28,9400849094049688e-28,1122435026096556e-27,1183840321267481e-27,1122435026096556e-27,940084909404969e-27,6423305872147839e-28,2382191739347918e-28,5456116108943412e-27,4878985199565852e-27,4240448995017367e-27,3559909094758252e-27,2858043359288075e-27,2156177623817898e-27,1475637723558783e-27,8371015190102974e-28,2599706096327376e-28,-5456116108943412e-27,-4878985199565852e-27,-4240448995017367e-27,-3559909094758252e-27,-2858043359288076e-27,-2156177623817898e-27,-1475637723558783e-27,-8371015190102975e-28,-2599706096327376e-28,-2382191739347923e-28,-6423305872147843e-28,-9400849094049696e-28,-1122435026096556e-27,-1183840321267481e-27,-1122435026096556e-27,-9400849094049694e-28,-642330587214784e-27,-2382191739347918e-28],[2382191739347913e-28,6423305872147834e-28,9400849094049688e-28,1122435026096556e-27,1183840321267481e-27,1122435026096556e-27,9400849094049688e-28,6423305872147841e-28,2382191739347918e-28,5456116108943413e-27,4878985199565852e-27,4240448995017367e-27,3559909094758253e-27,2858043359288075e-27,2156177623817898e-27,1475637723558782e-27,8371015190102975e-28,2599706096327376e-28,-5461314069809755e-27,-4921085770524055e-27,-4343405037091838e-27,-3732668368707687e-27,-3093523840190885e-27,-2430835727329465e-27,-1734679010007751e-27,-974825365660928e-27,-2797435120168326e-28,0,0,0,0,0,0,-2283748241799531e-28,-4037858874020686e-28,-2146547464825323e-28],[.1316524975873958,.414213562373095,.7673269879789602,1.091308501069271,1.303225372841206,1.56968557711749,1.920982126971166,2.414213562373094,3.171594802363212,4.510708503662055,7.595754112725146,22.90376554843115,.984807753012208,.6427876096865394,.3420201433256688,.9396926207859084,-.1736481776669303,-.7660444431189779,.8660254037844387,.5,-.5144957554275265,-.4717319685649723,-.3133774542039019,-.1819131996109812,-.09457419252642064,-.04096558288530405,-.01419856857247115,-.003699974673760037,.8574929257125442,.8817419973177052,.9496286491027329,.9833145924917901,.9955178160675857,.9991605581781475,.999899195244447,.9999931550702802],[0,0,0,0,0,0,2283748241799531e-28,4037858874020686e-28,2146547464825323e-28,5461314069809755e-27,4921085770524055e-27,4343405037091838e-27,3732668368707687e-27,3093523840190885e-27,2430835727329466e-27,1734679010007751e-27,974825365660928e-27,2797435120168326e-28,-5456116108943413e-27,-4878985199565852e-27,-4240448995017367e-27,-3559909094758253e-27,-2858043359288075e-27,-2156177623817898e-27,-1475637723558782e-27,-8371015190102975e-28,-2599706096327376e-28,-2382191739347913e-28,-6423305872147834e-28,-9400849094049688e-28,-1122435026096556e-27,-1183840321267481e-27,-1122435026096556e-27,-9400849094049688e-28,-6423305872147841e-28,-2382191739347918e-28]],r=t[z.SHORT_TYPE],a=t[z.SHORT_TYPE],i=t[z.SHORT_TYPE],s=t[z.SHORT_TYPE],l=[0,1,16,17,8,9,24,25,4,5,20,21,12,13,28,29,2,3,18,19,10,11,26,27,6,7,22,23,14,15,30,31];function c(t,n,r){for(var a,i,o,s=10,l=n+238-14-286,c=-15;c<0;c++){var f,u,p;f=e[s+-10],u=t[l+-224]*f,p=t[n+224]*f,f=e[s+-9],u+=t[l+-160]*f,p+=t[n+160]*f,f=e[s+-8],u+=t[l+-96]*f,p+=t[n+96]*f,f=e[s+-7],u+=t[l+-32]*f,p+=t[n+32]*f,f=e[s+-6],u+=t[l+32]*f,p+=t[n+-32]*f,f=e[s+-5],u+=t[l+96]*f,p+=t[n+-96]*f,f=e[s+-4],u+=t[l+160]*f,p+=t[n+-160]*f,f=e[s+-3],u+=t[l+224]*f,p+=t[n+-224]*f,f=e[s+-2],u+=t[n+-256]*f,p-=t[l+256]*f,f=e[s+-1],u+=t[n+-192]*f,p-=t[l+192]*f,f=e[s+0],u+=t[n+-128]*f,p-=t[l+128]*f,f=e[s+1],u+=t[n+-64]*f,p-=t[l+64]*f,f=e[s+2],u+=t[n+0]*f,p-=t[l+0]*f,f=e[s+3],u+=t[n+64]*f,p-=t[l+-64]*f,f=e[s+4],u+=t[n+128]*f,p-=t[l+-128]*f,f=e[s+5],u+=t[n+192]*f,f=(p-=t[l+-192]*f)-(u*=e[s+6]),r[30+2*c]=p+u,r[31+2*c]=e[s+7]*f,s+=18,n--,l++}p=t[n+-16]*e[s+-10],u=t[n+-32]*e[s+-2],p+=(t[n+-48]-t[n+16])*e[s+-9],u+=t[n+-96]*e[s+-1],p+=(t[n+-80]+t[n+48])*e[s+-8],u+=t[n+-160]*e[s+0],p+=(t[n+-112]-t[n+80])*e[s+-7],u+=t[n+-224]*e[s+1],p+=(t[n+-144]+t[n+112])*e[s+-6],u-=t[n+32]*e[s+2],p+=(t[n+-176]-t[n+144])*e[s+-5],u-=t[n+96]*e[s+3],p+=(t[n+-208]+t[n+176])*e[s+-4],u-=t[n+160]*e[s+4],p+=(t[n+-240]-t[n+208])*e[s+-3],a=(u-=t[n+224])-p,i=u+p,p=r[14],u=r[15]-p,r[31]=i+p,r[30]=a+u,r[15]=a-u,r[14]=i-p,o=r[28]-r[0],r[0]+=r[28],r[28]=o*e[s+-36+7],o=r[29]-r[1],r[1]+=r[29],r[29]=o*e[s+-36+7],o=r[26]-r[2],r[2]+=r[26],r[26]=o*e[s+-72+7],o=r[27]-r[3],r[3]+=r[27],r[27]=o*e[s+-72+7],o=r[24]-r[4],r[4]+=r[24],r[24]=o*e[s+-108+7],o=r[25]-r[5],r[5]+=r[25],r[25]=o*e[s+-108+7],o=r[22]-r[6],r[6]+=r[22],r[22]=o*d.SQRT2,o=r[23]-r[7],r[7]+=r[23],r[23]=o*d.SQRT2-r[7],r[7]-=r[6],r[22]-=r[7],r[23]-=r[22],o=r[6],r[6]=r[31]-o,r[31]=r[31]+o,o=r[7],r[7]=r[30]-o,r[30]=r[30]+o,o=r[22],r[22]=r[15]-o,r[15]=r[15]+o,o=r[23],r[23]=r[14]-o,r[14]=r[14]+o,o=r[20]-r[8],r[8]+=r[20],r[20]=o*e[s+-180+7],o=r[21]-r[9],r[9]+=r[21],r[21]=o*e[s+-180+7],o=r[18]-r[10],r[10]+=r[18],r[18]=o*e[s+-216+7],o=r[19]-r[11],r[11]+=r[19],r[19]=o*e[s+-216+7],o=r[16]-r[12],r[12]+=r[16],r[16]=o*e[s+-252+7],o=r[17]-r[13],r[13]+=r[17],r[17]=o*e[s+-252+7],o=-r[20]+r[24],r[20]+=r[24],r[24]=o*e[s+-216+7],o=-r[21]+r[25],r[21]+=r[25],r[25]=o*e[s+-216+7],o=r[4]-r[8],r[4]+=r[8],r[8]=o*e[s+-216+7],o=r[5]-r[9],r[5]+=r[9],r[9]=o*e[s+-216+7],o=r[0]-r[12],r[0]+=r[12],r[12]=o*e[s+-72+7],o=r[1]-r[13],r[1]+=r[13],r[13]=o*e[s+-72+7],o=r[16]-r[28],r[16]+=r[28],r[28]=o*e[s+-72+7],o=-r[17]+r[29],r[17]+=r[29],r[29]=o*e[s+-72+7],o=d.SQRT2*(r[2]-r[10]),r[2]+=r[10],r[10]=o,o=d.SQRT2*(r[3]-r[11]),r[3]+=r[11],r[11]=o,o=d.SQRT2*(-r[18]+r[26]),r[18]+=r[26],r[26]=o-r[18],o=d.SQRT2*(-r[19]+r[27]),r[19]+=r[27],r[27]=o-r[19],o=r[2],r[19]-=r[3],r[3]-=o,r[2]=r[31]-o,r[31]+=o,o=r[3],r[11]-=r[19],r[18]-=o,r[3]=r[30]-o,r[30]+=o,o=r[18],r[27]-=r[11],r[19]-=o,r[18]=r[15]-o,r[15]+=o,o=r[19],r[10]-=o,r[19]=r[14]-o,r[14]+=o,o=r[10],r[11]-=o,r[10]=r[23]-o,r[23]+=o,o=r[11],r[26]-=o,r[11]=r[22]-o,r[22]+=o,o=r[26],r[27]-=o,r[26]=r[7]-o,r[7]+=o,o=r[27],r[27]=r[6]-o,r[6]+=o,o=d.SQRT2*(r[0]-r[4]),r[0]+=r[4],r[4]=o,o=d.SQRT2*(r[1]-r[5]),r[1]+=r[5],r[5]=o,o=d.SQRT2*(r[16]-r[20]),r[16]+=r[20],r[20]=o,o=d.SQRT2*(r[17]-r[21]),r[17]+=r[21],r[21]=o,o=-d.SQRT2*(r[8]-r[12]),r[8]+=r[12],r[12]=o-r[8],o=-d.SQRT2*(r[9]-r[13]),r[9]+=r[13],r[13]=o-r[9],o=-d.SQRT2*(r[25]-r[29]),r[25]+=r[29],r[29]=o-r[25],o=-d.SQRT2*(r[24]+r[28]),r[24]-=r[28],r[28]=o-r[24],o=r[24]-r[16],r[24]=o,o=r[20]-o,r[20]=o,o=r[28]-o,r[28]=o,o=r[25]-r[17],r[25]=o,o=r[21]-o,r[21]=o,o=r[29]-o,r[29]=o,o=r[17]-r[1],r[17]=o,o=r[9]-o,r[9]=o,o=r[25]-o,r[25]=o,o=r[5]-o,r[5]=o,o=r[21]-o,r[21]=o,o=r[13]-o,r[13]=o,o=r[29]-o,r[29]=o,o=r[1]-r[0],r[1]=o,o=r[16]-o,r[16]=o,o=r[17]-o,r[17]=o,o=r[8]-o,r[8]=o,o=r[9]-o,r[9]=o,o=r[24]-o,r[24]=o,o=r[25]-o,r[25]=o,o=r[4]-o,r[4]=o,o=r[5]-o,r[5]=o,o=r[20]-o,r[20]=o,o=r[21]-o,r[21]=o,o=r[12]-o,r[12]=o,o=r[13]-o,r[13]=o,o=r[28]-o,r[28]=o,o=r[29]-o,r[29]=o,o=r[0],r[0]+=r[31],r[31]-=o,o=r[1],r[1]+=r[30],r[30]-=o,o=r[16],r[16]+=r[15],r[15]-=o,o=r[17],r[17]+=r[14],r[14]-=o,o=r[8],r[8]+=r[23],r[23]-=o,o=r[9],r[9]+=r[22],r[22]-=o,o=r[24],r[24]+=r[7],r[7]-=o,o=r[25],r[25]+=r[6],r[6]-=o,o=r[4],r[4]+=r[27],r[27]-=o,o=r[5],r[5]+=r[26],r[26]-=o,o=r[20],r[20]+=r[11],r[11]-=o,o=r[21],r[21]+=r[10],r[10]-=o,o=r[12],r[12]+=r[19],r[19]-=o,o=r[13],r[13]+=r[18],r[18]-=o,o=r[28],r[28]+=r[3],r[3]-=o,o=r[29],r[29]+=r[2],r[2]-=o}function f(e,n){for(var r=0;r<3;r++){var a,i,o,s,l,c;i=(s=e[n+6]*t[z.SHORT_TYPE][0]-e[n+15])+(a=e[n+0]*t[z.SHORT_TYPE][2]-e[n+9]),o=s-a,l=(s=e[n+15]*t[z.SHORT_TYPE][0]+e[n+6])+(a=e[n+9]*t[z.SHORT_TYPE][2]+e[n+0]),c=-s+a,a=2069978111953089e-26*(e[n+3]*t[z.SHORT_TYPE][1]-e[n+12]),s=2069978111953089e-26*(e[n+12]*t[z.SHORT_TYPE][1]+e[n+3]),e[n+0]=190752519173728e-25*i+a,e[n+15]=190752519173728e-25*-l+s,o=.8660254037844387*o*1907525191737281e-26,l=.5*l*1907525191737281e-26+s,e[n+3]=o-l,e[n+6]=o+l,i=.5*i*1907525191737281e-26-a,c=.8660254037844387*c*1907525191737281e-26,e[n+9]=i+c,e[n+12]=i-c,n++}}this.mdct_sub48=function(e,u,d){for(var h,v,m,b,g,y,w,S,x,k,A,M,T,R,C,E,B,O,L,I,P,$=u,N=286,D=0;D<e.channels_out;D++){for(var H=0;H<e.mode_gr;H++){for(var j,F=e.l3_side.tt[H][D],V=F.xr,U=0,X=e.sb_sample[D][1-H],q=0,Y=0;Y<9;Y++)for(c($,N,X[q]),c($,N+32,X[q+1]),q+=2,N+=64,j=1;j<32;j+=2)X[q-1][j]*=-1;for(j=0;j<32;j++,U+=18){var W=F.block_type,G=e.sb_sample[D][H],K=e.sb_sample[D][1-H];if(0!=F.mixed_block_flag&&j<2&&(W=0),e.amp_filter[j]<1e-12)p.fill(V,U+0,U+18,0);else if(e.amp_filter[j]<1&&n(),W==z.SHORT_TYPE){for(Y=-3;Y<0;Y++){var Z=t[z.SHORT_TYPE][Y+3];V[U+3*Y+9]=G[9+Y][l[j]]*Z-G[8-Y][l[j]],V[U+3*Y+18]=G[14-Y][l[j]]*Z+G[15+Y][l[j]],V[U+3*Y+10]=G[15+Y][l[j]]*Z-G[14-Y][l[j]],V[U+3*Y+19]=K[2-Y][l[j]]*Z+K[3+Y][l[j]],V[U+3*Y+11]=K[3+Y][l[j]]*Z-K[2-Y][l[j]],V[U+3*Y+20]=K[8-Y][l[j]]*Z+K[9+Y][l[j]]}f(V,U)}else{var Q=o(18);for(Y=-9;Y<0;Y++){var J,ee;J=t[W][Y+27]*K[Y+9][l[j]]+t[W][Y+36]*K[8-Y][l[j]],ee=t[W][Y+9]*G[Y+9][l[j]]-t[W][Y+18]*G[8-Y][l[j]],Q[Y+9]=J-ee*r[3+Y+9],Q[Y+18]=J*r[3+Y+9]+ee}h=V,v=U,b=void 0,g=void 0,y=void 0,w=void 0,S=void 0,x=void 0,k=void 0,A=void 0,M=void 0,T=void 0,R=void 0,C=void 0,E=void 0,B=void 0,O=void 0,L=void 0,I=void 0,P=void 0,y=(m=Q)[17]-m[9],S=m[15]-m[11],x=m[14]-m[12],k=m[0]+m[8],A=m[1]+m[7],M=m[2]+m[6],T=m[3]+m[5],h[v+17]=k+M-T-(A-m[4]),g=(k+M-T)*a[19]+(A-m[4]),b=(y-S-x)*a[18],h[v+5]=b+g,h[v+6]=b-g,w=(m[16]-m[10])*a[18],A=A*a[19]+m[4],b=y*a[12]+w+S*a[13]+x*a[14],g=-k*a[16]+A-M*a[17]+T*a[15],h[v+1]=b+g,h[v+2]=b-g,b=y*a[13]-w-S*a[14]+x*a[12],g=-k*a[17]+A-M*a[15]+T*a[16],h[v+9]=b+g,h[v+10]=b-g,b=y*a[14]-w+S*a[12]-x*a[13],g=k*a[15]-A+M*a[16]-T*a[17],h[v+13]=b+g,h[v+14]=b-g,R=m[8]-m[0],E=m[6]-m[2],B=m[5]-m[3],O=m[17]+m[9],L=m[16]+m[10],I=m[15]+m[11],P=m[14]+m[12],h[v+0]=O+I+P+(L+m[13]),b=(O+I+P)*a[19]-(L+m[13]),g=(R-E+B)*a[18],h[v+11]=b+g,h[v+12]=b-g,C=(m[7]-m[1])*a[18],L=m[13]-L*a[19],b=O*a[15]-L+I*a[16]+P*a[17],g=R*a[14]+C+E*a[12]+B*a[13],h[v+3]=b+g,h[v+4]=b-g,b=-O*a[17]+L-I*a[15]-P*a[16],g=R*a[13]+C-E*a[14]-B*a[12],h[v+7]=b+g,h[v+8]=b-g,b=-O*a[16]+L-I*a[17]-P*a[15],g=R*a[12]-C+E*a[13]-B*a[14],h[v+15]=b+g,h[v+16]=b-g}if(W!=z.SHORT_TYPE&&0!=j)for(Y=7;Y>=0;--Y){var te,ne;te=V[U+Y]*i[20+Y]+V[U+-1-Y]*s[28+Y],ne=V[U+Y]*s[28+Y]-V[U+-1-Y]*i[20+Y],V[U+-1-Y]=te,V[U+Y]=ne}}}if($=d,N=286,1==e.mode_gr)for(var re=0;re<18;re++)_.arraycopy(e.sb_sample[D][1][re],0,e.sb_sample[D][0][re],0,32)}}}function V(){this.thm=new q,this.en=new q}function z(){z.FFTOFFSET;var e=z.MPG_MD_MS_LR,t=null;this.psy=null;var r=null,a=null;this.setModules=function(e,n,i,o){t=e,this.psy=n,r=n,a=o};var s=new F;this.lame_encode_mp3_frame=function(c,f,p,_,d,h){var v,g=u([2,2]);g[0][0]=new V,g[0][1]=new V,g[1][0]=new V,g[1][1]=new V;var y,w=u([2,2]);w[0][0]=new V,w[0][1]=new V,w[1][0]=new V,w[1][1]=new V;var S,x,k,A=[null,null],M=c.internal_flags,T=l([2,4]),R=[[0,0],[0,0]],C=[[0,0],[0,0]];if(A[0]=f,A[1]=p,0==M.lame_encode_frame_init&&function(e,t){var n,r,a=e.internal_flags;if(0==a.lame_encode_frame_init){var i,l,c=o(2014),f=o(2014);for(a.lame_encode_frame_init=1,i=0,l=0;i<286+576*(1+a.mode_gr);++i)i<576*a.mode_gr?(c[i]=0,2==a.channels_out&&(f[i]=0)):(c[i]=t[0][l],2==a.channels_out&&(f[i]=t[1][l]),++l);for(r=0;r<a.mode_gr;r++)for(n=0;n<a.channels_out;n++)a.l3_side.tt[r][n].block_type=z.SHORT_TYPE;s.mdct_sub48(a,c,f)}}(c,A),M.padding=0,(M.slot_lag-=M.frac_SpF)<0&&(M.slot_lag+=c.out_samplerate,M.padding=1),0!=M.psymodel){var E,B=[null,null],O=0,L=i(2);for(k=0;k<M.mode_gr;k++){for(x=0;x<M.channels_out;x++)B[x]=A[x],O=576+576*k-z.FFTOFFSET;if(c.VBR==m.vbr_mtrh||c.VBR==m.vbr_mt?n():E=r.L3psycho_anal_ns(c,B,O,k,g,w,R[k],C[k],T[k],L),0!=E)return-4;for(c.mode==b.JOINT_STEREO&&n(),x=0;x<M.channels_out;x++){var I=M.l3_side.tt[k][x];I.block_type=L[x],I.mixed_block_flag=0}}}else n();if(function(e){var t,r;if(0!=e.ATH.useAdjust)if(r=e.loudness_sq[0][0],t=e.loudness_sq[1][0],2==e.channels_out?n():(r+=r,t+=t),2==e.mode_gr&&(r=Math.max(r,t)),r*=.5,(r*=e.ATH.aaSensitivityP)>.03125)e.ATH.adjust>=1?e.ATH.adjust=1:e.ATH.adjust<e.ATH.adjustLimit&&(e.ATH.adjust=e.ATH.adjustLimit),e.ATH.adjustLimit=1;else{var a=31.98*r+625e-6;e.ATH.adjust>=a?(e.ATH.adjust*=.075*a+.925,e.ATH.adjust<a&&(e.ATH.adjust=a)):e.ATH.adjustLimit>=a?e.ATH.adjust=a:e.ATH.adjust<e.ATH.adjustLimit&&(e.ATH.adjust=e.ATH.adjustLimit),e.ATH.adjustLimit=a}else e.ATH.adjust=1}(M),s.mdct_sub48(M,A[0],A[1]),M.mode_ext=z.MPG_MD_LR_LR,c.force_ms?M.mode_ext=z.MPG_MD_MS_LR:c.mode==b.JOINT_STEREO&&n(),M.mode_ext==e?(y=w,S=C):(y=g,S=R),c.analysis&&null!=M.pinfo&&n(),c.VBR==m.vbr_off||c.VBR==m.vbr_abr){var P,$;for(P=0;P<18;P++)M.nsPsy.pefirbuf[P]=M.nsPsy.pefirbuf[P+1];for($=0,k=0;k<M.mode_gr;k++)for(x=0;x<M.channels_out;x++)$+=S[k][x];for(M.nsPsy.pefirbuf[18]=$,$=M.nsPsy.pefirbuf[9],P=0;P<9;P++)$+=(M.nsPsy.pefirbuf[P]+M.nsPsy.pefirbuf[18-P])*z.fircoef[P];for($=3350*M.mode_gr*M.channels_out/$,k=0;k<M.mode_gr;k++)for(x=0;x<M.channels_out;x++)S[k][x]*=$}return M.iteration_loop.iteration_loop(c,S,[.5,.5],y),t.format_bitstream(c),v=t.copy_buffer(M,_,d,h,1),c.bWriteVbrTag&&a.addVbrFrame(c),c.analysis&&null!=M.pinfo&&n(),function(e){var t,r;for(e.bitrate_stereoMode_Hist[e.bitrate_index][4]++,e.bitrate_stereoMode_Hist[15][4]++,2==e.channels_out&&n(),t=0;t<e.mode_gr;++t)for(r=0;r<e.channels_out;++r){var a=0|e.l3_side.tt[t][r].block_type;0!=e.l3_side.tt[t][r].mixed_block_flag&&(a=4),e.bitrate_blockType_Hist[e.bitrate_index][a]++,e.bitrate_blockType_Hist[e.bitrate_index][5]++,e.bitrate_blockType_Hist[15][a]++,e.bitrate_blockType_Hist[15][5]++}}(M),v}}function U(){this.sum=0,this.seen=0,this.want=0,this.pos=0,this.size=0,this.bag=null,this.nVbrNumFrames=0,this.nBytesWritten=0,this.TotalFrameSize=0}function X(){this.tt=[[null,null],[null,null]],this.main_data_begin=0,this.private_bits=0,this.resvDrain_pre=0,this.resvDrain_post=0,this.scfsi=[i(4),i(4)];for(var e=0;e<2;e++)for(var t=0;t<2;t++)this.tt[e][t]=new D}function q(){this.l=o(z.SBMAX_l),this.s=l([z.SBMAX_s,3]);var e=this;this.assign=function(t){_.arraycopy(t.l,0,e.l,0,z.SBMAX_l);for(var n=0;n<z.SBMAX_s;n++)for(var r=0;r<3;r++)e.s[n][r]=t.s[n][r]}}function Y(){this.last_en_subshort=l([4,9]),this.lastAttacks=i(4),this.pefirbuf=o(19),this.longfact=o(z.SBMAX_l),this.shortfact=o(z.SBMAX_s),this.attackthre=0,this.attackthre_s=0}function W(){function e(){this.write_timing=0,this.ptr=0,this.buf=r(40)}this.Class_ID=0,this.lame_encode_frame_init=0,this.iteration_init_init=0,this.fill_buffer_resample_init=0,this.mfbuf=l([2,W.MFSIZE]),this.mode_gr=0,this.channels_in=0,this.channels_out=0,this.resample_ratio=0,this.mf_samples_to_encode=0,this.mf_size=0,this.VBR_min_bitrate=0,this.VBR_max_bitrate=0,this.bitrate_index=0,this.samplerate_index=0,this.mode_ext=0,this.lowpass1=0,this.lowpass2=0,this.highpass1=0,this.highpass2=0,this.noise_shaping=0,this.noise_shaping_amp=0,this.substep_shaping=0,this.psymodel=0,this.noise_shaping_stop=0,this.subblock_gain=0,this.use_best_huffman=0,this.full_outer_loop=0,this.l3_side=new X,this.ms_ratio=o(2),this.padding=0,this.frac_SpF=0,this.slot_lag=0,this.tag_spec=null,this.nMusicCRC=0,this.OldValue=i(2),this.CurrentStep=i(2),this.masking_lower=0,this.bv_scf=i(576),this.pseudohalf=i(H.SFBMAX),this.sfb21_extra=!1,this.inbuf_old=new Array(2),this.blackfilt=new Array(2*W.BPC+1),this.itime=s(2),this.sideinfo_len=0,this.sb_sample=l([2,2,18,z.SBLIMIT]),this.amp_filter=o(32),this.header=new Array(W.MAX_HEADER_BUF),this.h_ptr=0,this.w_ptr=0,this.ancillary_flag=0,this.ResvSize=0,this.ResvMax=0,this.scalefac_band=new P,this.minval_l=o(z.CBANDS),this.minval_s=o(z.CBANDS),this.nb_1=l([4,z.CBANDS]),this.nb_2=l([4,z.CBANDS]),this.nb_s1=l([4,z.CBANDS]),this.nb_s2=l([4,z.CBANDS]),this.s3_ss=null,this.s3_ll=null,this.decay=0,this.thm=new Array(4),this.en=new Array(4),this.tot_ener=o(4),this.loudness_sq=l([2,2]),this.loudness_sq_save=o(2),this.mld_l=o(z.SBMAX_l),this.mld_s=o(z.SBMAX_s),this.bm_l=i(z.SBMAX_l),this.bo_l=i(z.SBMAX_l),this.bm_s=i(z.SBMAX_s),this.bo_s=i(z.SBMAX_s),this.npart_l=0,this.npart_s=0,this.s3ind=c([z.CBANDS,2]),this.s3ind_s=c([z.CBANDS,2]),this.numlines_s=i(z.CBANDS),this.numlines_l=i(z.CBANDS),this.rnumlines_l=o(z.CBANDS),this.mld_cb_l=o(z.CBANDS),this.mld_cb_s=o(z.CBANDS),this.numlines_s_num1=0,this.numlines_l_num1=0,this.pe=o(4),this.ms_ratio_s_old=0,this.ms_ratio_l_old=0,this.ms_ener_ratio_old=0,this.blocktype_old=i(2),this.nsPsy=new Y,this.VBR_seek_table=new U,this.ATH=null,this.PSY=null,this.nogap_total=0,this.nogap_current=0,this.decode_on_the_fly=!0,this.findReplayGain=!0,this.findPeakSample=!0,this.PeakSample=0,this.RadioGain=0,this.AudiophileGain=0,this.rgdata=null,this.noclipGainChange=0,this.noclipScale=0,this.bitrate_stereoMode_Hist=c([16,5]),this.bitrate_blockType_Hist=c([16,6]),this.pinfo=null,this.hip=null,this.in_buffer_nsamples=0,this.in_buffer_0=null,this.in_buffer_1=null,this.iteration_loop=null;for(var t=0;t<this.en.length;t++)this.en[t]=new q;for(t=0;t<this.thm.length;t++)this.thm[t]=new q;for(t=0;t<this.header.length;t++)this.header[t]=new e}function G(){var e=o(z.BLKSIZE),t=o(z.BLKSIZE_s/2),n=[.9238795325112867,.3826834323650898,.9951847266721969,.0980171403295606,.9996988186962042,.02454122852291229,.9999811752826011,.006135884649154475];function r(e,t,r){var a,i,o,s=0,l=t+(r<<=1);a=4;do{var c,f,u,p,_,h,v;v=a>>1,h=(_=a<<1)+(p=a),a=_<<1,o=(i=t)+v;do{x=e[i+0]-e[i+p],S=e[i+0]+e[i+p],T=e[i+_]-e[i+h],A=e[i+_]+e[i+h],e[i+_]=S-A,e[i+0]=S+A,e[i+h]=x-T,e[i+p]=x+T,x=e[o+0]-e[o+p],S=e[o+0]+e[o+p],T=d.SQRT2*e[o+h],A=d.SQRT2*e[o+_],e[o+_]=S-A,e[o+0]=S+A,e[o+h]=x-T,e[o+p]=x+T,o+=a,i+=a}while(i<l);for(f=n[s+0],c=n[s+1],u=1;u<v;u++){var m,b;m=1-2*c*c,b=2*c*f,i=t+u,o=t+p-u;do{var g,y,w,S,x,k,A,M,T,R;y=b*e[i+p]-m*e[o+p],g=m*e[i+p]+b*e[o+p],x=e[i+0]-g,S=e[i+0]+g,k=e[o+0]-y,w=e[o+0]+y,y=b*e[i+h]-m*e[o+h],g=m*e[i+h]+b*e[o+h],T=e[i+_]-g,A=e[i+_]+g,R=e[o+_]-y,M=e[o+_]+y,y=c*A-f*R,g=f*A+c*R,e[i+_]=S-g,e[i+0]=S+g,e[o+h]=k-y,e[o+p]=k+y,y=f*M-c*T,g=c*M+f*T,e[o+_]=w-g,e[o+0]=w+g,e[i+h]=x-y,e[i+p]=x+y,o+=a,i+=a}while(i<l);f=(m=f)*n[s+0]-c*n[s+1],c=m*n[s+1]+c*n[s+0]}s+=2}while(a<r)}var a=[0,128,64,192,32,160,96,224,16,144,80,208,48,176,112,240,8,136,72,200,40,168,104,232,24,152,88,216,56,184,120,248,4,132,68,196,36,164,100,228,20,148,84,212,52,180,116,244,12,140,76,204,44,172,108,236,28,156,92,220,60,188,124,252,2,130,66,194,34,162,98,226,18,146,82,210,50,178,114,242,10,138,74,202,42,170,106,234,26,154,90,218,58,186,122,250,6,134,70,198,38,166,102,230,22,150,86,214,54,182,118,246,14,142,78,206,46,174,110,238,30,158,94,222,62,190,126,254];this.fft_short=function(e,n,i,o,s){for(var l=0;l<3;l++){var c=z.BLKSIZE_s/2,f=65535&192*(l+1),u=z.BLKSIZE_s/8-1;do{var p,_,d,h,v,m=255&a[u<<2];_=(p=t[m]*o[i][s+m+f])-(v=t[127-m]*o[i][s+m+f+128]),p+=v,h=(d=t[m+64]*o[i][s+m+f+64])-(v=t[63-m]*o[i][s+m+f+192]),d+=v,c-=4,n[l][c+0]=p+d,n[l][c+2]=p-d,n[l][c+1]=_+h,n[l][c+3]=_-h,_=(p=t[m+1]*o[i][s+m+f+1])-(v=t[126-m]*o[i][s+m+f+129]),p+=v,h=(d=t[m+65]*o[i][s+m+f+65])-(v=t[62-m]*o[i][s+m+f+193]),d+=v,n[l][c+z.BLKSIZE_s/2+0]=p+d,n[l][c+z.BLKSIZE_s/2+2]=p-d,n[l][c+z.BLKSIZE_s/2+1]=_+h,n[l][c+z.BLKSIZE_s/2+3]=_-h}while(--u>=0);r(n[l],c,z.BLKSIZE_s/2)}},this.fft_long=function(t,n,i,o,s){var l=z.BLKSIZE/8-1,c=z.BLKSIZE/2;do{var f,u,p,_,d,h=255&a[l];u=(f=e[h]*o[i][s+h])-(d=e[h+512]*o[i][s+h+512]),f+=d,_=(p=e[h+256]*o[i][s+h+256])-(d=e[h+768]*o[i][s+h+768]),p+=d,n[0+(c-=4)]=f+p,n[c+2]=f-p,n[c+1]=u+_,n[c+3]=u-_,u=(f=e[h+1]*o[i][s+h+1])-(d=e[h+513]*o[i][s+h+513]),f+=d,_=(p=e[h+257]*o[i][s+h+257])-(d=e[h+769]*o[i][s+h+769]),p+=d,n[c+z.BLKSIZE/2+0]=f+p,n[c+z.BLKSIZE/2+2]=f-p,n[c+z.BLKSIZE/2+1]=u+_,n[c+z.BLKSIZE/2+3]=u-_}while(--l>=0);r(n,c,z.BLKSIZE/2)},this.init_fft=function(n){for(var r=0;r<z.BLKSIZE;r++)e[r]=.42-.5*Math.cos(2*Math.PI*(r+.5)/z.BLKSIZE)+.08*Math.cos(4*Math.PI*(r+.5)/z.BLKSIZE);for(r=0;r<z.BLKSIZE_s/2;r++)t[r]=.5*(1-Math.cos(2*Math.PI*(r+.5)/z.BLKSIZE_s))}}function K(){var e=new G,t=2.302585092994046,r=1/217621504/(z.BLKSIZE/2);function a(t,a,i,o,s,l,c,f,u,p,_){var d=t.internal_flags;u<2?(e.fft_long(d,o[s],u,p,_),e.fft_short(d,l[c],u,p,_)):2==u&&n(),a[0]=o[s+0][0],a[0]*=a[0];for(var h=z.BLKSIZE/2-1;h>=0;--h){var v=o[s+0][z.BLKSIZE/2-h],m=o[s+0][z.BLKSIZE/2+h];a[z.BLKSIZE/2-h]=.5*(v*v+m*m)}for(var b=2;b>=0;--b)for(i[b][0]=l[c+0][b][0],i[b][0]*=i[b][0],h=z.BLKSIZE_s/2-1;h>=0;--h)v=l[c+0][b][z.BLKSIZE_s/2-h],m=l[c+0][b][z.BLKSIZE_s/2+h],i[b][z.BLKSIZE_s/2-h]=.5*(v*v+m*m);var g=0;for(h=11;h<z.HBLKSIZE;h++)g+=a[h];d.tot_ener[u]=g,t.analysis&&n(),2==t.athaa_loudapprox&&u<2&&(d.loudness_sq[f][u]=d.loudness_sq_save[u],d.loudness_sq_save[u]=function(e,t){for(var n=0,a=0;a<z.BLKSIZE/2;++a)n+=e[a]*t.ATH.eql_w[a];return n*=r}(a,d))}var s,c,f,u=[1,.79433,.63096,.63096,.63096,.63096,.63096,.25119,.11749],_=[3.3246*3.3246,3.23837*3.23837,9.9500500969,9.0247369744,8.1854926609,7.0440875649,2.46209*2.46209,2.284*2.284,4.4892710641,1.96552*1.96552,1.82335*1.82335,1.69146*1.69146,2.4621061921,2.1508568964,1.37074*1.37074,1.31036*1.31036,1.5691069696,1.4555939904,1.16203*1.16203,1.2715945225,1.09428*1.09428,1.0659*1.0659,1.0779838276,1.0382591025,1],g=[1.7782755904,1.35879*1.35879,1.38454*1.38454,1.39497*1.39497,1.40548*1.40548,1.3537*1.3537,1.6999465924,1.22321*1.22321,1.3169398564,1],y=[5.5396212496,2.29259*2.29259,4.9868695969,2.12675*2.12675,2.02545*2.02545,1.87894*1.87894,1.74303*1.74303,1.61695*1.61695,2.2499700001,1.39148*1.39148,1.29083*1.29083,1.19746*1.19746,1.2339655056,1.0779838276];function w(e,t,n,r,a,i){var o,l,u;if(t>e){if(!(t<e*c))return e+t;o=t/e}else{if(e>=t*c)return e+t;o=e/t}if(e+=t,r+3<=6){if(o>=s)return e;var p=0|d.FAST_LOG10_X(o,16);return e*g[p]}return p=0|d.FAST_LOG10_X(o,16),t=0!=i?a.ATH.cb_s[n]*a.ATH.adjust:a.ATH.cb_l[n]*a.ATH.adjust,e<f*t?e>t?(l=1,p<=13&&(l=y[p]),u=d.FAST_LOG10_X(e/t,10/15),e*((_[p]-l)*u+l)):p>13?e:e*y[p]:e*_[p]}function S(e,t,n,r,a){var i,o,s=0,l=0;for(i=o=0;i<z.SBMAX_s;++o,++i){for(var c=e.bo_s[i],f=e.npart_s,u=c<f?c:f;o<u;)s+=t[o],l+=n[o],o++;if(e.en[r].s[i][a]=s,e.thm[r].s[i][a]=l,o>=f){++i;break}var p=e.PSY.bo_s_weight[i],_=1-p;s=p*t[o],l=p*n[o],e.en[r].s[i][a]+=s,e.thm[r].s[i][a]+=l,s=_*t[o],l=_*n[o]}for(;i<z.SBMAX_s;++i)e.en[r].s[i][a]=0,e.thm[r].s[i][a]=0}function x(e,t,n,r){var a,i,o=0,s=0;for(a=i=0;a<z.SBMAX_l;++i,++a){for(var l=e.bo_l[a],c=e.npart_l,f=l<c?l:c;i<f;)o+=t[i],s+=n[i],i++;if(e.en[r].l[a]=o,e.thm[r].l[a]=s,i>=c){++a;break}var u=e.PSY.bo_l_weight[a],p=1-u;o=u*t[i],s=u*n[i],e.en[r].l[a]+=o,e.thm[r].l[a]+=s,o=p*t[i],s=p*n[i]}for(;a<z.SBMAX_l;++a)e.en[r].l[a]=0,e.thm[r].l[a]=0}function k(e,t,n,r,a,i){var o,s,l=e.internal_flags;for(s=o=0;s<l.npart_s;++s){for(var c=0,f=0,u=l.numlines_s[s],p=0;p<u;++p,++o){var _=t[i][o];c+=_,f<_&&(f=_)}n[s]=c}for(o=s=0;s<l.npart_s;s++){var d=l.s3ind_s[s][0],h=l.s3_ss[o++]*n[d];for(++d;d<=l.s3ind_s[s][1];)h+=l.s3_ss[o]*n[d],++o,++d;var v=2*l.nb_s1[a][s];if(r[s]=Math.min(h,v),l.blocktype_old[1&a]==z.SHORT_TYPE){v=16*l.nb_s2[a][s];var m=r[s];r[s]=Math.min(v,m)}l.nb_s2[a][s]=l.nb_s1[a][s],l.nb_s1[a][s]=h}for(;s<=z.CBANDS;++s)n[s]=0,r[s]=0}function A(e,t,n){return n>=1?e:n<=0?t:t>0?Math.pow(e/t,n)*t:0}var M=[11.8,13.6,17.2,32,46.5,51.3,57.5,67.1,71.5,84.6,97.6,130];function T(e,n){for(var r=309.07,a=0;a<z.SBMAX_s-1;a++)for(var i=0;i<3;i++){var o=e.thm.s[a][i];if(o>0){var s=o*n,l=e.en.s[a][i];l>s&&(r+=l>1e10*s?M[a]*(10*t):M[a]*d.FAST_LOG10(l/s))}}return r}var R=[6.8,5.8,5.8,6.4,6.5,9.9,12.1,14.4,15,18.9,21.6,26.9,34.2,40.2,46.8,56.5,60.7,73.9,85.7,93.4,126.1];function C(e,n){for(var r=281.0575,a=0;a<z.SBMAX_l-1;a++){var i=e.thm.l[a];if(i>0){var o=i*n,s=e.en.l[a];s>o&&(r+=s>1e10*o?R[a]*(10*t):R[a]*d.FAST_LOG10(s/o))}}return r}function E(e,t,n,r,a){var i,o;for(i=o=0;i<e.npart_l;++i){var s,l=0,c=0;for(s=0;s<e.numlines_l[i];++s,++o){var f=t[o];l+=f,c<f&&(c=f)}n[i]=l,r[i]=c,a[i]=l*e.rnumlines_l[i]}}function B(e,t,n,r){var a=u.length-1,i=0,o=n[i]+n[i+1];for(o>0?((s=t[i])<t[i+1]&&(s=t[i+1]),(l=0|(o=20*(2*s-o)/(o*(e.numlines_l[i]+e.numlines_l[i+1]-1))))>a&&(l=a),r[i]=l):r[i]=0,i=1;i<e.npart_l-1;i++){var s,l;(o=n[i-1]+n[i]+n[i+1])>0?((s=t[i-1])<t[i]&&(s=t[i]),s<t[i+1]&&(s=t[i+1]),(l=0|(o=20*(3*s-o)/(o*(e.numlines_l[i-1]+e.numlines_l[i]+e.numlines_l[i+1]-1))))>a&&(l=a),r[i]=l):r[i]=0}(o=n[i-1]+n[i])>0?((s=t[i-1])<t[i]&&(s=t[i]),(l=0|(o=20*(2*s-o)/(o*(e.numlines_l[i-1]+e.numlines_l[i]-1))))>a&&(l=a),r[i]=l):r[i]=0}var O=[-1730326e-23,-.01703172,-1349528e-23,.0418072,-673278e-22,-.0876324,-30835e-21,.1863476,-1104424e-22,-.627638];function L(e){return e<0&&(e=0),e*=.001,13*Math.atan(.76*e)+3.5*Math.atan(e*e/56.25)}function I(e,t,n,r,a,s,l,c,f,u,p,_){var d,h=o(z.CBANDS+1),v=c/(_>15?1152:384),m=i(z.HBLKSIZE);c/=f;var b=0,g=0;for(d=0;d<z.CBANDS;d++){var y;for(C=L(c*b),h[d]=c*b,y=b;L(c*y)-C<.34&&y<=f/2;y++);for(e[d]=y-b,g=d+1;b<y;)m[b++]=d;if(b>f/2){b=f/2,++d;break}}h[d]=c*b;for(var w=0;w<_;w++){var S,x,k,A,M;k=u[w],A=u[w+1],(S=0|Math.floor(.5+p*(k-.5)))<0&&(S=0),(x=0|Math.floor(.5+p*(A-.5)))>f/2&&(x=f/2),n[w]=(m[S]+m[x])/2,t[w]=m[x];var T=v*A;l[w]=(T-h[t[w]])/(h[t[w]+1]-h[t[w]]),l[w]<0?l[w]=0:l[w]>1&&(l[w]=1),M=L(c*u[w]*p),M=Math.min(M,15.5)/15.5,s[w]=Math.pow(10,1.25*(1-Math.cos(Math.PI*M))-2.5)}b=0;for(var R=0;R<g;R++){var C,E,B=e[R];C=L(c*b),E=L(c*(b+B-1)),r[R]=.5*(C+E),C=L(c*(b-.5)),E=L(c*(b+B-.5)),a[R]=E-C,b+=B}return g}function P(e,t,r,a,i,s){var c,f,u,p,_,d,h=l([z.CBANDS,z.CBANDS]),v=0;if(s)for(var m=0;m<t;m++)for(c=0;c<t;c++){var b=(f=r[m]-r[c],u=void 0,p=void 0,_=void 0,d=void 0,u=f,p=(u*=u>=0?3:1.5)>=.5&&u<=2.5?8*((d=u-.5)*d-2*d):0,((_=15.811389+7.5*(u+=.474)-17.5*Math.sqrt(1+u*u))<=-60?0:(u=Math.exp(.2302585093*(p+_)),u/=.6609193))*a[c]);h[m][c]=b*i[m]}else n();for(m=0;m<t;m++){for(c=0;c<t&&!(h[m][c]>0);c++);for(e[m][0]=c,c=t-1;c>0&&!(h[m][c]>0);c--);e[m][1]=c,v+=e[m][1]-e[m][0]+1}var g=o(v),y=0;for(m=0;m<t;m++)for(c=e[m][0];c<=e[m][1];c++)g[y++]=h[m][c];return g}function $(e){var t=L(e);return t=Math.min(t,15.5)/15.5,Math.pow(10,1.25*(1-Math.cos(Math.PI*t))-2.5)}function N(e,t){return e<-.3&&(e=3410),e/=1e3,e=Math.max(.1,e),3.64*Math.pow(e,-.8)-6.8*Math.exp(-.6*Math.pow(e-3.4,2))+6*Math.exp(-.15*Math.pow(e-8.7,2))+.001*(.6+.04*t)*Math.pow(e,4)}this.L3psycho_anal_ns=function(e,t,r,s,c,f,_,d,v,g){var y,M,R,L,I,P,$,N,D,H=e.internal_flags,j=l([2,z.BLKSIZE]),F=l([2,3,z.BLKSIZE_s]),V=o(z.CBANDS+1),U=o(z.CBANDS+1),X=o(z.CBANDS+2),q=i(2),Y=i(2),W=l([2,576]),G=i(z.CBANDS+2),K=i(z.CBANDS+2);for(p.fill(K,0),y=H.channels_out,e.mode==b.JOINT_STEREO&&(y=4),D=e.VBR==m.vbr_off?0==H.ResvMax?0:H.ResvSize/H.ResvMax*.5:e.VBR==m.vbr_rh||e.VBR==m.vbr_mtrh||e.VBR==m.vbr_mt?.6:1,M=0;M<H.channels_out;M++){var Z=t[M],Q=r+576-350-21+192;for(L=0;L<576;L++){var J,ee;for(J=Z[Q+L+10],ee=0,I=0;I<9;I+=2)J+=O[I]*(Z[Q+L+I]+Z[Q+L+21-I]),ee+=O[I+1]*(Z[Q+L+I+1]+Z[Q+L+21-I-1]);W[M][L]=J+ee}c[s][M].en.assign(H.en[M]),c[s][M].thm.assign(H.thm[M]),y>2&&n()}for(M=0;M<y;M++){var te,ne=o(12),re=[0,0,0,0],ae=o(12),ie=1,oe=o(z.CBANDS),se=o(z.CBANDS),le=[0,0,0,0],ce=o(z.HBLKSIZE),fe=l([3,z.HBLKSIZE_s]);for(L=0;L<3;L++)ne[L]=H.nsPsy.last_en_subshort[M][L+6],ae[L]=ne[L]/H.nsPsy.last_en_subshort[M][L+4],re[0]+=ne[L];2==M&&n();var ue=W[1&M],pe=0;for(L=0;L<9;L++){for(var _e=pe+64,de=1;pe<_e;pe++)de<Math.abs(ue[pe])&&(de=Math.abs(ue[pe]));H.nsPsy.last_en_subshort[M][L]=ne[L+3]=de,re[1+L/3]+=de,de>ne[L+3-2]?de/=ne[L+3-2]:de=ne[L+3-2]>10*de?ne[L+3-2]/(10*de):0,ae[L+3]=de}for(e.analysis&&n(),te=3==M?H.nsPsy.attackthre_s:H.nsPsy.attackthre,L=0;L<12;L++)0==le[L/3]&&ae[L]>te&&(le[L/3]=L%3+1);for(L=1;L<4;L++)(re[L-1]>re[L]?re[L-1]/re[L]:re[L]/re[L-1])<1.7&&(le[L]=0,1==L&&(le[0]=0));for(0!=le[0]&&0!=H.nsPsy.lastAttacks[M]&&(le[0]=0),3!=H.nsPsy.lastAttacks[M]&&le[0]+le[1]+le[2]+le[3]==0||(ie=0,0!=le[1]&&0!=le[0]&&(le[1]=0),0!=le[2]&&0!=le[1]&&(le[2]=0),0!=le[3]&&0!=le[2]&&(le[3]=0)),M<2?Y[M]=ie:n(),v[M]=H.tot_ener[M],a(e,ce,fe,j,1&M,F,1&M,s,M,t,r),E(H,ce,V,oe,se),B(H,oe,se,G),N=0;N<3;N++){var he,ve;for(k(e,fe,U,X,M,N),S(H,U,X,M,N),$=0;$<z.SBMAX_s;$++){if(ve=H.thm[M].s[$][N],ve*=.8,le[N]>=2||1==le[N+1]){var me=0!=N?N-1:2;de=A(H.thm[M].s[$][me],ve,.6*D),ve=Math.min(ve,de)}1==le[N]?(me=0!=N?N-1:2,de=A(H.thm[M].s[$][me],ve,.3*D),ve=Math.min(ve,de)):(0!=N&&3==le[N-1]||0==N&&3==H.nsPsy.lastAttacks[M])&&(me=2!=N?N+1:0,de=A(H.thm[M].s[$][me],ve,.3*D),ve=Math.min(ve,de)),he=ne[3*N+3]+ne[3*N+4]+ne[3*N+5],6*ne[3*N+5]<he&&(ve*=.5,6*ne[3*N+4]<he&&(ve*=.5)),H.thm[M].s[$][N]=ve}}for(H.nsPsy.lastAttacks[M]=le[2],P=0,R=0;R<H.npart_l;R++){for(var be=H.s3ind[R][0],ge=V[be]*u[G[be]],ye=H.s3_ll[P++]*ge;++be<=H.s3ind[R][1];)ge=V[be]*u[G[be]],ye=w(ye,H.s3_ll[P++]*ge,be,be-R,H,0);ye*=.158489319246111,H.blocktype_old[1&M]==z.SHORT_TYPE?X[R]=ye:X[R]=A(Math.min(ye,Math.min(2*H.nb_1[M][R],16*H.nb_2[M][R])),ye,D),H.nb_2[M][R]=H.nb_1[M][R],H.nb_1[M][R]=ye}for(;R<=z.CBANDS;++R)V[R]=0,X[R]=0;x(H,V,X,M)}for(e.mode!=b.STEREO&&e.mode!=b.JOINT_STEREO||n(),e.mode==b.JOINT_STEREO&&n(),function(e,t,n,r){var a=e.internal_flags;e.short_blocks!=h.short_block_coupled||0!=t[0]&&0!=t[1]||(t[0]=t[1]=0);for(var i=0;i<a.channels_out;i++)r[i]=z.NORM_TYPE,e.short_blocks==h.short_block_dispensed&&(t[i]=1),e.short_blocks==h.short_block_forced&&(t[i]=0),0!=t[i]?a.blocktype_old[i]==z.SHORT_TYPE&&(r[i]=z.STOP_TYPE):(r[i]=z.SHORT_TYPE,a.blocktype_old[i]==z.NORM_TYPE&&(a.blocktype_old[i]=z.START_TYPE),a.blocktype_old[i]==z.STOP_TYPE&&(a.blocktype_old[i]=z.SHORT_TYPE)),n[i]=a.blocktype_old[i],a.blocktype_old[i]=r[i]}(e,Y,g,q),M=0;M<y;M++){var we,Se,xe,ke=0;M>1?n():(we=_,ke=0,Se=g[M],xe=c[s][M]),Se==z.SHORT_TYPE?we[ke+M]=T(xe,H.masking_lower):we[ke+M]=C(xe,H.masking_lower),e.analysis&&(H.pinfo.pe[s][M]=we[ke+M])}return 0},this.psymodel_init=function(n){var r,a,i=n.internal_flags,l=!0,u=13,p=0,_=0,d=-8.25,h=-4.5,b=o(z.CBANDS),g=o(z.CBANDS),y=o(z.CBANDS),w=n.out_samplerate;switch(n.experimentalZ){default:case 0:l=!0;break;case 1:l=n.VBR!=m.vbr_mtrh&&n.VBR!=m.vbr_mt;break;case 2:l=!1;break;case 3:u=8,p=-1.75,_=-.0125,d=-8.25,h=-2.25}for(i.ms_ener_ratio_old=.25,i.blocktype_old[0]=i.blocktype_old[1]=z.NORM_TYPE,r=0;r<4;++r){for(var S=0;S<z.CBANDS;++S)i.nb_1[r][S]=1e20,i.nb_2[r][S]=1e20,i.nb_s1[r][S]=i.nb_s2[r][S]=1;for(var x=0;x<z.SBMAX_l;x++)i.en[r].l[x]=1e20,i.thm[r].l[x]=1e20;for(S=0;S<3;++S){for(x=0;x<z.SBMAX_s;x++)i.en[r].s[x][S]=1e20,i.thm[r].s[x][S]=1e20;i.nsPsy.lastAttacks[r]=0}for(S=0;S<9;S++)i.nsPsy.last_en_subshort[r][S]=10}for(i.loudness_sq_save[0]=i.loudness_sq_save[1]=0,i.npart_l=I(i.numlines_l,i.bo_l,i.bm_l,b,g,i.mld_l,i.PSY.bo_l_weight,w,z.BLKSIZE,i.scalefac_band.l,z.BLKSIZE/1152,z.SBMAX_l),r=0;r<i.npart_l;r++){var k=p;b[r]>=u&&(k=_*(b[r]-u)/(24-u)+p*(24-b[r])/(24-u)),y[r]=Math.pow(10,k/10),i.numlines_l[r]>0?i.rnumlines_l[r]=1/i.numlines_l[r]:i.rnumlines_l[r]=0}for(i.s3_ll=P(i.s3ind,i.npart_l,b,g,y,l),S=0,r=0;r<i.npart_l;r++){T=v.MAX_VALUE;for(var A=0;A<i.numlines_l[r];A++,S++){var M=w*S/(1e3*z.BLKSIZE);R=this.ATHformula(1e3*M,n)-20,R=Math.pow(10,.1*R),T>(R*=i.numlines_l[r])&&(T=R)}i.ATH.cb_l[r]=T,(T=20*b[r]/10-20)>6&&(T=100),T<-15&&(T=-15),T-=8,i.minval_l[r]=Math.pow(10,T/10)*i.numlines_l[r]}for(i.npart_s=I(i.numlines_s,i.bo_s,i.bm_s,b,g,i.mld_s,i.PSY.bo_s_weight,w,z.BLKSIZE_s,i.scalefac_band.s,z.BLKSIZE_s/384,z.SBMAX_s),S=0,r=0;r<i.npart_s;r++){var T;for(k=d,b[r]>=u&&(k=h*(b[r]-u)/(24-u)+d*(24-b[r])/(24-u)),y[r]=Math.pow(10,k/10),T=v.MAX_VALUE,A=0;A<i.numlines_s[r];A++,S++){var R;M=w*S/(1e3*z.BLKSIZE_s),R=this.ATHformula(1e3*M,n)-20,R=Math.pow(10,.1*R),T>(R*=i.numlines_s[r])&&(T=R)}i.ATH.cb_s[r]=T,T=7*b[r]/12-7,b[r]>12&&(T*=1+3.1*Math.log(1+T)),b[r]<12&&(T*=1+2.3*Math.log(1-T)),T<-15&&(T=-15),T-=8,i.minval_s[r]=Math.pow(10,T/10)*i.numlines_s[r]}i.s3_ss=P(i.s3ind_s,i.npart_s,b,g,y,l),s=Math.pow(10,9/16),c=Math.pow(10,1.5),f=Math.pow(10,1.5),e.init_fft(i),i.decay=Math.exp(-1*t/(.01*w/192)),a=3.5,0!=(2&n.exp_nspsytune)&&(a=1),Math.abs(n.msfix)>0&&(a=n.msfix),n.msfix=a;for(var C=0;C<i.npart_l;C++)i.s3ind[C][1]>i.npart_l-1&&(i.s3ind[C][1]=i.npart_l-1);var E=576*i.mode_gr/w;if(i.ATH.decay=Math.pow(10,-1.2*E),i.ATH.adjust=.01,i.ATH.adjustLimit=1,-1!=n.ATHtype){var B=n.out_samplerate/z.BLKSIZE,O=0;for(M=0,r=0;r<z.BLKSIZE/2;++r)M+=B,i.ATH.eql_w[r]=1/Math.pow(10,this.ATHformula(M,n)/10),O+=i.ATH.eql_w[r];for(O=1/O,r=z.BLKSIZE/2;--r>=0;)i.ATH.eql_w[r]*=O}for(C=S=0;C<i.npart_s;++C)for(r=0;r<i.numlines_s[C];++r)++S;for(C=S=0;C<i.npart_l;++C)for(r=0;r<i.numlines_l[C];++r)++S;for(S=0,r=0;r<i.npart_l;r++)M=w*(S+i.numlines_l[r]/2)/(1*z.BLKSIZE),i.mld_cb_l[r]=$(M),S+=i.numlines_l[r];for(;r<z.CBANDS;++r)i.mld_cb_l[r]=1;for(S=0,r=0;r<i.npart_s;r++)M=w*(S+i.numlines_s[r]/2)/(1*z.BLKSIZE_s),i.mld_cb_s[r]=$(M),S+=i.numlines_s[r];for(;r<z.CBANDS;++r)i.mld_cb_s[r]=1;return 0},this.ATHformula=function(e,t){var n;switch(t.ATHtype){case 0:n=N(e,9);break;case 1:n=N(e,-1);break;case 2:n=N(e,0);break;case 3:n=N(e,1)+6;break;case 4:n=N(e,t.ATHcurve);break;default:n=N(e,0)}return n}}function Z(){var e,t,r,a,i,s=this;Z.V9=410,Z.V8=420,Z.V7=430,Z.V6=440,Z.V5=450,Z.V4=460,Z.V3=470,Z.V2=480,Z.V1=490,Z.V0=500,Z.R3MIX=1e3,Z.STANDARD=1001,Z.EXTREME=1002,Z.INSANE=1003,Z.STANDARD_FAST=1004,Z.EXTREME_FAST=1005,Z.MEDIUM=1006,Z.MEDIUM_FAST=1007,Z.LAME_MAXMP3BUFFER=147456;var l,u,p=new K;function _(){this.mask_adjust=0,this.mask_adjust_short=0,this.bo_l_weight=o(z.SBMAX_l),this.bo_s_weight=o(z.SBMAX_s)}function d(){this.lowerlimit=0}function v(e,t){this.lowpass=t}function g(e,t){var n=[new v(8,2e3),new v(16,3700),new v(24,3900),new v(32,5500),new v(40,7e3),new v(48,7500),new v(56,1e4),new v(64,11e3),new v(80,13500),new v(96,15100),new v(112,15600),new v(128,17e3),new v(160,17500),new v(192,18600),new v(224,19400),new v(256,19700),new v(320,20500)],r=s.nearestBitrateFullIndex(t);e.lowerlimit=n[r].lowpass}function y(e){var t=z.BLKSIZE+e.framesize-z.FFTOFFSET;return t=Math.max(t,512+e.framesize-32)}function S(e,t,n,r,a,i){var o=s.enc.lame_encode_mp3_frame(e,t,n,r,a,i);return e.frameNum++,o}function x(){this.n_in=0,this.n_out=0}function k(e,t,r,a,i,o){var s=e.internal_flags;if(s.resample_ratio<.9999||s.resample_ratio>1.0001)n();else{o.n_out=Math.min(e.framesize,i),o.n_in=o.n_out;for(var l=0;l<o.n_out;++l)t[0][s.mf_size+l]=r[0][a+l],2==s.channels_out&&(t[1][s.mf_size+l]=r[1][a+l])}}this.enc=new z,this.setModules=function(n,o,s,c,f,_,d,h,v){e=n,t=o,r=s,a=c,i=f,l=_,u=h,this.enc.setModules(t,p,a,l)},this.lame_init=function(){var e=new O;return 0!=function(e){var t;return e.class_id=4294479419,t=e.internal_flags=new W,e.mode=b.NOT_SET,e.original=1,e.in_samplerate=44100,e.num_channels=2,e.num_samples=-1,e.bWriteVbrTag=!0,e.quality=-1,e.short_blocks=null,t.subblock_gain=-1,e.lowpassfreq=0,e.highpassfreq=0,e.lowpasswidth=-1,e.highpasswidth=-1,e.VBR=m.vbr_off,e.VBR_q=4,e.ATHcurve=-1,e.VBR_mean_bitrate_kbps=128,e.VBR_min_bitrate_kbps=0,e.VBR_max_bitrate_kbps=0,e.VBR_hard_min=0,t.VBR_min_bitrate=1,t.VBR_max_bitrate=13,e.quant_comp=-1,e.quant_comp_short=-1,e.msfix=-1,t.resample_ratio=1,t.OldValue[0]=180,t.OldValue[1]=180,t.CurrentStep[0]=4,t.CurrentStep[1]=4,t.masking_lower=1,t.nsPsy.attackthre=-1,t.nsPsy.attackthre_s=-1,e.scale=-1,e.athaa_type=-1,e.ATHtype=-1,e.athaa_loudapprox=-1,e.athaa_sensitivity=0,e.useTemporal=null,e.interChRatio=-1,t.mf_samples_to_encode=z.ENCDELAY+z.POSTDELAY,e.encoder_padding=0,t.mf_size=z.ENCDELAY-z.MDCTDELAY,e.findReplayGain=!1,e.decode_on_the_fly=!1,t.decode_on_the_fly=!1,t.findReplayGain=!1,t.findPeakSample=!1,t.RadioGain=0,t.AudiophileGain=0,t.noclipGainChange=0,t.noclipScale=-1,e.preset=0,e.write_id3tag_automatic=!0,0}(e)?null:(e.lame_allocated_gfp=1,e)},this.nearestBitrateFullIndex=function(e){var t=[8,16,24,32,40,48,56,64,80,96,112,128,160,192,224,256,320],n=0,r=0,a=0,i=0;i=t[16],a=16,r=t[16],n=16;for(var o=0;o<16;o++)if(Math.max(e,t[o+1])!=e){i=t[o+1],a=o+1,r=t[o],n=o;break}return i-e>e-r?n:a},this.lame_init_params=function(e){var o=e.internal_flags;if(o.Class_ID=0,null==o.ATH&&(o.ATH=new B),null==o.PSY&&(o.PSY=new _),null==o.rgdata&&(o.rgdata=new I),o.channels_in=e.num_channels,1==o.channels_in&&(e.mode=b.MONO),o.channels_out=e.mode==b.MONO?1:2,o.mode_ext=z.MPG_MD_MS_LR,e.mode==b.MONO&&(e.force_ms=!1),e.VBR==m.vbr_off&&128!=e.VBR_mean_bitrate_kbps&&0==e.brate&&(e.brate=e.VBR_mean_bitrate_kbps),e.VBR==m.vbr_off||e.VBR==m.vbr_mtrh||e.VBR==m.vbr_mt||(e.free_format=!1),e.VBR==m.vbr_off&&0==e.brate&&n(),e.VBR==m.vbr_off&&e.compression_ratio>0&&n(),0!=e.out_samplerate&&(e.out_samplerate<16e3?(e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,8),e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,64)):e.out_samplerate<32e3?(e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,8),e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,160)):(e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,32),e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,320))),0==e.lowpassfreq){var s=16e3;switch(e.VBR){case m.vbr_off:g(f=new d,e.brate),s=f.lowerlimit;break;case m.vbr_abr:var f;g(f=new d,e.VBR_mean_bitrate_kbps),s=f.lowerlimit;break;case m.vbr_rh:n();default:n()}e.mode!=b.MONO||e.VBR!=m.vbr_off&&e.VBR!=m.vbr_abr||(s*=1.5),e.lowpassfreq=0|s}switch(0==e.out_samplerate&&n(),e.lowpassfreq=Math.min(20500,e.lowpassfreq),e.lowpassfreq=Math.min(e.out_samplerate/2,e.lowpassfreq),e.VBR==m.vbr_off&&(e.compression_ratio=16*e.out_samplerate*o.channels_out/(1e3*e.brate)),e.VBR==m.vbr_abr&&n(),e.bWriteVbrTag||(e.findReplayGain=!1,e.decode_on_the_fly=!1,o.findPeakSample=!1),o.findReplayGain=e.findReplayGain,o.decode_on_the_fly=e.decode_on_the_fly,o.decode_on_the_fly&&(o.findPeakSample=!0),o.findReplayGain&&n(),o.decode_on_the_fly&&!e.decode_only&&n(),o.mode_gr=e.out_samplerate<=24e3?1:2,e.framesize=576*o.mode_gr,e.encoder_delay=z.ENCDELAY,o.resample_ratio=e.in_samplerate/e.out_samplerate,e.VBR){case m.vbr_mt:case m.vbr_rh:case m.vbr_mtrh:e.compression_ratio=[5.7,6.5,7.3,8.2,10,11.9,13,14,15,16.5][e.VBR_q];break;case m.vbr_abr:e.compression_ratio=16*e.out_samplerate*o.channels_out/(1e3*e.VBR_mean_bitrate_kbps);break;default:e.compression_ratio=16*e.out_samplerate*o.channels_out/(1e3*e.brate)}e.mode==b.NOT_SET&&(e.mode=b.JOINT_STEREO),e.highpassfreq>0?n():(o.highpass1=0,o.highpass2=0),e.lowpassfreq>0?(o.lowpass2=2*e.lowpassfreq,e.lowpasswidth>=0?n():o.lowpass1=2*e.lowpassfreq,o.lowpass1/=e.out_samplerate,o.lowpass2/=e.out_samplerate):n(),function(e){var t,r=e.internal_flags,a=32;if(r.lowpass1>0){for(var i=999,o=0;o<=31;o++)(c=o/31)>=r.lowpass2&&(a=Math.min(a,o)),r.lowpass1<c&&c<r.lowpass2&&(i=Math.min(i,o));r.lowpass1=999==i?(a-.75)/31:(i-.75)/31,r.lowpass2=a/31}for(r.highpass2>0&&n(),r.highpass2>0&&n(),o=0;o<32;o++){var s,l,c=o/31;r.highpass2>r.highpass1?n():s=1,l=r.lowpass2>r.lowpass1?(t=(c-r.lowpass1)/(r.lowpass2-r.lowpass1+1e-20))>1?0:t<=0?1:Math.cos(Math.PI/2*t):1,r.amp_filter[o]=s*l}}(e),o.samplerate_index=function(e,t){switch(e){case 44100:return t.version=1,0;case 48e3:return t.version=1,1;case 32e3:return t.version=1,2;case 22050:return t.version=0,0;case 24e3:return t.version=0,1;case 16e3:return t.version=0,2;case 11025:return t.version=0,0;case 12e3:return t.version=0,1;case 8e3:return t.version=0,2;default:return t.version=0,-1}}(e.out_samplerate,e),o.samplerate_index<0&&n(),e.VBR==m.vbr_off?e.free_format?o.bitrate_index=0:(e.brate=function(e,t,n){n<16e3&&(t=2);for(var r=T.bitrate_table[t][1],a=2;a<=14;a++)T.bitrate_table[t][a]>0&&Math.abs(T.bitrate_table[t][a]-e)<Math.abs(r-e)&&(r=T.bitrate_table[t][a]);return r}(e.brate,e.version,e.out_samplerate),o.bitrate_index=function(e,t,n){n<16e3&&(t=2);for(var r=0;r<=14;r++)if(T.bitrate_table[t][r]>0&&T.bitrate_table[t][r]==e)return r;return-1}(e.brate,e.version,e.out_samplerate),o.bitrate_index<=0&&n()):o.bitrate_index=1,e.analysis&&(e.bWriteVbrTag=!1),null!=o.pinfo&&(e.bWriteVbrTag=!1),t.init_bit_stream_w(o);for(var v,y=o.samplerate_index+3*e.version+6*(e.out_samplerate<16e3?1:0),w=0;w<z.SBMAX_l+1;w++)o.scalefac_band.l[w]=a.sfBandIndex[y].l[w];for(w=0;w<z.PSFB21+1;w++){var S=(o.scalefac_band.l[22]-o.scalefac_band.l[21])/z.PSFB21,x=o.scalefac_band.l[21]+w*S;o.scalefac_band.psfb21[w]=x}for(o.scalefac_band.psfb21[z.PSFB21]=576,w=0;w<z.SBMAX_s+1;w++)o.scalefac_band.s[w]=a.sfBandIndex[y].s[w];for(w=0;w<z.PSFB12+1;w++)S=(o.scalefac_band.s[13]-o.scalefac_band.s[12])/z.PSFB12,x=o.scalefac_band.s[12]+w*S,o.scalefac_band.psfb12[w]=x;for(o.scalefac_band.psfb12[z.PSFB12]=192,1==e.version?o.sideinfo_len=1==o.channels_out?21:36:o.sideinfo_len=1==o.channels_out?13:21,e.error_protection&&(o.sideinfo_len+=2),function(e){var t=e.internal_flags;e.frameNum=0,e.write_id3tag_automatic&&u.id3tag_write_v2(e),t.bitrate_stereoMode_Hist=c([16,5]),t.bitrate_blockType_Hist=c([16,6]),t.PeakSample=0,e.bWriteVbrTag&&l.InitVbrTag(e)}(e),o.Class_ID=4294479419,v=0;v<19;v++)o.nsPsy.pefirbuf[v]=700*o.mode_gr*o.channels_out;switch(-1==e.ATHtype&&(e.ATHtype=4),e.VBR){case m.vbr_mt:e.VBR=m.vbr_mtrh;case m.vbr_mtrh:null==e.useTemporal&&(e.useTemporal=!1),r.apply_preset(e,500-10*e.VBR_q,0),e.quality<0&&(e.quality=LAME_DEFAULT_QUALITY),e.quality<5&&(e.quality=0),e.quality>5&&(e.quality=5),o.PSY.mask_adjust=e.maskingadjust,o.PSY.mask_adjust_short=e.maskingadjust_short,e.experimentalY?o.sfb21_extra=!1:o.sfb21_extra=e.out_samplerate>44e3,o.iteration_loop=new VBRNewIterationLoop(i);break;case m.vbr_rh:r.apply_preset(e,500-10*e.VBR_q,0),o.PSY.mask_adjust=e.maskingadjust,o.PSY.mask_adjust_short=e.maskingadjust_short,e.experimentalY?o.sfb21_extra=!1:o.sfb21_extra=e.out_samplerate>44e3,e.quality>6&&(e.quality=6),e.quality<0&&(e.quality=LAME_DEFAULT_QUALITY),o.iteration_loop=new VBROldIterationLoop(i);break;default:var k;o.sfb21_extra=!1,e.quality<0&&(e.quality=LAME_DEFAULT_QUALITY),(k=e.VBR)==m.vbr_off&&(e.VBR_mean_bitrate_kbps=e.brate),r.apply_preset(e,e.VBR_mean_bitrate_kbps,0),e.VBR=k,o.PSY.mask_adjust=e.maskingadjust,o.PSY.mask_adjust_short=e.maskingadjust_short,k==m.vbr_off?o.iteration_loop=new L(i):n()}return e.VBR!=m.vbr_off&&n(),e.tune&&n(),function(e){var t=e.internal_flags;switch(e.quality){default:case 9:t.psymodel=0,t.noise_shaping=0,t.noise_shaping_amp=0,t.noise_shaping_stop=0,t.use_best_huffman=0,t.full_outer_loop=0;break;case 8:e.quality=7;case 7:t.psymodel=1,t.noise_shaping=0,t.noise_shaping_amp=0,t.noise_shaping_stop=0,t.use_best_huffman=0,t.full_outer_loop=0;break;case 6:case 5:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),t.noise_shaping_amp=0,t.noise_shaping_stop=0,-1==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=0,t.full_outer_loop=0;break;case 4:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),t.noise_shaping_amp=0,t.noise_shaping_stop=0,-1==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=1,t.full_outer_loop=0;break;case 3:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),t.noise_shaping_amp=1,t.noise_shaping_stop=1,-1==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=1,t.full_outer_loop=0;break;case 2:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),0==t.substep_shaping&&(t.substep_shaping=2),t.noise_shaping_amp=1,t.noise_shaping_stop=1,-1==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=1,t.full_outer_loop=0;break;case 1:case 0:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),0==t.substep_shaping&&(t.substep_shaping=2),t.noise_shaping_amp=2,t.noise_shaping_stop=1,-1==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=1,t.full_outer_loop=0}}(e),e.athaa_type<0?o.ATH.useAdjust=3:o.ATH.useAdjust=e.athaa_type,o.ATH.aaSensitivityP=Math.pow(10,e.athaa_sensitivity/-10),null==e.short_blocks&&(e.short_blocks=h.short_block_allowed),e.short_blocks!=h.short_block_allowed||e.mode!=b.JOINT_STEREO&&e.mode!=b.STEREO||(e.short_blocks=h.short_block_coupled),e.quant_comp<0&&(e.quant_comp=1),e.quant_comp_short<0&&(e.quant_comp_short=0),e.msfix<0&&(e.msfix=0),e.exp_nspsytune=1|e.exp_nspsytune,e.internal_flags.nsPsy.attackthre<0&&(e.internal_flags.nsPsy.attackthre=K.NSATTACKTHRE),e.internal_flags.nsPsy.attackthre_s<0&&(e.internal_flags.nsPsy.attackthre_s=K.NSATTACKTHRE_S),e.scale<0&&(e.scale=1),e.ATHtype<0&&(e.ATHtype=4),e.ATHcurve<0&&(e.ATHcurve=4),e.athaa_loudapprox<0&&(e.athaa_loudapprox=2),e.interChRatio<0&&(e.interChRatio=0),null==e.useTemporal&&(e.useTemporal=!0),o.slot_lag=o.frac_SpF=0,e.VBR==m.vbr_off&&(o.slot_lag=o.frac_SpF=72e3*(e.version+1)*e.brate%e.out_samplerate|0),a.iteration_init(e),p.psymodel_init(e),0},this.lame_encode_flush=function(e,r,a,i){var o,s,l,c,u=e.internal_flags,p=f([2,1152]),_=0,d=u.mf_samples_to_encode-z.POSTDELAY,h=y(e);if(u.mf_samples_to_encode<1)return 0;for(o=0,e.in_samplerate!=e.out_samplerate&&n(),(l=e.framesize-d%e.framesize)<576&&(l+=e.framesize),e.encoder_padding=l,c=(d+l)/e.framesize;c>0&&_>=0;){var v=h-u.mf_size,m=e.frameNum;v*=e.in_samplerate,(v/=e.out_samplerate)>1152&&(v=1152),v<1&&(v=1),s=i-o,0==i&&(s=0),a+=_=this.lame_encode_buffer(e,p[0],p[1],v,r,a,s),o+=_,c-=m!=e.frameNum?1:0}return u.mf_samples_to_encode=0,_<0?_:(s=i-o,0==i&&(s=0),t.flush_bitstream(e),(_=t.copy_buffer(u,r,a,s,1))<0?_:(a+=_,s=i-(o+=_),0==i&&(s=0),e.write_id3tag_automatic&&n(),o))},this.lame_encode_buffer=function(r,a,i,s,l,c,f){var u=r.internal_flags,p=[null,null];if(4294479419!=u.Class_ID)return-3;if(0==s)return 0;!function(e,t){(null==e.in_buffer_0||e.in_buffer_nsamples<t)&&(e.in_buffer_0=o(t),e.in_buffer_1=o(t),e.in_buffer_nsamples=t)}(u,s),p[0]=u.in_buffer_0,p[1]=u.in_buffer_1;for(var _=0;_<s;_++)p[0][_]=a[_],u.channels_in>1&&(p[1][_]=i[_]);return function(r,a,i,o,s,l,c){var f,u,p,_,d,h=r.internal_flags,v=0,m=[null,null],b=[null,null];if(4294479419!=h.Class_ID)return-3;if(0==o)return 0;if((d=t.copy_buffer(h,s,l,c,0))<0)return d;if(l+=d,v+=d,b[0]=a,b[1]=i,A.NEQ(r.scale,0)&&A.NEQ(r.scale,1))for(u=0;u<o;++u)b[0][u]*=r.scale,2==h.channels_out&&(b[1][u]*=r.scale);if(A.NEQ(r.scale_left,0)&&A.NEQ(r.scale_left,1))for(u=0;u<o;++u)b[0][u]*=r.scale_left;if(A.NEQ(r.scale_right,0)&&A.NEQ(r.scale_right,1))for(u=0;u<o;++u)b[1][u]*=r.scale_right;2==r.num_channels&&1==h.channels_out&&n(),_=y(r),m[0]=h.mfbuf[0],m[1]=h.mfbuf[1];for(var g=0;o>0;){var M,T,R=[null,null];R[0]=b[0],R[1]=b[1];var C=new x;if(k(r,m,R,g,o,C),M=C.n_in,T=C.n_out,h.findReplayGain&&!h.decode_on_the_fly&&e.AnalyzeSamples(h.rgdata,m[0],h.mf_size,m[1],h.mf_size,T,h.channels_out)==w.GAIN_ANALYSIS_ERROR)return-6;if(o-=M,g+=M,h.channels_out,h.mf_size+=T,h.mf_samples_to_encode<1&&n(),h.mf_samples_to_encode+=T,h.mf_size>=_){var E=c-v;if(0==c&&(E=0),(f=S(r,m[0],m[1],s,l,E))<0)return f;for(l+=f,v+=f,h.mf_size-=r.framesize,h.mf_samples_to_encode-=r.framesize,p=0;p<h.channels_out;p++)for(u=0;u<h.mf_size;u++)m[p][u]=m[p][u+r.framesize]}}return v}(r,p[0],p[1],s,l,c,f)}}function Q(){this.setModules=function(e,t){}}function J(){this.setModules=function(e,t,n){}}function ee(){}function te(){this.setModules=function(e,t){}}H.SFBMAX=3*z.SBMAX_s,z.ENCDELAY=576,z.POSTDELAY=1152,z.MDCTDELAY=48,z.FFTOFFSET=224+z.MDCTDELAY,z.DECDELAY=528,z.SBLIMIT=32,z.CBANDS=64,z.SBPSY_l=21,z.SBPSY_s=12,z.SBMAX_l=22,z.SBMAX_s=13,z.PSFB21=6,z.PSFB12=6,z.BLKSIZE=1024,z.HBLKSIZE=z.BLKSIZE/2+1,z.BLKSIZE_s=256,z.HBLKSIZE_s=z.BLKSIZE_s/2+1,z.NORM_TYPE=0,z.START_TYPE=1,z.SHORT_TYPE=2,z.STOP_TYPE=3,z.MPG_MD_LR_LR=0,z.MPG_MD_LR_I=1,z.MPG_MD_MS_LR=2,z.MPG_MD_MS_I=3,z.fircoef=[-.1039435,-.1892065,5*-.0432472,-.155915,3898045e-23,.0467745*5,.50455,.756825,.187098*5],W.MFSIZE=3456+z.ENCDELAY-z.MDCTDELAY,W.MAX_HEADER_BUF=256,W.MAX_BITS_PER_CHANNEL=4095,W.MAX_BITS_PER_GRANULE=7680,W.BPC=320,H.SFBMAX=3*z.SBMAX_s,t.Mp3Encoder=function(e,t,a){1!=e&&n("fix cc: only supports mono");var i=new Z,o=new Q,s=new w,l=new A,c=new S,f=new $,u=new j,p=new k,_=new g,d=new te,h=new x,v=new y,m=new J,M=new ee;i.setModules(s,l,c,f,u,p,_,d,M),l.setModules(s,M,_,p),d.setModules(l,_),c.setModules(i),u.setModules(l,h,f,v),f.setModules(v,h,i.enc.psy),h.setModules(l),v.setModules(f),p.setModules(i,l,_),o.setModules(m,M),m.setModules(_,d,c);var T=i.lame_init();T.num_channels=e,T.in_samplerate=t,T.out_samplerate=t,T.brate=a,T.mode=b.STEREO,T.quality=3,T.bWriteVbrTag=!1,T.disable_reservoir=!0,T.write_id3tag_automatic=!1,i.lame_init_params(T);var R=1152,C=0|1.25*R+7200,E=r(C);this.encodeBuffer=function(t,n){1==e&&(n=t),t.length>R&&(R=t.length,E=r(C=0|1.25*R+7200));var a=i.lame_encode_buffer(T,t,n,t.length,E,0,C);return new Int8Array(E.subarray(0,a))},this.flush=function(){var e=i.lame_encode_flush(T,E,0,C);return new Int8Array(E.subarray(0,e))}}}t(),e.lamejs=t}(("object"==("undefined"==typeof window?"undefined":r(window))&&window.document?window:Object).Recorder)},function(e,t,n){"use strict";var r,a,i,o,s,l,c,f,u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};l="object"==("undefined"==typeof window?"undefined":u(window))&&!!window.document,c=(l?window:Object).Recorder,f=c.i18n,r=c,a=f.$T,i=l,o=function(e){return new s(e)},(s=function(e){var t={scale:2,speed:9,phase:21.8,fps:20,keep:!0,lineWidth:3,linear1:[0,"rgba(150,96,238,1)",.2,"rgba(170,79,249,1)",1,"rgba(53,199,253,1)"],linear2:[0,"rgba(209,130,255,0.6)",1,"rgba(53,199,255,0.6)"],linearBg:[0,"rgba(255,255,255,0.2)",1,"rgba(54,197,252,0.2)"]};for(var n in e)t[n]=e[n];if(this.set=e=t,e.compatibleCanvas)var r=this.canvas=e.compatibleCanvas;else{if(!i)throw new Error(a.G("NonBrowser-1",["WaveView"]));var o=e.elem;o&&("string"==typeof o?o=document.querySelector(o):o.length&&(o=o[0])),o&&(e.width=o.offsetWidth,e.height=o.offsetHeight);var s=this.elem=document.createElement("div");s.style.fontSize=0,s.innerHTML='<canvas style="width:100%;height:100%;"/>',r=this.canvas=s.querySelector("canvas"),o&&(o.innerHTML="",o.appendChild(s))}var l=e.scale,c=e.width*l,f=e.height*l;if(!c||!f)throw new Error(a.G("IllegalArgs-1",["WaveView width=0 height=0"]));r.width=c,r.height=f;var u=this.ctx=r.getContext("2d");this.linear1=this.genLinear(u,c,e.linear1),this.linear2=this.genLinear(u,c,e.linear2),this.linearBg=this.genLinear(u,f,e.linearBg,!0),this._phase=0}).prototype=o.prototype={genLinear:function(e,t,n,r){for(var a=e.createLinearGradient(0,0,r?0:t,r?t:0),i=0;i<n.length;)a.addColorStop(n[i++],n[i++]);return a},genPath:function(e,t,n){for(var r=[],a=this.set,i=a.scale,o=a.width*i,s=a.height*i/2,l=0;l<=o;l+=i){var c=(1+Math.cos(Math.PI+l/o*2*Math.PI))/2*s*t*Math.sin(2*Math.PI*(l/o)*e+n)+s;r.push(c)}return r},input:function(e,t,n){this.sampleRate=n,this.pcmData=e,this.pcmPos=0,this.inputTime=Date.now(),this.schedule()},schedule:function(){var e=this,t=e.set,n=Math.floor(1e3/t.fps);e.timer||(e.timer=setInterval((function(){e.schedule()}),n));var a=Date.now();if(!(a-(e.drawTime||0)<n)){e.drawTime=a;for(var i=e.sampleRate/t.fps,o=e.pcmData,s=e.pcmPos,l=Math.max(0,Math.min(i,o.length-s)),c=0,f=0;f<l;f++,s++)c+=Math.abs(o[s]);e.pcmPos=s,!l&&t.keep||e.draw(r.PowerLevel(c,l)),!l&&a-e.inputTime>1300&&(clearInterval(e.timer),e.timer=0)}},draw:function(e){var t=this.set,n=this.ctx,r=t.scale,a=t.width*r,i=t.height*r,o=t.speed/t.fps,s=this._phase-=o,l=s+o*t.phase,c=e/100,f=this.genPath(2,c,s),u=this.genPath(1.8,c,l);n.clearRect(0,0,a,i),n.beginPath();for(var p=0,_=0;_<=a;p++,_+=r)0==_?n.moveTo(_,f[p]):n.lineTo(_,f[p]);for(p--,_=a-1;_>=0;p--,_-=r)n.lineTo(_,u[p]);n.closePath(),n.fillStyle=this.linearBg,n.fill(),this.drawPath(u,this.linear2),this.drawPath(f,this.linear1)},drawPath:function(e,t){var n=this.set,r=this.ctx,a=n.scale,i=n.width*a;r.beginPath();for(var o=0,s=0;s<=i;o++,s+=a)0==s?r.moveTo(s,e[o]):r.lineTo(s,e[o]);r.lineWidth=n.lineWidth*a,r.strokeStyle=t,r.stroke()}},r.WaveView=o},function(e,t,n){"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n=function(e,t){var n=e[1]||"",r=e[3];if(!r)return n;if(t&&"function"==typeof btoa){var a=(o=r,s=btoa(unescape(encodeURIComponent(JSON.stringify(o)))),l="sourceMappingURL=data:application/json;charset=utf-8;base64,".concat(s),"/*# ".concat(l," */")),i=r.sources.map((function(e){return"/*# sourceURL=".concat(r.sourceRoot).concat(e," */")}));return[n].concat(i).concat([a]).join("\n")}var o,s,l;return[n].join("\n")}(t,e);return t[2]?"@media ".concat(t[2],"{").concat(n,"}"):n})).join("")},t.i=function(e,n){"string"==typeof e&&(e=[[null,e,""]]);for(var r={},a=0;a<this.length;a++){var i=this[a][0];null!=i&&(r[i]=!0)}for(var o=0;o<e.length;o++){var s=e[o];null!=s[0]&&r[s[0]]||(n&&!s[2]?s[2]=n:n&&(s[2]="(".concat(s[2],") and (").concat(n,")")),t.push(s))}},t}},function(e,t,n){"use strict";var r,a={},i=function(){return void 0===r&&(r=Boolean(window&&document&&document.all&&!window.atob)),r},o=function(){var e={};return function(t){if(void 0===e[t]){var n=document.querySelector(t);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}e[t]=n}return e[t]}}();function s(e,t){for(var n=[],r={},a=0;a<e.length;a++){var i=e[a],o=t.base?i[0]+t.base:i[0],s={css:i[1],media:i[2],sourceMap:i[3]};r[o]?r[o].parts.push(s):n.push(r[o]={id:o,parts:[s]})}return n}function l(e,t){for(var n=0;n<e.length;n++){var r=e[n],i=a[r.id],o=0;if(i){for(i.refs++;o<i.parts.length;o++)i.parts[o](r.parts[o]);for(;o<r.parts.length;o++)i.parts.push(v(r.parts[o],t))}else{for(var s=[];o<r.parts.length;o++)s.push(v(r.parts[o],t));a[r.id]={id:r.id,refs:1,parts:s}}}}function c(e){var t=document.createElement("style");if(void 0===e.attributes.nonce){var r=n.nc;r&&(e.attributes.nonce=r)}if(Object.keys(e.attributes).forEach((function(n){t.setAttribute(n,e.attributes[n])})),"function"==typeof e.insert)e.insert(t);else{var a=o(e.insert||"head");if(!a)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");a.appendChild(t)}return t}var f,u=(f=[],function(e,t){return f[e]=t,f.filter(Boolean).join("\n")});function p(e,t,n,r){var a=n?"":r.css;if(e.styleSheet)e.styleSheet.cssText=u(t,a);else{var i=document.createTextNode(a),o=e.childNodes;o[t]&&e.removeChild(o[t]),o.length?e.insertBefore(i,o[t]):e.appendChild(i)}}function _(e,t,n){var r=n.css,a=n.media,i=n.sourceMap;if(a&&e.setAttribute("media",a),i&&btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),e.styleSheet)e.styleSheet.cssText=r;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(r))}}var d=null,h=0;function v(e,t){var n,r,a;if(t.singleton){var i=h++;n=d||(d=c(t)),r=p.bind(null,n,i,!1),a=p.bind(null,n,i,!0)}else n=c(t),r=_.bind(null,n,t),a=function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(n)};return r(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;r(e=t)}else a()}}e.exports=function(e,t){(t=t||{}).attributes="object"==typeof t.attributes?t.attributes:{},t.singleton||"boolean"==typeof t.singleton||(t.singleton=i());var n=s(e,t);return l(n,t),function(e){for(var r=[],i=0;i<n.length;i++){var o=n[i],c=a[o.id];c&&(c.refs--,r.push(c))}e&&l(s(e,t),t);for(var f=0;f<r.length;f++){var u=r[f];if(0===u.refs){for(var p=0;p<u.parts.length;p++)u.parts[p]();delete a[u.id]}}}}},function(e,t,n){"use strict";var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"main"},[e._t("top"),e._v(" "),n("div",{staticClass:"mainBox"},[n("div",{staticClass:"pd"},[e._v("\r\n\t\t\t类型："+e._s(e.type)+"\r\n\t\t\t"),n("span",{staticStyle:{margin:"0 20px"}},[e._v("\r\n\t\t\t比特率: "),n("input",{directives:[{name:"model",rawName:"v-model",value:e.bitRate,expression:"bitRate"}],staticStyle:{width:"60px"},attrs:{type:"text"},domProps:{value:e.bitRate},on:{input:function(t){t.target.composing||(e.bitRate=t.target.value)}}}),e._v(" kbps\r\n\t\t\t")]),e._v("\r\n\t\t\t采样率: "),n("input",{directives:[{name:"model",rawName:"v-model",value:e.sampleRate,expression:"sampleRate"}],staticStyle:{width:"60px"},attrs:{type:"text"},domProps:{value:e.sampleRate},on:{input:function(t){t.target.composing||(e.sampleRate=t.target.value)}}}),e._v(" hz\r\n\t\t")]),e._v(" "),n("div",{staticClass:"btns"},[n("div",[n("button",{on:{click:e.recOpen}},[e._v("打开录音,请求权限")]),e._v(" "),n("button",{on:{click:e.recClose}},[e._v("关闭录音,释放资源")])]),e._v(" "),n("button",{on:{click:e.recStart}},[e._v("录制")]),e._v(" "),n("button",{staticStyle:{"margin-right":"80px"},on:{click:e.recStop}},[e._v("停止")]),e._v(" "),n("span",{staticStyle:{display:"inline-block"}},[n("button",{on:{click:e.recPause}},[e._v("暂停")]),e._v(" "),n("button",{on:{click:e.recResume}},[e._v("继续")])]),e._v(" "),n("span",{staticStyle:{display:"inline-block"}},[n("button",{on:{click:e.recPlayLast}},[e._v("播放")]),e._v(" "),n("button",{on:{click:e.recUploadLast}},[e._v("上传")]),e._v(" "),n("button",{on:{click:e.recDownLast}},[e._v("本地下载")])])])]),e._v(" "),n("div",{staticClass:"mainBox"},[n("div",{staticClass:"ctrlProcessWave",staticStyle:{height:"100px",width:"300px",border:"1px solid #ccc","box-sizing":"border-box",display:"inline-block","vertical-align":"bottom"}}),e._v(" "),n("div",{staticStyle:{height:"40px",width:"300px",display:"inline-block",background:"#999",position:"relative","vertical-align":"bottom"}},[n("div",{staticClass:"ctrlProcessX",staticStyle:{height:"40px",background:"#0B1",position:"absolute"},style:{width:e.powerLevel+"%"}}),e._v(" "),n("div",{staticClass:"ctrlProcessT",staticStyle:{"padding-left":"50px","line-height":"40px",position:"relative"}},[e._v(e._s(e.durationTxt+"/"+e.powerLevel))])])]),e._v(" "),n("div",{staticClass:"mainBox"},[n("AUDIO",{ref:"LogAudioPlayer",staticStyle:{width:"100%"}}),e._v(" "),n("div",{staticClass:"mainLog"},e._l(e.logs,(function(t){return n("div",{key:t.idx},[n("div",{style:{color:1==t.color?"red":2==t.color?"green":t.color}},[e._o(n("span",[e._v("["+e._s(e.getTime())+"]")]),0,t.idx),n("span",{domProps:{innerHTML:e._s(t.msg)}}),e._v(" "),t.res?[e._v("\r\n\t\t\t\t\t\t"+e._s(e.intp(t.res.rec.set.bitRate,3))+"kbps\r\n\t\t\t\t\t\t"+e._s(e.intp(t.res.rec.set.sampleRate,5))+"hz\r\n\t\t\t\t\t\t编码"+e._s(e.intp(t.res.blob.size,6))+"b\r\n\t\t\t\t\t\t["+e._s(t.res.rec.set.type)+"]"+e._s(t.res.durationTxt)+"ms \r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t"),n("button",{on:{click:function(n){return e.recdown(t.idx)}}},[e._v("下载")]),e._v(" "),n("button",{on:{click:function(n){return e.recplay(t.idx)}}},[e._v("播放")]),e._v(" "),n("span",{domProps:{innerHTML:e._s(t.playMsg)}}),e._v(" "),t.down?n("span",[n("span",{staticStyle:{color:"red"}},[e._v(e._s(t.down))]),e._v("\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t没弹下载？试一下链接或复制文本"),n("button",{on:{click:function(n){return e.recdown64(t.idx)}}},[e._v("生成Base64文本")]),e._v(" "),t.down64Val?n("textarea",{directives:[{name:"model",rawName:"v-model",value:t.down64Val,expression:"obj.down64Val"}],domProps:{value:t.down64Val},on:{input:function(n){n.target.composing||e.$set(t,"down64Val",n.target.value)}}}):e._e()]):e._e()]:e._e()],2)])})),0)],1),e._v(" "),e._t("bottom")],2)},a=[];r._withStripped=!0,n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}))},,function(e,t,n){"use strict";var r=i(n(9)),a=i(n(22));function i(e){return e&&e.__esModule?e:{default:e}}var o=new r.default({el:".rootView",data:{},components:{MainView:a.default},template:'\n<MainView ref="mainView">\n    <template #top>\n        <div class="topHead"></div>\n    </template>\n\n    <template #bottom>\n\t\t<div class="bottomBox"></div>\n    </template>\n</MainView>\n    '});window.vue_vue=r.default,window.vue_root=o,window.vue_main=o.$refs.mainView,console.log("mainView",vue_main),console.log("Vue",r.default),console.log("Recorder",vue_main.Rec)},function(e,t,n){"use strict";n.r(t);var r=n(19),a=n(3);for(var i in a)"default"!==i&&function(e){n.d(t,e,(function(){return a[e]}))}(i);n(23);var o=n(1),s=Object(o.a)(a.default,r.a,r.b,!1,null,null,null);s.options.__file="component/recorder.vue",t.default=s.exports},function(e,t,n){"use strict";var r=n(5);n.n(r).a},function(e,t,n){(e.exports=n(17)(!1)).push([e.i,"\nbody{\r\n\tword-wrap: break-word;\r\n\tbackground:#f5f5f5 center top no-repeat;\r\n\tbackground-size: auto 680px;\n}\npre{\r\n\twhite-space:pre-wrap;\n}\na{\r\n\ttext-decoration: none;\r\n\tcolor:#06c;\n}\na:hover{\r\n\tcolor:#f00;\n}\n.main{\r\n\tmax-width:700px;\r\n\tmargin:0 auto;\r\n\tpadding-bottom:80px\n}\n.mainBox{\r\n\tmargin-top:12px;\r\n\tpadding: 12px;\r\n\tborder-radius: 6px;\r\n\tbackground: #fff;\r\n\t--border: 1px solid #0b1;\r\n\tbox-shadow: 2px 2px 3px #aaa;\n}\n.btns button{\r\n\tdisplay: inline-block;\r\n\tcursor: pointer;\r\n\tborder: none;\r\n\tborder-radius: 3px;\r\n\tbackground: #0b1;\r\n\tcolor:#fff;\r\n\tpadding: 0 15px;\r\n\tmargin:3px 20px 3px 0;\r\n\tline-height: 36px;\r\n\theight: 36px;\r\n\toverflow: hidden;\r\n\tvertical-align: middle;\n}\n.btns button:active{\r\n\tbackground: #0a1;\n}\n.pd{\r\n\tpadding:0 0 6px 0;\n}\n.lb{\r\n\tdisplay:inline-block;\r\n\tvertical-align: middle;\r\n\tbackground:#00940e;\r\n\tcolor:#fff;\r\n\tfont-size:14px;\r\n\tpadding:2px 8px;\r\n\tborder-radius: 99px;\n}\r\n",""])}]);