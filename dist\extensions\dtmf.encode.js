/*
录音
https://github.com/xiangyuecn/Recorder
src: extensions/dtmf.encode.js
*/
!function(t){var e="object"==typeof window&&!!window.document,n=(e?window:Object).Recorder,r=n.i18n;!function(l,t,e,n){"use strict";l.DTMF_Encode=function(t,e,n,r){for(var i=Math.floor(e*(n||100)/1e3),a=Math.floor(e*(null==r?50:r)/1e3),o=new Int16Array(i+2*a),s=new Int16Array(i+2*a),u=k[t][0],c=k[t][1],d=0;d<i;d++){var h=.3*Math.sin(2*Math.PI*u*(d/e)),f=.3*Math.sin(2*Math.PI*c*(d/e));o[d+a]=32767*Math.max(-1,Math.min(1,h)),s[d+a]=32767*Math.max(-1,Math.min(1,f))}return x(o,0,s,0),o},l.DTMF_EncodeMix=function(t){return new r(t)};var r=function(t){var e=this;for(var n in e.set={duration:100,mute:25,interval:200},t)e.set[n]=t[n];e.keys="",e.idx=0,e.state={keyIdx:-1,skip:0}};r.prototype={add:function(t){this.keys+=t},mix:function(t,e,n){n||(n=0);var r=this,i=r.set,a=[],o=r.state,s=0;t:for(var u=n;u<t.length;u++){var c=t[u],d=r.keys.charAt(r.idx);if(d)for(;d;){if(o.skip){var h=c.length-s;if(h<=o.skip){o.skip-=h,s=0;continue t}s+=o.skip,o.skip=0}var f=o.keyPcm;o.keyIdx==r.idx&&o.cur>=f.length&&(o.keyIdx=-1),o.keyIdx!=r.idx&&(f=l.DTMF_Encode(d,e,i.duration,i.mute),o.keyIdx=r.idx,o.cur=0,o.keyPcm=f,a.push({key:d,data:f}));var k=x(c,s,f,o.cur,!0);if(o.cur=k.cur,s=k.last,k.cur>=f.length&&(r.idx++,d=r.keys.charAt(r.idx),o.skip=Math.floor(e*(i.interval-i.duration-2*i.mute)/1e3)),k.last>=c.length){s=0;continue t}}else o.skip=Math.max(0,o.skip-c.length)}return{newEncodes:a,hasNext:r.idx<r.keys.length}}};var x=function(t,e,n,r,i){for(var a=e,o=r;;a++,o++){if(a>=t.length||o>=n.length)return{last:a,cur:o};i&&(t[a]=0);var s,u=t[a],c=n[o];s=u<0&&c<0?u+c-u*c/-32767:u+c-u*c/32767,t[a]=s}},k={1:[697,1209],2:[697,1336],3:[697,1477],A:[697,1633],4:[770,1209],5:[770,1336],6:[770,1477],B:[770,1633],7:[852,1209],8:[852,1336],9:[852,1477],C:[852,1633],"*":[941,1209],0:[941,1336],"#":[941,1477],D:[941,1633]}}(n,0,r.$T)}();